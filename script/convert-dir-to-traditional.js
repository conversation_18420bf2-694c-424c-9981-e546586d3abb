#!/usr/bin/env node

const fs = require('fs-extra');
const glob = require('glob');
const path = require('path');
const OpenCC = require('opencc-js');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const os = require('os');
const crypto = require('crypto');

const CONFIG = {
  numWorkers: os.cpus().length,
  fileExtensions: ['.html', '.js', '.json', '.ts', '.tsx', '.md', '.txt', '.yaml', '.yml'],
  convertFilenames: true,
  specialMappings: {},
  protectedTextPatterns: [], 
  excludePatterns: ['node_modules/**', '.git/**', 'build/**']
};

function createConverter(from, to, customMapping) {
  const standardConverter = OpenCC.Converter({ from, to });
  if (customMapping && customMapping.length > 0) {
    const customConverter = OpenCC.CustomConverter(customMapping);
    return (text) => customConverter(standardConverter(text));
  }
  return standardConverter;
}

function protectAndConvert(content, converter, patterns) {
  if (!patterns || patterns.length === 0) {
    return converter(content);
  }
  const placeholders = new Map();
  const runId = crypto.randomUUID();
  let currentIndex = 0;
  let processedText = content;
  for (const pattern of patterns) {
    try {
      const regex = new RegExp(pattern, 'g');
      processedText = processedText.replace(regex, (match) => {
        const placeholder = `%%__PROTECTED_${runId}_${currentIndex++}__%%`;
        placeholders.set(placeholder, match);
        return placeholder;
      });
    } catch (e) {
      parentPort.postMessage({ type: 'warn', message: `无效的保护模式 (Invalid Regex): ${pattern}. 错误: ${e.message}` });
    }
  }
  if (placeholders.size === 0) {
    return converter(content);
  }
  let convertedText = converter(processedText);
  for (const [placeholder, originalText] of placeholders.entries()) {
    convertedText = convertedText.split(placeholder).join(originalText);
  }
  return convertedText;
}

// --- 工作线程逻辑 ---
if (!isMainThread) {
  const { inputDir, outputDir, convertFilenames, from, to, customMapping, protectedTextPatterns } = workerData;
  const workerConverter = createConverter(from, to, customMapping);

  parentPort.on('message', (files) => {
    let processedCount = 0;
    for (const file of files) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        const convertedContent = protectAndConvert(content, workerConverter, protectedTextPatterns);
        const relativePath = path.relative(inputDir, file);
        let convertedRelativePath = relativePath;
        if (convertFilenames) {
          convertedRelativePath = relativePath.split(path.sep).map(part => workerConverter(part)).join(path.sep);
        }
        const outputPath = path.join(outputDir, convertedRelativePath);
        fs.ensureDirSync(path.dirname(outputPath));
        fs.writeFileSync(outputPath, convertedContent, 'utf8');
      } catch (error) {
        parentPort.postMessage({ type: 'error', file, error: error.message });
      }
      processedCount++;
    }
    // 当这个批次的任务完成后，向主线程报告已处理的文件数量
    parentPort.postMessage({ type: 'done', count: processedCount });
    
    // <<< 关键修复：在这里添加此行代码。
    // 在报告任务完成后，立即关闭端口，以允许工作线程进程正常退出。
    // 这将触发主线程中的 'exit' 事件，从而解决程序挂起的问题。
    parentPort.close();
  });
}

async function main() {
  const args = process.argv.slice(2);
  const parsedArgs = {
    config: './s2t.config.json',
    input: '',
    output: '',
  };
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    if (arg === '--config' || arg === '-c') {
      parsedArgs.config = args[++i];
    } else if (arg === '--no-convert-filenames' || arg === '-n') {
      CONFIG.convertFilenames = false;
    } else if (!parsedArgs.input) {
      parsedArgs.input = arg;
    } else if (!parsedArgs.output) {
      parsedArgs.output = arg;
    }
  }
  if (!parsedArgs.input) {
    console.error('错误: 未指定输入目录。');
    console.log('用法: node convert.js <输入目录> [输出目录] [选项]');
    console.log('选项:');
    console.log('  -c, --config <路径>     指定JSON配置文件路径 (默认: ./s2t.config.json)');
    console.log('  -n, --no-convert-filenames 不转换文件名和目录名');
    process.exit(1);
  }
  const inputDir = path.resolve(process.cwd(), parsedArgs.input);
  const outputDir = parsedArgs.output 
    ? path.resolve(process.cwd(), parsedArgs.output) 
    : path.join(path.dirname(inputDir), `${path.basename(inputDir)}-traditional`);

  const configFile = path.resolve(process.cwd(), parsedArgs.config);
  if (fs.existsSync(configFile)) {
    try {
      const configFromFile = fs.readJsonSync(configFile);
      if (typeof configFromFile.convertFilenames === 'boolean' && !(args.includes('-n') || args.includes('--no-convert-filenames'))) {
         CONFIG.convertFilenames = configFromFile.convertFilenames;
      }
      Object.assign(CONFIG, configFromFile);
      console.log(`已从 ${configFile} 加载配置。`);
    } catch (e) {
      console.error(`加载或解析配置文件 ${configFile} 失败:`, e.message);
      process.exit(1);
    }
  } else {
    console.log('未找到配置文件，将使用默认配置。');
  }
  console.log('--- 开始处理 ---');
  console.log(`输入目录: ${inputDir}`);
  console.log(`输出目录: ${outputDir}`);
  console.log(`工作线程数: ${CONFIG.numWorkers}`);
  console.log(`转换文件名: ${CONFIG.convertFilenames}`);
  if (CONFIG.protectedTextPatterns.length > 0) {
    console.log(`受保护的文本模式: ${CONFIG.protectedTextPatterns.length} 个`);
  }
  fs.emptyDirSync(outputDir);
  const allFiles = glob.sync('**/*', {
    cwd: inputDir,
    nodir: true,
    ignore: CONFIG.excludePatterns,
    absolute: true,
  });
  const filesToConvert = [];
  const filesToCopy = [];
  const convertExtSet = new Set(CONFIG.fileExtensions);
  for (const file of allFiles) {
    if (convertExtSet.has(path.extname(file))) {
      filesToConvert.push(file);
    } else {
      filesToCopy.push(file);
    }
  }
  console.log(`总共找到 ${allFiles.length} 个文件：${filesToConvert.length} 个待转换，${filesToCopy.length} 个待复制。`);
  if (filesToConvert.length > 0) {
    await processFilesWithWorkers(filesToConvert, inputDir, outputDir);
  }
  if (filesToCopy.length > 0) {
    console.log('\n开始复制其他文件...');
    const copyConverter = createConverter('cn', 'hk', Object.entries(CONFIG.specialMappings));
    let copiedCount = 0;
    for(const file of filesToCopy) {
      const relativePath = path.relative(inputDir, file);
      let targetPath = path.join(outputDir, relativePath);
      if (CONFIG.convertFilenames) {
        const convertedRelativePath = relativePath.split(path.sep).map(part => copyConverter(part)).join(path.sep);
        targetPath = path.join(outputDir, convertedRelativePath);
      }
      fs.copySync(file, targetPath);
      copiedCount++;
    }
    console.log(`复制完成，共复制 ${copiedCount} 个文件。`);
  }
  console.log('\n--- 所有操作完成！ ---');
}

function processFilesWithWorkers(files, inputDir, outputDir) {
  return new Promise((resolve, reject) => {
    const numFiles = files.length;
    let finishedWorkers = 0;
    let totalProcessed = 0;
    const failures = [];
    const workerInitData = {
      inputDir,
      outputDir,
      convertFilenames: CONFIG.convertFilenames,
      from: 'cn',
      to: 'hk',
      customMapping: Object.entries(CONFIG.specialMappings),
      protectedTextPatterns: CONFIG.protectedTextPatterns,
    };
    console.log(`\n使用 ${CONFIG.numWorkers} 个工作线程开始转换 ${numFiles} 个文件...`);
    const workers = [];
    for (let i = 0; i < CONFIG.numWorkers; i++) {
      const worker = new Worker(__filename, { workerData: workerInitData });
      worker.on('message', (msg) => {
        switch (msg.type) {
          case 'done':
            totalProcessed += msg.count;
            const progress = Math.round((totalProcessed / numFiles) * 100);
            process.stdout.write(`进度: ${progress}% (${totalProcessed}/${numFiles})\r`);
            break;
          case 'error':
            failures.push(msg);
            break;
          case 'warn':
            console.warn(`\n[来自Worker的警告] ${msg.message}`);
            break;
        }
      });
      worker.on('error', (err) => {
        console.error('\n工作线程发生严重错误:', err);
        reject(err);
      });
      worker.on('exit', (code) => {
        finishedWorkers++;
        if (finishedWorkers === CONFIG.numWorkers) {
          process.stdout.write('\n');
          if (failures.length > 0) {
            console.error('\n转换过程中出现以下错误:');
            failures.forEach(f => console.error(`- 文件: ${f.file}\n  错误: ${f.error}`));
          }
          console.log(`转换完成！成功：${totalProcessed - failures.length}，失败：${failures.length}`);
          resolve();
        }
      });
      workers.push(worker);
    }
    const filesPerWorker = Math.ceil(numFiles / CONFIG.numWorkers);
    for (let i = 0; i < CONFIG.numWorkers; i++) {
      const start = i * filesPerWorker;
      const end = start + filesPerWorker;
      const chunk = files.slice(start, end);
      if (chunk.length > 0) {
        workers[i].postMessage(chunk);
      } else {
        workers[i].terminate();
      }
    }
  });
}

if (isMainThread) {
  main().catch(err => {
    console.error('\n程序执行期间发生未捕获的错误:', err);
    process.exit(1);
  });
}