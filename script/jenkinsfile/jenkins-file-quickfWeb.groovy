/**
 * JENKINS的流水线脚本文件--多分支流水线
 * 更新莲融前端
 * 同时更新莲融与财税智享两套前端
 */
builderEnv = [:]

properties([
        disableConcurrentBuilds(),//禁止并发构建
        buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '', numToKeepStr: '2')),
        parameters([ //构建任务的时候，可选的参数
                     booleanParam(name: 'NPM_INSTALL', defaultValue: false, description: '是否执行npm i命令'),
                      booleanParam(name: 'B_HK', defaultValue: false, description: '分支环境-HK')
        ])
])


if (env.BRANCH_NAME.startsWith("trunk")){
    builderEnv.BRANCHES = "DEV"

}else if (env.BRANCH_NAME.startsWith("test")){
    builderEnv.BRANCHES = "TEST"
}
builderEnv.SVN_LOG_LIMIT = 10 // 打印最近几条log
builderEnv.NPM_NODE_MODULES_DIR = "/media/repository/npm/${builderEnv.BRANCHES.toLowerCase()}/node_modules/quickfWeb"//存放node_modules的共享存储目录，不同项目不同环境不允许相同
builderEnv.NPM_REPOSITORY = 'http://**************:8082/repository/quickf_node_module_GROUP/'//npm公司私仓
builderEnv.SVN_URL_ROOT = scm.locations[0].remote.substring(0, scm.locations[0].remote.lastIndexOf("@"))
builderEnv.SVN_VERSION = 'HEAD' //scm.locations[0].remote.substring(scm.locations[0].remote.lastIndexOf("@") + 1)
//print ">>>>>>>>>>>>>>>>>>>> env ---------------------\n${env}\n<<<<<<<<<<<<<<<<<<<< env ---------------------\n\n"
print "\n>>>>>>>>>>>>>>>>>>>> builderEnv ---------------------\n${builderEnv}\n<<<<<<<<<<<<<<<<<<<< builderEnv ---------------------\n"
print "\n>>>>>>>>>>>>>>>>>>>> params ---------------------\n${params}\n<<<<<<<<<<<<<<<<<<<< params ---------------------\n"


//将下载更新node_module所需的package.json和.npmrc复制到共享存储上，在其上下载完整的node_modules
//源码下载后，将其node_modules通过软链挂载到已下好的共享存储目录，达到不需反复下载node_modules的目的
if (params.NPM_INSTALL==true) {
    podTemplate(inheritFrom: "slave-node", showRawYaml: true, label: 'jenkins-qf-quickfWeb-install') {
        node('jenkins-qf-quickfWeb-install') {
            stage('更新依赖包(npm i)') {
                sh 'ls -lah ~/.npm/'
                sh 'ls -lah /app/.npm'
                sh 'whoami'
                
                sh 'pnpm set store-dir /media/repository/.pnpm-store'
                checkout([$class: 'SubversionSCM', additionalCredentials: [], excludedCommitMessages: '', excludedRegions: '', excludedRevprop: '', excludedUsers: '', filterChangelog: false, ignoreDirPropChanges: false, includedRegions: '', 
                    locations: [[cancelProcessOnExternalsFail: true, credentialsId: '79c82c47-3cdf-4cb8-818a-0f1b93907ba5', depthOption: 'immediates', ignoreExternalsOption: true, local: '.', 
                    remote: "${builderEnv.SVN_URL_ROOT}@HEAD"]],
                    quietOperation: true, workspaceUpdater: [$class: 'UpdateUpdater']])
                sh(label: '创建共享存储目录:node_module', script: "mkdir -p ${builderEnv.NPM_NODE_MODULES_DIR}")
                sh(label: '设置NODE私仓', script: "npm config set registry ${builderEnv.NPM_REPOSITORY}")
                sh(label: '复制pacekage.json到共享存储', script: "cp ./package.json ${builderEnv.NPM_NODE_MODULES_DIR}/")
                sh(label: '复制pnpm-lock.yaml到共享存储', script: "cp ./pnpm-lock.yaml ${builderEnv.NPM_NODE_MODULES_DIR}/")
                sh(label: '复制.npmrc到共享存储', script: "cp ./.npmrc ${builderEnv.NPM_NODE_MODULES_DIR}/")
                dir("${builderEnv.NPM_NODE_MODULES_DIR}") {
                    sh "cat package.json"
                    sh(label: '从共享存储更新nodModules', script: "pnpm install")
                }
            }
        }
    }
}

def buildArgs = ["${builderEnv.BRANCHES.toLowerCase()}-build:agent","${builderEnv.BRANCHES.toLowerCase()}-build:enterprise"]
def buildParallel = [:]
buildArgs.each { args ->
    buildParallel[args]={
        podTemplate(inheritFrom: "slave-node", showRawYaml: true ,label : args.replaceAll(":","_")) {
            node(args.replaceAll(":","_")) {
                def buildType = args.split(":", 2)[1]
                stage("编译:${buildType}") {
                    checkout([$class: 'SubversionSCM', additionalCredentials: [], excludedCommitMessages: '', excludedRegions: '', excludedRevprop: '', excludedUsers: '', filterChangelog: false, ignoreDirPropChanges: false, includedRegions: '', 
                        locations: [[cancelProcessOnExternalsFail: true, credentialsId: '79c82c47-3cdf-4cb8-818a-0f1b93907ba5', depthOption: 'infinity', ignoreExternalsOption: true, local: '.', 
                        remote: "${builderEnv.SVN_URL_ROOT}@HEAD"]],
                        quietOperation: true, workspaceUpdater: [$class: 'UpdateUpdater']])
                    // sh(label: "SVN日志:quickfWeb", script: "svn log -v -l ${builderEnv.SVN_LOG_LIMIT} \"${builderEnv.SVN_URL_ROOT}\"")
                    // sh(label: '检出根目录:quickfWeb', script: "svn export --force \"${builderEnv.SVN_URL_ROOT}\" -r ${builderEnv.SVN_VERSION} ./ ")
                    sh(label: '创建共享存储软链:node_module', script: "ln -s ${builderEnv.NPM_NODE_MODULES_DIR}/node_modules ./node_modules")
                    sh(label: '设置NODE私仓', script: "npm config set registry ${builderEnv.NPM_REPOSITORY}")
                    sh(label: 'npm build', script: "npm run ${args}")
                    if(params.B_HK) {
                        sh(label: '转换繁体', script: "npm run hk:${buildType}")
                    }
                    // sh "ls -lah dist"
                }
                stage("同步dist到nginx的HTML目录"){//注意各自项目的nginx目录位置
                    sh(label: '删除多余的目录(后端更新的静态资源)', script: "rm -rf ./dist/${buildType}/static/data")
                    if(buildType == 'enterprise') {
                        sh(label: '复制dist到231', script: "sshpass -p hwsoft scp -o StrictHostKeyChecking=no -r ./dist/${buildType}/* qf_appuser@**************:/sharedisk/html/cmb/${builderEnv.BRANCHES.toLowerCase()}/")
                        if(params.B_HK) {
                            sh(label: '复制dist到231', script: "sshpass -p hwsoft scp -o StrictHostKeyChecking=no -r ./dist/${buildType}-hk/* qf_appuser@**************:/sharedisk/html/cmb/hk-${builderEnv.BRANCHES.toLowerCase()}/" )
                        }
                    }
                    if(buildType == 'agent') {
                        sh(label: '复制dist到231', script: "sshpass -p hwsoft scp -o StrictHostKeyChecking=no -r ./dist/${buildType}/* qf_appuser@**************:/sharedisk/html/${builderEnv.BRANCHES.toLowerCase()}/" )
                        if(params.B_HK) {
                            sh(label: '复制dist到231', script: "sshpass -p hwsoft scp -o StrictHostKeyChecking=no -r ./dist/${buildType}-hk/* qf_appuser@**************:/sharedisk/html/hk-${builderEnv.BRANCHES.toLowerCase()}/" )
                        }
                    }
                }
            }
        }
    }
}
parallel buildParallel
