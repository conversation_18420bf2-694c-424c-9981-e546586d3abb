<html lang="zh-CN"><head>
  <meta charset="UTF-8">
  <meta name="renderer" content="webkit">
  <meta name="force-rendering" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>请升级你的浏览器</title>
  <meta name="description" content="这是一个开放的旧版IE升级提示页，普通用户可通过本页介绍快速了解为何要升级旧版IE浏览器以及如何升级浏览器，开发者可引用本页提供的代码为网站接入旧版IE升级提示。">
  <!-- <link rel="canonical" href="https://support.dmeng.net/upgrade-your-browser.html"> -->
  <!-- <base href="https://support.dmeng.net" target="_blank"> -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <meta http-equiv="Cache-Control" content="no-siteapp">
  <meta http-equiv="Cache-Control" content="no-transform"> 
  <link type="text/css" rel="stylesheet" href="./css/support.style.css">
  <script>
      var l = window.location, s = l.search.substr(1),
          r = '', dr = document.referrer, r1 = s.match(/(^|&)referrer=((http|https)[^&]*)/), r2 = s.match(/^referrer=((http|https)\:\/\/.*)$/);
      if (r2 !== null) r = r2[1]; else if (r1 !== null) r = r1[2];
      if (r === '' && dr !== '' && dr.match(/\/(?:.*\.dmeng\.net|(?:(?:(?:www|m|wap|cn|search)\.)?(?:google|baidu|sogou|bing|so|yahoo|yandex|duckduckgo)\.com(?:\.[a-z]+)?))/) === null) {
          l.href = l.protocol + '//' + l.hostname + l.pathname + '?referrer=' + encodeURIComponent(dr) + '&' + s;
      }
      var url = decodeURIComponent(r);
      if (url.indexOf(window.location.hostname + '/') != -1) url = '';
      var targetUrlHTML = '';
      if (url !== '') {
          var m = url.match(/^(https?\:\/\/[^/]+\/)index\.html$/);
          targetUrlHTML = '&nbsp;<a href="'+url+'" onclick="alert(\'点击右键“复制快捷方式”（复制链接）到更先进的浏览器访问。\');return false;" rel="noreferrer">'+(m ? m[1] : url)+'</a>&nbsp;';
      }
  </script>
</head>
<body>
<div class="page">
<h1>是时候升级你的浏览器了</h1>
<p>你正在使用旧版 Internet Explorer（IE11以下版本或使用该内核的浏览器）。这意味着在升级浏览器前，你将无法访问此网站。</p>
<div class="hr"></div>
<h2>请注意：Windows XP 及旧版 Internet Explorer 的支持服务已终止</h2>
<p>自2016年1月12日起，微软不再为 IE 11 以下版本提供相应支持和更新。没有关键的浏览器安全更新，您的电脑可能易受有害病毒、间谍软件和其他恶意软件的攻击，它们可以窃取或损害您的业务数据和信息。请参阅 <a href="https://support.dmeng.net/end-of-ie-support.html">微软对旧版 Internet Explorer 的支持服务已终止的说明</a> 。</p>
<div class="hr"></div>
<h2>更先进的浏览器</h2>
<p class="targetline">推荐使用以下浏览器的最新版本。如果你的电脑已有以下浏览器的最新版本则直接使用该浏览器<strong>极速模式</strong>访问<b id="referrer"><script>document.write(targetUrlHTML);</script></b>即可。</p>

<!-- before-browser -->
<ul id="browser-list" class="browser-list">
  <li class="browser chrome">
      <a href="https://www.google.cn/chrome/thank-you.html?standalone=1&amp;statcb=0&amp;installdataindex=empty" id="browser-chrome-link"><img src="./images/chrome.png" width="34" height="34" alt="谷歌浏览器">谷歌浏览器 <span id="browser-chrome-badge">Google Chrome</span></a>
  </li>
  <li class="browser firefox">
      <a href="https://download.mozilla.org/?product=firefox-latest-ssl&amp;os=win&amp;lang=zh-CN" id="browser-firefox-link"><img src="./images/firefox.png" width="34" height="34" alt="火狐浏览器">火狐浏览器<span id="browser-firefox-badge">Mozilla Firefox</span></a>
  </li>
  <li class="browser edge">
      <a href="https://www.microsoft.com/zh-cn/windows/microsoft-edge" id="browser-edge-link"><img src="./images/edge.png" width="34" height="34" alt="微软浏览器">微软浏览器<span id="browser-edge-badge">Microsoft Edge</span></a>
  </li>
  <li class="browser ie2345">
      <a href="https://dn-2345.cdn.bcebos.com/2345explorer/p8_k2345886_v2.0.exe" id="browser-ie2345-link"><img src="./images/ie2345.png" width="34" height="34" alt="2345浏览器">2345加速浏览器<span id="browser-ie2345-badge">双内核安全版</span></a>
  </li>
  <li class="browser se360">
      <a href="https://dl.360safe.com/netunion/20140425/360se%2b75526%2bn1abed0ce91.exe" id="browser-se360-link"><img src="./images/se360.png" width="34" height="34" alt="360安全浏览器">360安全浏览器<span id="browser-se360-badge">360用户选择</span></a>
  </li>
  <li class="browser qqbrowser">
      <a href="https://qqbrowser-1251013107.file.myqcloud.com/QQBrowser_subid%40100002_urlid%40100002.exe" id="browser-qqbrowser-link"><img src="./images/qqbrowser.png" width="34" height="34" alt="QQ浏览器9">QQ浏览器9<span id="browser-qqbrowser-badge">腾讯用户推荐</span></a>
  </li>
  <li class="browser clearleft"></li>
</ul>
<p>切换方法</p>

  <img src="./images/10.png" alt="" srcset="">

<!-- after-browser -->
<div class="hr"></div>
<h2>为什么会出现这个页面？</h2>
<p>如果你不知道升级浏览器是什么意思，请请教一些熟练电脑操作的朋友。如果你使用的不是IE6/7/8/9/10，而是360浏览器、QQ浏览器、搜狗浏览器等，出现这个页面是因为你使用的不是该浏览器的最新版本，升级至最新即可。</p>

</body></html>