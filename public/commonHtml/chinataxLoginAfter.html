<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>Document</title>
</head>
<body>
    <span id="info" style="padding-right: 20px;"></span><a id="relogin" href="javascript:window.close();" style="display: none;">关闭</a>
  <script>
    function sendLoginSuccess() {
      window.opener.postMessage('SUCCESS')
    }
    function getQueryVariable(variable) {
    	var query = window.location.search.substring(1);
      var vars = query.split("&");
    	for (var i = 0; i < vars.length; i++) {
    		var pair = vars[i].split("=");
    		if (pair[0] == variable) {
    			return pair[1];
    		}
    	}
    	return (false);
    }
    var errorMessage = getQueryVariable('message');
    var errorId = getQueryVariable('errorId');
    var code = getQueryVariable('code');
    if(code === 'SUCCESS') {
      sendLoginSuccess();
    } else {
      if (errorMessage) {
        document.querySelector('#info').innerHTML = '登录失败，错误为：' + decodeURI(errorMessage) + '  ' + decodeURI(errorId);
        document.querySelector('#relogin').style.display = 'block';
      }
    }
    
    
  </script>
</body>
</html>