<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>公众号授权</title>
</head>
<body>
    
  <script>
    function getQueryVariable(variable) {
    	var query = window.location.search.substring(1);
      var vars = query.split("&");
    	for (var i = 0; i < vars.length; i++) {
    		var pair = vars[i].split("=");
    		if (pair[0] == variable) {
    			return pair[1];
    		}
    	}
    	return (false);
    }
    var url = getQueryVariable('url');
    if (url) {
      console.log(decodeURIComponent(url))
      window.location.href = decodeURIComponent(url)
    } else {
    }
    
  </script>
</body>
</html>