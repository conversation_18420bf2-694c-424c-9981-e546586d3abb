await setupStorageAndCookies()
await gotoEtaxPage()


async function setupStorageAndCookies() {
  context.logger.print('准备设置 localStorage & cookies...')

  const loginInfo = context.loginInfo

  const data = parseCookie(loginInfo.etax.cookie, loginInfo.etax.access_token)
  const cookieArr = makeCookieItems(data)

  const options = {
    timeout: 30000,
    preventRedirect: true,
    preventImage: true,
    preventStyle: true,
    preventFont: true,
    preventScript: true,
  }

  await Promise.allSettled([
    context.execOnNewPage('https://etax.guangdong.chinatax.gov.cn/xxmh', async newPage => {
      try {
        await newPage.setCookie(...cookieArr)
      } catch (err) {
        context.logger.print('设置 cookie 出错')
      }
      try {
        await newPage.evaluate((data) => {
          Object.keys(data).forEach(key => {
            const value = data[key]
            if (value) {
              window.sessionStorage.setItem(key, value)
            } else {
              window.sessionStorage.removeItem(key)
            }
          })
        }, { qybdid: 'AFD507E7141A498AA0FFFB5BE67F7A3E' })
      } catch (err) {
        context.logger.print('设置 localStorage 出错')
      }
    }, options),

    context.execOnNewPage('https://tpass.guangdong.chinatax.gov.cn:8443', async newPage => {
      try {
        await newPage.setCookie(...cookieArr)
      } catch (err) {
        context.logger.print('设置 cookie 出错')
      }
      try {
        await newPage.evaluate((data) => {
          Object.keys(data).forEach(key => {
            const value = data[key]
            if (value) {
              window.localStorage.setItem(key, value)
            } else {
              window.localStorage.removeItem(key)
            }
          })
        }, loginInfo.tpass.localStorage)
      } catch (err) {
        context.logger.print('设置 localStorage 出错')
      }
    }, options),

    context.execOnNewPage('https://dppt.guangdong.chinatax.gov.cn:8443', async newPage => {
      try {
        await newPage.setCookie(...cookieArr)
      } catch (err) {
        context.logger.print('设置 cookie 出错')
      }
      try {
        await newPage.evaluate((data) => {
          Object.keys(data).forEach(key => {
            const value = data[key]
            if (value) {
              window.localStorage.setItem(key, value)
            } else {
              window.localStorage.removeItem(key)
            }
          })
        }, loginInfo.tpass.localStorage)
      } catch (err) {
        context.logger.print('设置 localStorage 出错')
      }
    }, options),
  ])
}

async function gotoEtaxPage() {
  context.logger.print('打开目标页面...')
  const pageUrl = 'https://etax.guangdong.chinatax.gov.cn/xxmh/html/index_login.html?ticket=ST-1938372-1GzYtgSRbccA1zuXD2RR-gddzswj'
  const page = await context.ensureEmptyPage()
  await page.goto(pageUrl)
  context.logger.print('目标页面已打开')
}

/**
 * @param {string} cookie
 * @param {string} token
 * @returns {Record<string, string>}
 */
function parseCookie(cookie, token) {
  const data = cookie
    .split(';')
    .map(part => part.trim())
    .map(part => part.split('='))
    .reduce((acc, [key, value]) => {
      acc[key] = value
      return acc
    }, {})

  data.token = token
  return data
}

/**
 * @param {Record<string, string>} data
 * @returns {Array<{ name: string; value: string; expires: number; domain: string; path: string; httpOnly: boolean; secure: boolean; }>}
 */
function makeCookieItems(data) {
  const arr = []

  const push = (data) => {
    const item = Object.assign({
      httpOnly: true, // 可以通过非HTTP访问cookie
      secure: false, // 仅通过HTTPS传输cookie
      domain: 'etax.guangdong.chinatax.gov.cn',
      path: '/',
    }, data)
    arr.push(item)
  }

  // 该 cookie 必须
  if (data.DZSWJ_TGC) {
    push({ name: 'DZSWJ_TGC', value: data.DZSWJ_TGC, domain: '.guangdong.chinatax.gov.cn', })
  }
  if (data.SERVERID) {
    push({ name: 'SERVERID', value: data.SERVERID, })
  }
  if (data.acw_tc) {
    push({ name: 'acw_tc', value: data.acw_tc, expires: (new Date().getTime() + 1800000) / 1000, })
  }
  if (data.SYS_CHANNEL_ID) {
    push({ name: 'SYS_CHANNEL_ID', value: data.SYS_CHANNEL_ID, })
  }
  if (data.JSESSIONID) {
    push({ name: 'JSESSIONID', value: data.JSESSIONID, path: '/sso' })
  }
  if (data.token) {
    push({ name: 'token', value: data.token, domain: 'tpass.guangdong.chinatax.gov.cn', })
  }


  if (data.TGC) {
    push({ name: 'TGC', value: data.TGC, domain: '.guangdong.chinatax.gov.cn', })
  }
  if (data.dzswj_lang) {
    push({ name: 'dzswj_lang', value: data.dzswj_lang, })
  }

  return arr
}
