﻿<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title></title>
    <style>
    .errmsg {  width: 320px; margin: 0 auto; background:url("/static/imgs/error.png") 0 200px no-repeat; padding-left:150px; height: 100%;}
    .errmsg h4 { font-size: 28px; color:#505050; font-weight: normal; margin-bottom: 26px;padding-top: 200px; }
    .errmsg h6 { font-size:14px; color: #666; font-weight: normal; line-height: 2;}
    .close_btn{
      display: inline-block;
      line-height: 1;
      white-space: nowrap;
      cursor: pointer;
      background: #fff;
      border: 1px solid #dcdfe6;
      color: #606266;
      -webkit-appearance: none;
      text-align: center;
      box-sizing: border-box;
      outline: none;
      margin: 0;
      transition: .1s;
      font-weight: 500;
      border-radius: 20px;
      padding: 12px 23px;
    }
    </style>
  </head>
  <body>
    <div class=" body_content">
      <div class="errmsg">
        <h4>抱歉，页面出错了！</h4>
        <h6 id="errorMessage"></h6>
        <h6 id="errorId"></h6>
        <button class="close_btn">关闭当前页面</button>
      </div>
    </div>
  </body>
  <script>
    function getQueryVariable(variable) {
      var query = window.location.search.substring(1);
      var vars = query.split("&");
      for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
          return pair[1];
        }
      }
      return (false);
    }
    document.querySelector('.close_btn').addEventListener('click', function() {
      window.close()
    })

    var errorMessage = getQueryVariable('errorMessage');
    var errorId = getQueryVariable('errorId');

    document.querySelector('#errorMessage').innerHTML = errorMessage ? decodeURIComponent(errorMessage) : '您正在访问的页面因为某种原因出错了。请确认网页链接是否正确或稍候刷新再试。'
    document.querySelector('#errorId').innerHTML = errorId ? `事件ID：${decodeURIComponent(errorId)}` : ''
  </script>
</html>

