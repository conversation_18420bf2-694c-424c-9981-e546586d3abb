<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>公众号授权</title>
    <style>
      body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 8px;
          height: 100vh;
          background-color: #f5f5f5;
          display: none;
      }
      .error-container {
          text-align: center;
          padding-top: 20vh;
          /* background-color: #fff;
          border-radius: 10px; */
          /* box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); */
          /* padding: 20px;
          width: 90%;
          max-width: 400px; */
      }
      .error-container img {
          width: 38vw;
          margin-bottom: 20px;
          max-width: 200px;
      }
      .error-title {
          font-size: 18px;
          font-weight: bold;
          color: #333;
          margin-bottom: 10px;
      }
      .error-message {
          font-size: 16px;
          color: #A6A6A6;
      }
      #relogin{
        display: none;
      }
  </style>
</head>
<body>
  <div class="error-container">
      <img src="/static/imgs/icons/h5Error.png" alt="错误图标">
      <div class="error-message"></div>
      <a id="relogin" href="javascript:history.back(-1);" style="display: none;">返回</a>
      <a id="toLoginPage" href="javascript:toLoginPage();" style="display: none;">返回登录页</a>
  </div>
  <script>
      function appendParameterToUrl(url, parameterName, parameterValue) {
        // 检查URL是否已经包含参数
        var separator = url.indexOf('?') !== -1 ? '&' : '?';
        
        // 构建新的URL，将参数追加到末尾
        var newUrl = url + separator + encodeURIComponent(parameterName) + '=' + parameterValue;
        
        return newUrl;
      }
      function sendAuthSuccess() {
        // 创建一个新的消息对象
        const message = {
          type: 'authSuccess',
          data: {
            message: '授权成功',
          },
        }

        // 向父级页面发送消息
        window.opener.postMessage(message, window.opener.location.origin)
      }
      function getQueryVariable(variable) {
        var query = window.location.search.substring(1)
        var vars = query.split('&')
        for (var i = 0; i < vars.length; i++) {
          var pair = vars[i].split('=')
          if (pair[0] == variable) {
            return pair[1]
          }
        }
        return ''
      }
      function showErrorMessage(message) {
        document.querySelector('.error-message').innerHTML = message
        document.body.style.display = 'block'
      }
      var errorMessage = decodeURIComponent(getQueryVariable('errorMessage'))
      var source = getQueryVariable('source')
      var appid = getQueryVariable('appid')
      var redirect = decodeURIComponent(getQueryVariable('redirect'))
      function toLoginPage() {
        window.location.href = window.location.origin + `/crmMobile.html#/auth?appid=${appid}`
      }
      // 登录场景
      if(source === 'login') {
        if(errorMessage.indexOf('微信用户未绑定平台账号') !== -1) {
          showErrorMessage(errorMessage)
          document.querySelector('#toLoginPage').style.display = 'block'
        } else {
          try{
            window.location.href = redirect ? appendParameterToUrl(redirect, 'appid', appid) : window.location.origin + `/crmMobile.html#/userCenter?appid=${appid}`
          } catch(e) {
            showErrorMessage(e)
            document.querySelector('#relogin').style.display = 'block'
          }
          
        }
      } else if (errorMessage) {
        showErrorMessage(errorMessage)
        document.querySelector('#relogin').style.display = 'block'
      } else if(source === 'componentAuthorized') {
        // 公众号授权第三方场景
        sendAuthSuccess()
      } else if(source === 'userBinding') {
         // 用户授权绑定公众号场景
        window.location.href = window.location.origin + '/crmMobile.html#/userCenter'
      }
        // window.close();
      
    </script>
  </body>
</html>
