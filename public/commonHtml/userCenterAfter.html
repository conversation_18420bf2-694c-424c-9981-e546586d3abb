<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>Document</title>
</head>
<body>
    <span id="info" style="padding-right: 20px;"></span><a id="relogin" href="javascript:history.back(-1);" style="display: none;">重新登录</a>
  <script>
    function setCookie(name, value, domain) {
    var Days = 30; 
    var exp = new Date(); 
    exp.setTime(exp.getTime() + Days*24*60*60*1000); 
      document.cookie = name + '=' + escape(value) + ';path=/;expires=' + exp.toGMTString();
    }
    function sendLoginSuccess() {
      setCookie('usercenter_loginSuccess', 'true', document.domain);
    }
    function getQueryVariable(variable) {
    	var query = window.location.search.substring(1);
      var vars = query.split("&");
    	for (var i = 0; i < vars.length; i++) {
    		var pair = vars[i].split("=");
    		if (pair[0] == variable) {
    			return pair[1];
    		}
    	}
    	return (false);
    }
    var errorMessage = getQueryVariable('errorMessage');
    if (errorMessage) {
      document.querySelector('#info').innerHTML = '登录失败，错误为：' + decodeURI(errorMessage);
      document.querySelector('#relogin').style.display = 'block';
    } else {
      sendLoginSuccess();
      window.close();
    }
    
  </script>
</body>
</html>