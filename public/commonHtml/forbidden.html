﻿<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title>票靓在线大会计</title>
    <!-- Script for polyfilling Promises on IE9 and 10 -->
  </head>
  <body>
    <div class="errmsg">
      <h4>抱歉，页面访问失败！</h4>
      <h6>当前页面禁止访问，原因是服务器正在更新维护或没有正确的访问权限。</h6>
    </div>
    <div class="notice">
      <h4></h4>
      <h6>
        <ul><li>尊敬的用户：</li><li> 您好！为给您提供更优质的服务，我司将于12月8日18:00至12月9日6:00进行系统升级维护。</li><li>    升级期间网站系统将会出现无法打开界面的情况，请您错开升级时间，待网站系统恢复正常后再进行操作。</li><li>    以上给您带来的不便，敬请谅解！感谢您的理解与支持。祝您生活愉快！</li></ul>
      </h6>
    </div>
<script type="text/javascript">
let noticeContent = '';
let noticeTitle = '';
const xmlhttp = new XMLHttpRequest();
xmlhttp.open('GET','/static/data/sysnotices/allUsers.json',true);
xmlhttp.send(null);
xmlhttp.onreadystatechange = function () {
  if(xmlhttp.readyState == 4 && xmlhttp.status == 200) {
    console.log(xmlhttp);
    let res = JSON.parse(xmlhttp.response);
    console.log(res);
    res = [
	{
		"noticeContent":"<p>尊敬的用户：</p>\n<p>&nbsp; &nbsp; &nbsp; 您好！我司将于2024年06月27日18:00至06月28日6：00进行系统升级维护。 升级期间网站系统将会出现无法打开界面的情况，请您错开升级时间，待网站系统恢复正常后再进行操作。 以上给您带来的不便，敬请谅解！感谢您的理解与支持。祝您生活愉快！</p>",
		"noticeId":112,
		"noticeModifyDate":1719306024000,
		"noticePublicDate":1719305996000,
		"noticeScope":1,
		"noticeTitle":"2024年6月27日系统维护公告",
		"noticeValidBeginDate":1719288000000,
		"noticeValidEndDate":1719547200000
	}
]
    if(res.length > 0) {
      noticeContent = res[0].noticeContent;
      noticeTitle = res[0].noticeTitle;
      document.querySelector('.notice h4').innerHTML = noticeTitle;
      document.querySelector('.notice h6').innerHTML = noticeContent;
      document.querySelector('.errmsg').style.display = 'none'
      document.querySelector('.notice').style.display = 'block'
    }
    
  }
}
</script>

<style>
  .errmsg { width: 320px;height: 100%; margin:0 auto; background:url("/static/imgs/forbidden.png") 0 100% no-repeat; padding-left:150px; }
  .errmsg h4 { font-size: 28px; color:#eb4f4f; font-weight: normal; margin-bottom: 26px;padding-top: 200px; }
  .errmsg h6 { font-size: 14px; color: #999; font-weight: normal; line-height: 2}
  .notice h6 ul {margin: 0;padding: 0;}
  .notice { padding-top: 15vh; margin:0 auto; width: 320px;height: 100%; display: none;}
  .notice h4 {font-size: 20px; color:#7e0909; font-weight: normal; }
  .notice h6 { font-size: 14px; color: #999; font-weight: normal}
  li { list-style: none; }
</style>
  </body>
 
</html>

