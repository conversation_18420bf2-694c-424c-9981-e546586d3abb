<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>公众号授权</title>
</head>
<body>
  <script>
    function doLoginWx(appid, component_appid, redirect) {
      const targetUrl =
        "https://open.weixin.qq.com/connect/oauth2/authorize?" +
        "appid=" +
        appid +
        "&response_type=code&scope=snsapi_base" +
        `&state=${Math.floor(Math.random() * 100000000)}&component_appid=` +
        component_appid +
        `&redirect_uri=${window.location.origin}%2Fauth%2Flogin%2FqfWechatComponent%3FcomponentAppId%3D${component_appid}%26redirect%3D${encodeURIComponent(redirect)}` +
        "#wechat_redirect";
      console.log(targetUrl);
      window.location.href = targetUrl;
    }
    function getQueryVariable(variable) {
    	var query = window.location.search.substring(1);
      var vars = query.split("&");
    	for (var i = 0; i < vars.length; i++) {
    		var pair = vars[i].split("=");
    		if (pair[0] == variable) {
    			return pair[1];
    		}
    	}
    	return '';
    }
    var appid = getQueryVariable('appid');
    var component_appid = getQueryVariable('component_appid');
    var redirect = getQueryVariable('redirect');
    doLoginWx(appid, component_appid, redirect)
    
  </script>
</body>
</html>