<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>用户中心</title>
  <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js"></script>
</head>
<body>
	<span id="info" style="padding-right: 20px;"></span><a id="relogin" href="javascript:history.back(-1);" style="display: none;">重新登录</a>
  <script>
    
    function getQueryVariable(variable) {
    	var query = window.location.search.substring(1);
    	var vars = query.split("&");
    	for (var i = 0; i < vars.length; i++) {
    		var pair = vars[i].split("=");
    		if (pair[0] == variable) {
    			return pair[1];
    		}
    	}
    	return (false);
    }
    console.log('session', getQueryVariable('sessiontoken'));
    // alert(window.location.href);
    var errorMessage = getQueryVariable('errorMessage');
    if (errorMessage) {
      document.querySelector('#info').innerHTML = '登录失败，错误为：' + decodeURI(errorMessage);
      document.querySelector('#relogin').style.display = 'block';
    } else {
      document.querySelector('#info').innerHTML = '登录成功。';
      setTimeout(() => {
        wx.miniProgram.switchTab({ url: '/pages/remburse/home/<USER>' });
        wx.miniProgram.postMessage({ data: getQueryVariable('sessiontoken') });
      }, 500);
    }
    // setTimeout(function () {
    	// alert('xxx');
    // }, 3000);
  </script>
</body>
</html>