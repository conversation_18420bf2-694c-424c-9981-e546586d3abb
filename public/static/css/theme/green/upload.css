@charset "UTF-8";
/* BEM support Func
 -------------------------- */
/* Element Chalk Variables */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* 53a8ff */
/* 66b1ff */
/* 79bbff */
/* 8cc5ff */
/* a0cfff */
/* b3d8ff */
/* c6e2ff */
/* d9ecff */
/* ecf5ff */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* Size
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Empty
-------------------------- */
/* Descriptions
-------------------------- */
/* Skeleton 
--------------------------*/
/* Svg
--------------- */
/* Result
-------------------------- */
/* Break-point
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
/* BEM support Func
 -------------------------- */
/* Element Chalk Variables */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* 53a8ff */
/* 66b1ff */
/* 79bbff */
/* 8cc5ff */
/* a0cfff */
/* b3d8ff */
/* c6e2ff */
/* d9ecff */
/* ecf5ff */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* Size
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Empty
-------------------------- */
/* Descriptions
-------------------------- */
/* Skeleton 
--------------------------*/
/* Svg
--------------- */
/* Result
-------------------------- */
/* Break-point
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
/* Element Chalk Variables */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* 53a8ff */
/* 66b1ff */
/* 79bbff */
/* 8cc5ff */
/* a0cfff */
/* b3d8ff */
/* c6e2ff */
/* d9ecff */
/* ecf5ff */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* Size
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Empty
-------------------------- */
/* Descriptions
-------------------------- */
/* Skeleton 
--------------------------*/
/* Svg
--------------- */
/* Result
-------------------------- */
/* Break-point
--------------------------*/
/* Break-point
--------------------------*/
.el-progress {
  position: relative;
  line-height: 1; }
  .el-progress__text {
    font-size: 14px;
    color: #262626;
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px;
    line-height: 1; }
    .el-progress__text i {
      vertical-align: middle;
      display: block; }
  .el-progress--circle, .el-progress--dashboard {
    display: inline-block; }
    .el-progress--circle .el-progress__text, .el-progress--dashboard .el-progress__text {
      position: absolute;
      top: 50%;
      left: 0;
      width: 100%;
      text-align: center;
      margin: 0;
      -webkit-transform: translate(0, -50%);
      transform: translate(0, -50%); }
      .el-progress--circle .el-progress__text i, .el-progress--dashboard .el-progress__text i {
        vertical-align: middle;
        display: inline-block; }
  .el-progress--without-text .el-progress__text {
    display: none; }
  .el-progress--without-text .el-progress-bar {
    padding-right: 0;
    margin-right: 0;
    display: block; }
  .el-progress--text-inside .el-progress-bar {
    padding-right: 0;
    margin-right: 0; }
  .el-progress.is-success .el-progress-bar__inner {
    background-color: #88cc48; }
  .el-progress.is-success .el-progress__text {
    color: #88cc48; }
  .el-progress.is-warning .el-progress-bar__inner {
    background-color: #e6a23c; }
  .el-progress.is-warning .el-progress__text {
    color: #e6a23c; }
  .el-progress.is-exception .el-progress-bar__inner {
    background-color: #c0192a; }
  .el-progress.is-exception .el-progress__text {
    color: #c0192a; }

.el-progress-bar {
  padding-right: 50px;
  display: inline-block;
  vertical-align: middle;
  width: 100%;
  margin-right: -55px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box; }
  .el-progress-bar__outer {
    height: 6px;
    border-radius: 100px;
    background-color: #ebeef5;
    overflow: hidden;
    position: relative;
    vertical-align: middle; }
  .el-progress-bar__inner {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    background-color: #39C4BD;
    text-align: right;
    border-radius: 100px;
    line-height: 1;
    white-space: nowrap;
    -webkit-transition: width 0.6s ease;
    transition: width 0.6s ease; }
    .el-progress-bar__inner::after {
      display: inline-block;
      content: "";
      height: 100%;
      vertical-align: middle; }
  .el-progress-bar__innerText {
    display: inline-block;
    vertical-align: middle;
    color: #ffffff;
    font-size: 12px;
    margin: 0 5px; }

@-webkit-keyframes progress {
  0% {
    background-position: 0 0; }
  100% {
    background-position: 32px 0; } }

@keyframes progress {
  0% {
    background-position: 0 0; }
  100% {
    background-position: 32px 0; } }

/* Element Chalk Variables */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* 53a8ff */
/* 66b1ff */
/* 79bbff */
/* 8cc5ff */
/* a0cfff */
/* b3d8ff */
/* c6e2ff */
/* d9ecff */
/* ecf5ff */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* Size
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Empty
-------------------------- */
/* Descriptions
-------------------------- */
/* Skeleton 
--------------------------*/
/* Svg
--------------- */
/* Result
-------------------------- */
/* Break-point
--------------------------*/
/* Break-point
--------------------------*/
.el-upload {
  display: inline-block;
  text-align: center;
  cursor: pointer;
  outline: none;
  /* 照片墙模式 */ }
  .el-upload__input {
    display: none; }
  .el-upload__tip {
    font-size: 12px;
    color: #262626;
    margin-top: 7px; }
  .el-upload iframe {
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    opacity: 0;
    filter: alpha(opacity=0); }
  .el-upload--picture-card {
    background-color: #fbfdff;
    border: 1px dashed #c0ccda;
    border-radius: 6px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 148px;
    height: 148px;
    cursor: pointer;
    line-height: 146px;
    vertical-align: top; }
    .el-upload--picture-card i {
      font-size: 28px;
      color: #8c939d; }
    .el-upload--picture-card:hover {
      border-color: #39C4BD;
      color: #39C4BD; }
  .el-upload:focus {
    border-color: #39C4BD;
    color: #39C4BD; }
    .el-upload:focus .el-upload-dragger {
      border-color: #39C4BD; }

.el-upload-dragger {
  background-color: #fff;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 360px;
  height: 180px;
  text-align: center;
  cursor: pointer;
  position: relative;
  overflow: hidden; }
  .el-upload-dragger .el-icon-upload {
    font-size: 67px;
    color: #bfbfbf;
    margin: 40px 0 16px;
    line-height: 50px; }
  .el-upload-dragger + .el-upload__tip {
    text-align: center; }
  .el-upload-dragger ~ .el-upload__files {
    border-top: 1px solid #c8c8c8;
    margin-top: 7px;
    padding-top: 5px; }
  .el-upload-dragger .el-upload__text {
    color: #262626;
    font-size: 14px;
    text-align: center; }
    .el-upload-dragger .el-upload__text em {
      color: #39C4BD;
      font-style: normal; }
  .el-upload-dragger:hover {
    border-color: #39C4BD; }
  .el-upload-dragger.is-dragover {
    background-color: rgba(32, 159, 255, 0.06);
    border: 2px dashed #39C4BD; }

.el-upload-list {
  margin: 0;
  padding: 0;
  list-style: none; }
  .el-upload-list__item {
    -webkit-transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
    transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
    font-size: 14px;
    color: #262626;
    line-height: 1.8;
    margin-top: 5px;
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 4px;
    width: 100%; }
    .el-upload-list__item .el-progress {
      position: absolute;
      top: 20px;
      width: 100%; }
    .el-upload-list__item .el-progress__text {
      position: absolute;
      right: 0;
      top: -13px; }
    .el-upload-list__item .el-progress-bar {
      margin-right: 0;
      padding-right: 0; }
    .el-upload-list__item:first-child {
      margin-top: 10px; }
    .el-upload-list__item .el-icon-upload-success {
      color: #88cc48; }
    .el-upload-list__item .el-icon-close {
      display: none;
      position: absolute;
      top: 5px;
      right: 5px;
      cursor: pointer;
      opacity: .75;
      color: #262626; }
      .el-upload-list__item .el-icon-close:hover {
        opacity: 1; }
    .el-upload-list__item .el-icon-close-tip {
      display: none;
      position: absolute;
      top: 5px;
      right: 5px;
      font-size: 12px;
      cursor: pointer;
      opacity: 1;
      color: #39C4BD; }
    .el-upload-list__item:hover {
      background-color: #f5f7fa; }
      .el-upload-list__item:hover .el-icon-close {
        display: inline-block; }
      .el-upload-list__item:hover .el-progress__text {
        display: none; }
    .el-upload-list__item.is-success .el-upload-list__item-status-label {
      display: block; }
    .el-upload-list__item.is-success .el-upload-list__item-name:hover, .el-upload-list__item.is-success .el-upload-list__item-name:focus {
      color: #39C4BD;
      cursor: pointer; }
    .el-upload-list__item.is-success:focus:not(:hover) {
      /* 键盘focus */ }
      .el-upload-list__item.is-success:focus:not(:hover) .el-icon-close-tip {
        display: inline-block; }
    .el-upload-list__item.is-success:not(.focusing):focus, .el-upload-list__item.is-success:active {
      /* click时 */
      outline-width: 0; }
      .el-upload-list__item.is-success:not(.focusing):focus .el-icon-close-tip, .el-upload-list__item.is-success:active .el-icon-close-tip {
        display: none; }
    .el-upload-list__item.is-success:hover .el-upload-list__item-status-label, .el-upload-list__item.is-success:focus .el-upload-list__item-status-label {
      display: none; }
  .el-upload-list.is-disabled .el-upload-list__item:hover .el-upload-list__item-status-label {
    display: block; }
  .el-upload-list__item-name {
    color: #262626;
    display: block;
    margin-right: 40px;
    overflow: hidden;
    padding-left: 4px;
    text-overflow: ellipsis;
    -webkit-transition: color .3s;
    transition: color .3s;
    white-space: nowrap; }
    .el-upload-list__item-name [class^="el-icon"] {
      height: 100%;
      margin-right: 7px;
      color: #262626;
      line-height: inherit; }
  .el-upload-list__item-status-label {
    position: absolute;
    right: 5px;
    top: 0;
    line-height: inherit;
    display: none; }
  .el-upload-list__item-delete {
    position: absolute;
    right: 10px;
    top: 0;
    font-size: 12px;
    color: #262626;
    display: none; }
    .el-upload-list__item-delete:hover {
      color: #39C4BD; }
  .el-upload-list--picture-card {
    margin: 0;
    display: inline;
    vertical-align: top; }
    .el-upload-list--picture-card .el-upload-list__item {
      overflow: hidden;
      background-color: #fff;
      border: 1px solid #c0ccda;
      border-radius: 6px;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      width: 148px;
      height: 148px;
      margin: 0 8px 8px 0;
      display: inline-block; }
      .el-upload-list--picture-card .el-upload-list__item .el-icon-check,
      .el-upload-list--picture-card .el-upload-list__item .el-icon-circle-check {
        color: #ffffff; }
      .el-upload-list--picture-card .el-upload-list__item .el-icon-close {
        display: none; }
      .el-upload-list--picture-card .el-upload-list__item:hover .el-upload-list__item-status-label {
        display: none; }
      .el-upload-list--picture-card .el-upload-list__item:hover .el-progress__text {
        display: block; }
    .el-upload-list--picture-card .el-upload-list__item-name {
      display: none; }
    .el-upload-list--picture-card .el-upload-list__item-thumbnail {
      width: 100%;
      height: 100%; }
    .el-upload-list--picture-card .el-upload-list__item-status-label {
      position: absolute;
      right: -15px;
      top: -6px;
      width: 40px;
      height: 24px;
      background: #13ce66;
      text-align: center;
      -webkit-transform: rotate(45deg);
      transform: rotate(45deg);
      -webkit-box-shadow: 0 0 1pc 1px rgba(0, 0, 0, 0.2);
      box-shadow: 0 0 1pc 1px rgba(0, 0, 0, 0.2); }
      .el-upload-list--picture-card .el-upload-list__item-status-label i {
        font-size: 12px;
        margin-top: 11px;
        -webkit-transform: rotate(-45deg);
        transform: rotate(-45deg); }
    .el-upload-list--picture-card .el-upload-list__item-actions {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      cursor: default;
      text-align: center;
      color: #fff;
      opacity: 0;
      font-size: 20px;
      background-color: rgba(0, 0, 0, 0.5);
      -webkit-transition: opacity .3s;
      transition: opacity .3s; }
      .el-upload-list--picture-card .el-upload-list__item-actions::after {
        display: inline-block;
        content: "";
        height: 100%;
        vertical-align: middle; }
      .el-upload-list--picture-card .el-upload-list__item-actions span {
        display: none;
        cursor: pointer; }
      .el-upload-list--picture-card .el-upload-list__item-actions span + span {
        margin-left: 15px; }
      .el-upload-list--picture-card .el-upload-list__item-actions .el-upload-list__item-delete {
        position: static;
        font-size: inherit;
        color: inherit; }
      .el-upload-list--picture-card .el-upload-list__item-actions:hover {
        opacity: 1; }
        .el-upload-list--picture-card .el-upload-list__item-actions:hover span {
          display: inline-block; }
    .el-upload-list--picture-card .el-progress {
      top: 50%;
      left: 50%;
      -webkit-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
      bottom: auto;
      width: 126px; }
      .el-upload-list--picture-card .el-progress .el-progress__text {
        top: 50%; }
  .el-upload-list--picture .el-upload-list__item {
    overflow: hidden;
    z-index: 0;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin-top: 10px;
    padding: 10px 10px 10px 90px;
    height: 92px; }
    .el-upload-list--picture .el-upload-list__item .el-icon-check,
    .el-upload-list--picture .el-upload-list__item .el-icon-circle-check {
      color: #ffffff; }
    .el-upload-list--picture .el-upload-list__item:hover .el-upload-list__item-status-label {
      background: transparent;
      -webkit-box-shadow: none;
      box-shadow: none;
      top: -2px;
      right: -12px; }
    .el-upload-list--picture .el-upload-list__item:hover .el-progress__text {
      display: block; }
    .el-upload-list--picture .el-upload-list__item.is-success .el-upload-list__item-name {
      line-height: 70px;
      margin-top: 0; }
      .el-upload-list--picture .el-upload-list__item.is-success .el-upload-list__item-name i {
        display: none; }
  .el-upload-list--picture .el-upload-list__item-thumbnail {
    vertical-align: middle;
    display: inline-block;
    width: 70px;
    height: 70px;
    float: left;
    position: relative;
    z-index: 1;
    margin-left: -80px;
    background-color: #ffffff; }
  .el-upload-list--picture .el-upload-list__item-name {
    display: block;
    margin-top: 20px; }
    .el-upload-list--picture .el-upload-list__item-name i {
      font-size: 70px;
      line-height: 1;
      position: absolute;
      left: 9px;
      top: 10px; }
  .el-upload-list--picture .el-upload-list__item-status-label {
    position: absolute;
    right: -17px;
    top: -7px;
    width: 46px;
    height: 26px;
    background: #13ce66;
    text-align: center;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-box-shadow: 0 1px 1px #ccc;
    box-shadow: 0 1px 1px #ccc; }
    .el-upload-list--picture .el-upload-list__item-status-label i {
      font-size: 12px;
      margin-top: 12px;
      -webkit-transform: rotate(-45deg);
      transform: rotate(-45deg); }
  .el-upload-list--picture .el-progress {
    position: relative;
    top: -7px; }

.el-upload-cover {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 10;
  cursor: default; }
  .el-upload-cover::after {
    display: inline-block;
    content: "";
    height: 100%;
    vertical-align: middle; }
  .el-upload-cover img {
    display: block;
    width: 100%;
    height: 100%; }
  .el-upload-cover__label {
    position: absolute;
    right: -15px;
    top: -6px;
    width: 40px;
    height: 24px;
    background: #13ce66;
    text-align: center;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-box-shadow: 0 0 1pc 1px rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 1pc 1px rgba(0, 0, 0, 0.2); }
    .el-upload-cover__label i {
      font-size: 12px;
      margin-top: 11px;
      -webkit-transform: rotate(-45deg);
      transform: rotate(-45deg);
      color: #fff; }
  .el-upload-cover__progress {
    display: inline-block;
    vertical-align: middle;
    position: static;
    width: 243px; }
    .el-upload-cover__progress + .el-upload__inner {
      opacity: 0; }
  .el-upload-cover__content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%; }
  .el-upload-cover__interact {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.72);
    text-align: center; }
    .el-upload-cover__interact .btn {
      display: inline-block;
      color: #ffffff;
      font-size: 14px;
      cursor: pointer;
      vertical-align: middle;
      -webkit-transition: opacity 300ms cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 300ms cubic-bezier(0.23, 1, 0.32, 1);
      transition: opacity 300ms cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 300ms cubic-bezier(0.23, 1, 0.32, 1);
      transition: transform 300ms cubic-bezier(0.23, 1, 0.32, 1), opacity 300ms cubic-bezier(0.23, 1, 0.32, 1);
      transition: transform 300ms cubic-bezier(0.23, 1, 0.32, 1), opacity 300ms cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 300ms cubic-bezier(0.23, 1, 0.32, 1);
      margin-top: 60px; }
      .el-upload-cover__interact .btn i {
        margin-top: 0; }
      .el-upload-cover__interact .btn span {
        opacity: 0;
        -webkit-transition: opacity .15s linear;
        transition: opacity .15s linear; }
      .el-upload-cover__interact .btn:not(:first-child) {
        margin-left: 35px; }
      .el-upload-cover__interact .btn:hover {
        -webkit-transform: translateY(-13px);
        transform: translateY(-13px); }
        .el-upload-cover__interact .btn:hover span {
          opacity: 1; }
      .el-upload-cover__interact .btn i {
        color: #ffffff;
        display: block;
        font-size: 24px;
        line-height: inherit;
        margin: 0 auto 5px; }
  .el-upload-cover__title {
    position: absolute;
    bottom: 0;
    left: 0;
    background-color: #ffffff;
    height: 36px;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: normal;
    text-align: left;
    padding: 0 10px;
    margin: 0;
    line-height: 36px;
    font-size: 14px;
    color: #262626; }
  .el-upload-cover + .el-upload__inner {
    opacity: 0;
    position: relative;
    z-index: 1; }
