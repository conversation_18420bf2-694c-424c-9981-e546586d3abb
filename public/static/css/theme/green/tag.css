/* BEM support Func
 -------------------------- */
/* Element Chalk Variables */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* 53a8ff */
/* 66b1ff */
/* 79bbff */
/* 8cc5ff */
/* a0cfff */
/* b3d8ff */
/* c6e2ff */
/* d9ecff */
/* ecf5ff */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* Size
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Empty
-------------------------- */
/* Descriptions
-------------------------- */
/* Skeleton 
--------------------------*/
/* Svg
--------------- */
/* Result
-------------------------- */
/* Break-point
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
/* Element Chalk Variables */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* 53a8ff */
/* 66b1ff */
/* 79bbff */
/* 8cc5ff */
/* a0cfff */
/* b3d8ff */
/* c6e2ff */
/* d9ecff */
/* ecf5ff */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* Size
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Empty
-------------------------- */
/* Descriptions
-------------------------- */
/* Skeleton 
--------------------------*/
/* Svg
--------------- */
/* Result
-------------------------- */
/* Break-point
--------------------------*/
/* Break-point
--------------------------*/
.el-tag {
  background-color: #ebf9f8;
  border-color: #d7f3f2;
  color: #39c4bd;
  display: inline-block;
  height: 32px;
  padding: 0 10px;
  line-height: 30px;
  font-size: 12px;
  color: #39C4BD;
  border-width: 1px;
  border-style: solid;
  border-radius: 4px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  white-space: nowrap; }
  .el-tag.is-hit {
    border-color: #39C4BD; }
  .el-tag .el-tag__close {
    color: #39c4bd; }
    .el-tag .el-tag__close:hover {
      color: #ffffff;
      background-color: #39c4bd; }
  .el-tag.el-tag--info {
    background-color: #f4f4f5;
    border-color: #e9e9eb;
    color: #909399; }
    .el-tag.el-tag--info.is-hit {
      border-color: #909399; }
    .el-tag.el-tag--info .el-tag__close {
      color: #909399; }
      .el-tag.el-tag--info .el-tag__close:hover {
        color: #ffffff;
        background-color: #909399; }
  .el-tag.el-tag--success {
    background-color: #f3faed;
    border-color: #e7f5da;
    color: #88cc48; }
    .el-tag.el-tag--success.is-hit {
      border-color: #88cc48; }
    .el-tag.el-tag--success .el-tag__close {
      color: #88cc48; }
      .el-tag.el-tag--success .el-tag__close:hover {
        color: #ffffff;
        background-color: #88cc48; }
  .el-tag.el-tag--warning {
    background-color: #fdf6ec;
    border-color: #faecd8;
    color: #e6a23c; }
    .el-tag.el-tag--warning.is-hit {
      border-color: #e6a23c; }
    .el-tag.el-tag--warning .el-tag__close {
      color: #e6a23c; }
      .el-tag.el-tag--warning .el-tag__close:hover {
        color: #ffffff;
        background-color: #e6a23c; }
  .el-tag.el-tag--danger {
    background-color: #f9e8ea;
    border-color: #f2d1d4;
    color: #c0192a; }
    .el-tag.el-tag--danger.is-hit {
      border-color: #c0192a; }
    .el-tag.el-tag--danger .el-tag__close {
      color: #c0192a; }
      .el-tag.el-tag--danger .el-tag__close:hover {
        color: #ffffff;
        background-color: #c0192a; }
  .el-tag .el-icon-close {
    border-radius: 50%;
    text-align: center;
    position: relative;
    cursor: pointer;
    font-size: 12px;
    height: 16px;
    width: 16px;
    line-height: 16px;
    vertical-align: middle;
    top: -1px;
    right: -5px; }
    .el-tag .el-icon-close::before {
      display: block; }
  .el-tag--dark {
    background-color: #39c4bd;
    border-color: #39c4bd;
    color: white; }
    .el-tag--dark.is-hit {
      border-color: #39C4BD; }
    .el-tag--dark .el-tag__close {
      color: white; }
      .el-tag--dark .el-tag__close:hover {
        color: #ffffff;
        background-color: #61d0ca; }
    .el-tag--dark.el-tag--info {
      background-color: #909399;
      border-color: #909399;
      color: white; }
      .el-tag--dark.el-tag--info.is-hit {
        border-color: #909399; }
      .el-tag--dark.el-tag--info .el-tag__close {
        color: white; }
        .el-tag--dark.el-tag--info .el-tag__close:hover {
          color: #ffffff;
          background-color: #a6a9ad; }
    .el-tag--dark.el-tag--success {
      background-color: #88cc48;
      border-color: #88cc48;
      color: white; }
      .el-tag--dark.el-tag--success.is-hit {
        border-color: #88cc48; }
      .el-tag--dark.el-tag--success .el-tag__close {
        color: white; }
        .el-tag--dark.el-tag--success .el-tag__close:hover {
          color: #ffffff;
          background-color: #a0d66d; }
    .el-tag--dark.el-tag--warning {
      background-color: #e6a23c;
      border-color: #e6a23c;
      color: white; }
      .el-tag--dark.el-tag--warning.is-hit {
        border-color: #e6a23c; }
      .el-tag--dark.el-tag--warning .el-tag__close {
        color: white; }
        .el-tag--dark.el-tag--warning .el-tag__close:hover {
          color: #ffffff;
          background-color: #ebb563; }
    .el-tag--dark.el-tag--danger {
      background-color: #c0192a;
      border-color: #c0192a;
      color: white; }
      .el-tag--dark.el-tag--danger.is-hit {
        border-color: #c0192a; }
      .el-tag--dark.el-tag--danger .el-tag__close {
        color: white; }
        .el-tag--dark.el-tag--danger .el-tag__close:hover {
          color: #ffffff;
          background-color: #cd4755; }
  .el-tag--plain {
    background-color: white;
    border-color: #b0e7e5;
    color: #39c4bd; }
    .el-tag--plain.is-hit {
      border-color: #39C4BD; }
    .el-tag--plain .el-tag__close {
      color: #39c4bd; }
      .el-tag--plain .el-tag__close:hover {
        color: #ffffff;
        background-color: #39c4bd; }
    .el-tag--plain.el-tag--info {
      background-color: white;
      border-color: #d3d4d6;
      color: #909399; }
      .el-tag--plain.el-tag--info.is-hit {
        border-color: #909399; }
      .el-tag--plain.el-tag--info .el-tag__close {
        color: #909399; }
        .el-tag--plain.el-tag--info .el-tag__close:hover {
          color: #ffffff;
          background-color: #909399; }
    .el-tag--plain.el-tag--success {
      background-color: white;
      border-color: #cfebb6;
      color: #88cc48; }
      .el-tag--plain.el-tag--success.is-hit {
        border-color: #88cc48; }
      .el-tag--plain.el-tag--success .el-tag__close {
        color: #88cc48; }
        .el-tag--plain.el-tag--success .el-tag__close:hover {
          color: #ffffff;
          background-color: #88cc48; }
    .el-tag--plain.el-tag--warning {
      background-color: white;
      border-color: #f5dab1;
      color: #e6a23c; }
      .el-tag--plain.el-tag--warning.is-hit {
        border-color: #e6a23c; }
      .el-tag--plain.el-tag--warning .el-tag__close {
        color: #e6a23c; }
        .el-tag--plain.el-tag--warning .el-tag__close:hover {
          color: #ffffff;
          background-color: #e6a23c; }
    .el-tag--plain.el-tag--danger {
      background-color: white;
      border-color: #e6a3aa;
      color: #c0192a; }
      .el-tag--plain.el-tag--danger.is-hit {
        border-color: #c0192a; }
      .el-tag--plain.el-tag--danger .el-tag__close {
        color: #c0192a; }
        .el-tag--plain.el-tag--danger .el-tag__close:hover {
          color: #ffffff;
          background-color: #c0192a; }
  .el-tag--medium {
    height: 28px;
    line-height: 26px; }
    .el-tag--medium .el-icon-close {
      -webkit-transform: scale(0.8);
      transform: scale(0.8); }
  .el-tag--small {
    height: 24px;
    padding: 0 8px;
    line-height: 22px; }
    .el-tag--small .el-icon-close {
      -webkit-transform: scale(0.8);
      transform: scale(0.8); }
  .el-tag--mini {
    height: 20px;
    padding: 0 5px;
    line-height: 19px; }
    .el-tag--mini .el-icon-close {
      margin-left: -3px;
      -webkit-transform: scale(0.7);
      transform: scale(0.7); }
