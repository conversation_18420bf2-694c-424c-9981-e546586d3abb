/* BEM support Func
 -------------------------- */
/* Element Chalk Variables */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* 53a8ff */
/* 66b1ff */
/* 79bbff */
/* 8cc5ff */
/* a0cfff */
/* b3d8ff */
/* c6e2ff */
/* d9ecff */
/* ecf5ff */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* Size
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Empty
-------------------------- */
/* Descriptions
-------------------------- */
/* Skeleton 
--------------------------*/
/* Svg
--------------- */
/* Result
-------------------------- */
/* Break-point
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
/* Element Chalk Variables */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* 53a8ff */
/* 66b1ff */
/* 79bbff */
/* 8cc5ff */
/* a0cfff */
/* b3d8ff */
/* c6e2ff */
/* d9ecff */
/* ecf5ff */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* Size
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Empty
-------------------------- */
/* Descriptions
-------------------------- */
/* Skeleton 
--------------------------*/
/* Svg
--------------- */
/* Result
-------------------------- */
/* Break-point
--------------------------*/
/* Break-point
--------------------------*/
.el-color-predefine {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 12px;
  margin-top: 8px;
  width: 280px; }
  .el-color-predefine__colors {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap; }
  .el-color-predefine__color-selector {
    margin: 0 0 8px 8px;
    width: 20px;
    height: 20px;
    border-radius: 4px;
    cursor: pointer; }
    .el-color-predefine__color-selector:nth-child(10n + 1) {
      margin-left: 0; }
    .el-color-predefine__color-selector.selected {
      -webkit-box-shadow: 0 0 3px 2px #198AE2;
      box-shadow: 0 0 3px 2px #198AE2; }
    .el-color-predefine__color-selector > div {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      height: 100%;
      border-radius: 3px; }
    .el-color-predefine__color-selector.is-alpha {
      background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==); }

.el-color-hue-slider {
  position: relative;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 280px;
  height: 12px;
  background-color: #f00;
  padding: 0 2px; }
  .el-color-hue-slider__bar {
    position: relative;
    background: -webkit-gradient(linear, left top, right top, from(#f00), color-stop(17%, #ff0), color-stop(33%, #0f0), color-stop(50%, #0ff), color-stop(67%, #00f), color-stop(83%, #f0f), to(#f00));
    background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);
    height: 100%; }
  .el-color-hue-slider__thumb {
    position: absolute;
    cursor: pointer;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    border-radius: 1px;
    background: #fff;
    border: 1px solid #f0f0f0;
    -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, 0.6);
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.6);
    z-index: 1; }
  .el-color-hue-slider.is-vertical {
    width: 12px;
    height: 180px;
    padding: 2px 0; }
    .el-color-hue-slider.is-vertical .el-color-hue-slider__bar {
      background: -webkit-gradient(linear, left top, left bottom, from(#f00), color-stop(17%, #ff0), color-stop(33%, #0f0), color-stop(50%, #0ff), color-stop(67%, #00f), color-stop(83%, #f0f), to(#f00));
      background: linear-gradient(to bottom, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%); }
    .el-color-hue-slider.is-vertical .el-color-hue-slider__thumb {
      left: 0;
      top: 0;
      width: 100%;
      height: 4px; }

.el-color-svpanel {
  position: relative;
  width: 280px;
  height: 180px; }
  .el-color-svpanel__white, .el-color-svpanel__black {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0; }
  .el-color-svpanel__white {
    background: -webkit-gradient(linear, left top, right top, from(#fff), to(rgba(255, 255, 255, 0)));
    background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0)); }
  .el-color-svpanel__black {
    background: -webkit-gradient(linear, left bottom, left top, from(#000), to(rgba(0, 0, 0, 0)));
    background: linear-gradient(to top, #000, rgba(0, 0, 0, 0)); }
  .el-color-svpanel__cursor {
    position: absolute; }
    .el-color-svpanel__cursor > div {
      cursor: head;
      width: 4px;
      height: 4px;
      -webkit-box-shadow: 0 0 0 1.5px #fff, inset 0 0 1px 1px rgba(0, 0, 0, 0.3), 0 0 1px 2px rgba(0, 0, 0, 0.4);
      box-shadow: 0 0 0 1.5px #fff, inset 0 0 1px 1px rgba(0, 0, 0, 0.3), 0 0 1px 2px rgba(0, 0, 0, 0.4);
      border-radius: 50%;
      -webkit-transform: translate(-2px, -2px);
      transform: translate(-2px, -2px); }

.el-color-alpha-slider {
  position: relative;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 280px;
  height: 12px;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==); }
  .el-color-alpha-slider__bar {
    position: relative;
    background: -webkit-gradient(linear, left top, right top, from(rgba(255, 255, 255, 0)), to(white));
    background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, white 100%);
    height: 100%; }
  .el-color-alpha-slider__thumb {
    position: absolute;
    cursor: pointer;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    border-radius: 1px;
    background: #fff;
    border: 1px solid #f0f0f0;
    -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, 0.6);
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.6);
    z-index: 1; }
  .el-color-alpha-slider.is-vertical {
    width: 20px;
    height: 180px; }
    .el-color-alpha-slider.is-vertical .el-color-alpha-slider__bar {
      background: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0)), to(white));
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, white 100%); }
    .el-color-alpha-slider.is-vertical .el-color-alpha-slider__thumb {
      left: 0;
      top: 0;
      width: 100%;
      height: 4px; }

.el-color-dropdown {
  width: 300px; }
  .el-color-dropdown__main-wrapper {
    margin-bottom: 6px; }
    .el-color-dropdown__main-wrapper::after {
      content: "";
      display: table;
      clear: both; }
  .el-color-dropdown__btns {
    margin-top: 6px;
    text-align: right; }
  .el-color-dropdown__value {
    float: left;
    line-height: 26px;
    font-size: 12px;
    color: #000000;
    width: 160px; }
  .el-color-dropdown__btn {
    border: 1px solid #dcdcdc;
    color: #333;
    line-height: 24px;
    border-radius: 2px;
    padding: 0 20px;
    cursor: pointer;
    background-color: transparent;
    outline: none;
    font-size: 12px; }
    .el-color-dropdown__btn[disabled] {
      color: #cccccc;
      cursor: not-allowed; }
    .el-color-dropdown__btn:hover {
      color: #198AE2;
      border-color: #198AE2; }
  .el-color-dropdown__link-btn {
    cursor: pointer;
    color: #198AE2;
    text-decoration: none;
    padding: 15px;
    font-size: 12px; }
    .el-color-dropdown__link-btn:hover {
      color: tint(#198AE2, 20%); }

.el-color-picker {
  display: inline-block;
  position: relative;
  line-height: normal;
  height: 40px; }
  .el-color-picker.is-disabled .el-color-picker__trigger {
    cursor: not-allowed; }
  .el-color-picker--medium {
    height: 36px; }
    .el-color-picker--medium .el-color-picker__trigger {
      height: 36px;
      width: 36px; }
    .el-color-picker--medium .el-color-picker__mask {
      height: 34px;
      width: 34px; }
  .el-color-picker--small {
    height: 32px; }
    .el-color-picker--small .el-color-picker__trigger {
      height: 32px;
      width: 32px; }
    .el-color-picker--small .el-color-picker__mask {
      height: 30px;
      width: 30px; }
    .el-color-picker--small .el-color-picker__icon,
    .el-color-picker--small .el-color-picker__empty {
      -webkit-transform: translate3d(-50%, -50%, 0) scale(0.8);
      transform: translate3d(-50%, -50%, 0) scale(0.8); }
  .el-color-picker--mini {
    height: 28px; }
    .el-color-picker--mini .el-color-picker__trigger {
      height: 28px;
      width: 28px; }
    .el-color-picker--mini .el-color-picker__mask {
      height: 26px;
      width: 26px; }
    .el-color-picker--mini .el-color-picker__icon,
    .el-color-picker--mini .el-color-picker__empty {
      -webkit-transform: translate3d(-50%, -50%, 0) scale(0.8);
      transform: translate3d(-50%, -50%, 0) scale(0.8); }
  .el-color-picker__mask {
    height: 38px;
    width: 38px;
    border-radius: 4px;
    position: absolute;
    top: 1px;
    left: 1px;
    z-index: 1;
    cursor: not-allowed;
    background-color: rgba(255, 255, 255, 0.7); }
  .el-color-picker__trigger {
    display: inline-block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 40px;
    width: 40px;
    padding: 4px;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    font-size: 0;
    position: relative;
    cursor: pointer; }
  .el-color-picker__color {
    position: relative;
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border: 1px solid #999;
    border-radius: 2px;
    width: 100%;
    height: 100%;
    text-align: center; }
    .el-color-picker__color.is-alpha {
      background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==); }
  .el-color-picker__color-inner {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0; }
  .el-color-picker__empty {
    font-size: 12px;
    color: #999;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate3d(-50%, -50%, 0);
    transform: translate3d(-50%, -50%, 0); }
  .el-color-picker__icon {
    display: inline-block;
    position: absolute;
    width: 100%;
    top: 50%;
    left: 50%;
    -webkit-transform: translate3d(-50%, -50%, 0);
    transform: translate3d(-50%, -50%, 0);
    color: #ffffff;
    text-align: center;
    font-size: 12px; }
  .el-color-picker__panel {
    position: absolute;
    z-index: 10;
    padding: 6px;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    background-color: #ffffff;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); }
