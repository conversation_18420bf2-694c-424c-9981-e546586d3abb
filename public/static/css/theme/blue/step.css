/* BEM support Func
 -------------------------- */
/* Element Chalk Variables */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* 53a8ff */
/* 66b1ff */
/* 79bbff */
/* 8cc5ff */
/* a0cfff */
/* b3d8ff */
/* c6e2ff */
/* d9ecff */
/* ecf5ff */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* Size
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Empty
-------------------------- */
/* Descriptions
-------------------------- */
/* Skeleton 
--------------------------*/
/* Svg
--------------- */
/* Result
-------------------------- */
/* Break-point
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
/* Element Chalk Variables */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* 53a8ff */
/* 66b1ff */
/* 79bbff */
/* 8cc5ff */
/* a0cfff */
/* b3d8ff */
/* c6e2ff */
/* d9ecff */
/* ecf5ff */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* Size
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Empty
-------------------------- */
/* Descriptions
-------------------------- */
/* Skeleton 
--------------------------*/
/* Svg
--------------- */
/* Result
-------------------------- */
/* Break-point
--------------------------*/
/* Break-point
--------------------------*/
.el-step {
  position: relative;
  -ms-flex-negative: 1;
  flex-shrink: 1; }
  .el-step:last-of-type .el-step__line {
    display: none; }
  .el-step:last-of-type.is-flex {
    -ms-flex-preferred-size: auto !important;
    flex-basis: auto !important;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0; }
  .el-step:last-of-type .el-step__main, .el-step:last-of-type .el-step__description {
    padding-right: 0; }
  .el-step__head {
    position: relative;
    width: 100%; }
    .el-step__head.is-process {
      color: #262626;
      border-color: #262626; }
    .el-step__head.is-wait {
      color: #bfbfbf;
      border-color: #bfbfbf; }
    .el-step__head.is-success {
      color: #88cc48;
      border-color: #88cc48; }
    .el-step__head.is-error {
      color: #c0192a;
      border-color: #c0192a; }
    .el-step__head.is-finish {
      color: #198AE2;
      border-color: #198AE2; }
  .el-step__icon {
    position: relative;
    z-index: 1;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 24px;
    height: 24px;
    font-size: 14px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #ffffff;
    -webkit-transition: .15s ease-out;
    transition: .15s ease-out; }
    .el-step__icon.is-text {
      border-radius: 50%;
      border: 2px solid;
      border-color: inherit; }
    .el-step__icon.is-icon {
      width: 40px; }
  .el-step__icon-inner {
    display: inline-block;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    text-align: center;
    font-weight: bold;
    line-height: 1;
    color: inherit; }
    .el-step__icon-inner[class*=el-icon]:not(.is-status) {
      font-size: 25px;
      font-weight: normal; }
    .el-step__icon-inner.is-status {
      -webkit-transform: translateY(1px);
      transform: translateY(1px); }
  .el-step__line {
    position: absolute;
    border-color: inherit;
    background-color: #bfbfbf; }
  .el-step__line-inner {
    display: block;
    border-width: 1px;
    border-style: solid;
    border-color: inherit;
    -webkit-transition: .15s ease-out;
    transition: .15s ease-out;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 0;
    height: 0; }
  .el-step__main {
    white-space: normal;
    text-align: left; }
  .el-step__title {
    font-size: 16px;
    line-height: 38px; }
    .el-step__title.is-process {
      font-weight: bold;
      color: #262626; }
    .el-step__title.is-wait {
      color: #bfbfbf; }
    .el-step__title.is-success {
      color: #88cc48; }
    .el-step__title.is-error {
      color: #c0192a; }
    .el-step__title.is-finish {
      color: #198AE2; }
  .el-step__description {
    padding-right: 10%;
    margin-top: -5px;
    font-size: 12px;
    line-height: 20px;
    font-weight: normal; }
    .el-step__description.is-process {
      color: #262626; }
    .el-step__description.is-wait {
      color: #bfbfbf; }
    .el-step__description.is-success {
      color: #88cc48; }
    .el-step__description.is-error {
      color: #c0192a; }
    .el-step__description.is-finish {
      color: #198AE2; }
  .el-step.is-horizontal {
    display: inline-block; }
    .el-step.is-horizontal .el-step__line {
      height: 2px;
      top: 11px;
      left: 0;
      right: 0; }
  .el-step.is-vertical {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex; }
    .el-step.is-vertical .el-step__head {
      -webkit-box-flex: 0;
      -ms-flex-positive: 0;
      flex-grow: 0;
      width: 24px; }
    .el-step.is-vertical .el-step__main {
      padding-left: 10px;
      -webkit-box-flex: 1;
      -ms-flex-positive: 1;
      flex-grow: 1; }
    .el-step.is-vertical .el-step__title {
      line-height: 24px;
      padding-bottom: 8px; }
    .el-step.is-vertical .el-step__line {
      width: 2px;
      top: 0;
      bottom: 0;
      left: 11px; }
    .el-step.is-vertical .el-step__icon.is-icon {
      width: 24px; }
  .el-step.is-center .el-step__head {
    text-align: center; }
  .el-step.is-center .el-step__main {
    text-align: center; }
  .el-step.is-center .el-step__description {
    padding-left: 20%;
    padding-right: 20%; }
  .el-step.is-center .el-step__line {
    left: 50%;
    right: -50%; }
  .el-step.is-simple {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center; }
    .el-step.is-simple .el-step__head {
      width: auto;
      font-size: 0;
      padding-right: 10px; }
    .el-step.is-simple .el-step__icon {
      background: transparent;
      width: 16px;
      height: 16px;
      font-size: 12px; }
    .el-step.is-simple .el-step__icon-inner[class*=el-icon]:not(.is-status) {
      font-size: 18px; }
    .el-step.is-simple .el-step__icon-inner.is-status {
      -webkit-transform: scale(0.8) translateY(1px);
      transform: scale(0.8) translateY(1px); }
    .el-step.is-simple .el-step__main {
      position: relative;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: stretch;
      -ms-flex-align: stretch;
      align-items: stretch;
      -webkit-box-flex: 1;
      -ms-flex-positive: 1;
      flex-grow: 1; }
    .el-step.is-simple .el-step__title {
      font-size: 16px;
      line-height: 20px; }
    .el-step.is-simple:not(:last-of-type) .el-step__title {
      max-width: 50%;
      word-break: break-all; }
    .el-step.is-simple .el-step__arrow {
      -webkit-box-flex: 1;
      -ms-flex-positive: 1;
      flex-grow: 1;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      justify-content: center; }
      .el-step.is-simple .el-step__arrow::before, .el-step.is-simple .el-step__arrow::after {
        content: '';
        display: inline-block;
        position: absolute;
        height: 15px;
        width: 1px;
        background: #bfbfbf; }
      .el-step.is-simple .el-step__arrow::before {
        -webkit-transform: rotate(-45deg) translateY(-4px);
        transform: rotate(-45deg) translateY(-4px);
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0; }
      .el-step.is-simple .el-step__arrow::after {
        -webkit-transform: rotate(45deg) translateY(4px);
        transform: rotate(45deg) translateY(4px);
        -webkit-transform-origin: 100% 100%;
        transform-origin: 100% 100%; }
    .el-step.is-simple:last-of-type .el-step__arrow {
      display: none; }
