/* BEM support Func
 -------------------------- */
/* Element Chalk Variables */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* 53a8ff */
/* 66b1ff */
/* 79bbff */
/* 8cc5ff */
/* a0cfff */
/* b3d8ff */
/* c6e2ff */
/* d9ecff */
/* ecf5ff */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* Size
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Empty
-------------------------- */
/* Descriptions
-------------------------- */
/* Skeleton 
--------------------------*/
/* Svg
--------------- */
/* Result
-------------------------- */
/* Break-point
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
/* Element Chalk Variables */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* 53a8ff */
/* 66b1ff */
/* 79bbff */
/* 8cc5ff */
/* a0cfff */
/* b3d8ff */
/* c6e2ff */
/* d9ecff */
/* ecf5ff */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* Size
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Empty
-------------------------- */
/* Descriptions
-------------------------- */
/* Skeleton 
--------------------------*/
/* Svg
--------------- */
/* Result
-------------------------- */
/* Break-point
--------------------------*/
/* Break-point
--------------------------*/
.el-progress {
  position: relative;
  line-height: 1; }
  .el-progress__text {
    font-size: 14px;
    color: #262626;
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px;
    line-height: 1; }
    .el-progress__text i {
      vertical-align: middle;
      display: block; }
  .el-progress--circle, .el-progress--dashboard {
    display: inline-block; }
    .el-progress--circle .el-progress__text, .el-progress--dashboard .el-progress__text {
      position: absolute;
      top: 50%;
      left: 0;
      width: 100%;
      text-align: center;
      margin: 0;
      -webkit-transform: translate(0, -50%);
      transform: translate(0, -50%); }
      .el-progress--circle .el-progress__text i, .el-progress--dashboard .el-progress__text i {
        vertical-align: middle;
        display: inline-block; }
  .el-progress--without-text .el-progress__text {
    display: none; }
  .el-progress--without-text .el-progress-bar {
    padding-right: 0;
    margin-right: 0;
    display: block; }
  .el-progress--text-inside .el-progress-bar {
    padding-right: 0;
    margin-right: 0; }
  .el-progress.is-success .el-progress-bar__inner {
    background-color: #88cc48; }
  .el-progress.is-success .el-progress__text {
    color: #88cc48; }
  .el-progress.is-warning .el-progress-bar__inner {
    background-color: #e6a23c; }
  .el-progress.is-warning .el-progress__text {
    color: #e6a23c; }
  .el-progress.is-exception .el-progress-bar__inner {
    background-color: #c0192a; }
  .el-progress.is-exception .el-progress__text {
    color: #c0192a; }

.el-progress-bar {
  padding-right: 50px;
  display: inline-block;
  vertical-align: middle;
  width: 100%;
  margin-right: -55px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box; }
  .el-progress-bar__outer {
    height: 6px;
    border-radius: 100px;
    background-color: #ebeef5;
    overflow: hidden;
    position: relative;
    vertical-align: middle; }
  .el-progress-bar__inner {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    background-color: #198AE2;
    text-align: right;
    border-radius: 100px;
    line-height: 1;
    white-space: nowrap;
    -webkit-transition: width 0.6s ease;
    transition: width 0.6s ease; }
    .el-progress-bar__inner::after {
      display: inline-block;
      content: "";
      height: 100%;
      vertical-align: middle; }
  .el-progress-bar__innerText {
    display: inline-block;
    vertical-align: middle;
    color: #ffffff;
    font-size: 12px;
    margin: 0 5px; }

@-webkit-keyframes progress {
  0% {
    background-position: 0 0; }
  100% {
    background-position: 32px 0; } }

@keyframes progress {
  0% {
    background-position: 0 0; }
  100% {
    background-position: 32px 0; } }
