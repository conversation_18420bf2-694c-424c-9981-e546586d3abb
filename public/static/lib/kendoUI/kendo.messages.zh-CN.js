/* FlatColorPicker messages */  
  
if (kendo.ui.FlatColorPicker) {  
kendo.ui.FlatColorPicker.prototype.options.messages =  
$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{  
  "apply": "确定",  
  "cancel": "取消"  
});  
}  
  
/* ColorPicker messages */  
  
if (kendo.ui.ColorPicker) {  
kendo.ui.ColorPicker.prototype.options.messages =  
$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{  
  "apply": "确定",  
  "cancel": "取消"  
});  
}  
  
/* ColumnMenu messages */  
  
if (kendo.ui.ColumnMenu) {  
kendo.ui.ColumnMenu.prototype.options.messages =  
$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{  
  "sortAscending": "升序",  
  "sortDescending": "降序",  
  "filter": "过滤",  
  "columns": "列",  
  "done": "完成",  
  "settings": "列设置",  
  "lock": "锁定",  
  "unlock": "解除锁定"  
});  
}  
  
/* Editor messages */  
  
if (kendo.ui.Editor) {  
kendo.ui.Editor.prototype.options.messages =  
$.extend(true, kendo.ui.Editor.prototype.options.messages,{  
  "bold": "粗体",  
  "italic": "斜体",  
  "underline": "下划线",  
  "strikethrough": "删除线",  
  "superscript": "上标",  
  "subscript": "下标",  
  "justifyCenter": "居中",  
  "justifyLeft": "左对齐",  
  "justifyRight": "右对齐",  
  "justifyFull": "两端对齐",  
  "insertUnorderedList": "插入无序列表",  
  "insertOrderedList": "插入有序列表",  
  "indent": "增加缩进",  
  "outdent": "减少缩进",  
  "createLink": "插入链接",  
  "unlink": "移除链接",  
  "insertImage": "插入图片",  
  "insertFile": "插入文件",  
  "insertHtml": "插入 HTML",  
  "viewHtml": "查看 HTML",  
  "fontName": "选择字体",  
  "fontNameInherit": "（继承的字体）",  
  "fontSize": "选择字号",  
  "fontSizeInherit": "（继承的字号）",  
  "formatBlock": "格式化块",  
  "formatting": "格式化",  
  "foreColor": "颜色",  
  "backColor": "背景色",  
  "style": "风格",  
  "emptyFolder": "文件夹为空",  
  "uploadFile": "上传",  
  "orderBy": "排序条件:",  
  "orderBySize": "大小",  
  "orderByName": "名字",  
  "invalidFileType": "选中的文件 \"{0}\" 非法，支持的文件类型为 {1}。",  
  "deleteFile": '您确定要删除 \"{0}\"?',  
  "overwriteFile": '当前文件夹已存在文件名为 \"{0}\" 的文件，您确定要覆盖么？',  
  "directoryNotFound": "此文件夹未找到",  
  "imageWebAddress": "图片地址",  
  "imageAltText": "替代文本",  
  "imageWidth": "宽度 (px)",  
  "imageHeight": "高度 (px)",  
  "fileWebAddress": "文件地址",  
  "fileTitle": "标题",  
  "linkWebAddress": "链接地址",  
  "linkText": "链接文字",  
  "linkToolTip": "链接提示",  
  "linkOpenInNewWindow": "在新窗口中打开",  
  "dialogUpdate": "上传",  
  "dialogInsert": "插入",  
  "dialogButtonSeparator": "或",  
  "dialogCancel": "取消",  
  "createTable": "创建表格",  
  "addColumnLeft": "左侧添加列",  
  "addColumnRight": "右侧添加列",  
  "addRowAbove": "上方添加行",  
  "addRowBelow": "下方添加行",  
  "deleteRow": "删除行",  
  "deleteColumn": "删除列"  
});  
}  
  
/* FileBrowser messages */  
  
if (kendo.ui.FileBrowser) {  
kendo.ui.FileBrowser.prototype.options.messages =  
$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{  
  "uploadFile": "上传",  
  "orderBy": "排序条件",  
  "orderByName": "名称",  
  "orderBySize": "大小",  
  "directoryNotFound": "此文件夹未找到",  
  "emptyFolder": "文件夹为空",  
  "deleteFile": '您确定要删除 \"{0}\"?',  
  "invalidFileType": "选中的文件 \"{0}\" 非法，支持的文件类型为 {1}。",  
  "overwriteFile": "当前文件夹已存在文件名为 \"{0}\" 的文件，您确定要覆盖么？",  
  "dropFilesHere": "拖拽要上传的文件到此处",  
  "search": "搜索"  
});  
}  
  
/* FilterMenu messages */  
  
if (kendo.ui.FilterMenu) {  
kendo.ui.FilterMenu.prototype.options.messages =  
$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{  
  "info": "显示符合以下条件的行",  
  "isTrue": "为真",  
  "isFalse": "为假",  
  "filter": "过滤",  
  "clear": "清除",  
  "and": "并且",  
  "or": "或",  
  "selectValue": "-选择-",  
  "operator": "运算符",  
  "value": "值",  
  "cancel": "取消"  
});  
}  
  
/* Filter menu operator messages */  
  
if (kendo.ui.FilterMenu) {  
kendo.ui.FilterMenu.prototype.options.operators =  
$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{  
  "string": {  
    "eq": "等于",  
    "neq": "不等于",  
    "startswith": "开头为",  
    "contains": "包含",  
    "doesnotcontain": "不包含",  
    "endswith": "结尾为"  
  },  
  "number": {  
    "eq": "等于",  
    "neq": "不等于",  
    "gte": "大于等于",  
    "gt": "大于",  
    "lte": "小于等于",  
    "lt": "小于"  
  },  
  "date": {  
    "eq": "等于",  
    "neq": "不等于",  
    "gte": "大于等于",  
    "gt": "大于",  
    "lte": "小于等于",  
    "lt": "小于"  
  },  
  "enum": {  
    "eq": "等于",  
    "neq": "不等于"  
  }  
});  
}  
  
  
/* Gantt messages */  
  
if (kendo.ui.Gantt) {  
kendo.ui.Gantt.prototype.options.messages =  
$.extend(true, kendo.ui.Gantt.prototype.options.messages,{  
  "views": {  
    "day": "日",  
    "week": "周",  
    "month": "月"  
  },  
  "actions": {  
    "append": "添加任务",  
    "addChild": "添加子任务",  
    "insertBefore": "添加到前面",  
    "insertAfter": "添加到后面"  
  }  
});  
}  
  
/* Grid messages */  
  
if (kendo.ui.Grid) {  
kendo.ui.Grid.prototype.options.messages =  
$.extend(true, kendo.ui.Grid.prototype.options.messages,{  
  "commands": {  
    "cancel": "取消",  
    "canceledit": "取消",  
    "create": "新增",  
    "destroy": "删除",  
    "edit": "编辑",  
    "save": "保存",  
    "select": "选择",  
    "update": "确认" , 
    "excel": "导出Excel",
    "pdf": "导出PDF"
  },  
  "editable": {  
    "cancelDelete": "取消",  
    "confirmation": "确定要删除吗？",  
    "confirmDelete": "删除"  
  }  
});  
}  
  
/* TreeList messages */
if (kendo.ui.TreeList) {  
kendo.ui.TreeList.prototype.options.messages =  
$.extend(true, kendo.ui.TreeList.prototype.options.messages,{  
  "commands": {  
      edit: "编辑",
      update: "确认",
      canceledit: "取消",
      create: "新增",
      createchild: "新增子节点",
      destroy: "删除",
      excel: "导出Excel",
      pdf: "导出PDF"
  },  
  "editable": {  
    "cancelDelete": "取消",  
    "confirmation": "确定要删除吗？",  
    "confirmDelete": "删除"  
  } ,
  "noRows": "未找到记录"
});  
}  



/* Groupable messages */  
  
if (kendo.ui.Groupable) {  
kendo.ui.Groupable.prototype.options.messages =  
$.extend(true, kendo.ui.Groupable.prototype.options.messages,{  
  "empty": "托拽列标题到此处按列组合显示"  
});  
}  
  
/* NumericTextBox messages */  
  
if (kendo.ui.NumericTextBox) {  
kendo.ui.NumericTextBox.prototype.options =  
$.extend(true, kendo.ui.NumericTextBox.prototype.options,{  
  "upArrowText": "增加",  
  "downArrowText": "减少"  
});  
}  
  
/* Pager messages */  
  
if (kendo.ui.Pager) {  
kendo.ui.Pager.prototype.options.messages =  
$.extend(true, kendo.ui.Pager.prototype.options.messages,{  
  "display": "显示条目 {0} - {1} 共 {2}",  
  "empty": "没有可显示的记录。",  
  "page": "页",  
  "of": "共 {0}",  
  "itemsPerPage": "每页",  
  "first": "首页",  
  "last": "末页",  
  "next": "下一页",  
  "previous": "上一页",  
  "refresh": "刷新",  
  "morePages": "更多..."  
});  
}  
  
/* PivotGrid messages */  
  
if (kendo.ui.PivotGrid) {  
kendo.ui.PivotGrid.prototype.options.messages =  
$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{  
  "measureFields": "拖放数据字段于此",  
  "columnFields": "拖放列字段于此",  
  "rowFields": "拖放行字段于此"  
});  
}  
  
/* RecurrenceEditor messages */  
  
if (kendo.ui.RecurrenceEditor) {  
kendo.ui.RecurrenceEditor.prototype.options.messages =  
$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{  
  "frequencies": {  
    "never": "从不",  
    "hourly": "每小时",  
    "daily": "每天",  
    "weekly": "每周",  
    "monthly": "每月",  
    "yearly": "每年"  
  },  
  "hourly": {  
    "repeatEvery": "重复周期: ",  
    "interval": " 小时"  
  },  
  "daily": {  
    "repeatEvery": "重复周期: ",  
    "interval": " 天"  
  },  
  "weekly": {  
    "interval": " 周",  
    "repeatEvery": "重复周期: ",  
    "repeatOn": "重复于:"  
  },  
  "monthly": {  
    "repeatEvery": "重复周期: ",  
    "repeatOn": "重复于:",  
    "interval": " 月",  
    "day": "日期"  
  },  
  "yearly": {  
    "repeatEvery": "重复周期: ",  
    "repeatOn": "重复于: ",  
    "interval": " 年",  
    "of": " 月份: "  
  },  
  "end": {  
    "label": "截止时间:",  
    "mobileLabel": "截止时间",  
    "never": "从不",  
    "after": "重复 ",  
    "occurrence": " 次后",  
    "on": "止于 "  
  },  
  "offsetPositions": {  
    "first": "第一",  
    "second": "第二",  
    "third": "第三",  
    "fourth": "第四",  
    "last": "最后"  
  },  
  "weekdays": {  
    "day": "天",  
    "weekday": "工作日",  
    "weekend": "周末"  
  }  
});  
}  
  
  
/* Scheduler messages */  
  
if (kendo.ui.Scheduler) {  
kendo.ui.Scheduler.prototype.options.messages =  
$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{  
  "today": "今天",  
  "save": "保存",  
  "cancel": "取消",  
  "destroy": "删除",  
  "deleteWindowTitle": "删除事件",  
  "ariaSlotLabel": "选择从 {0:t} 到 {1:t}",  
  "ariaEventLabel": "{0} on {1:D} at {2:t}",  
  "views": {  
    "day": "日",  
    "week": "周",  
    "workWeek": "工作日",  
    "agenda": "日程",  
    "month": "月"  
  },  
  "recurrenceMessages": {  
    "deleteWindowTitle": "删除周期条目",  
    "deleteWindowOccurrence": "删除当前事件",  
    "deleteWindowSeries": "删除序列",  
    "editWindowTitle": "修改周期条目",  
    "editWindowOccurrence": "修改当前事件",  
    "editWindowSeries": "修改序列"  
  },  
  "editor": {  
    "title": "标题",  
    "start": "起始",  
    "end": "终止",  
    "allDayEvent": "全天事件",  
    "description": "描述",  
    "repeat": "重复",  
    "timezone": " ",  
    "startTimezone": "起始时区",  
    "endTimezone": "终止时区",  
    "separateTimezones": "使用独立的起始和终止时区",  
    "timezoneEditorTitle": "时区",  
    "timezoneEditorButton": "时区",  
    "timezoneTitle": "选择时区",  
    "noTimezone": "无",  
    "editorTitle": "事件"  
  }  
});  
}  
  
/* Slider messages */  
  
if (kendo.ui.Slider) {  
kendo.ui.Slider.prototype.options =  
$.extend(true, kendo.ui.Slider.prototype.options,{  
  "increaseButtonTitle": "增加",  
  "decreaseButtonTitle": "减少"  
});  
}  
  
/* TreeView messages */  
  
if (kendo.ui.TreeView) {  
kendo.ui.TreeView.prototype.options.messages =  
$.extend(true, kendo.ui.TreeView.prototype.options.messages,{  
  "loading": "加载中...",  
  "requestFailed": "加载失败",  
  "retry": "重试"  
});  
}  
  
/* Upload messages */  
  
if (kendo.ui.Upload) {  
kendo.ui.Upload.prototype.options.localization =  
$.extend(true, kendo.ui.Upload.prototype.options.localization,{  
  "select": "选择...",  
  "cancel": "取消",  
  "retry": "重试",  
  "clearSelectedFiles":"取消",
  "remove": "移除",  
  "uploadSelectedFiles": "上传文件",  
  "dropFilesHere": "拖拽要上传的文件到此处",  
  "statusUploading": "上传中",  
  "statusUploaded": "已上传",  
  "statusWarning": "警告",  
  "statusFailed": "失败",  
  "headerStatusUploading": "上传...",  
  "headerStatusUploaded": "完成"  
});  
}  

/* Spreadsheet messages */

if (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {
    kendo.spreadsheet.messages.borderPalette =
        $.extend(true, kendo.spreadsheet.messages.borderPalette, {
            "allBorders": "所有边框",
            "insideBorders": "内边框",
            "insideHorizontalBorders": "水平内边框",
            "insideVerticalBorders": "垂直内边框",
            "outsideBorders": "外边框",
            "leftBorder": "左边框",
            "topBorder": "顶边框",
            "rightBorder": "右边框",
            "bottomBorder": "底边框",
            "noBorders": "没有边框",
            "reset": "重设颜色",
            "customColor": "自定义颜色...",
            "apply": "确认",
            "cancel": "取消"
        });
}

if (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {
    kendo.spreadsheet.messages.dialogs =
        $.extend(true, kendo.spreadsheet.messages.dialogs, {
            "apply": "确认",
            "save": "储存",
            "cancel": "取消",
            "remove": "移除",
            "retry": "重试",
            "revert": "还原",
            "okText": "是",
            "formatCellsDialog": {
                "title": "格式",
                "categories": {
                    "number": "数字",
                    "currency": "货币",
                    "date": "日期"
                }
            },
            "fontFamilyDialog": {
                "title": "字体"
            },
            "fontSizeDialog": {
                "title": "字体大小"
            },
            "bordersDialog": {
                "title": "边框"
            },
            "alignmentDialog": {
                "title": "对齐",
                "buttons": {
                    "justtifyLeft": "向左对齐",
                    "justifyCenter": "左右中央对齐",
                    "justifyRight": "向右对齐",
                    "justifyFull": "两端对齐",
                    "alignTop": "顶端对齐",
                    "alignMiddle": "垂直对齐",
                    "alignBottom": "底部对齐"
                }
            },
            "mergeDialog": {
                "title": "合并单元格",
                "buttons": {
                    "mergeCells": "全部合并",
                    "mergeHorizontally": "水平合并",
                    "mergeVertically": "垂直合并",
                    "unmerge": "取消合并"
                }
            },
            "freezeDialog": {
                "title": "锁定单元格",
                "buttons": {
                    "freezePanes": "锁定单元格",
                    "freezeRows": "锁定行",
                    "freezeColumns": "锁定列",
                    "unfreeze": "取消锁定单元格"
                }
            },
            "validationDialog": {
                "title": "数据验证",
                "hintMessage": "请输入有效的 {0} 数值 {1}.",
                "hintTitle": "验证 {0}",
                "criteria": {
                    "any": "任何数值",
                    "number": "数字",
                    "text": "文字",
                    "date": "日期",
                    "custom": "自定义公式",
                    "list": "列表"
                },
                "comparers": {
                    "greaterThan": "大于",
                    "lessThan": "小于",
                    "between": "在 ... 之间",
                    "notBetween": "不在 ... 之间",
                    "equalTo": "等于",
                    "notEqualTo": "不等于",
                    "greaterThanOrEqualTo": "大于或等于",
                    "lessThanOrEqualTo": "小于或等于"
                },
                "comparerMessages": {
                    "greaterThan": "大于 {0}",
                    "lessThan": "小于 {0}",
                    "between": "在 {0} 和 {1} 之间",
                    "notBetween": "不在 {0} 和 {1} 之间",
                    "equalTo": "等于 {0}",
                    "notEqualTo": "不等于 {0}",
                    "greaterThanOrEqualTo": "大于或等于 {0}",
                    "lessThanOrEqualTo": "小于或等于 {0}",
                    "custom": "符合公式: {0}"
                },
                "labels": {
                    "criteria": "准则",
                    "comparer": "比较的",
                    "min": "最小",
                    "max": "最大",
                    "value": "数值",
                    "start": "开始",
                    "end": "结束",
                    "onInvalidData": "在数据无效时",
                    "rejectInput": "拒绝输入",
                    "showWarning": "显示警告",
                    "showHint": "显示提示",
                    "hintTitle": "提示标题",
                    "hintMessage": "提示信息",
                    "ignoreBlank": "忽略空白"
                },
                "placeholders": {
                    "typeTitle": "类型标题",
                    "typeMessage": "类型信息"
                }
            },
            "saveAsDialog": {
                "title": "另存...",
                "labels": {
                    "fileName": "文件名称",
                    "saveAsType": "另存类型"
                }
            },
            "exportAsDialog": {
                "title": "导出...",
                "labels": {
                    "fileName": "文件名称",
                    "saveAsType": "另存类型",
                    "exportArea": "导出",
                    "paperSize": "页面大小",
                    "margins": "边距",
                    "orientation": "方向",
                    "print": "打印",
                    "guidelines": "参考线",
                    "center": "置中",
                    "horizontally": "水平地",
                    "vertically": "垂直地"
                }
            },
            "modifyMergedDialog": {
                "errorMessage": "不能转换部份已合并的单元格"
            },
            "useKeyboardDialog": {
                "title": "复制与粘贴",
                "errorMessage": "此操作不能通过菜单命令实现，请使用快捷键进行操作:",
                "labels": {
                    "forCopy": "复制",
                    "forCut": "剪切",
                    "forPaste": "粘贴"
                }
            },
            "unsupportedSelectionDialog": {
                "errorMessage": "此操作不能应用于复合选区"
            }
        });
}

if (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {
    kendo.spreadsheet.messages.filterMenu =
        $.extend(true, kendo.spreadsheet.messages.filterMenu, {
            "sortAscending": "按名称升序",
            "sortDescending": "按名称降序",
            "filterByValue": "数据过滤",
            "filterByCondition": "条件过滤",
            "apply": "确定",
            "search": "搜索",
            "addToCurrent": "添加到当前选择",
            "clear": "清除",
            "blanks": "(Blanks)",
            "operatorNone": "None",
            "and": "AND",
            "or": "OR",
            "operators": {
                "string": {
                    "contains": "包含文本",
                    "doesnotcontain": "不包含文本",
                    "startswith": "开始文本",
                    "endswith": "结束文字"
                },
                "date": {
                    "eq": "日期为",
                    "neq": "日期不为",
                    "lt": "在此之前",
                    "gt": "在此之后"
                },
                "number": {
                    "eq": "等于",
                    "neq": "不等于",
                    "gte": "大于或等于",
                    "gt": "大于",
                    "lte": "小于或等于",
                    "lt": "小于"
                }
            }
        });
}

if (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {
    kendo.spreadsheet.messages.toolbar =
        $.extend(true, kendo.spreadsheet.messages.toolbar, {
            "addColumnLeft": "向左添加列",
            "addColumnRight": "向右添加列",
            "addRowAbove": "向上添加行",
            "addRowBelow": "向下添加行",
            "alignment": "对齐",
            "alignmentButtons": {
                "justtifyLeft": "左对齐",
                "justifyCenter": "居中对齐",
                "justifyRight": "右对齐",
                "justifyFull": "两端对齐",
                "alignTop": "顶端对齐",
                "alignMiddle": "垂直居中",
                "alignBottom": "底部对齐"
            },
            "backgroundColor": "背景色",
            "bold": "粗体",
            "borders": "边框",
            "colorPicker": {
                "reset": "重置颜色",
                "customColor": "自定义颜色..."
            },
            "copy": "复制",
            "cut": "剪切",
            "deleteColumn": "删除列",
            "deleteRow": "删除行",
            "excelImport": "从Excel导入...",
            "filter": "过滤",
            "fontFamily": "字体",
            "fontSize": "字号",
            "format": "自定义格式...",
            "formatTypes": {
                "automatic": "自动",
                "number": "数值",
                "percent": "百分比",
                "financial": "会计专用",
                "currency": "货币",
                "date": "日期",
                "time": "时间",
                "dateTime": "日期与时间",
                "duration": "时间段",
                "moreFormats": "更多格式..."
            },
            "formatDecreaseDecimal": "降序",
            "formatIncreaseDecimal": "升序",
            "freeze": "冻结区域",
            "freezeButtons": {
                "freezePanes": "冻结区域",
                "freezeRows": "冻结行",
                "freezeColumns": "冻结列",
                "unfreeze": "解除冻结"
            },
            "italic": "斜体",
            "merge": "合并单元格",
            "mergeButtons": {
                "mergeCells": "全部合并",
                "mergeHorizontally": "水平合并",
                "mergeVertically": "垂直合并",
                "unmerge": "拆分"
            },
            "open": "打开...",
            "paste": "粘贴",
            "quickAccess": {
                "redo": "重做",
                "undo": "撤消"
            },
            "saveAs": "另存为...",
            "sortAsc": "升序",
            "sortDesc": "降序",
            "sortButtons": {
                "sortSheetAsc": "Sort sheet A to Z",
                "sortSheetDesc": "Sort sheet Z to A",
                "sortRangeAsc": "Sort range A to Z",
                "sortRangeDesc": "Sort range Z to A"
            },
            "textColor": "文本颜色",
            "textWrap": "自动换行",
            "underline": "下划线",
            "validation": "验证"
        });
}

if (kendo.spreadsheet && kendo.spreadsheet.messages.view) {
    kendo.spreadsheet.messages.view =
        $.extend(true, kendo.spreadsheet.messages.view, {
            "errors": {
                "shiftingNonblankCells": "不能在该区域插入数据，请尝试重新选择插入区域或删除工作表中最后的数据。",
                "filterRangeContainingMerges": "在合并区域中不能应用该过滤规则",
                "validationError": "输入数据验证错误"
            },
            "tabs": {
                "home": "主页",
                "insert": "插入",
                "data": "数据"
            }
        });
}




/* Validator messages */  
  
if (kendo.ui.Validator) {  
kendo.ui.Validator.prototype.options.messages =  
$.extend(true, kendo.ui.Validator.prototype.options.messages,{  
	  "required": "请输入此必填项",  
	  "pattern": "请输入正确的内容",  
	  "min": "请输入大于或等于 {1}的数值",  
	  "max": "请输入小于或等于 {1}的数值",  
	  "step": "请输入正确内容",  
	  "email": "请输入正确的邮箱地址(如:<EMAIL>)",  
	  "url": "请输入正确的URL",  
	  "date": "请输入正确的日期"  
});  
}  
  
/*if(Date){  
    Date.prototype.toISOString = function () {  
        return kendo.toString(this, "yyyy-MM-dd HH:mm:ss");  
    };  
}*/