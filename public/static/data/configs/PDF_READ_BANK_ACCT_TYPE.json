{"data": [{"jsonInfo": "{\"charsetName\":\"GB18030\",\"cnName\":\"中国银行_明细列表\",\"columnConfig\":[{\"cnName\":\"未使用1\",\"fieldName\":\"UNUSED1\",\"length\":4,\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"cnName\":\"交易日期\",\"fieldName\":\"businessTime\",\"format\":[\"yyMMdd\"],\"length\":6,\"typeConvert\":\"com.spsoft.common.pdfread.convert.DateConvert\"},{\"cnName\":\"未使用2\",\"fieldName\":\"UNUSED2\",\"length\":6,\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"cnName\":\"未使用3\",\"fieldName\":\"UNUSED3\",\"length\":8,\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"cnName\":\"未使用4\",\"fieldName\":\"UNUSED4\",\"length\":4,\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"cnName\":\"摘要\",\"fieldName\":\"bankAbstract\",\"length\":45,\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"cnName\":\"支出\",\"fieldName\":\"expenditure\",\"length\":18,\"typeConvert\":\"com.spsoft.common.pdfread.convert.NumberConvert$BigDecimalConvert\"},{\"cnName\":\"收入\",\"fieldName\":\"income\",\"length\":18,\"typeConvert\":\"com.spsoft.common.pdfread.convert.NumberConvert$BigDecimalConvert\"},{\"cnName\":\"未使用5\",\"fieldName\":\"UNUSED5\",\"length\":20,\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"cnName\":\"唯一流水号\",\"fieldName\":\"serialNumber\",\"length\":23,\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"cnName\":\"对方名称\",\"fieldName\":\"oppositeName\",\"length\":26,\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"}],\"confMatcher\":\"^   账号\\\\s+\\\\d*\\\\s+账户名称\\\\s+\\\\S+\\\\s+开户行\\\\s+中国银行\\\\S+\\\\s+起始日期\\\\s*\\\\d{8}.*Balance At the End of the Period.*$\",\"mergeRowIfNull\":{\"fieldName\":\"businessTime\",\"length\":0},\"name\":\"BOC_LIST\",\"rowEndStr\":\"\\n ───────────────────────────────────────────────────────────────────────────────────────────────\",\"rowStartStr\":\"Notes           |\\n ───────────────────────────────────────────────────────────────────────────────────────────────\\n \",\"splitChar\":\"|\",\"type\":\"com.spsoft.qf.companyaccount.bankaccount.vo.PdfReadFixedLengthConf\"}", "keyId": 1380, "keyName": "PDF_READ_BANK_ACCT_TYPE", "keyNotes": "中国银行_明细列表", "keyValue": 1, "modifyTime": *************}, {"jsonInfo": "{\"cnName\":\"中国农业银行_明细回单\",\"columnConfig\":[{\"alignType\":2,\"cnName\":\"交易日期\",\"fieldName\":\"businessTime\",\"format\":[\"yyyyMMddHH:mm:ss\"],\"titleText\":\"交易时间\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.DateConvert\"},{\"alignType\":2,\"cnName\":\"摘要\",\"fieldName\":\"bankAbstract\",\"titleText\":\"交易用途\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"alignType\":2,\"cnName\":\"对方名称\",\"fieldName\":\"oppositeName\",\"titleText\":\"对方户名\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"alignType\":2,\"cnName\":\"收入\",\"fieldName\":\"income\",\"titleText\":\"收入金额\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.NumberConvert$BigDecimalConvert\"},{\"alignType\":2,\"cnName\":\"支出\",\"fieldName\":\"expenditure\",\"titleText\":\"支出金额\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.NumberConvert$BigDecimalConvert\"}],\"confMatcher\":\"^账户明细.*交易时间.*汇总收入金额.*汇总支出金额.*$\",\"deviationX\":0.1,\"endText\":\"汇总收入金额\",\"mergeDeviation\":0.0,\"name\":\"ABC1_LIST\",\"sortRowDeviation\":0.0,\"sortRowMode\":0,\"type\":\"com.spsoft.qf.companyaccount.bankaccount.vo.PdfReadCoordinateConf\"}", "keyId": 1381, "keyName": "PDF_READ_BANK_ACCT_TYPE", "keyNotes": "中国农业银行_明细回单", "keyValue": 2, "modifyTime": *************}, {"jsonInfo": "{\"cnName\":\"中国农业银行_明细回单\",\"columnConfig\":[{\"alignType\":2,\"cnName\":\"交易日期\",\"fieldName\":\"businessTime\",\"format\":[\"yyyy-MM-ddHH:mm:ss\"],\"titleText\":\"交易时间\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.DateConvert\"},{\"alignType\":2,\"cnName\":\"摘要\",\"fieldName\":\"bankAbstract\",\"titleText\":\"摘要\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"alignType\":2,\"cnName\":\"对方名称\",\"fieldName\":\"oppositeName\",\"titleText\":\"对方户名\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"alignType\":2,\"cnName\":\"收入\",\"fieldName\":\"income\",\"titleText\":\"收入金额\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.NumberConvert$BigDecimalConvert\"},{\"alignType\":2,\"cnName\":\"支出\",\"fieldName\":\"expenditure\",\"titleText\":\"支出金额\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.NumberConvert$BigDecimalConvert\"}],\"confMatcher\":\"^账户明细.*交易时间.*总收入笔数.*总收入金额.*总支出笔数.*总支出金额.*$\",\"deviationX\":0.1,\"endText\":\"(第\\\\d页/共)|(总收入笔数)\",\"mergeDeviation\":0.0,\"name\":\"ABC2_LIST\",\"sortRowDeviation\":0.0,\"sortRowMode\":0,\"type\":\"com.spsoft.qf.companyaccount.bankaccount.vo.PdfReadCoordinateConf\"}", "keyId": 1382, "keyName": "PDF_READ_BANK_ACCT_TYPE", "keyNotes": "中国农业银行_明细回单", "keyValue": 3, "modifyTime": *************}, {"jsonInfo": "{\"cnName\":\"中国建设银行_中国建设银行账户明细信息\",\"columnConfig\":[{\"alignType\":2,\"cnName\":\"交易日期\",\"fieldName\":\"businessTime\",\"format\":[\"yyyyMMddHH:mm:ss\"],\"titleText\":\"交易时间\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.DateConvert\"},{\"alignType\":2,\"cnName\":\"摘要\",\"fieldName\":\"bankAbstract\",\"titleText\":\"摘要\",\"typeConvert\":\"com.spsoft.qf.companyaccount.bankaccount.transform.CcbAbstractConvert\"},{\"alignType\":2,\"cnName\":\"备注\",\"fieldName\":\"bankRemarks\",\"titleText\":\"备注\",\"typeConvert\":\"com.spsoft.qf.companyaccount.bankaccount.transform.CcbAbstractConvert\"},{\"alignType\":2,\"cnName\":\"对方名称\",\"fieldName\":\"oppositeName\",\"titleText\":\"对方户名\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"alignType\":2,\"cnName\":\"支出\",\"fieldName\":\"expenditure\",\"titleText\":\"借方发生额\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.NumberConvert$BigDecimalConvert\"},{\"alignType\":2,\"cnName\":\"收入\",\"fieldName\":\"income\",\"titleText\":\"贷方发生额\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.NumberConvert$BigDecimalConvert\"}],\"confMatcher\":\"\\\\d+\\n中国建设银行账户明细信息.*\\n账号\\n交易时\\n间\\n借方发\\n生额\\n贷方发\\n生额\\n余额 币种\\n对方户\\n名\\n对方账\\n号\\n对方开\\n户机构\\n记账日\\n期\\n摘要 备注\\n账户明\\n细编号-\\n交易流\\n水号\\n企业流\\n水号\\n凭证种\\n类\\n凭证号\\n交易介\\n质编号.*\",\"deviationX\":0.1,\"endText\":\"温馨提示：本明细文件生成时间为\\\\d{4}年\\\\d{1,2}月\\\\d{1,2}日\\\\s*\\\\d{1,2}:\\\\d{1,2}，不作为收、付款方交易的最终依据。\",\"mergeDeviation\":0.0,\"name\":\"CCB_LIST\",\"sortRowDeviation\":0.0,\"sortRowMode\":0,\"type\":\"com.spsoft.qf.companyaccount.bankaccount.vo.PdfReadCoordinateConf\"}", "keyId": 1383, "keyName": "PDF_READ_BANK_ACCT_TYPE", "keyNotes": "中国建设银行_账户明细信息", "keyValue": 4, "modifyTime": *************}, {"jsonInfo": "{\"cnName\":\"中国工商银行_中国工商银行企业存款对账单\",\"columnConfig\":[{\"alignType\":2,\"cnName\":\"交易日期\",\"fieldName\":\"businessTime\",\"format\":[\"yyyy-MM-dd HH:mm:ss\"],\"titleText\":\"交易时间\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.DateConvert\"},{\"alignType\":2,\"cnName\":\"唯一流水号\",\"fieldName\":\"serialNumber\",\"titleText\":\"电子回单编号\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"alignType\":2,\"cnName\":\"摘要\",\"fieldName\":\"bankAbstract\",\"titleText\":\"摘要\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"alignType\":2,\"cnName\":\"对方名称\",\"fieldName\":\"oppositeName\",\"titleText\":\"对方户名\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"alignType\":2,\"cnName\":\"支出\",\"fieldName\":\"expenditure\",\"titleText\":\"借方发生额\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.NumberConvert$BigDecimalConvert\"},{\"alignType\":2,\"cnName\":\"收入\",\"fieldName\":\"income\",\"titleText\":\"贷方发生额\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.NumberConvert$BigDecimalConvert\"}],\"confMatcher\":\"^中国工商银行企业存款对账单.*重要提示：请仔细核对发生额明细及账户余额，如有疑问请与我们联系。.*$\",\"deviationX\":0.1,\"endText\":\"重要提示：请仔细核对发生额明细及账户余额，如有疑问请与我们联系。文件下载后请妥善保管，如若被伪造、变造、篡改，不具有法律效力。客服电话：95588\",\"mergeDeviation\":0.0,\"name\":\"ICBC_LIST\",\"sortRowDeviation\":0.0,\"sortRowMode\":0,\"type\":\"com.spsoft.qf.companyaccount.bankaccount.vo.PdfReadCoordinateConf\"}", "keyId": 1384, "keyName": "PDF_READ_BANK_ACCT_TYPE", "keyNotes": "中国工商银行_账户明细信息", "keyValue": 5, "modifyTime": *************}, {"jsonInfo": "{\"cnName\":\"招商银行_账务明细清单\",\"columnConfig\":[{\"alignType\":2,\"cnName\":\"交易日期\",\"fieldName\":\"businessTime\",\"format\":[\"yyyyMMdd\"],\"titleText\":\"日期日期Date\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.DateConvert\"},{\"alignType\":2,\"cnName\":\"唯一流水号\",\"fieldName\":\"serialNumber\",\"titleText\":\"票据号票据号Bill No.\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"alignType\":2,\"cnName\":\"摘要\",\"fieldName\":\"bankAbstract\",\"titleText\":\"摘要摘要Description\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"alignType\":2,\"cnName\":\"对方名称\",\"fieldName\":\"oppositeName\",\"titleText\":\"对手户名对手户名Counterparty Account Name\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"alignType\":3,\"cnName\":\"收入\",\"fieldName\":\"income\",\"titleText\":\"贷方金额贷方金额\",\"typeConvert\":\"com.spsoft.qf.companyaccount.bankaccount.transform.BankIncomeConvert\"}],\"confMatcher\":\"^开户银行:.*账单所属期间:.*\\\\d{8}  \\\\d{8}.*招商银行股份有限公司.*账务明细清单.*Statem ent Of Account.*$\",\"deviationX\":2.0,\"endText\":\"第\\\\d页/共\\\\d页\",\"mergeDeviation\":0.5,\"name\":\"CMB_LIST\",\"sortRowDeviation\":10.0,\"sortRowMode\":2,\"type\":\"com.spsoft.qf.companyaccount.bankaccount.vo.PdfReadCoordinateConf\"}", "keyId": 1385, "keyName": "PDF_READ_BANK_ACCT_TYPE", "keyNotes": "招商银行_账务明细清单", "keyValue": 6, "modifyTime": *************}, {"jsonInfo": "{\"cnName\":\"中国平安银行_交易明细\",\"columnConfig\":[{\"alignType\":2,\"cnName\":\"交易日期\",\"fieldName\":\"businessTime\",\"format\":[\"yyyyMMddHHmsss\"],\"titleText\":\"交易时间\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.DateConvert\"},{\"alignType\":2,\"cnName\":\"摘要\",\"fieldName\":\"bankAbstract\",\"titleText\":\"摘要\",\"typeConvert\":\"com.spsoft.qf.companyaccount.bankaccount.transform.CcbAbstractConvert\"},{\"alignType\":2,\"cnName\":\"备注\",\"fieldName\":\"bankRemarks\",\"titleText\":\"用途\",\"typeConvert\":\"com.spsoft.qf.companyaccount.bankaccount.transform.CcbAbstractConvert\"},{\"alignType\":2,\"cnName\":\"对方名称\",\"fieldName\":\"oppositeName\",\"titleText\":\"对方户名\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"alignType\":2,\"cnName\":\"收入\",\"fieldName\":\"income\",\"titleText\":\"收入\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.NumberConvert$BigDecimalConvert\"},{\"alignType\":2,\"cnName\":\"支出\",\"fieldName\":\"expenditure\",\"titleText\":\"支出\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.NumberConvert$BigDecimalConvert\"}],\"confMatcher\":\"^\\\\d{8} - \\\\d{8}交易明细\\n收入总金额：.*收入总笔数：.*支出总金额：.*支出总笔数：.*账号.*户名.*币种.*交易时间 收入 支出 冲正标志 账户余额 对方账号 对方户名 摘要 用途.*$\",\"deviationX\":0.1,\"mergeDeviation\":0.0,\"name\":\"PAB_LIST\",\"sortRowDeviation\":0.0,\"sortRowMode\":0,\"type\":\"com.spsoft.qf.companyaccount.bankaccount.vo.PdfReadCoordinateConf\"}", "keyId": 1386, "keyName": "PDF_READ_BANK_ACCT_TYPE", "keyNotes": "中国平安银行_交易明细", "keyValue": 7, "modifyTime": *************}, {"jsonInfo": "{\"cnName\":\"顺德农商银行_账户对账单\",\"columnConfig\":[{\"alignType\":2,\"cnName\":\"交易日期\",\"fieldName\":\"businessTime\",\"format\":[\"yyyyMMdd\"],\"titleText\":\"交易日期DATE\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.DateConvert\"},{\"alignType\":2,\"cnName\":\"唯一流水号\",\"fieldName\":\"serialNumber\",\"titleText\":\"交易流水JNLNO\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"alignType\":2,\"cnName\":\"摘要\",\"fieldName\":\"bankAbstract\",\"titleText\":\"交易摘要DESCRIPTION\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"alignType\":3,\"cnName\":\"收入\",\"fieldName\":\"income\",\"titleText\":\"贷方（收入） DEPOSIT\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.NumberConvert$BigDecimalConvert\"},{\"alignType\":3,\"cnName\":\"支出\",\"fieldName\":\"expenditure\",\"titleText\":\"借方（支出）WITHDRAWAL\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.NumberConvert$BigDecimalConvert\"}],\"confMatcher\":\"^账号\\nA/C NO\\\\..*户名\\nA/C NAME.*货币\\nCURRENCY.*完    END  OF  LIST.*开户机构：.*$\",\"deviationX\":1.9,\"endText\":\"合计：\",\"mergeDeviation\":0.0,\"name\":\"SDRCB_LIST\",\"sortRowDeviation\":0.0,\"sortRowMode\":0,\"type\":\"com.spsoft.qf.companyaccount.bankaccount.vo.PdfReadCoordinateConf\"}", "keyId": 1387, "keyName": "PDF_READ_BANK_ACCT_TYPE", "keyNotes": "顺德农商银行_账户对账单", "keyValue": 8, "modifyTime": *************}, {"jsonInfo": "{\"cnName\":\"中信银行_账户交易明细\",\"columnConfig\":[{\"alignType\":2,\"cnName\":\"交易日期\",\"fieldName\":\"businessTime\",\"format\":[\"yyyy-MM-dd\"],\"titleText\":\"交易日期\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.DateConvert\"},{\"alignType\":1,\"cnName\":\"摘要\",\"fieldName\":\"bankAbstract\",\"titleText\":\"摘要信息\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"alignType\":1,\"cnName\":\"对方名称\",\"fieldName\":\"oppositeName\",\"titleText\":\"对方账户名称\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.StringConvert\"},{\"alignType\":3,\"cnName\":\"收入\",\"fieldName\":\"income\",\"titleText\":\"贷方发生额\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.NumberConvert$BigDecimalConvert\"},{\"alignType\":3,\"cnName\":\"支出\",\"fieldName\":\"expenditure\",\"titleText\":\"借方发生额\",\"typeConvert\":\"com.spsoft.common.pdfread.convert.NumberConvert$BigDecimalConvert\"}],\"confMatcher\":\"^账户交易明细.*查询周期：.*币种：.*借贷标识：.*交易日期 柜员交易号 摘要信息 对方账号 对方账户名称 动账资金分簿 借方发生额 贷方发生额 余额.*总笔数：.*借方总笔数：.*借方合计金额：.*贷方总笔数：.*贷方合计金额：.*$\",\"deviationX\":0.1,\"endText\":\"账户交易明细\",\"mergeDeviation\":0.0,\"name\":\"SDRCB_LIST\",\"sortRowDeviation\":0.0,\"sortRowMode\":0,\"type\":\"com.spsoft.qf.companyaccount.bankaccount.vo.PdfReadCoordinateConf\"}", "keyId": 1388, "keyName": "PDF_READ_BANK_ACCT_TYPE", "keyNotes": "中信银行_账户交易明细", "keyValue": 9, "modifyTime": *************}, {"jsonInfo": null, "keyId": 1389, "keyName": "PDF_READ_BANK_ACCT_TYPE", "keyNotes": "东莞农村商业银行_电子对账单", "keyValue": 10, "modifyTime": *************}], "page": 1, "pageSize": 10, "returnCode": 0, "total": 10}