.box[style] {
	width: 100% !important;
	font-size: 14px;
	text-align: center;
}
.text-center {
text-align: center;
}
.highlight {
font-weight: bold;
}
.fr{
	float:right
}
.fl{
	float:left
}
.el-col-8 {
	width: 33.33333%;
}
.el-col-1, .el-col-2, .el-col-3, .el-col-4, .el-col-5, .el-col-6, .el-col-7, .el-col-8, .el-col-9, .el-col-10, .el-col-11, .el-col-12, .el-col-13, .el-col-14, .el-col-15, .el-col-16, .el-col-17, .el-col-18, .el-col-19, .el-col-20, .el-col-21, .el-col-22, .el-col-23, .el-col-24 {
	float: left;
	box-sizing: border-box;
}
.clearfix:after {
content: "";
display: block;
height: 0;
visibility: hidden;
clear: both
}
.el-table{
	width:100%  !important;
	border-top: 0 !important;
}
.clearfix {
*zoom: 1
}
.el-row {
	position: relative;
	box-sizing: border-box;
}
.expand-trigger>div {
	height: 0 !important;
}
.printFlooter {
	padding-top: 10px;
	text-align: left;
}
.pageNumberClass {
position:absolute;
right:0px;
}
	@page :pseudo-class {
			size: A4 landscape;
			margin:2cm;
	}
	.box .el-table td, .box .el-table th {
			font-family:"宋体";
			height: 22px;
			border-right: 1px solid #000 !important;
			border-bottom: 1px solid #000 !important;
			background: transparent;
			word-break: break-all;
	}
	.box .el-table .printStyle td, .box .el-table .printStyle th {
			font-weight: 800 !important;
	}
	.box .el-table.table_tr_height35 td, .box .el-table.table_tr_height35 th {
			height: 35px;
			border-right: 1px solid #000 !important;
			border-bottom: 1px solid #000 !important;
			background: transparent;
			word-break: break-all;
	}
	.box .el-table.table_tr_height35spaceLine .highlight {
		font-weight: bold;
	}
	.box .el-table.table_tr_height35spaceLine .spaceLine .cell{
		padding-left: 35px !important;
	}
	.box .el-table.table_tr_height37 td, .box .el-table.table_tr_height37 th {
			height: 37px;
			border-right: 1px solid #000 !important;
			border-bottom: 1px solid #000 !important;
			background: transparent;
			word-break: break-all;
	}
	.box .el-table.table_tr_height32 td, .box .el-table.table_tr_height32 th {
		height: 32px;
		border-right: 1px solid #000 !important;
		border-bottom: 1px solid #000 !important;
		background: transparent;
		word-break: break-all;
	}
	.box .el-table.table_tr_height31 td, .box .el-table.table_tr_height31 th {
		height: 31px;
		border-right: 1px solid #000 !important;
		border-bottom: 1px solid #000 !important;
		background: transparent;
		word-break: break-all;
	}
	.box .el-table.table_tr_height30 td, .box .el-table.table_tr_height30 th {
		height: 30px;
		border-right: 1px solid #000 !important;
		border-bottom: 1px solid #000 !important;
		background: transparent;
		word-break: break-all;
	}
	.highlightStyle {
		font-weight: 800;
	}
	.textCenter {
		text-align: center;
	}
	.el-table th.is-center, .el-table td.is-center {
			text-align: center;
	}
	.el-table th.is-right, .el-table td.is-right {
			text-align: right;
	}
	.el-table .el-table__header-wrapper th.is-right, .el-table .el-table__header-wrapper td.is-right{
		text-align: center;
	}
	.box .el-table.table_tr_height35 .cell, .box .el-table.table_tr_height35 .cell span{
		font-size: 16px;
	}
	.box .el-table.table_tr_height37 .cell, .box .el-table.table_tr_height37 .cell span{
		font-size: 16px;
	}
	.box .el-table.table_tr_height31 .cell, .box .el-table.table_tr_height31 .cell span{
		font-size: 16px;
	}
	.cell, .cell span {
			font-size: 12px;
	}
	.cell {
			width: 100% !important;
			padding-left: 2px;
			padding-right: 2px;
			box-sizing: border-box;
	}
	body {
			background: none !important;
			background: transparent !important;
	}
	.A4{
			page-break-before: auto;        
			page-break-after: always;
			position: relative;
	}
	.el-table__body-wrapper .el-scrollbar__view{
			min-height: 0 !important;
	}
	/*.el-table th,
	.el-table__header-wrapper thead div {
			background: none;
	}*/
	
	.el-table__header-wrapper .el-table__header {
			border-top: 1px solid;
	}
	.el-table .el-table__header-wrapper table[style],
	.el-table .el-table__body-wrapper table[style] {
			/* width: 100% !important; */
	}
	.el-table--enable-row-hover .el-table__body tr>td.col-hidden {
			display: none;
	}

	.el-table .el-table__header-wrapper table col[name^="el-table"][width],
	.el-table .el-table__body-wrapper table col[name^="el-table"][width],
	.el-table .el-table__body-wrapper .el-table__empty-block[style] {
		/* width: auto !important; */
	}
	.el-table{
			border: none;
			border-left: 1px solid #000;
	}
	
	.el-table > div[style], .el-table .el-table__body-wrapper .el-table__empty-block[style] {
			height: auto !important;
			overflow: visible !important;
			margin-bottom: 7px;
	}

	.subAccountTable{ margin-top: 15px;  margin-bottom: 15px; }
	.subAccountTable .noBorder{
			border:none;
			box-shadow:0;
			height:100%;
			width:100%;
			display:block;
			box-sizing: border-box;
	}
	.subAccountTable td{ border-right: 1px solid rgb(236, 223, 223); border-bottom: 1px solid rgb(236, 223, 223); }
	.subAccountTable td .el-row.xiahuaxian .el-col{  min-width:0 !important; }
	.subAccountTable td .el-row.xiahuaxian .el-col span{ text-decoration:underline; }
	.subAccountTable td .el-row.xiahuaxian .el-input{ width: 60%; }
	.subAccountTable td .el-row.xiahuaxian .el-input input{
			width: 100%;
			height: auto;
			border:none;
			border-bottom: 1px solid #000;
			background: #fff;
			padding: 0;
			box-shadow:none;
			outline: none;
	}
	.subAccountTable td .el-row.xiahuaxian .el-input input[disabled] { background: transparent; }
	.subAccountTable tr td{ height:30px; padding:0; }
	.subAccountTable tr td .cell{ height:30px; }
	.subAccountTable .unChangableCell { background-color: rgb(238, 240, 246) !important;  }
	.subAccountTable .thTop{ height:50%; padding: 6px 0; border-bottom: 1px solid rgb(236, 223, 223); }
	.subAccountTable .unitTd, .subAccountTable  .unitTd .cell{  padding:0; }
	.subAccountTable .moneyUint{ height:50%; width: 199px; background-image: url(/static/imgs/1410MoneyUint.png) !important;}
	.subAccountTable .moneyUintTd{ height: 30px; overflow: hidden; background-image: url(/static/imgs/1410MoneyUint.png) !important; background-repeat-y: repeat;}
	.subAccountTable .editInput{ display:none; height:100%; }
	.subAccountTable .moneyUint span{
			float: left;
			display: inline-block;
			width: 17px;
			height: 100%;
			margin-right: 1px;
			background-color: #fff;
			text-align: center;
			font-size: 12px;
	}
	.subAccountTable .moneyUint span.first{width: 16px !important;margin-left:2px}
	.subAccountTable .moneyUint span.last{width: 16px !important;}
	.subAccountTable .currency .currency_content { height: 26px; margin-top: -1px;}
	.subAccountTable .currency .currency_content .el-select .el-input { width: 70px; }
	.subAccountTable .currency .currency_content .el-select .el-input .el-input__inner { cursor: pointer; }
	.subAccountTable .currency .currency_content label { float: left; width: 71px; display: inline-block; white-space: nowrap; text-align: right;}
	.subAccountTable .currency .currency_content label + * { float: left; width: calc("100% - 71px"); } 
	.subAccountTable .noTooltip .cell{  white-space: inherit; }
	.subAccountTable .moneyUintTd span{
			font-family: 'tahoma';
			position: relative;
			display: block;
			height:30px;
			line-height: 30px;
			font-weight: bold;
			right:-5px;
			font-size: 14px;
			letter-spacing: 11px;
			overflow: hidden;
			text-align: right;
			/* border-right: 1px solid yellow; */
			}
	.subAccountTable .moneyUintTd span.redColor{
			color:red;
			}
	/*  */
/* 资产凭证组件 */
.voucherWithAssetsItem-wrapper{
	display: flex;
}
.voucherWithAssetsItem-itemBox{
	flex: 1;
	line-height: 28px;
}
.voucherWithAssetsItem-item{
	display: flex;
}
.voucher-code{
	flex: 1;
	white-space: pre-line;
}
.operate{
	width: 30px;
}
