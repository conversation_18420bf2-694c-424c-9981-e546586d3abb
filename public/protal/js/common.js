$(document).ready(() => {
  _initNavSubmenu();
  _initHeaderExternalLinks();
  _initSiteFloatButtons();
  _initContactDialog();
  _initNotification();
});

function _initNavSubmenu() {
  initDropdown('site-header-nav__item', 'site-header-nav__submenu');
}

function _initHeaderExternalLinks() {
  initDropdown('site-header-link', 'site-header-link__submenu');
}

function _initSiteFloatButtons() {
  $('.site-float .J_backtop').click(() => {
    document.documentElement.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth',
    });
  });
}

function _initContactDialog() {
  $('.J_contact_dialog').on('click', showContactDialog);
}

function _initNotification() {
  // showNotification();
}

/**
 * 弹出联系界面
 */
function showContactDialog() {
  return showDialog({
    width: 480,
    height: 330,
    template: '\
    <div class="site-contact-dialog">\
      <h2 class="title-1"><span>请您扫描下方二维码添加客服咨询</span></h2>\
      <div class="qrcode">\
        <div class="item"><img src="/protal/img/site-contact/wechat.png"><span>企业微信客服</span></div>\
        <div class="item"><img src="/protal/img/site-contact/qq.jpg"><span>QQ客服</span></div>\
      </div>\
      <h2 class="title-2"><span>或拨打我们的热线电话</span></h2>\
      <div class="phone"><span>************</span></div>\
      <button class="close"></button>\
    </div>\
    ',
  });
}

/**
 * 弹出公告界面
 */
function showNotification() {
  if (localStorage.getItem('show-notification') === 'false') return;

  function remember() {
    localStorage.setItem('show-notification', 'false');
  }

  return showDialog({
    width: 690,
    height: 300,
    title: '更名公告',
    template: '\
    <div class="site-notification">\
      <p style="margin-bottom:10px;font-size:16px;"><strong>尊敬的莲融客户：</strong></p>\
      <p>您好，从2023年8月1日开始，本网站正式更名为“票靓在线大会计”，莲融系统仍可正常使用。</p>\
      <p style="margin: 10px 0;"><strong>进入方法:</strong></p>\
      <p>① 访问地址: <a target="_blank" href="https://www.quickf.cn/app.html#/login">https://www.quickf.cn/app.html#/login</a>，即可进入莲融登录页；</p>\
      <p>② 在本页面右上角，点击“进入工作台”，选择“票靓服务版 (原莲融)”，即可进入莲融登录页。</p>\
    </div>\
    ',
    onConfirm(hide) {
      remember();
      hide();
    },
    onClose() {
      remember();
    },
  });
}

/**
 * @typedef {Object} DialogOption
 * @property {(hide: () => void) => void} onConfirm
 * @property {(hide: () => void) => void} onCancel
 * @property {() => void} onClose
 * @property {number} width
 * @property {number} height
 * @property {string} title
 * @property {boolean} showMask
 * @property {boolean} showClose
 * @property {boolean} removeAfterHide
 * @property {string} template
 */
/**
 * 弹出界面
 * @param {DialogOption} options
 */
function showDialog(options) {
  var options = $.extend({
    width: 480,
    height: 320,
    title: '',
    showMask: true,
    showClose: true,
    template: '',
    removeAfterHide: true,
  }, options || {});

  const template = '<div class="site-dialog">\
    <div class="site-dialog__mask"></div>\
    <div class="site-dialog__layout">\
      <div class="site-dialog__title"></div>\
      <div class="site-dialog__slot"></div>\
      <button class="site-dialog__close"></button>\
      <div class="site-dialog__footer">\
        <button class="site-dialog__confirm button primary">确定</button>\
        <button class="site-dialog__cancel button">取消</button>\
      </div>\
    </div>\
  </div>';

  const $dialog = $(template);
  const $mask = $dialog.find('.site-dialog__mask');
  const $layout = $dialog.find('.site-dialog__layout');
  const $slot = $dialog.find('.site-dialog__slot');
  const $title = $dialog.find('.site-dialog__title');
  const $footer = $dialog.find('.site-dialog__footer');
  const $closeButton = $dialog.find('.site-dialog__close');
  const $confirmButton = $dialog.find('.site-dialog__confirm');
  const $cancelButton = $dialog.find('.site-dialog__cancel');

  $layout.width(options.width).height(options.height);

  if (!options.showMask) {
    $mask.remove();
  } else {
    $layout.addClass('with-mask');
  }

  if (!options.title) {
    $title.remove();
  } else {
    $layout.addClass('with-title');
    $title.html(options.title);
  }

  if (!options.showClose) {
    $layout.addClass('with-close');
    $closeButton.remove();
  }

  if (!options.onConfirm && !options.onCancel) {
    $footer.remove();
  } else {
    $layout.addClass('with-footer');
    if (!options.onConfirm) {
      $confirmButton.remove();
    } else {
      $layout.addClass('with-confirm');
    }
    if (!options.onCancel) {
      $cancelButton.remove();
    } else {
      $layout.addClass('with-cancel');
    }
  }

  if (options.template) {
    $slot.html(options.template);
  }

  $closeButton.off().on('click', () => {
    if (options.onClose) {
      options.onClose();
    }
    hide();
  });
  $confirmButton.off().on('click', () => {
    if (options.onConfirm) {
      options.onConfirm(hide);
    }
  });
  $cancelButton.off().on('click', () => {
    if (options.onCancel) {
      options.onCancel(hide);
    }
  });

  function mount() {
    $(document.body).append($dialog);
  }

  function show() {
    $dialog.stop(true).fadeIn(160);
  }

  function hide() {
    $dialog.stop(true).fadeOut(160, () => {
      $dialog.remove();
    });
  }

  function destroy() {
    $confirmButton.off();
    $cancelButton.off();
    $dialog.remove();
  }

  mount();
  show();

  return {
    destroy,
    hide,
    mount,
    show,
  };
}

function initDropdown(itemClass, submenuClass) {
  const hoverClass = `${itemClass }--hover`;
  const $items = $(`.${ itemClass}`);
  const onEnter = function () {
    $(this).addClass(hoverClass);
    const $submenu = $(this).find(`.${ submenuClass}`);
    if (!$submenu.length) return;
    const itemWidth = $(this).innerWidth();
    const menuWidth = $submenu.outerWidth();
    $submenu
    .stop(true)
    .css({
      left: `${(menuWidth - itemWidth) / -2 }px`,
    })
    .slideDown(100);
  };
  const onLeave = function () {
    $(this).removeClass(hoverClass);
    $(this).find(`.${ submenuClass}`).slideUp(100);
  };
  $items.hover(onEnter, onLeave);
}

function initSiteFloatButtons() {
  $('.site-float .J_backtop').click(() => {
    document.documentElement.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth',
    });
  });
}

(function ($) {
  $.fn.modal = function (options) {
    const settings = $.extend({
      title: '',
      content: '',
    }, options);

    return this.each(function () {
      const $modal = $(this);
      const $mask = $('<div class="modal-mask"></div>');
      const $modalBody = $('<div class="modal-body"></div>');
      const $modalHeader = $('<div class="modal-header"></div>');
      const $modalTitle = $(`<h2>${ settings.title }</h2>`);
      const $modalClose = $('<div class="modal-close"><img src="/protal/img/close.png" /></div>');
      const $modalContent = $(`<div class="modal-content">${ settings.content }</div>`);
      $modal.append($mask);
      $modalBody.append($modalHeader);
      $modalHeader.append($modalTitle);
      $modalHeader.append($modalClose);
      $modalBody.append($modalContent);
      $mask.append($modalBody);
      $modal.append($mask);
      $modal.show();

      $modalClose.on('click', () => {
        $modal.hide();
        $mask.remove();
      });
    });
  };
}(jQuery));
