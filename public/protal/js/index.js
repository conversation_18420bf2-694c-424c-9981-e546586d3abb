$(document).ready(function() {
  initCarousel()
  initFloor6()
})

// floor-1 轮播图
function initCarousel() {
  if (document.getElementById('carousel')) {
    new Swiper('#carousel', {
      mode: 'horizontal',
      autoplay: 4000,
      speed: 500,
      loop: true,
      pagination: '#carousel-pagination',
      paginationClickable: true
    })
  }

  // 由于轮播图生成得时机比较晚，公共得弹窗事件注册不会生效，此处手动注册
  $('#carousel .J_contact_dialog').on('click', showContactDialog)
}

// floot-6 手风琴 tab
function initFloor6() {
  var $navs = $('.floor-6 .tabs-navs__item')
  var $panes = $('.floor-6 .tabs-panes__item')

  $navs.on('mouseenter', function() {
    var $activeNav = $(this)
    var $activePane = $panes.eq($(this).index())
    var $otherSummary = $navs.find('.summary').filter(function() {
      return !$.contains($activeNav.get(0), this)
    })
    var $activeSummary = $activeNav.find('.summary')

    $navs.removeClass('tabs-navs__item--active')
    $activeNav.addClass('tabs-navs__item--active')
    $otherSummary.stop(true).slideUp(160)
    $activeSummary.stop(true).slideDown(160)

    $panes.removeClass('tabs-panes__item--active')
    $activePane.addClass('tabs-panes__item--active')
  })
}
