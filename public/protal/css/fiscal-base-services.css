.floor-1 {
  box-sizing: border-box;
  height: 420px;
  background-repeat: no-repeat;
  background-position: center center;
}

.floor-title {
  margin: auto;
  font-size: 36px;
  font-weight: bold;
  color: #202125;
  text-align: center;
  line-height: 54px;
}

.floor-subtitle {
  margin: auto;
  font-size: 24px;
  font-family: Gotham-LightItalic, Gotham;
  font-weight: normal;
  color: #707784;
  line-height: 29px;
  text-align: center;
  line-height: 27px;
}

.box-1 {
  display: flex;
  justify-content: space-between;
  padding-bottom: 60px;
}

.box-1 .box-item {
  width: 278px;
  height: 290px;
  background: rgba(238, 246, 255, 0.9);
  border-radius: 4px;
  border: 1px solid;
  border-color: #DBE8F9;
}

.box-item .box-item-icon {
  text-align: center;
  padding-top: 10px;
  height: 113px;
}

.box-item-title {
  font-size: 20px;
  font-weight: bold;
  color: #202125;
  margin-top: 15px;
  text-align: center;
}

.box-item-subtitle {
  font-size: 16px;
  font-weight: 400;
  color: #707784;
  line-height: 25px;
  text-align: center;
  padding-top: 15px;
}

.box-item-price {
  text-align: center;
  font-size: 16px;
  font-weight: 400;
  color: #1689FF;
  margin-top: 15px;
}

.box-item-actions {
  display: none;
}

.box-item:hover {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0px 12px 50px 0px rgba(144, 184, 236, 0.2);
  border-radius: 4px;
  border: none;
  transform: translateY(-20px);
  transition: all 0.3s ease-in-out;
}

.box-item:hover .box-item-actions {
  display: flex;
}

.box-item:hover .box-item-subtitle {
  display: none;
}

.box-item-actions {
  padding: 0 30px;
  justify-content: space-between;
  margin-top: 32px;
}

.box-item-actions .button {
  width: 100px;
}

.floor-3 {
  background: #F5F9FD;
  padding-bottom: 60px;
}

.service-flow-content {
  display: flex;
}

.service-flow-item {
  width: 150px;
  position: relative;
  text-align: center;
  padding-top: 30px;
  box-sizing: border-box;
}

.service-flow-item:not(:last-child) {
  margin-right: 25px;
}

.service-flow-item:not(:last-child)::after {
  content: '';
  position: absolute;
  width: 68px;
  height: 36px;
  top: 60px;
  right: -50px;
  background-image: url(/protal/img/fiscal-base-services-floor-3/箭头.png);
}

.service-flow-item-bottom {
  width: 150px;
  height: 161px;
  background: url(/protal/img/fiscal-base-services-floor-3/card1.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 35px 12px 19px;
}

.service-flow-item__title {
  font-size: 18px;
  font-weight: 500;
  color: #202125;
  line-height: 27px;
  text-align: center;
}

.service-flow-item__tip {
  font-size: 14px;
  font-weight: 400;
  color: #707784;
  line-height: 20px;
  margin-top: 8px;
  text-align: left;
}

.floor-4 {
  background: url('/protal/img/fiscal-base-services-floor-4/bg4.jpg');
  height: 968px;
  width: 100%;
}

.box-2 {
  width: 1200px;
  height: 730px;
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0px 12px 50px 0px rgba(144, 184, 236, 0.2);
  border: 2px solid;
  border-image: linear-gradient(138deg, rgba(255, 255, 255, 0.7), rgba(250, 252, 255, 0.5)) 2 2;
  backdrop-filter: blur(6px);
  margin: 0 auto;
  box-sizing: border-box;
  display: flex;
  padding: 40px 45px;
  justify-content: space-between;
  flex-wrap: wrap;
}

.box2-item {
  width: 300px;
  flex: 0 0 auto;
  margin-bottom: 20px;
}

.box2-item-header {
  position: relative;
  border-bottom: 1px solid #D4DDE7;
}

.box2-item-header::after {
  content: '';
  width: 40px;
  height: 2px;
  background: #1689FF;
  position: absolute;
  left: 0;
  bottom: -1px;
}

.box2-item-index {
  position: absolute;
  right: 0;
  bottom: -5px;
}

.box2-item-title {
  font-size: 20px;
  font-weight: bold;
  color: #202125;
  margin-top: 16px;
  margin-bottom: 16px;
}

.box2-item-content {
  margin-top: 16px;
}

.box2-item-line {
  line-height: 20px;
}

.box2-item-line__type {
  color: #6C6C6C;
  font-size: 14px;
  font-weight: bold;
}

.box2-item-line__text {
  color: #6C6C6C;
  font-size: 14px;
}

.floor-5 {
  padding-bottom: 100px;
}

.floor-5 .tabs {
  background-color: #E1EDFF;
  display: flex;
  flex-flow: row nowrap;
  width: 1180px;
  height: 400px;
  background: #FFFFFF;

}

.floor-5 .tabs-navs {
  flex: 0 0 240px;
  box-sizing: border-box;
  width: 240px;
  padding: 20px;
  background: #E1EDFF;
  opacity: 0.8;
}

.floor-5 .tabs-navs__item {
  height: 50px;
  line-height: 50px;
  margin-bottom: 16px;
  border-radius: 6px;
  background: #F3F8FE;
  font-size: 16px;
  font-family: SourceHanSansCN-Medium, SourceHanSansCN;
  font-weight: 500;
  color: #202125;
  cursor: pointer;
  transition: all .2s;
  padding-left: 20px;
}

.floor-5 .tabs-navs__item:hover,
.floor-5 .tabs-navs__item--active {
  color: #fff;
  background: linear-gradient(218deg, #52A7FF 0%, #1689FF 100%);
}

.floor-5 .tabs-panes {
  flex: 1 1 100%;
  min-height: 400px;
  background-color: #fff;
  box-shadow: 0px 12px 50px 0px rgba(206, 216, 229, 0.4);
  border: 2px solid;
  border-image: linear-gradient(138deg, rgba(255, 255, 255, 0.7), rgba(250, 252, 255, 0.5)) 2 2;
}

.floor-5 .tabs-panes__item {
  display: none;
}

.floor-5 .tabs-panes__item--active {
  display: block;
}


.floor-5 .pane {
  padding: 32px 0;
}

.tabs-navs__item .icon {
  display: inline-block;
  width: 22px;
  height: 20px;
  vertical-align: sub;
}

.tabs-navs__item1-icon {
  background-image: url('/protal/img/fiscal-base-services-floor-5/财税数字化.png');
}

.tabs-navs__item--active .tabs-navs__item1-icon {
  background-image: url('/protal/img/fiscal-base-services-floor-5/财税数字化_active.png');
}

.tabs-navs__item2-icon {
  background-image: url('/protal/img/fiscal-base-services-floor-5/实时数据监控.png');
}

.tabs-navs__item--active .tabs-navs__item2-icon {
  background-image: url('/protal/img/fiscal-base-services-floor-5/实时数据监控_active.png');
}

.tabs-navs__item3-icon {
  background-image: url('/protal/img/fiscal-base-services-floor-5/专业服务团队.png');
}

.tabs-navs__item--active .tabs-navs__item3-icon {
  background-image: url('/protal/img/fiscal-base-services-floor-5/专业服务团队_active.png');
}

.tabs-navs__item4-icon {
  background-image: url('/protal/img/fiscal-base-services-floor-5/最新资讯推送.png');
}

.tabs-navs__item--active .tabs-navs__item4-icon {
  background-image: url('/protal/img/fiscal-base-services-floor-5/最新资讯推送_active.png');
}

.tabs-panes .pane-1 {
  padding: 30px 60px;
}

.pane-line-title {
  font-size: 24px;
  font-weight: 500;
  color: #202125;
  border-bottom: 1px solid #E2EAF2;
  position: relative;
  line-height: 60px;
}

.pane-line-title::after {
  content: '';
  position: absolute;
  width: 48px;
  height: 2px;
  background: #1689FF;
  left: 0;
  bottom: -1px;
}

.pane-line {
  margin-bottom: 58px;
}

.pane-line-tip {
  font-size: 16px;
  font-weight: 400;
  color: #707784;
  line-height: 28px;
  margin-top: 15px;
}

.tabs-panes .pane-2 {
  padding: 10px 60px;
}

.pane-2 .pane-line {
  margin-bottom: 20px;
}

.tabs-panes .pane-3 {
  display: flex;
  padding: 23px 27px;
}

.pane3-col {
  width: 224px;
  height: 346px;
  background: #F7FBFF;
  border: 1px solid #F1F4F7;
  text-align: center;
}

.pane3-col:not(:last-child) {
  border-right: none;
}

.pane3-col-img {
  margin-top: 27px;
}

.pane3-col-title {
  font-size: 22px;
  font-weight: 600;
  color: #202125;
  margin-top: 14px;
}

.pane3-col-tip {
  font-size: 14px;
  line-height: 24px;
  font-weight: 400;
  color: #707784;
  margin-top: 14px;
  text-align: left;
  padding: 0 14px;
}

.tabs-panes .pane-4 {
  padding: 40px 50px;
  display: flex;
  justify-content: space-between;
}
