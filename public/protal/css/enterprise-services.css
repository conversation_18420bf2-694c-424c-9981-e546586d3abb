.floor-title {
  margin: auto;
  padding: 70px 0 10px;
  text-align: center;
  font-size: 36px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  color: #202125;
  line-height: 54px;
}

.floor-subtitle {
  margin: auto;
  padding: 0px 0 45px;
  text-align: center;
  font-size: 18px;
  font-weight: 400;
  color: #8E96A5;
  line-height: 27px;
}

.actions {
  text-align: center;
}

.floor-1 {
  box-sizing: border-box;
  height: 420px;
  background-color: rgb(51, 136, 238);
  background-repeat: no-repeat;
  background-position: center center;
}


.floor-2 {
  box-sizing: border-box;
  height: 450px;
  background-color: #FBFDFF;
}

.floor-2 .flow {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
}

.floor-2 .flow__item {
  position: relative;
  width: 92px;
  text-align: center;
}

.floor-2 .flow__item::before {
  position: absolute;
  top: 46px;
  left: 92px;
  width: 60px;
  height: 0;
  border-bottom: 1px dashed #1689FF;
  content: "";
}

.floor-2 .flow__item::after {
  position: absolute;
  top: 40px;
  left: 152px;
  width: 0;
  height: 0;
  border: 7px dashed transparent;
  border-left: 9px solid #1689FF;
  content: "";
}

.floor-2 .flow__item:last-child::before,
.floor-2 .flow__item:last-child::after {
  display: none;
}

.floor-2 .flow__item img {
  display: block;
  width: 92px;
  height: 92px;
}

.floor-2 .flow__item h3 {
  font-size: 18px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #202125;
  line-height: 25px;
}

.floor-2 .flow__item p {
  font-size: 14px;
  font-weight: 400;
  color: #8E96A5;
  line-height: 21px;
}


.floor-3 {
  box-sizing: border-box;
  padding-bottom: 70px;
  background-color: #EEF5FF;
}

.floor-3 .content {
  height: 490px;
  background: #F9FDFF;
  box-shadow: 0px 12px 50px 0px rgba(144, 184, 236, 0.2);
  display: flex;
  flex-flow: row wrap;
}

.floor-3 .box {
  box-sizing: border-box;
  width: 50%;
  height: 50%;
  border-right: 1px solid #E2E9ED;
  border-bottom: 1px solid #E2E9ED;
  display: flex;
  flex-flow: row nowrap;
}

.floor-3 .box:nth-child(2),
.floor-3 .box:nth-child(4) {
  border-right: none;
}

.floor-3 .box:nth-child(3),
.floor-3 .box:nth-child(4) {
  border-bottom: none;
}

.floor-3 .box .main {
  flex: 1 1 auto;
  padding-left: 40px;
}

.floor-3 .box .main h3 {
  font-size: 24px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  color: #202125;
  line-height: 36px;
}

.floor-3 .box .main .amount {
  border-bottom: 1px solid #E2EAF2;
}

.floor-3 .box .main .amount strong {
  display: inline-block;
  font-size: 20px;
  font-weight: 400;
  color: #1689FF;
  line-height: 30px;
  padding-bottom: 10px;
  border-bottom: 2px solid #1689FF;
  position: relative;
  top: 1px;
}

.floor-3 .box .main p {
  font-size: 14px;
  font-weight: 400;
  color: #6C6C6C;
  line-height: 22px;
}

.floor-3 .box .sub {
  flex: 0 0 220px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.floor-3 .box .sub img {
  display: block;
  width: 138px;
  height: 138px;
}


.floor-4 {
  box-sizing: border-box;
  padding-bottom: 70px;
  background-color: #F8FBFE;
}

.floor-4 .tabs {
  background-color: #E1EDFF;
  display: flex;
  flex-flow: row nowrap;
}

.floor-4 .tabs-navs {
  flex: 0 0 240px;
  box-sizing: border-box;
  width: 240px;
  padding: 20px;
}

.floor-4 .tabs-navs__item {
  height: 50px;
  line-height: 50px;
  margin-bottom: 16px;
  border-radius: 6px;
  background: #F3F8FE;
  font-size: 16px;
  font-family: SourceHanSansCN-Medium, SourceHanSansCN;
  font-weight: 500;
  text-align: center;
  color: #202125;
  cursor: pointer;
  transition: all .2s;
}

.floor-4 .tabs-navs__item:hover,
.floor-4 .tabs-navs__item--active {
  color: #fff;
  background: linear-gradient(218deg, #52A7FF 0%, #1689FF 100%);
}

.floor-4 .tabs-panes {
  flex: 1 1 100%;
  min-height: 400px;
  background-color: #fff;
}

.floor-4 .tabs-panes__item {
  display: none;
}

.floor-4 .tabs-panes__item--active {
  display: block;
}


.floor-4 .pane {
  padding: 32px 0;
}

.floor-4 .pane .cols {
  display: flex;
  flex-flow: row nowrap;
  margin-bottom: 30px;
}

.floor-4 .pane .col-1 {
  flex: 1 1 500px;
  box-sizing: border-box;
  border-right: 1px solid #EFEFEF;
  padding: 0 50px;
}

.floor-4 .pane .col-1 img {
  display: block;
  width: 330px;
  height: 230px;
  margin: auto auto 30px;
  background-color: #f8f8f8;
  box-shadow: 0px 12px 50px 0px rgba(144, 184, 236, 0.2);
  border-radius: 8px;
  border-image: linear-gradient(138deg, rgba(253, 254, 255, 0.6), rgba(250, 252, 255, 0.45)) 2 2;
}

.floor-4 .pane .col-1 p {
  font-size: 14px;
  font-weight: 400;
  color: #6C6C6C;
  line-height: 22px;
}

.floor-4 .pane .col-2 {
  flex: 1 1 420px;
  box-sizing: border-box;
  padding: 0 50px;
}

.floor-4 .pane .col-2 h3 {
  margin: 0 auto 20px;
  font-size: 18px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  color: #202125;
  line-height: 27px;
}

.floor-4 .pane .col-2 ul {}

.floor-4 .pane .col-2 li {
  padding-left: 10px;
  position: relative;
  font-size: 14px;
  font-weight: 400;
  color: #202125;
  line-height: 28px;
}

.floor-4 .pane .col-2 li::before {
  content: "";
  position: absolute;
  top: 13px;
  left: 0;
  right: auto;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  background-color: #202125;
}


.floor-5 {
  padding-bottom: 70px;
  background-color: #fff;
}

.floor-5 .rc-bricks {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  margin-bottom: 25px;
}

.floor-5 .rc-bricks__item {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  width: 278px;
  height: 112px;
  background: #FFFFFF;
  box-shadow: 0px 12px 50px 0px rgba(144, 184, 236, 0.2);
  border-radius: 8px;
  border-image: linear-gradient(138deg, rgba(253, 254, 255, 0.6), rgba(250, 252, 255, 0.45)) 2 2;
}

.floor-5 .rc-bricks__item img {
  width: 48px;
  height: 48px;
  margin: 0 18px 0 34px;
}

.floor-5 .rc-bricks__item h2 {
  font-size: 18px;
  font-weight: 400;
  color: #202125;
  line-height: 27px;
}
