.floor-title {
  margin: auto;
  padding: 60px 0 10px;
  text-align: center;
  font-size: 32px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  color: #202125;
  line-height: 48px;
}

.floor-subtitle {
  margin: auto;
  padding: 0px 0 45px;
  text-align: center;
  font-size: 18px;
    font-weight: 400;
  color: #8E96A5;
  line-height: 27px;
}

.actions {
  text-align: center;
}
.actions .button {
  height: 48px;
  line-height: 46px;
  padding: 0px 30px;
  font-size: 18px;
}


.floor-1 {
  box-sizing: border-box;
  height: 420px;
  background-color: rgb(51,136,238);
  background-repeat: no-repeat;
  background-position: center center;
}


.floor-2 {
  box-sizing: border-box;
  height: 560px;
  background-color: #F7FBFF;
}
.floor-2 .rc-bricks {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
}
.floor-2 .rc-bricks__item {
  box-sizing: border-box;
  width: 278px;
  height: 280px;
  padding: 10px;
  background: #FFFFFF;
}
.floor-2 .rc-bricks__item img {
  display: block;
  width: 100%;
  height: 166px;
  margin-bottom: 24px;
}
.floor-2 .rc-bricks__item h2 {
  position: relative;
  height: 26px;
  line-height: 26px;
  font-size: 24px;
  margin-bottom: 15px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  color: #202125;
}
.floor-2 .rc-bricks__item h2 span {
  position: relative;
  z-index: 1;
}
.floor-2 .rc-bricks__item h2::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -1px;
  width: 84px;
  height: 6px;
  background: linear-gradient(270deg, #FFFFFF 0%, #D6EAFF 47%, #2E95FF 100%);
  border-radius: 3px;
}
.floor-2 .rc-bricks__item p {
  height: 16px;
  line-height: 16px;
  font-size: 14px;
    font-weight: 400;
  color: #707784;
  
}


.floor-3 {
  box-sizing: border-box;
  height: 680px;
  background-color: #EDF5FE;
  background-repeat: no-repeat;
  background-position: center 197px;
  background-size: 609px 445px;
}
.floor-3 .content {
  position: relative;
  width: 100%;
}
.floor-3 .content p {
  position: absolute;
  font-size: 16px;
    font-weight: 400;
  color: #425671;
  line-height: 24px;
}
.floor-3 .content .p1 {
  top: -10px;
  left: 30px;
}
.floor-3 .content .p2 {
  top: -10px;
  right: 0;
}
.floor-3 .content .p3 {
  top: 170px;
  left: 40px;
}
.floor-3 .content .p4 {
  top: 170px;
  right: 30px
}


.floor-4 {
  box-sizing: border-box;
  height: 500px;
  background-color: #F7FBFF;
}
.floor-4 .rc-bricks {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
}
.floor-4 .rc-bricks__item {
  position: relative;
  box-sizing: border-box;
  width: 194px;
  height: 216px;
  padding: 14px;
  text-align: center;
  background: rgba(255,255,255,0.8);
  box-shadow: 0px 8px 40px 0px rgba(194,215,241,0.2);
  border-radius: 16px;
}
.floor-4 .rc-bricks__item img {
  display: block;
  width: 96px;
  height: 96px;
  margin: auto;
  margin-bottom: 5px;
}
.floor-4 .rc-bricks__item h2 {
  margin: 0;
  padding: 0;
  height: 24px;
  line-height: 24px;
  font-size: 22px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  color: #202125;
  margin-bottom: 8px;
}
.floor-4 .rc-bricks__item p {
  margin: 0;
  padding: 0;
  line-height: 22px;
  font-size: 14px;
    font-weight: 400;
  color: #707784;
}
.floor-4 .rc-bricks__item .arrow {
  position: absolute;
  top: 76px;
  left: 203px;
  width: 41px;
  height: 64px;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 41px 64px;
}


.floor-5 {
  height: 104px;
  line-height: 104px;
  font-size: 30px;
  color: #FFF;
  background-color: rgb(51,118,241);
  background-repeat: no-repeat;
  background-position: center center;
  text-align: center;
}
.floor-5 img {
  vertical-align: middle;
}
