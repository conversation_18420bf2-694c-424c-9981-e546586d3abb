.breadcrumb {
  font-size: 12px;
  color: #333333;
  margin-bottom: 10px;
  margin-top: 14px;
}

.breadcrumb a {
  color: #666;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.breadcrumb span {
  color: #8E96A5;
}

.service-header {
  display: flex;
  margin-top: 40px;
}

.service-header-img {
  width: 450px;
  height: 300px;
}

.service-header-right {
  margin-left: 70px;
}

.service-header-title {
  font-size: 32px;
  font-weight: bold;
  color: #333333;
  line-height: 48px;
}

.service-header-price {
  margin-top: 39px;
}

.service-header-price__tip {
  font-size: 16px;
  color: #999999;
  line-height: 24px;
}
.service-header-price__number{
  font-size: 18px;
color: #E02020;
line-height: 30px;
}

.service-header-price__number > span{
  font-size: 20px;
  font-weight: bold;
}

.service-header-actions{
  display: flex;
  margin-top: 77px;
}
.service-header-button{
  width: 172px;
  height: 52px;
  border-radius: 4px;
  font-weight: 500;
  color: #FFFFFF;
  font-size: 22px;
  text-align: center;
  line-height: 52px;
  text-decoration: none;
  box-sizing: border-box;
}
.service-header-actions__pay{
  background: #1689FF;
}
.service-header-actions__contact{
  border-radius: 4px;
  border: 2px solid #1689FF;
  color: #1689FF;
  /* margin-left: 22px; */
  line-height: 46px;
}
.service-header-actions__contact span{
  vertical-align: middle;
}
.service-header-actions__contact img{
  vertical-align: middle;
}


.service-flow{
  width: 1180px;
  height: 440px;
  margin-top: 60px;
  background-image: url(/protal/img/service-page/bg.png);
  background-size: 100% 100%;
  margin-left: 10px
}
.service-flow-title{
  text-align: center;
  padding-top: 70px;
}

.service-flow-content{
  margin-top: 80px;
  display: flex;
  padding: 0 50px
}
.service-flow-item{
  width: 116px;
  height: 112px;
  background: #FFFFFF;
  box-shadow: 0px 12px 50px 0px rgba(119,163,220,0.2);
  border-radius: 8px;
  position: relative;
  text-align: center;
  padding-top: 40px;
  box-sizing: border-box;
  font-size: 18px;
  font-weight: 400;
  color: #202125;
  line-height: 24px;
}
.service-flow-item:not(:last-child){
  margin-right: 45px;
}
.service-flow-item:not(:last-child)::after{
  content: '';
  position: absolute;
  width: 41px;
  height: 36px;
  top: 50%;
  transform: translateY(-50%);
  right: -40px;
  background-image: url('/img/service-page/箭头.png');
}
.service-flow-item__img{
  position: absolute;
  top: -36px;
  left: 50%;
  transform: translateX(-50%);
}

.service-content{
  margin-top: 60px;
}
.service-content-title{
  text-align: center;
}
.service-content-box{
  border: 1px solid #9CB1CF;
  width: 1100px;
  box-sizing: border-box;
  margin: 0 auto;
  margin-top: 32px;
  border-bottom: none;
}
.service-content-line{
  display: flex;
}
.service-content-col__title{
  background: #EFF6FE;
  width: 186px;
  border-right: 1px solid #9CB1CF;
  border-bottom: 1px solid #9CB1CF;
  font-size: 22px;font-weight: bold;
  color: #202125;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content:center;
  flex: 0 0 auto;
}
.service-content-col__detail{
  padding-left: 31px;
  padding-top: 20px;
  padding-bottom: 20px;
  font-size: 18px;
  font-weight: 400;
  color: #202125;
  line-height: 38px;
  border-bottom: 1px solid #9CB1CF;
  flex: 1 100%;
}

.service-required{
  width: 1100px;
  margin: 0 auto;
  margin-top: 60px;
  margin-bottom: 110px;
}
.service-required-title{
  text-align: center;
}
.service-required-box{
  display: flex;
  justify-content: space-between;
  margin-top: 32px;
}

.service-required-item{
  width: 530px;
  background: #EFF6FE;
  border: 1px solid #9CB1CF;
}
.service-required-item__title{
  width: 530px;
  height: 60px;
  background: linear-gradient(218deg, #52A7FF 0%, #1689FF 100%);
  font-size: 22px;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 60px;
  padding-left: 32px;
  box-sizing: border-box;
}
.service-required-item__detail{
  padding: 32px;
  box-sizing: border-box;
  font-size: 18px;
  font-weight: 400;
  color: #202125;
  line-height: 38px;
}

.shop-list {
  display: flex;
  justify-content: space-around;
  margin-top: 44px;
  padding: 0 60px;
}
.shop-item{
  width: 175px;
}
.shop-item-title{
  font-size: 16px;
  color: #666666;
  margin-bottom: 14px;
  text-align: center;
}
/* .shop-item-qrcode img{
  width: 140px;
  height: 140px;
} */
.modal-link-button{
  width: 200px;
  height: 40px;
  border-radius: 4px;
  border: 1px solid #1689FF;
  text-align: center;
  line-height: 40px;
  margin: 46px auto 0;
  color: #1689FF;
  cursor: pointer;
}