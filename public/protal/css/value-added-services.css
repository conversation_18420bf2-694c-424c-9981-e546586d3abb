.floor-title {
  margin: auto;
  padding-top: 60px;
  padding-bottom: 40px;
  font-size: 30px;
  font-family: SourceHanSansCN-Normal, SourceHanSansCN;
  font-weight: 400;
  color: #202125;
  line-height: 45px;
  text-align: center;
}

.floor-title strong {
  font-size: 30px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  color: #202125;
  line-height: 45px;
}

.actions {
  text-align: center;
}

.indexed-item {
  position: relative;
  padding-left: 35px;
  font-size: 16px;
  font-weight: 400;
  color: #202125;
  line-height: 20px;
}

.indexed-item>i {
  position: absolute;
  top: 0;
  left: 0;
  height: 20px;
  line-height: 20px;
  padding: 0 7px 0 5px;
  border-radius: 0 10px 10px 0;
  background-color: #178aff;
  color: #fff;
  font-size: 14px;
  font-weight: 400;
  font-style: normal;
}



.floor-1 {
  box-sizing: border-box;
  height: 420px;
  background-repeat: no-repeat;
  background-position: center center;
}


.floor-2 {
  box-sizing: border-box;
  height: 420px;
  padding-top: 85px;
  background-color: #fff;
}

.floor-2 .rc-bricks {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}

.floor-2 .rc-bricks__item {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  width: 280px;
  height: 110px;
  margin-bottom: 25px;
  background-color: #fff;
  box-shadow: 0px 12px 50px 0px rgba(144, 184, 236, 0.2);
  border-radius: 8px;
  border-image: linear-gradient(138deg, rgba(253, 254, 255, 0.6), rgba(250, 252, 255, 0.45)) 2 2;
}

.floor-2 .rc-bricks__item img {
  width: 50px;
  height: 50px;
  margin: 0 20px 0 30px;
  background-color: #f8f8f8;
}

.floor-2 .rc-bricks__item h2 {
  font-size: 18px;
  font-weight: 400;
  color: #202125;
  line-height: 27px;
}


.floor-3 {
  box-sizing: border-box;
  height: 670px;
  background: linear-gradient(90deg, #F2F8FF 0%, #EAF2FE 100%);
}

.floor-3 .content {
  position: relative;
  height: 378px;
  margin-bottom: 38px;
  background-repeat: no-repeat;
  background-position: center center;
}

.floor-3 .content .col-1 {
  position: absolute;
  top: 0;
  bottom: 0;
  height: 100%;
  left: 30px;
  display: flex;
  flex-flow: column;
  justify-content: space-around;
}

.floor-3 .content .col-1 .indexed-item>i {
  background-color: rgb(116, 138, 255);
}

.floor-3 .content .col-2 {
  position: absolute;
  top: 0;
  bottom: 0;
  height: 100%;
  right: 30px;
  display: flex;
  flex-flow: column;
  justify-content: space-around;
}


.floor-4 {
  box-sizing: border-box;
  height: 638px;
  background-color: #F8FBFF;
}

.floor-4 .splitter {
  width: 800px;
  height: 49px;
  margin: auto;
  background-repeat: no-repeat;
  background-position: center center;
}

.floor-4 .rc-bricks {
  margin-top: 50px;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
}

.floor-4 .rc-bricks__item {
  box-sizing: border-box;
  position: relative;
  width: 380px;
  height: 180px;
  padding: 0 34px;
  background-color: #fff;
  box-shadow: 0px 12px 50px 0px rgba(144, 184, 236, 0.2);
  border-radius: 8px;
  border-image: linear-gradient(138deg, rgba(253, 254, 255, 0.6), rgba(250, 252, 255, 0.45)) 2 2;
}

.floor-4 .rc-bricks__item h2 {
  position: relative;
  top: -25px;
  width: 230px;
  height: 49px;
  margin: auto auto -25px;
  background: #178aff;
  background-image: linear-gradient(to right, #178aff, #55aaff);
  font-size: 18px;
  font-weight: 400;
  color: #fff;
  line-height: 49px;
  border-radius: 25px;
  text-align: center;
}

.floor-4 .rc-bricks__item p {
  position: relative;
  padding-left: 16px;
  line-height: 25px;
}

.floor-4 .rc-bricks__item p::before {
  position: absolute;
  top: 10px;
  left: 0;
  content: "";
  width: 5px;
  height: 5px;
  background: rgb(193, 209, 235);
  border-radius: 50%;
}

.floor-4 .rc-bricks__item p strong {
  color: #178aff;
}

.floor-4 .text {
  margin: 15px auto;
  font-size: 14px;
  font-weight: 400;
  color: #707784;
  line-height: 21px;
}

.floor-4 .box {
  text-align: center;
  margin-bottom: 25px;
}

.floor-4 .box .wrapper {
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
  height: 60px;
  background-color: #fff;
  box-shadow: 0px 12px 50px 0px rgba(144, 184, 236, 0.2);
  border-radius: 8px;
  border-image: linear-gradient(138deg, rgba(253, 254, 255, 0.6), rgba(250, 252, 255, 0.45)) 2 2;
}

.floor-4 .box .item {
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  height: 100%;
  padding: 0 30px 0 30px;
}

.floor-4 .box .item::after {
  position: absolute;
  top: -25px;
  right: 10px;
  width: 25px;
  height: 100px;
  content: "";
  background-image: linear-gradient(to right, #fff, rgb(244, 248, 253));
  transform: rotate(20deg);
}

.floor-4 .box .item .indexed-item {
  position: relative;
  z-index: 1;
}

.floor-4 .box .item:last-child::after {
  display: none;
}


.floor-5 {
  height: 360px;
  background-color: rgb(138, 193, 254);
}

.floor-5 .floor-title {
  padding-top: 40px;
  color: #fff;
}

.floor-5 .floor-title strong {
  color: #fff;
}

.floor-5 .rc-bricks {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  margin-bottom: 50px;
}

.floor-5 .rc-bricks__item {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  width: 278px;
  height: 104px;
  background: rgba(238, 246, 255, 0.9);
  border-radius: 8px;
  border-image: linear-gradient(168deg, rgba(244, 249, 255, 1), rgba(233, 244, 255, 1)) 1 1;
  backdrop-filter: blur(2px);
}

.floor-5 .rc-bricks__item img {
  width: 50px;
  height: 50px;
  margin: 0 20px 0 30px;
}

.floor-5 .rc-bricks__item h2 {
  font-size: 18px;
  font-weight: 400;
  color: #202125;
  line-height: 27px;
}


.floor-6 {
  height: 476px;
  background-color: #F8FBFF;
}

.floor-6 .rc-bricks {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  margin-bottom: 25px;
}

.floor-6 .rc-bricks__item {
  box-sizing: border-box;
  display: flex;
  width: 380px;
  height: 188px;
  padding: 38px 38px 0;
  background: #FFFFFF;
  box-shadow: 0px 8px 40px 0px rgba(194, 215, 241, 0.2);
  border-radius: 8px;
}

.floor-6 .rc-bricks__item img {
  width: 58px;
  height: 44px;
  margin: 0 20px 0 0;
}

.floor-6 .rc-bricks__item h2 {
  margin: -5px 0 10px;
  font-size: 22px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  color: #202125;
  line-height: 33px;
}

.floor-6 .rc-bricks__item p {
  font-size: 16px;
  font-weight: 400;
  color: #666;
  line-height: 26px;
}



.floor-7 {
  background-color: #fff;
  padding-bottom: 40px;
}

.floor-7 .text {
  margin-bottom: 40px;
  text-align: center;
  height: 14px;
  font-size: 14px;
  font-weight: 400;
  color: #707784;
  line-height: 21px;
}

.floor-7 .content {
  box-sizing: border-box;
  display: flex;
  flex-flow: row nowrap;
  height: 252px;
  margin-bottom: 40px;
  border: 4px solid rgb(0, 122, 246);
  ;
  background: rgb(0, 122, 246);
}

.floor-7 .col {
  box-sizing: border-box;
  width: 50%;
  height: 100%;
}

.floor-7 .col-1 {
  background: #fff;
}

.floor-7 .col-1 h3 {
  margin: 18px auto;
  text-align: center;
  font-size: 20px;
  font-family: SourceHanSansCN-Medium, SourceHanSansCN;
  font-weight: 700;
  color: #202125;
  line-height: 26px;
}

.floor-7 .col-1 h3 em {
  color: rgb(224, 32, 32);
  font-style: normal;
}

.floor-7 .col-1 ul {
  padding-left: 25px;
}

.floor-7 .col-1 li {
  font-size: 14px;
  font-weight: 400;
  color: #202125;
  line-height: 22px;
}

.floor-7 .col-2 h3 {
  margin: 18px auto;
  text-align: center;
  font-size: 20px;
  font-family: SourceHanSansCN-Medium, SourceHanSansCN;
  font-weight: 700;
  color: #fff;
  line-height: 26px;
}

.floor-7 .col-2 .bricks {
  display: flex;
  flex-flow: row wrap;
  width: 528px;
  height: 160px;
  margin: auto;
  background-color: rgb(36, 140, 247);
}

.floor-7 .col-2 .bricks__item {
  flex: 0 0 auto;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 176px;
  height: 80px;
  border: 1px solid rgb(69, 158, 248);
  color: #fff;
  line-height: 25px;
  font-size: 16px;
  font-weight: 400;
  text-align: center;
  box-shadow: inset 2px 2px 8px rgba(255, 255, 255, .07);
}


.floor-8 {
  height: 480px;
  background-repeat: no-repeat;
  background-position: center center;
}

.floor-8 .rc-bricks {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  margin-bottom: 25px;
}

.floor-8 .rc-bricks__item {
  box-sizing: border-box;
  width: 278px;
  height: 230px;
  padding: 30px 0 0;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  text-align: center;
}

.floor-8 .rc-bricks__item img {
  display: block;
  width: 120px;
  height: 103px;
  margin: auto;
}

.floor-8 .rc-bricks__item h2 {
  font-size: 18px;
  font-family: SourceHanSansCN-Medium, SourceHanSansCN;
  font-weight: 500;
  color: #202125;
  line-height: 26px;
}


.floor-9 {
  padding-bottom: 60px;
  background-color: #F1F7FF;
}

.floor-9 .box {
  display: flex;
  flex-flow: row nowrap;
  height: 115px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 25px;
}

.floor-9 .box .item {
  flex: 1 1 auto;
  box-sizing: border-box;
  height: 100%;
  padding: 32px 30px 0;
  display: flex;
  flex-flow: row nowrap;
  background: #FFFFFF;
  box-shadow: 0px 8px 40px 0px rgba(194, 215, 241, 0.2);
  border-radius: 8px;
}

.floor-9 .box .item:first-child {
  border-radius: 8px 0 0 8px;
}

.floor-9 .box .item:last-child {
  border-radius: 0 8px 8px 0;
}

.floor-9 .box .item img {
  width: 48px;
  height: 48px;
  margin-right: 20px;
}

.floor-9 .box .item h2 {
  margin: 0;
  height: 48px;
  font-size: 16px;
  font-weight: 400;
  color: #202125;
  line-height: 24px;
}

.floor-9 .rc-bricks {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
}

.floor-9 .rc-bricks__item {
  box-sizing: border-box;
  width: 372px;
  padding: 15px;
  background: #FFFFFF;
  box-shadow: 0px 8px 40px 0px rgba(194, 215, 241, 0.2);
  border-radius: 8px;
}

.floor-9 .rc-bricks__item img {
  display: block;
  width: 100%;
  height: 180px;
}

.floor-9 .rc-bricks__item h2 {
  text-align: center;
  font-size: 20px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  color: #202125;
  line-height: 30px;
}

.floor-9 .rc-bricks__item ul {
  padding-left: 20px;
  margin-bottom: 20px;
}

.floor-9 .rc-bricks__item li {
  position: relative;
  padding-left: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #666;
  line-height: 22px;
}

.floor-9 .rc-bricks__item li::before {
  position: absolute;
  content: "";
  top: 10px;
  left: 0;
  border-radius: 50%;
  width: 4px;
  height: 4px;
  background: #007AF6;
}

.floor-9 .rc-bricks__item .action {
  text-align: center;
}

.floor-9 .rc-bricks__item .button {
  width: 108px;
  padding: 0;
}


.floor-10 {
  background-color: #F8FBFE;
  padding-bottom: 110px;
}

.floor-10 .rc-bricks {
  width: 1200px;
  margin: 0 auto;
  position: relative;
  height: 540px;
}

.floor-10 .rc-bricks__item {
  position: absolute;
  box-sizing: border-box;
  width: 380px;
  height: 240px;
  padding: 20px;
  background: #FFFFFF;
  box-shadow: 0px 0px 16px 0px rgba(144, 160, 179, 0.1);
  border-radius: 4px;
  border: 1px solid transparent;
  transition: all .2s;
  overflow: hidden;
  z-index: 100;
}
.floor-10 .rc-bricks__item:nth-child(1) {
  left: 0;
  top: 0;
  z-index: 110;
}
.floor-10 .rc-bricks__item:nth-child(2) {
  left: 410px;
  top: 0;
  z-index: 110;
}
.floor-10 .rc-bricks__item:nth-child(3) {
  left: 820px;
  top: 0;
  z-index: 110;
}
.floor-10 .rc-bricks__item:nth-child(4) {
  left: 0;
  top: 270px;
}
.floor-10 .rc-bricks__item:nth-child(5) {
  left: 410px;
  top: 270px;
}
.floor-10 .rc-bricks__item:nth-child(6) {
  left: 820px;
  top: 270px;
}
.floor-10 .rc-bricks__item:hover {
  box-shadow: 0px 4px 24px 0px rgba(124, 142, 166, 0.31);
  border: 1px solid #1689FF;
  transform: translate(0, -14px);
  height: auto;
}

.floor-10 .rc-bricks__item:hover .contact-card__statistics{
  height: auto;
}


.floor-11 {
  height: 104px;
  background-color: rgb(51, 118, 241);
  background-repeat: no-repeat;
  background-position: center center;
}

.floor-11 .floor-title {
  padding: 30px 0 0;
  color: #fff;
}

.floor-11 .button {
  width: 166px;
  padding: 0;
  background: #FFFFFF;
  font-size: 20px;
  font-family: SourceHanSansCN-Medium, SourceHanSansCN;
  font-weight: 500;
  color: #007AF6;
}
