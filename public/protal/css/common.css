html,
body {
  margin: 0;
  padding: 0;
}

ul,
li {
  margin: 0;
  padding: 0;
}

li {
  list-style: none;
}

body {
  font-family: SourceHanSansCN-Regular, SourceHanSansCN, sans-serif;
}


.site-header {
  position: fixed;
  z-index: 2;
  width: 100%;
  top: 0;
  min-width: 1320px;
  height: 60px;
  margin: auto;
  background-color: #fff;
}

.site-header-logo {
  float: left;
  width: 219px;
  height: 60px;
  margin: 0;
  padding: 0;
  position: relative;
}

.site-header-logo img {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 38px;
  margin: auto;
}

.site-header-nav {
  float: left;
  margin-left: 60px;
}

.site-header-nav__item {
  position: relative;
  float: left;
  height: 60px;
}

.site-header-nav__item .site-header-nav__link {
  display: inline-block;
  padding: 0 15px;
  line-height: 60px;
  font-size: 16px;
  font-weight: 400;
  color: #282D3B;
  text-decoration: none;
  white-space: nowrap;
}

.site-header-nav__item--active .site-header-nav__link,
.site-header-nav__item--hover .site-header-nav__link,
.site-header-nav__item:hover .site-header-nav__link {
  color: #1689FF;
}

.site-header-nav__item .site-header-nav__link.with-dropdown::after {
  display: inline-block;
  width: 6px;
  height: 6px;
  margin: 0 0 0 3px;
  top: -4px;
  position: relative;
  transform: rotate(135deg);
  content: "";
  border-top: 1px solid #282D3B;
  border-right: 1px solid #282D3B;
  transition: all .2s;
}

.site-header-nav__item--active .site-header-nav__link.with-dropdown::after {
  border-color: #1689FF;
}

.site-header-nav__item--hover .site-header-nav__link.with-dropdown::after {
  top: 0;
  transform: rotate(-45deg);
  border-color: #1689FF;
}

.site-header-nav__submenu {
  display: none;
  position: absolute;
  top: 50px;
  left: 0;
  right: auto;
  left: auto;
  padding: 10px 18px;
  background: #FFFFFF;
  box-shadow: 0px 2px 16px 0px rgba(132, 146, 164, 0.2);
  border-radius: 8px;
  border-image: linear-gradient(138deg, rgba(253, 254, 255, 0.6), rgba(250, 252, 255, 0.45)) 2 2;
}

.site-header-nav__submenu li {
  height: auto;
  font-size: 14px;
  font-weight: 400;
  color: #707784;
  line-height: 28px;
}

.site-header-nav__submenu a {
  height: auto;
  line-height: 28px;
  white-space: nowrap;
  color: #707784;
  text-decoration: none;
}

.site-header-nav__submenu a:hover {
  color: #1689FF;
}

.site-header-contact {
  float: left;
  margin-top: 11px;
  margin-left: 30px;
  white-space: nowrap;
}

.site-header-contact>.number >img {
  vertical-align: middle;
  margin: 0 4px 0 0;
  line-height: 24px;
}

.site-header-contact>.number {
  display: block;
  width: 142px;
  height: 20px;
  font-family: Gotham, Gotham;
  font-weight: normal;
  font-size: 14px;
  color: #202125;
  line-height: 20px;
  font-style: italic;
}
.site-header-contact>.number>.number-title {
  font-size: 12px;
  color: #707784;
  font-style: normal;
}
.site-header-contact>.number>span {
  margin: 0 4px;
}

.site-header-link {
  float: right;
  margin-top: 16px;
  position: relative;
  width: 112px;
}

.site-header-link .drop-button {
  box-sizing: border-box;
  display: inline-block;
  width: 100%;
  height: 28px;
  margin: 0;
  padding: 0;
  line-height: 26px;
  font-weight: 400;
  font-size: 14px;
  border: 1px solid #707784;
  background: #fff;
  color: #202125;
  border-radius: 2px;
  text-decoration: none;
  text-align: center;
  transition: all .16s;
  cursor: pointer;
}
.site-header-link .drop-button::after {
  display: inline-block;
  position: relative;
  top: -3px;
  content: "";
  transform: rotate(135deg);
  width: 6px;
  height: 6px;
  margin-left: 5px;
  border-top: 1px solid #202125;
  border-right: 1px solid #202125;
  transition: all .16s;
}
.site-header-link .drop-button:hover {
  border-color: #52A7FF;
  color: #52A7FF;
}
.site-header-link .drop-button:hover::after {
  border-top-color: #52A7FF;
  border-right-color: #52A7FF;
}
.site-header-link .drop-button:active {
  border-color: #1689FF;
  color: #1689FF;
}
.site-header-link .drop-button:active::after {
  border-top-color: #1689FF;
  border-right-color: #1689FF;
}
.site-header-link__submenu {
  box-sizing: border-box;
  display: none;
  position: absolute;
  top: 28px;
  left: 0;
  right: auto;
  left: auto;
  padding: 10px 18px;
  background: #FFFFFF;
  box-shadow: 0px 2px 16px 0px rgba(132, 146, 164, 0.2);
  border-radius: 8px;
  border-image: linear-gradient(138deg, rgba(253, 254, 255, 0.6), rgba(250, 252, 255, 0.45)) 2 2;
}
.site-header-link__submenu li {
  height: auto;
  font-size: 14px;
  font-weight: 400;
  color: #707784;
  line-height: 28px;
}
.site-header-link__submenu a {
  height: auto;
  line-height: 28px;
  white-space: nowrap;
  color: #707784;
  text-decoration: none;
}
.site-header-link__submenu a:hover {
  color: #1689FF;
}


.site-footer {
  position: relative;
  z-index: 1;
  min-width: 1320px;
  height: 330px;
  margin: auto;
  background-color: #131F45;
}

.site-footer-main {
  box-sizing: border-box;
  height: calc(100% - 60px);
  padding-top: 40px;
  background-color: #0B183F;
}

.site-footer-main .col:first-child {
  float: left;
}

.site-footer-main .col:last-child {
  float: right;
}

.site-footer__sns {
  box-sizing: border-box;
  width: 350px;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 19px;
  border-bottom: 1px solid rgba(255, 255, 255, .3);
}
.site-footer__sns__item {
  display: inline-block;
  position: relative;
}
.site-footer__sns__item:last-child {
  margin: 0;
}
.site-footer__sns__item .trigger img {
  height: 30px;
}
.site-footer__sns__item .popup {
  display: none;
  box-sizing: border-box;
  position: absolute;
  width: 300px;
  bottom: 48px;
  left: 50%;
  margin-left: -150px;
  padding: 8px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0px 4px 24px 0px rgba(144,184,236,0.2);
  border-image: linear-gradient(138deg, rgba(253, 254, 255, 0.6), rgba(250, 252, 255, 0.45)) 2 2;
  backdrop-filter: blur(7px);
  text-align: left;
  font-size: 14px;
  font-weight: 400;
  color: #41464F;
}
.J_call-contact {
  margin-top: 11px;
  margin-left: 50px;
  white-space: nowrap;
  width: 200px !important;
  height: 142px !important;
}

.J_call-contact>.number>.number-title>img {
  vertical-align: middle;
  margin: 0 4px 0 0;
  line-height: 24px;
}
.J_call-contact>.number:not(:last-child) {
  margin-bottom: 16px;
}

.J_call-contact>.number {
  display: block;
}
.J_call-contact>.number>.number-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #707784;
  line-height: 20px;
  margin-bottom: 4px;
  text-align: left;
  font-style: normal;
}
.J_call-contact>.number>.number-content {
  font-family: SourceHanSansCN, SourceHanSansCN;
  font-weight: bold;
  font-size: 18px;
  color: #2B3440;
  line-height: 27px;
  text-align: justify;
  font-style: normal;
}
.J_call-contact>.number>span {
  margin: 0 4px;
}

.site-footer__sns__item:nth-child(1) .popup {
  margin-left: -70px;
}
.site-footer__sns__item:nth-child(1) .popup::after {
  left: -150px;
}
.site-footer__sns__item:hover .popup {
  display: flex;
  justify-content: center;
  align-items: center;
}
.site-footer__sns__item .popup::after {
  position: absolute;
  content: "";
  width: 0;
  height: 0;
  border: 8px dashed transparent;
  border-top: 8px solid #fff;
  border-bottom: 0;
  top: auto;
  right: 0;
  bottom: -8px;
  left: 0;
  margin: auto;
}
.site-footer__sns__item .popup img {
  display: block;
  margin: auto;
  width: 100%;
  background-color: rgba(0,0,0,.1);
}


.site-footer__infos {
  height: 60px;
}

.site-footer__links {
  width: 350px;
}

.site-footer__links ul {
  display: flex;
  justify-content: space-between;
}

.site-footer__links li {
  flex: 0 0 auto;
}

.site-footer__links a {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  color: #FFFFFF;
  text-decoration: none;
}

.site-footer__contact span {
  height: 14px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, .6);
  line-height: 24px;
}


.site-footer__contact strong.phone {
  font-size: 20px;
  font-family: Gotham-Medium, Gotham;
  font-weight: 500;
  color: rgba(255, 255, 255, .8);
  line-height: 24px;
}

.site-footer__contact strong.address {
  font-size: 14px;
  font-family: Gotham-Medium, Gotham;
  font-weight: 400;
  color: rgba(255, 255, 255, .8);
  line-height: 24px;
}

.site-footer__friendly-link {
  display: flex;
  margin-bottom: 30px;
}

.site-footer__friendly-link>span {
  height: 14px;
  margin-right: 20px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, .6);
  line-height: 21px;
}

.site-footer__friendly-link>ul {
  display: flex;
  justify-content: space-between;
  flex-flow: row nowrap;
  width: 255px;
}

.site-footer__friendly-link a {
  font-size: 14px;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 21px;
  text-decoration: none;
}

.site-footer__qrcodes {}

.site-footer__qrcodes ul {
  float: right;
  display: flex;
}

.site-footer__qrcodes li {
  width: 100px;
  margin-left: 30px;
  text-align: center;
}

.site-footer__qrcodes img {
  display: block;
  margin-bottom: 10px;
  background: #000;
}

.site-footer__qrcodes span {
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  color: rgba(255, 255, 255, .6);
}

.site-footer-bottom {
  text-align: center;
  line-height: 60px;
}

.site-footer-bottom span,
.site-footer-bottom a {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(255, 255, 255, .6);
  line-height: 17px;
  text-decoration: none;
  margin: 0 20px;
}

.site-body {
  position: relative;
  z-index: 0;
  min-width: 1320px;
  height: auto;
  margin: auto;
  background-color: #fff;
  padding-top: 60px;
}

.site-float {
  position: fixed;
  z-index: 9;
  top: 0;
  bottom: 0;
  left: 50%;
  margin: auto auto auto 620px;
  width: 50px;
  height: 220px;
}

.site-float__group {
  width: 50px;
  border-radius: 25px;
  box-shadow: 0px 4px 24px 0px rgba(144, 184, 236, 0.2);
  backdrop-filter: blur(7px);
  margin-bottom: 10px;
}

.site-float__button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 62px;
  margin: auto;
  text-align: center;
  text-decoration: none;
  border: none;
  background: #F8FBFD;
  cursor: pointer;
}
.site-float__button.J_backtop{
  height: 50px;
}
.site-float__button:first-of-type {
  border-top-left-radius: 25px;
  border-top-right-radius: 25px;
}
.site-float__button:last-of-type {
  border-bottom-left-radius: 25px;
  border-bottom-right-radius: 25px;
}
.site-float__button:hover {
  background-color: #EDF6FF;
}

.site-float__button::after {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  top: auto;
  margin: 0 auto;
  content: "";
  width: 28px;
  height: 1px;
  background-color: #EAECF1;
}

.site-float__button:last-of-type::after {
  display: none;
}
.site-float__button .trigger svg {
  display: block;
  margin: auto;
  width: 22px;
  height: 22px;
  fill: #202125;
  stroke: #202125;
}
.site-float__button:hover .trigger svg {
  fill: #1689FF;
  stroke: #1689FF;
}
.site-float__button .trigger span {
  display: block;
  text-align: center;
  font-size: 12px;
  font-weight: 400;
  color: #202125;
  line-height: 18px;
}
.site-float__button:hover .trigger span {
  color: #1689FF;
}
.site-float__button .popup {
  display: none;
  box-sizing: border-box;
  position: absolute;
  top: 0;
  right: 60px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0px 4px 24px 0px rgba(144,184,236,0.2);
  border-image: linear-gradient(138deg, rgba(253, 254, 255, 0.6), rgba(250, 252, 255, 0.45)) 2 2;
  backdrop-filter: blur(7px);
  text-align: left;
  font-size: 14px;
  font-weight: 400;
  color: #41464F;
}
.site-float__button:hover .popup {
  display: block;
}
.site-float__button .popup::after {
  position: absolute;
  content: "";
  width: 0;
  height: 0;
  border: 8px dashed transparent;
  border-left: 8px solid #fff;
  border-right: 0;
  top: 0;
  right: -8px;
  bottom: 0;
  left: auto;
  margin: auto;
}
.site-float__button:nth-child(1) .popup {
  top: -20px;
  width: 230px;
  height: 78px;
  padding: 13px 0 0 25px;
  flex-flow: column nowrap;
  justify-content: center;
  line-height: 25px;
}
.site-float__button:nth-child(2) .popup {
  top: -80px;
  width: 300px;
  padding: 20px 0;
  text-align: center;
}
.site-float__button:nth-child(2) img {
  margin-top: 5px;
  width: 148px;
  height: 148px;
}
.site-float__button:nth-child(3) .popup {
  top: -80px;
  width: 300px;
  padding: 20px 0;
  text-align: center;
}
.site-float__button:nth-child(3) img {
  margin-top: 5px;
  width: 148px;
  height: 148px;
}

/* 公共 dialog */
.site-dialog {
  display: none;
  position: fixed;
  z-index: 2;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.site-dialog__mask {
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0,0,0,.3);
  z-index: 0;
}
.site-dialog__layout {
  box-sizing: border-box;
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  display: flex;
  flex-flow: column nowrap;
  background: #fff;
  box-shadow: 0px 4px 24px 0px rgba(144,184,236,0.2);
  border-image: linear-gradient(138deg, rgba(253, 254, 255, 0.6), rgba(250, 252, 255, 0.45)) 1 1;
}
.site-dialog__title {
  text-align: center;
  margin: 0;
  padding: 0;
  height: 68px;
  line-height: 68px;
  font-size: 18px;
  white-space: nowrap;
  font-weight: bold;
  color: #202125;
}
.site-dialog__close {
  position: absolute;
  z-index: 2;
  top: 4px;
  right: 4px;
  width: 30px;
  height: 30px;
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
}
.site-dialog__close::before,
.site-dialog__close::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 2px;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  background-color: #C6C9CF;
}
.site-dialog__close::before {
  transform: rotate(45deg);
}
.site-dialog__close::after {
  transform: rotate(-45deg);
}
.site-dialog__close:hover::before,
.site-dialog__close:hover::after {
  background-color: #888;
}
.site-dialog__footer {
  box-sizing: border-box;
  text-align: center;
  padding: 20px 0;
  height: 74px;
}
.site-dialog__confirm,
.site-dialog__cancel {
  margin: 0 10px;
  width: 100px;
}
.site-dialog__slot {
  box-sizing: border-box;
  position: relative;
  z-index: 1;
  height: 100%;
  overflow: auto;
}
.with-title > .site-dialog__slot {
  height: calc(100% - 68px);
}
.with-footer > .site-dialog__slot {
  height: calc(100% - 74px);
}
.with-footer.with-title > .site-dialog__slot {
  height: calc(100% - 142px);
}


/* 联系信息弹窗 */
.site-contact-dialog {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  width: 480px;
  height: 330px;
  background: #fff;
  box-shadow: 0px 4px 24px 0px rgba(144,184,236,0.2);
  border-image: linear-gradient(138deg, rgba(253, 254, 255, 0.6), rgba(250, 252, 255, 0.45)) 1 1;
}
.site-contact-dialog .close {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 30px;
  height: 30px;
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
}
.site-contact-dialog .close::before,
.site-contact-dialog .close::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 2px;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  background-color: #C6C9CF;
}
.site-contact-dialog .close::before {
  transform: rotate(45deg);
}
.site-contact-dialog .close::after {
  transform: rotate(-45deg);
}
.site-contact-dialog .close:hover::before,
.site-contact-dialog .close:hover::after {
  background-color: #888;
}
.site-contact-dialog .title-1 {
  padding: 20px 0 12px;
  text-align: center;
  line-height: 18px;
  height: 18px;
  font-size: 16px;
  font-weight: 700;
  color: #202125;
}
.site-contact-dialog .qrcode {
  display: flex;
  flex-flow: row nowrap;
  margin: 0 auto;
  justify-content: center;
}
.site-contact-dialog .qrcode .item {
  box-sizing: border-box;
  width: 112px;
  height: 132px;
  margin: 0 30px;
  padding: 6px;
  border: 1px solid #E6E6E6;
  text-align: center;
}
.site-contact-dialog .qrcode .item img {
  display: block;
  width: 100px;
  height: 100px;
}
.site-contact-dialog .qrcode .item span {
  line-height: 14px;
  height: 14px;
  font-size: 12px;
  font-weight: 400;
  color: #41464F;
}
.site-contact-dialog .title-2 {
  text-align: center;
  height: 14px;
  font-family: SourceHanSansCN-Regular, SourceHanSansCN;
  font-weight: 400;
  color: #707784;
  line-height: 18px;
}
.site-contact-dialog .title-2::before,
.site-contact-dialog .title-2::after {
  content: "";
  display: inline-block;
  width: 48px;
  height: 1px;
  margin: 0 5px;
  background-color: #E6E6E6;
  vertical-align: middle;
}
.site-contact-dialog .title-2 span {
  vertical-align: middle;
  height: 14px;
  line-height: 14px;
  font-size: 12px;
}
.site-contact-dialog .phone {
  text-align: center;
}
.site-contact-dialog .phone span {
  display: inline-block;
  height: 34px;
  line-height: 34px;
  margin: 0 auto;
  padding: 0 20px;
  border-radius: 18px;
  background: #1689FF;
  font-size: 18px;
  font-weight: 700;
  color: #FFFFFF;
}

/* 公告弹窗 */
.site-notification {
  padding: 0 35px;
  font-size: 14px;
  font-weight: 400;
  color: #202125;
  line-height: 24px;
}
.site-notification p {
  margin: 0;
  padding: 0;
}
.site-notification a {
  color: #1689FF;
}

.layout {
  margin: auto;
}

.layout::after {
  display: table;
  content: "";
  clear: both;
}

.layout1200 {
  width: 1200px;
}


.more-link {
  display: inline-block;
  position: relative;
  height: 16px;
  line-height: 16px;
  font-size: 14px;
  padding-right: 8px;
  font-weight: 400;
  color: #1689FF;
  text-decoration: none;
}

.more-link::after {
  content: "";
  height: 8px;
  width: 8px;
  position: absolute;
  right: 0;
  top: -1px;
  bottom: 0;
  left: auto;
  margin: auto;
  transform: rotate(45deg);
  border-top: 1px solid #1689FF;
  border-right: 1px solid #1689FF;
}



.flex-h {
  display: flex;
  flex-direction: row;
}

.flex-v {
  display: flex;
  flex-direction: column;
}

.flex-fixed {
  flex: 0 0 auto;
}

.flex-fluid {
  flex: 1 1 100%;
}


.tag {
  display: inline-block;
  box-sizing: border-box;
  height: 25px;
  line-height: 23px;
  padding: 0 8px;
  border-radius: 2px;
  border: 1px solid #C1C7D4;
  font-size: 12px;
  font-weight: 400;
  color: #A1A5AD;
}

.t-center {
  text-align: center;
}


.contact-card {
  position: relative;
}

.contact-card__info {
  position: relative;
  width: 100%;
  height: 66px;
  margin-bottom: 36px;
}

.contact-card__avatar {
  height: 66px;
  padding-left: 82px;
}

.contact-card__pic {
  position: absolute;
  left: 0;
  top: 0;
  width: 66px;
  height: 66px;
  border-radius: 50%;
}

.contact-card__name {
  display: block;
  height: 38px;
  line-height: 38px;
  font-size: 24px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: 600;
  color: #202125;
  margin-bottom: 8px;
}

.contact-card__title {
  height: 24px;
  line-height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #707784;
}

.contact-card__action {
  position: absolute;
  top: 0;
  right: 0;
}

.contact-card__statistics {
  box-sizing: border-box;
  height: 80px;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
.contact-card__statistics>li {
  box-sizing: border-box;
  position: relative;
  margin: 0;
  padding: 0 4px;
  list-style: none;
  display: flex;
  flex-flow: row nowrap;
}
.contact-card__statistics>li>strong {
  
  font-size: 14px;
  font-weight: 400;
  color: #A1A5AD;
  line-height: 21px;
  flex: 1 1 100%;
}

.contact-card__statistics>li>span {
  flex: 0 0 75px;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  line-height: 21px;
}

.button {
  box-sizing: border-box;
  display: inline-block;
  height: 34px;
  line-height: 32px;
  font-size: 14px;
  border-radius: 3px;
  text-align: center;
  margin: auto;
  padding-top: 0;
  padding-bottom: 0;
  background: transparent;
  border: 1px solid #1689FF;
  font-family: SourceHanSansCN-Regular, SourceHanSansCN sans-serif;
  font-weight: 400;
  color: #1689FF;
  text-decoration: none;
  transition: all .16s;
  cursor: pointer;
}

.button:hover {
  background: linear-gradient(218deg, #52A7FF 0%, #1689FF 100%);
  color: #fff;
}

.button:active {
  background: linear-gradient(218deg, #1689FF 0%, #1689FF 100%);
  color: #fff;
}

.button.primary {
  color: #fff;
  background: linear-gradient(218deg, #52A7FF 0%, #1689FF 100%);
}

.button.primary:hover {
  color: #fff;
  background: linear-gradient(218deg, #52A7FF 0%, #52A7FF 100%);
}

.button.primary:active {
  color: #fff;
  background: linear-gradient(218deg, #1689FF 0%, #1689FF 100%);
}

.button.invert {
  border-color: #fff;
  color: #fff;
  background: none;
}

.button.invert:hover {
  border-color: #fff;
  color: #fff;
  box-shadow: inset 0 0 8px rgba(255, 255, 255, .3);
}

.button.invert:active {
  border-color: #fff;
  color: #fff;
  box-shadow: inset 0 0 3px rgba(255, 255, 255, .5), inset 0 0 8px rgba(255, 255, 255, .5);
}

.button {
  padding-left: 14px;
  padding-right: 14px;
}

.button.size-s {
  height: 30px;
  line-height: 28px;
  font-size: 14px;
  padding-left: 13px;
  padding-right: 13px;
}

.button.size-l {
  height: 42px;
  line-height: 40px;
  font-size: 16px;
  padding-left: 32px;
  padding-right: 32px;
}

.button.size-xl {
  height: 44px;
  line-height: 42px;
  font-size: 18px;
  padding-left: 38px;
  padding-right: 38px;
  border-radius: 5px;
}

.button.with-counseling-icon::before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 18px;
  margin: 0 4px 0 0;
  padding: 0;
  vertical-align: middle;
  background-image: url(/protal/img/button-icon-contact.png);
  background-size: 16px 18px;
  background-repeat: no-repeat;
}

.button.with-counseling-icon:hover::before {
  background-image: url(/protal/img/button-icon-contact-invert.png);
}

.button.invert.with-counseling-icon::before,
.button.invert.with-counseling-icon:hover::before,
.button.invert.with-counseling-icon:active::before {
  background-image: url(/protal/img/button-icon-contact-invert.png);
}

.button.size-s.with-counseling-icon::before {
  width: 16px;
  height: 18px;
}

.button.size-l.with-counseling-icon::before {
  width: 18px;
  height: 20px;
  background-size: 18px 20px;
}

.button.size-xl.with-counseling-icon::before {
  width: 18px;
  height: 20px;
  background-size: 18px 20px;
  position: relative;
  top: -2px;
}


.button img {
  vertical-align: middle;
}

.modal-mask{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 2;
}
.modal-body {
  position: relative;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: #FFFFFF;
  width: 700px;
  height: 480px;
}

.modal-header {
  position: relative;
}

.modal-header h2 {
  font-size: 24px;
  font-weight: 500;
  color: #202125;
  line-height: 36px;
  padding-top: 42px;
  text-align: center;
}

.modal-close {
  cursor: pointer;
  position: absolute;
  right: 20px;
  top: 20px
}

.modal-content {
  
}
