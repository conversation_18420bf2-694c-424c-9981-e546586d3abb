/*
 $ 轮播广告 $
 */
#carousel {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 520px;
}

#carousel .swiper-slide {
  position: relative;
  background-repeat: no-repeat;
  background-position: center center;
}

#carousel .swiper-pagination {
  box-sizing: border-box;
  position: absolute;
  left: 50%;
  bottom: 64px;
  right: auto;
  margin-left: -600px;
}

#carousel .swiper-pagination-switch {
  display: inline-block;
  width: 32px;
  height: 6px;
  margin-right: 6px;
  background: #FFFFFF;
  opacity: 0.6;
}

#carousel .swiper-pagination-switch.swiper-active-switch {
  background: #007AF6;
}

#carousel .swiper-slide button.contact {
  position: absolute;
  background: transparent;
  border: 0 none;
  width: 150px;
  height: 42px;
  left: 50%;
  top: 50%;
  margin: 14px 0 0 -600px;
  cursor: pointer;
}


.floor-2 {
  position: relative;
  z-index: 1;
  height: 114px;
  margin-bottom: -30px;
  background-color: #F5F9FD;
}

.floor-2 .rc-bricks {
  position: relative;
  top: -30px;
  display: flex;
  justify-content: space-between;
  flex-flow: row nowrap;
}

.floor-2 .rc-bricks__item {
  box-sizing: border-box;
  flex: 0 0 278px;
  position: relative;
  width: 278px;
  height: 114px;
  padding: 0 20px 0 50px;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0px 8px 10px 0px rgba(233, 236, 241, 0.25);
  border-radius: 8px;
  backdrop-filter: blur(7px);
}

.floor-2 .rc-bricks__item>img {
  position: absolute;
  top: 20px;
  right: auto;
  bottom: auto;
  left: 20px;
  width: 24px;
  height: 24px;
}

.floor-2 .rc-bricks__item>h2 {
  width: 100%;
  height: 20px;
  font-size: 18px;
  font-family: SourceHanSansCN-Medium, SourceHanSansCN;
  font-weight: 500;
  color: #202125;
  line-height: 30px;
}

.floor-2 .rc-bricks__item>p {
  width: 100%;
  height: 44px;
  font-size: 14px;
  font-family: SourceHanSansCN-Normal, SourceHanSansCN;
  font-weight: 400;
  color: #707784;
  line-height: 22px;
}

.floor-title {
  margin-top: 0;
  text-align: center;
  height: 36px;
  font-size: 36px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  color: #202125;
  line-height: 54px;
}

.floor-subtitle {
  text-align: center;
  height: 18px;
  font-size: 18px;
  margin-top: -10px;
  font-family: SourceHanSansCN-Normal, SourceHanSansCN;
  font-weight: 400;
  color: #A1A5AD;
  line-height: 27px;
}

.floor-3 {
  height: 685px;
  padding: 30px;
  background-repeat: no-repeat;
  background-color: #F5F9FD;
  background-size: 1202px 546px;
  background-position: center bottom;
  margin-top: 30px;
}

.floor-3 .content {
  position: relative;
  width: 100%;
  height: 546px;
}

.floor-3 .button {
  width: 94px;
  margin: 0 10px;
  padding: 0;
}

.floor-3 .package-1,
.floor-3 .package-2,
.floor-3 .package-3,
.floor-3 .package-4,
.floor-3 .package-5 {
  box-sizing: border-box;
  position: absolute;
  left: 50%;
  width: 298px;
  height: 185px;
}

.floor-3 .package-1 header,
.floor-3 .package-2 header,
.floor-3 .package-3 header,
.floor-3 .package-4 header {
  margin: 0 5px 0 90px;
  padding: 22px 0 10px 20px;
  background-color: rgb(239,246,254);
}
.floor-3 .package-1 h2,
.floor-3 .package-2 h2,
.floor-3 .package-3 h2,
.floor-3 .package-4 h2 {
  margin: 0;
  padding: 0;
  height: 20px;
  line-height: 20px;
  font-size: 18px;
  font-family: SourceHanSansCN-Medium, SourceHanSansCN;
  font-weight: 700;
  color: #202125;
}
.floor-3 .package-1 ul,
.floor-3 .package-2 ul,
.floor-3 .package-3 ul,
.floor-3 .package-4 ul {
  background-color: rgb(239,246,254);
  margin: 0 5px 16px 90px;
  padding: 0 0 0 20px;
}
.floor-3 .package-1 li,
.floor-3 .package-2 li,
.floor-3 .package-3 li,
.floor-3 .package-4 li {
  position: relative;
  list-style: none;
  height: 25px;
  line-height: 25px;
  padding-left: 14px;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
}
.floor-3 .package-1 li::before,
.floor-3 .package-2 li::before,
.floor-3 .package-3 li::before,
.floor-3 .package-4 li::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  width: 5px;
  height: 5px;
  background-color: #CDD9E9;
  border-radius: 50%;
}
.floor-3 .package-1 li strong,
.floor-3 .package-2 li strong,
.floor-3 .package-3 li strong,
.floor-3 .package-4 li strong {
  font-weight: 500;
  color: #1689FF;
}
.floor-3 .package-1 .actions,
.floor-3 .package-2 .actions,
.floor-3 .package-3 .actions,
.floor-3 .package-4 .actions {
  text-align: center;
}

.floor-3 .package-1 {
  top: 94px;
  margin-left: -600px;
}

.floor-3 .package-2 {
  top: 94px;
  margin-left: 302px;
}

.floor-3 .package-3 {
  top: 338px;
  margin-left: -600px;
}

.floor-3 .package-4 {
  top: 338px;
  margin-left: 302px;
}

.floor-3 .package-5 {
  top: 374px;
  margin-left: -205px;
  overflow: hidden;
  width: 410px;
  height: 208px;
  border-radius: 12px;
  box-shadow: 0px 10px 30px 0px rgba(119,163,220,0.1);
}
.floor-3 .package-5 header {
  width: 410px;
  height: 44px;
  line-height: 44px;
  background: linear-gradient(158deg, #83BEF8 0%, #1689FF 100%);
  color: #fff;
}
.floor-3 .package-5 header h2 {
  margin: 0;
  padding: 0;
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  color: #FFFFFF;
}
.floor-3 .package-5 .body {
  width: 100%;
  height: 164px;
  background-color: #fff;
}
.floor-3 .package-5 ul {
  display: flex;
  flex-flow: row wrap;
  padding: 14px 0 16px 16px;
}
.floor-3 .package-5 li {
  box-sizing: border-box;
  position: relative;
  list-style: none;
  padding-left: 14px;
  height: 27px;
  width: 50%;
  line-height: 27px;
  font-size: 14px;
  font-weight: 400;
  color: #202125;
}
.floor-3 .package-5 li:nth-child(odd) {
  flex: 0 0 250px;
}
.floor-3 .package-5 li:nth-child(even) {
  flex: 0 0 140px;
}
.floor-3 .package-5 li::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  width: 5px;
  height: 5px;
  background-color: #1689FF;
  border-radius: 50%;
}
.floor-3 .package-5 .actions {
  text-align: center;
}


.floor-4 {
  height: 812px;
  background-repeat: no-repeat;
  background-color: #e9f2fc;
  background-position: center center;
}

.floor-4 .col-1 {
  float: left;
}

.floor-4 .col-2 {
  float: right;
}

.floor-4 .box-1,
.floor-4 .box-2 {
  box-sizing: border-box;
  width: 274px;
  padding: 10px 30px;
  background: rgba(165, 201, 255, 0.3);
  border-radius: 4px;
}

.floor-4 .box-1 {
  height: 372px;
  margin-bottom: 20px;
}

.floor-4 .box-2 {
  height: 176px;
}

.floor-4 .box-1 h2,
.floor-4 .box-2 h2 {
  height: 20px;
  font-size: 20px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  color: #202125;
  line-height: 30px;
}

.floor-4 .box-1 li,
.floor-4 .box-2 li {
  height: 16px;
  font-size: 16px;
  font-weight: 400;
  color: #666666;
  line-height: 24px;
  margin-bottom: 15px;
}

.floor-4 .rc-bricks {
  display: flex;
  flex-flow: row wrap;
  width: 900px;
  justify-content: space-between;
}

.floor-4 .rc-bricks__item {
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 210px;
  height: 176px;
  margin-bottom: 20px;
  text-align: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  transition: all .2s;
}

.floor-4 .rc-bricks__item:hover {
  transform: translate(0, -14px);
  box-shadow: 0px 8px 20px 0px rgba(144, 184, 236, 0.12);
}

.floor-4 .rc-bricks__item__content {
  display: inline-block;
}

.floor-4 .rc-bricks__item__content>img {
  width: 50px;
  height: 52px;
}

.floor-4 .rc-bricks__item__content>h3 {
  margin: 10px auto;
  padding: 0;
  text-align: center;
  height: 24px;
  line-height: 24px;
  font-size: 16px;
  font-weight: 400;
  color: #202125;
}

.floor-4 .rc-bricks__item__content>.button {
  display: none;
  line-height: 19px;
}
.floor-4 .rc-bricks__item__content>a.button {
  display: none;
  line-height: 32px;
}

.floor-4 .rc-bricks__item:hover>.rc-bricks__item__content>.button {
  display: inline-block;
}

.floor-4 .rc-bricks__item__content .button>img {
  width: 16px;
  height: 19px;
  vertical-align: middle;
}



.floor-5 {
  background-color: #F8FBFE;
}

.floor-5 .rc-bricks {
  width: 1200px;
  margin: 0 auto;
  position: relative;
  height: 540px;
}

.floor-5 .rc-bricks__item {
  position: absolute;
  box-sizing: border-box;
  width: 380px;
  height: 240px;
  padding: 20px;
  background: #FFFFFF;
  box-shadow: 0px 0px 16px 0px rgba(144, 160, 179, 0.1);
  border-radius: 4px;
  border: 1px solid transparent;
  transition: all .2s;
  overflow: hidden;
  z-index: 100;
}
.floor-5 .rc-bricks__item:nth-child(1) {
  left: 0;
  top: 0;
  z-index: 110;
}
.floor-5 .rc-bricks__item:nth-child(2) {
  left: 410px;
  top: 0;
  z-index: 110;
}
.floor-5 .rc-bricks__item:nth-child(3) {
  left: 820px;
  top: 0;
  z-index: 110;
}
.floor-5 .rc-bricks__item:nth-child(4) {
  left: 0;
  top: 270px;
}
.floor-5 .rc-bricks__item:nth-child(5) {
  left: 410px;
  top: 270px;
}
.floor-5 .rc-bricks__item:nth-child(6) {
  left: 820px;
  top: 270px;
}
.floor-5 .rc-bricks__item:hover {
  box-shadow: 0px 4px 24px 0px rgba(124, 142, 166, 0.31);
  border: 1px solid #1689FF;
  transform: translate(0, -14px);
  height: auto;
}
.floor-5 .rc-bricks__item:hover .contact-card__statistics{
  height: auto;
}


.floor-6 {
  height: 750px;
  background-repeat: no-repeat;
  background-position: top center;
}

.floor-6 .tabs {
  display: flex;
  flex-flow: row nowrap;
  width: 1200px;
  height: 500px;
  margin: auto;
  background: rgba(245, 249, 255, 0.9);
  box-shadow: inset 0px -1px 0px 0px #E6E9EE;
}

.floor-6 .tabs-navs {
  flex: 0 0 380px;
  width: 380px;
  height: 500px;
}

.floor-6 .tabs-panes {
  flex: 0 0 820px;
  width: 820px;
  height: 500px;
  background-color: #fff;
}

.floor-6 .tabs-navs__item {
  padding: 20px 30px;
  border-bottom: 1px solid #E6E9EE;
  cursor: default;
}

.floor-6 .tabs-navs__item:last-child {
  border-bottom: none;
}

.floor-6 .tabs-navs__item--active {
  background-color: #D9E9FF;
  border-color: #1689FF;
}

.floor-6 .tabs-navs__item h3 {
  margin: 0;
  font-size: 20px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  color: #202125;
  line-height: 30px;
}

.floor-6 .tabs-navs__item--active h3 {
  color: #1689FF;
}

.floor-6 .tabs-navs__item h3 sub {
  position: relative;
  top: -3px;
  font-size: 16px;
}

.floor-6 .tabs-navs__item h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 400;
  color: #202125;
  line-height: 21px;
}

.floor-6 .tabs-navs__item .summary {
  box-sizing: border-box;
  padding: 30px 0;
  height: 204px;
}
.floor-6 .tabs-navs__item p {
  margin: 0;
  font-size: 14px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  color: #666666;
  line-height: 26px;
}
.floor-6 .tabs-navs__item p strong {
  font-weight: bold;
  font-style: normal;
}

.floor-6 .tabs-panes__item {
  display: none;
}

.floor-6 .tabs-panes__item.tabs-panes__item--active {
  display: block;
}

.floor-6 .tabs-panes__item section {
  overflow: hidden;
  width: 730px;
  margin: 0 auto;
  padding: 0 0 30px;
  border-bottom: 1px solid #E6E9EE;
}

.floor-6 .tabs-panes__item section:last-child {
  border-bottom: none;
}

.floor-6 .tabs-panes__item section .main {
  float: left;
  width: calc(100% - 150px);
}

.floor-6 .tabs-panes__item section .action {
  float: right;
  width: 140px;
  padding: 30px 0 0;
}

.floor-6 .tabs-panes__item section .action .button {
  float: right;
  font-size: 16px;
}

.floor-6 .tabs-panes__item h2 {
  font-size: 20px;
  line-height: 30px;
  font-family: SourceHanSansCN-Medium, SourceHanSansCN;
  font-weight: 500;
  color: #202125;
}

.floor-6 .tabs-panes__item p {
  font-size: 14px;
  line-height: 21px;
  font-weight: 400;
  color: #666666;
}
