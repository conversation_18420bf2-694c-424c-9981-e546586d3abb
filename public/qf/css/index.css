/*主体样式*/
*{
  margin: 0;
  padding: 0;
}
.main-container {
  overflow-y: auto;
}
.header{
  width: 1280px;
  margin: 0 auto;
  height: 60px;
  line-height: 60px;
}
.header .logo{
  background: url(/static/imgs/new/logo.png) center 0 no-repeat;
  width: 120px;
  height: 40px;
  margin-top: 10px;
  display: inline-block;

}
.header .phone-wrapper{
  /* line-height: 60px; */
  display: inline-block;
  font-size: 16px;
  margin-left: 170px;
  vertical-align: top;
}
.header .phone-wrapper .phone-icon{
  background: url(/static/imgs/new/dianhua.png) center 0 no-repeat;
  background-size: 100%;
  width: 24px;
  height: 24px;
  vertical-align: sub;
  display: inline-block;
}
.header .phone-wrapper .phone-number{
  font-size: 20px;
  color: #DF4253;
}
.header .login-btn{
  width: 100px;
  height: 30px;
  background: #E74153;
  float: right;
  color: #fff;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  margin-top: 15px;
}

.body_bg {
  height: 100%;
}
@media (max-width: 1440px) {
  .body .login_box {
    top: 75px;
  }
  .JiangSuLogo {
    width: 420px !important;
    height: 90px !important;
  }
}
.top {
  height: 536px;
  background: url(/static/imgs/new/bgbanner.png) center 0 no-repeat;
}
.gray {
  background: #f0f0f0;
}
.body_bg1 {
  height: 100%;
  overflow-y: auto;
  
  
  
}
.about {
  margin-top: 80px;
  border-top: 1px solid #ddd;
  padding-top: 25px;
  text-align: center;
  padding-bottom: 25px;
  line-height: 40px;

  
}
.about  a {
  color: #333;
  margin-left: 16px;
  margin-right: 16px;
}
.about p {
  display: inline-block;
  color: #333;
  margin-left: 16px;
}

.container1280 {
  width: 1280px;
  margin: 0 auto;
}
.adtitle {
  text-align: center;
  margin-top: 140px;
  margin-bottom: 40px;
}
.box {
  width: 1600px;
  margin: 0 auto;
  position: relative;
  height: 536px;
}
.box .hidden_text{
  height:0;
  width: 0;
  overflow: hidden;
}
.top_ad {
  width: 100%;
  height: 100%;
  background: url('/static/imgs/new/topad_big.png') center no-repeat;
}
.gototop {
  position: fixed;
  width: 75px;
  height: 77px;
  opacity: 0.8;
  background: url(/static/imgs/new/gototop.png) no-repeat;
  bottom: 65px;
  right: 65px;
  cursor: pointer;
}
.gototop:hover {
  opacity: 1;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-active {
  opacity: 0;
}

@media (max-width: 1440px) {
  .top_ad {
    background: url(/static/imgs/new/topad_mid.png) center no-repeat;
  }
  .box {
    width: 1280px;
  }
}

.feedbackbox {
  float: left;
  width: 31%;
  height: 600px;
  background: #fff;
  margin-left: 2%;
  box-shadow: 0px 0px 8px rgba(33, 33, 33, 0.2);
  border-radius: 2px;
  
}
.feedbackbox .title {
  height: 10px;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
  background: #da251d;
}
.feedbackbox .userimg {
  text-align: center;
  padding-top: 100px;
}
.feedbackbox .line-1,
.feedbackbox .line-2,
.feedbackbox .line-3 {
  text-align: center;
  font-weight: normal;
}
.feedbackbox .line-1 {
  font-size: 28px;
  line-height: 34px;
  margin-top: 20px;
  color: #000;
}
.feedbackbox .line-2 {
  font-size: 22px;
  line-height: 36px;
  color: #999;
  margin-top: 30px;
}
.feedbackbox .line-3 {
  font-size: 18px;
  line-height: 40px;
  margin-top: 20px;
}