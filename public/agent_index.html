﻿<!DOCTYPE html>
<html>
  <head>
    <meta name="renderer" content="webkit" />
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title>票靓·服务版</title>
    <meta dynamic-meta name="description" content="您会发现，票靓·服务版和您想象中的自动化财税软件竟然如此接近">
    <meta dynamic-meta name="keywords" content="财税,莲融,票靓,代账,记账,财务,税务,凭证,申报,金蝶">
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="version" content="<%= htmlWebpackPlugin.options.version%>" />
    <meta http-equiv=X-UA-Compatible content="IE=edge,chrome=1">
    <meta content=always name=referrer>
    <!-- <script type="text/javascript" src="/static/lib/LodopFuncs.js"></script>
    <object  id="LODOP_OB" style="display: none;" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" width=0 height=0> 
          <embed id="LODOP_EM" type="application/x-print-lodop" width=0 height=0></embed>
    </object> -->
    <!-- Script for polyfilling Promises on IE9 and 10 -->
    <!--[if IE]> 
      <script src="/static/lib/polyfill.min.js"></script>
    <![endif]-->
    <script>if (/*@cc_on!@*/false || (!!window.MSInputMethodContext && !!document.documentMode)) window.location.href="/commonHtml/ieupdate/index.html#/login?referrer="+encodeURIComponent(window.location.href); </script>
    <script type="text/javascript" src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js"></script>
    <script src="/static/lib/kendoUI/jquery.js"></script>
    <script src="/static/lib/jquery.mousewheel.min.js"></script>
    <!-- <script src="/static/lib/kendoUI/kendo.custom.min.js"></script> -->
    <!-- <script src="/static/lib/kendoUI/kendo.culture.zh-CN.min.js"></script> -->
    <!-- <script src="/static/lib/kendoUI/jszip.min.js"></script> -->
    <link rel="icon" href="/favicon.ico">
    <script>
      const link = document.createElement('link');
      // const theme = window.localStorage.getItem('theme') || '<%= VUE_APP_DEFAULT_THEME %>'
      // TODO 暂时强制把所有用户主题色变成蓝色
      const theme = 'blue'
      link.setAttribute('rel', 'stylesheet');
      link.setAttribute('id', theme + '-style');
      document.head.appendChild(link);
      link.href = '/static/css/theme/' + theme + '/index.css';
      window.addEventListener('load', function() {
        document.body.className = 'theme-' + theme
      })
      
    </script>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but quickf-web doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
<script>
</script>
  </body>
</html>
