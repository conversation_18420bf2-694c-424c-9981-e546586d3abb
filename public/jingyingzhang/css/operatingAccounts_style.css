html,
body {
    padding: 0;
    min-width: 1280px;
    box-sizing: border-box;
    scroll-behavior: smooth;
    margin: 0 auto;
}

ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

img {
    padding: 0;
    margin: 0;
}

p {
    padding: 0;
    margin: 0;
}

button {
    border: none;
    cursor: pointer;
    padding: 0;
    margin: 0;
}

a {
    text-decoration: none;
}

.mainContent {
    max-width: 1920px;
    height: 100%;
    margin: 0 auto;
}

/* 顶部 */
.header {
    position: fixed;
    white-space: nowrap;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    z-index: 999;
    background-color: rgba(255, 255, 255, 0.7);
    min-width: 1280px;
    text-align: center;
}

/* 导航栏 */
.nav {
    display: inline-block;
    margin: 22px 0 22px 169px;
}

.navText li {
    cursor: pointer;
    float: left;
    font-size: 16px;
    font-weight: 400;
    line-height: 16px;
    margin-right: 40px;
    font-family: SourceHanSansCN-Regular, SourceHanSansCN;
}

.navText a {
    display: block;
    height: 16px;
    color: #282D3B;
}

.navText :last-child {
    margin-right: 0
}

/* logo */
.logo {
    overflow: hidden;
    display: inline-block;
    height: 32px;
    margin: 14px 0 14px 44px;
}

/* 操作按钮 */
.operateBtn {
    margin: 15px 0px 15px 217px;
    display: inline-block;
    width: 200px;
    overflow: hidden;
}

.login {
    font-size: 14px;
    width: 90px;
    height: 30px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 21px;
    background: #E74153;
}

/* 主体内容 */
.content {
    width: 100%;
}

/* 主体内容-首页 */
.content .home {
    max-width: 1920px;
    height: 660px;
    background: url("/static/imgs/newOperatingAccounts/top.png") no-repeat center;
    overflow: hidden;
    text-align: center;
}

.topText {
    height: 128px;
    font-size: 48px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    color: #282D3B;
    line-height: 64px;
    letter-spacing: 1px;
    margin-right: 735.5px;
    margin-top: 156px;
}

.content .home .eperienceBtn {
    margin-top: 61px;
    margin-right: 1009.5px;
}

.content .home .eperienceBtn button {
    font-size: 20px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 30px;
    width: 164px;
    height: 48px;
    background: #E74153;
    box-shadow: 0px 5px 11px 0px rgba(248, 35, 79, 0.22);
}

.home_footer {
    margin-top: 178px;
    text-align: center;
}

.footer_card {
    display: inline-block;
    padding-right: 128px;
}

.footer_card:nth-child(1) {
    padding-left: 101px;
}

.home_footer img {
    float: left;
    margin-top: 10px;
}

.home_footerText {
    text-align: left;
    float: right;
}

.home_footerText p:nth-child(1) {
    margin-left: 18px;
    height: 47px;
    font-size: 40px;
    font-family: Roboto-Medium, Roboto;
    font-weight: 500;
    color: #282D3B;
    line-height: 47px;
}

.home_footerText p:nth-child(2) {
    font-size: 14px;
    font-family: SourceHanSansCN-Regular, SourceHanSansCN;
    font-weight: 400;
    color: #898D95;
    line-height: 21px;
}

.home_footerText p span {
    font-size: 24px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 600;
    color: #282D3B;
    line-height: 24px;
}

/* 主体内容-产品介绍 */
.introduce {
    width: 100%;
    height: 700px;
    background: url("/static/imgs/newOperatingAccounts/bg1.png") no-repeat;
    background-size: 100% 100%;
    overflow: hidden;
    text-align: center;
}

.introduce_title {
    margin-top: 90px;
    font-size: 44px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    color: #282D3B;
    line-height: 66px;
    margin-bottom: 0;
}

.introduce_textDescription {
    margin: 25px;
    font-size: 18px;
    font-family: SourceHanSansCN-Normal, SourceHanSansCN;
    font-weight: 400;
    color: #898D95;
    line-height: 27px;
    margin-bottom: 0;
}

.introduce_mainContent {
    width: 100%;
    height: 320px;
}

.cardBox {
    height: 320px;
    margin-top: 80px;
    text-align: center;
}

.card {
    display: inline-block;
    width: 360px;
    height: 320px;
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0px 10px 40px 0px rgba(190, 201, 220, 0.3);
    border-radius: 20px;
    backdrop-filter: blur(6px);
    text-align: center;
}

.card .card_img {
    margin-top: 30px;
    margin-left: 80px;
    width: 200px;
    height: 160px;
}

.card .card_title {
    font-size: 28px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    color: #282D3B;
    line-height: 42px;
    margin-top: 26px;
}

.card .card_text {
    font-size: 16px;
    font-family: SourceHanSansCN-Regular, SourceHanSansCN;
    font-weight: 400;
    color: #858B9A;
    line-height: 24px;
}

.introduce_mainContent .card:nth-child(2) {
    margin: 0 58px 0 58px;
}

/* 主体内容-产品价值 */
.product_value {
    width: 100%;
    height: 840px;
    text-align: center;
    background: url("/static/imgs/newOperatingAccounts/bg2.png") no-repeat;
    background-size: 100% 100%;
    overflow: hidden;
}

.product_value_title {
    margin-top: 96px;
    font-size: 44px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    color: #333333;
    line-height: 66px;
}

.product_value_textDescription {
    margin-top: 25px;
    font-size: 18px;
    font-family: SourceHanSansCN-Normal, SourceHanSansCN;
    font-weight: 400;
    color: #898D95;
    line-height: 27px;
}

.product_value_mainContent {
    margin: 70px auto;
    width: 1180px;
    height: 480px;
    background: #FFFFFF;
    box-shadow: 0px 4px 28px 0px rgba(192, 203, 222, 0.4);
    overflow: hidden;
}

.product_value_mainContent_header {
    margin-top: 6px;
    text-align: center;
    height: 54px;
}

.product_value_mainContent_header span {
    cursor: pointer;
    display: inline-block;
    padding: 14px 46px 18px 42px;
    font-size: 22px;
    height: 22px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    line-height: 33px;
}

.btnClass1 {
    color: #333333;
}

.btnClass2 {
    position: relative;
    color: #DF4253;
}

.btnClass2::after {
    position: absolute;
    bottom: -3px;
    left: 50%;
    margin-left: -95px;
    content: '';
    display: block;
    width: 190px;
    height: 4px;
    background: linear-gradient(270deg, #E84151 0%, #FFACB3 100%);
    border-radius: 2px;
}

.product_value_mainContent_hr {
    margin: 0 auto;
    width: 1081px;
    border: 1px solid #F0EBEB;
}

.product_value_left_content {
    float: left;
    margin-top: 69px;
    margin-left: 50px;
}

.product_value_left_content_title {
    min-width: 224px;
    height: 32px;
    font-size: 32px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    color: #282D3B;
    line-height: 48px;
}

.product_value_left_content_text {
    margin-top: 40px;
    width: 100%;
    height: 149px;
    text-align: left;
}

.product_value_left_content_text div {
    min-width: 326px;
    height: 26px;
    border-bottom: 1px dashed #D6D9DE;
}

.product_value_left_content_text div:nth-child(1) {
    padding-bottom: 20px;
}

.product_value_left_content_text div:nth-child(2) {
    padding: 17px 0 18px 0;
}

.product_value_left_content_text div:nth-child(3) {
    padding: 17px 0 15px 0;
}

.product_value_left_content_text div:nth-child(4) {
    padding: 17px 0 15px 0;
    display: none;
}

.product_value_left_content_span {
    margin-left: 16px;
    width: 326px;
    height: 20px;
    font-size: 20px;
    font-family: SourceHanSansCN-Normal, SourceHanSansCN;
    font-weight: 400;
    color: #67686C;
    line-height: 30px;
}

.product_value_right_content {
    float: right;
    margin-top: 42px;
    margin-right: 50px;
}

/* 主体内容-品牌客户 */
.brand {
    max-width: 1920px;
    height: 650px;
    text-align: center;
    background: url("/static/imgs/newOperatingAccounts/bg4.png") no-repeat center;
    overflow: hidden;
}

.brand_title {
    margin-top: 90px;
    font-size: 44px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    color: #FFFFFF;
}

.brand_text {
    margin-top: 20px;
    font-size: 18px;
    font-family: SourceHanSansCN-Normal, SourceHanSansCN;
    font-weight: 400;
    color: #FFFFFF;
    opacity: 0.7;
}

.brand_main {
    margin-top: 70px;
}

.brand_main_margin {
    margin-bottom: 26px;
}

.brand_card {
    display: inline-block;
    color: #FFFFFF;
    background: linear-gradient(270deg, #D61B2F 0%, #FD4358 100%);
    box-shadow: 0px 8px 27px 0px rgba(222, 11, 11, 0.3);
    border-radius: 30px;
    opacity: 0.95;
    backdrop-filter: blur(50px);
    padding: 15px 25px 15px 25px;
}

.brand_card p {
    font-size: 20px;
    font-family: SourceHanSansCN-Regular, SourceHanSansCN;
    font-weight: 400;
    line-height: 30px;
}

.brand_main1 :nth-child(2) {
    margin: 0 45px 0 45px;
    padding: 15px 30px 15px 30px;
}

.brand_main2 div:first-child {
    margin-right: 45px;
    padding: 15px 30px 15px 30px;
}

.brand_main2 :nth-child(2) {
    padding: 15px 23px 15px 24px;
}

.brand_main3 :nth-child(2) {
    margin: 0 45px 0 45px;
}

.brand_main4 div:first-child {
    margin-right: 45px;
}

/* 主体内容-品牌资质 */
.aptitude {
    width: 100%;
    height: 790px;
    text-align: center;
    background-color: #F4F4F7;
    overflow: hidden;
}

.aptitude_title {
    margin-top: 90px;
    font-size: 44px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    color: #282D3B;
}

.aptitude_text {
    height: 28px;
    margin-top: 20px;
    font-size: 22px;
    font-family: SourceHanSansCN-Regular, SourceHanSansCN;
    font-weight: 400;
    color: #282D3B;
    line-height: 33px;
}

.aptitude_text span {
    color: #DF4253;
    font-size: 28px;
}

.partialDisplay {
    margin-top: 70px;
    font-size: 18px;
    font-family: SourceHanSansCN-Regular, SourceHanSansCN;
    font-weight: 400;
    color: #282D3B;
}

.aptitude_img {
    margin-top: 16px;
}

/* .aptitude_img_content {
    width: 1374px;
    margin-left: 277px;
} */

.aptitude_description {
    margin-bottom: 16px;
}

.aptitude_description span {
    font-size: 14px;
    font-family: SourceHanSansCN-Normal, SourceHanSansCN;
    font-weight: 400;
    color: #898D95;
}

/* .aptitude_img img {
    width: 1374px;
    height: 430px;
} */
.aptitude_bg {
    height: 430px;
    background: url('/static/imgs/newOperatingAccounts/zhengshu.png') no-repeat center;
}

.aptitude_footer span {
    cursor: pointer;
    position: absolute;
    right: 259px;
    bottom: 137px;
    font-size: 18px;
    color: #FFFFFF;
}

.aptitude_footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 143px;
    background: url('/static/imgs/newOperatingAccounts/red.png') no-repeat center;
    opacity: 0;
    transition: all 0.1s;
    text-align: center;
    min-width: 1280px;
}

.aptitude_footer_text {
    overflow: hidden;
    display: inline-block;
    font-size: 40px;
    margin-left:10px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 68px;
    margin-top: 37px;
}

.aptitude_footer_btn {
    overflow: hidden;
    width: 260px;
    height: 68px;
    margin-left: 190px;
    background: #FFFFFF;
    box-shadow: 0px 6px 6px 0px rgba(255, 57, 57, 0.5), 0px 3px 0px 0px rgba(255, 165, 165, 0.56);
    border-radius: 34px;
    font-size: 24px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    color: #EF4A4A;
}

/* 底部 */
footer {
    max-width: 1920px;
    height: 160px;
    background: #35363F;
    text-align: center;
    overflow: hidden;
}

.footerText {
    margin-top: 49px;
}

.footerText a {
    font-size: 16px;
    line-height: 21px;
    font-family: SourceHanSansCN-Regular, SourceHanSansCN;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 24px;
    margin-left: 30px;
    margin-right: 30px;
}

.footerText span {
    color: #848A94;
    font-size: 14px;
}

.record {
    margin-top: 34px;
}

.record img {
    vertical-align: sub;
}

.record p {
    display: inline-block;
    font-size: 14px;
    font-family: SourceHanSansCN-Regular, SourceHanSansCN;
    font-weight: 400;
    margin-left: 5px;
    color: #FFFFFF;
    line-height: 21px;
}