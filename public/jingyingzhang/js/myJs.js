function jumpLoginPage() {
  window.open('/app.html#/login');
}
window.onload = function loadFunction() {
  // 获取头部
  const header = document.getElementById('product_value_header');
  // 获取按钮
  const btns = header.getElementsByTagName('span');
  // 获取照片
  const rightImg = document.getElementById('product_value_right_img');
  // 获取标题
  const textTitle = document.getElementById('product_value_text_title');
  const contentSpan1 = document.getElementById('conten_span1');
  const contentSpan2 = document.getElementById('conten_span2');
  const contentSpan3 = document.getElementById('conten_span3');
  const contentSpan4 = document.getElementById('fourth_Div');
  const aptitudeFooter = document.getElementById('aptitude_footer');
  const footer = document.getElementById('footer');

  // 是否显示底部横条
  let isShowBottomBanner = false;
  let isshowFooter = true;

  // 关闭横条
  const closeBtn = document.getElementById('close_btn');

  // tab页绑定点击事件
  for (let i = 0; i < btns.length; i += 1) {
    btns[i].addEventListener('click', function getId() {
      if (this.className === 'btnClass2') {
        return;
      }
      for (let j = 0; j < btns.length; j += 1) {
        btns[j].className = 'btnClass1';
        contentSpan4.style.display = 'none';
      }
      this.className = 'btnClass2';
      switch (this.id) {
        case 'span1':
          rightImg.src = '/static/imgs/newOperatingAccounts/作业赋能.png';
          textTitle.innerText = '经营账作业过程';
          contentSpan1.innerText = '企业客户与代账人员协同作业';
          contentSpan2.innerText = '经营账数据智能形成';
          contentSpan3.innerText = '经营账成果即时交付，多端共享';
          break;
        case 'span2':
          rightImg.src = '/static/imgs/newOperatingAccounts/服务赋能.png';
          textTitle.innerText = '轻型经营账服务';
          contentSpan1.innerText = '深度绑定客户';
          contentSpan2.innerText = '客户粘性强';
          contentSpan3.innerText = '客单价高';
          contentSpan4.style.display = 'block';
          break;
        case 'span3':
          rightImg.src = '/static/imgs/newOperatingAccounts/人才赋能.png';
          textTitle.innerText = '代账企业，人才体系';
          contentSpan1.innerText = '标准化记账，助力人才快速上手';
          contentSpan2.innerText = '模块化分配，有针对性提升人才能力';
          contentSpan3.innerText = '专业化输出，保证人才可持续发展';
          break;
        default:
          rightImg.src = '/static/imgs/newOperatingAccounts/作业赋能.png';
          contentSpan1.innerText = '企业客户与代账人员协同作业';
          contentSpan2.innerText = '经营账数据智能形成';
          contentSpan3.innerText = '经营账成果即时交付，多端共享';
      }
    });
  }
 // 判断元素是否在可见区域内
 function isInViewPort(element) {
  const viewWidth = window.innerWidth || document.documentElement.clientWidth;
  const viewHeight = window.innerHeight || document.documentElement.clientHeight;
  const {
    top,
    right,
    bottom,
    left,
  } = element.getBoundingClientRect();
  return (
    top >= 0 && left >= 0 && right <= viewWidth && bottom <= viewHeight + 100
  );
}

  // 滚动显示底部横条
  window.addEventListener('scroll', function onScroll() {
    if (isshowFooter) {
      if (isInViewPort(footer)) {
        aptitudeFooter.style.marginBottom = '160px';
      } else {
        aptitudeFooter.style.marginBottom = 0;
      }
    }
     if (!this.sessionStorage.getItem('isShowBottomBanner')) {
      if (document.documentElement.scrollTop >= 600) {
        aptitudeFooter.style.opacity = 1;
        isShowBottomBanner = true;
        this.sessionStorage.setItem('isShowBottomBanner', isShowBottomBanner);
      }
   }
  });
  // 关闭横条
  closeBtn.addEventListener('click', () => {
    aptitudeFooter.style.opacity = 0;
    isshowFooter = false;
  });

  // 是否2k屏
  function getScreenWidth() {
    if (window.screen.width >= 2560) {
      closeBtn.style.right = '579px';
    } else {
      closeBtn.style.right = '259px';
    }
  }
     getScreenWidth();
};
