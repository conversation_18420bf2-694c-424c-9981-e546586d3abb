/// <reference path="./login-interfaces.ts" />

await setupStorageAndCookies()
await openTargetPage()
await preventLogout()

/**
 * 开始设置 localStorage、cookie 等信息
 */
async function setupStorageAndCookies() {
  context.logger.info('准备设置 localStorage & cookies...')
  await Promise.allSettled([setEtaxData(), setTpassData()])
}

/**
 * 为 tpass 页面设置登录所需的信息
 */
async function setTpassData() {
  context.logger.info('【tpass】准备设置 localStorage & cookies...')

  const tpass = context.loginInfo.tpass

  const cookieObj = parseCookie(tpass.cookie)
  context.logger.info('【tpass】parseCookie 结果:', JSON.stringify(cookieObj))

  /** @type {Array<PTask.CookieItem>} */
  const cookieArr = []
  Object.keys(cookieObj).forEach(name => {
    cookieArr.push({
      secure: false, // 仅通过HTTPS传输cookie
      httpOnly: false, // 可以通过非HTTP访问cookie
      name,
      domain: name === 'token' ? 'tpass.' + context.params.province + '.chinatax.gov.cn' : '.chinatax.gov.cn',
      value: cookieObj[name],
    })
  })

  context.logger.info('【tpass】解析出准备应用的 cookie:', JSON.stringify(cookieArr))

  await context.execOnNewPage(
    makeTpassUrl(context.params.province),
    async newPage => {
      if (cookieArr.length > 0) {
        try {
          await newPage.setCookie(...cookieArr)
        } catch (err) {
          context.logger.error('【tpass】设置 cookie 出错', err)
        }
      } else {
        context.logger.error('【tpass】解析 cookie 出错，cookie 数量为零，请分析接口返回')
      }

      const storage = tpass.localStorage ?? {}
      if (Object.keys(storage).length > 0) {
        try {
          await newPage.evaluate(data => {
            Object.keys(data).forEach(key => {
              const value = data[key]
              if (value) {
                window.localStorage.setItem(key, value)
              } else {
                window.localStorage.removeItem(key)
              }
            })
          }, storage)
        } catch (err) {
          context.logger.error('【tpass】设置 localStorage 出错', err)
        }
      } else {
        context.logger.error('【tpass】设置 localStorage 出错，localStorage 为空，请分析接口返回')
      }
    },
    {
      timeout: 30000,
      preventRedirect: true,
      preventImage: true,
      preventStyle: true,
      preventFont: true,
      preventScript: true,
    },
  )

  context.logger.error('【tpass】设置登录信息完成')
}

/**
 * 为 etax 页面设置登录所需的信息
 * 
 * 2025-06-10 测试发现，该函数不调用也能正常登录
 * 不过加上也没发现问题，暂时不处理
 */
async function setEtaxData() {
  context.logger.info('【etax】准备设置 localStorage & cookies...')

  const etax = context.loginInfo.etax
  const cookieObj = parseCookie(etax.cookie)

  context.logger.info('【etax】parseCookie 结果:', JSON.stringify(cookieObj))

  /** @type {Array<PTask.CookieItem>} */
  const cookieArr = []
  Object.keys(cookieObj).forEach(name => {
    cookieArr.push({
      secure: false,
      httpOnly: false,
      name,
      domain: ['YiNFJOIaS7YZO', 'YiNFJOIaS7YZP'].includes(name)
        ? 'etax.' + context.params.province + '.chinatax.gov.cn'
        : '.chinatax.gov.cn',
      value: cookieObj[name],
    })
  })

  context.logger.info('【etax】解析出准备应用的 cookie:', JSON.stringify(cookieArr))

  await context.execOnNewPage(
    makeEtaxUrl(context.params.province),
    async newPage => {
      if (cookieArr.length > 0) {
        try {
          await newPage.setCookie(...cookieArr)
        } catch (err) {
          context.logger.error('设置 cookie 出错', err)
        }
      }

      const storage = etax.localStorage ?? {}
      if (Object.keys(storage).length > 0) {
        try {
          await newPage.evaluate(data => {
            Object.keys(data).forEach(key => {
              const value = data[key]
              if (value) {
                window.localStorage.setItem(key, value)
              } else {
                window.localStorage.removeItem(key)
              }
            })
          }, storage)
        } catch (err) {
          context.logger.error('【etax】设置 localStorage 出错', err)
        }
      }
    },
    {
      timeout: 30000,
      preventRedirect: true,
      preventImage: true,
      preventStyle: true,
      preventFont: true,
      preventScript: true,
    },
  )

  context.logger.error('【etax】设置登录信息完成')
}

/**
 * 登录完毕，打开目标页面
 */
async function openTargetPage() {
  context.logger.info('打开目标页面...')

  let pageUrl = makeEtaxUrl(context.params.province)
  // 如果存在跳转目标参数，则需要调整最终打开的目标页面
  if (context.params.targetPage != null) {
    pageUrl = context.params.targetPage
  }

  const page = await context.ensureEmptyPage()
  await page.goto(pageUrl)
  context.logger.info('目标页面已打开')
}

/**
 * 阻止用户退出登录
 */
async function preventLogout() {
  context.browser.on('targetcreated', async target => {
    if (target.type() !== 'page') return
    const page = await target.page()
    if (!page) return
    // 在新页面中执行指定的 JS 逻辑
    await page.evaluate(() => {
      // console.log('在新页面中执行自定义逻辑')
    })
  })
}

/**
 * 解析接口拿到的 cookie 信息为对象
 * @param {string} cookie
 * @returns {Record<string, string>}
 */
function parseCookie(cookie) {
  context.logger.info('【parseCookie】解析 cookie...')
  try {
    const data = String(cookie)
      .split(';')
      .map(part => part.trim())
      .map(part => part.split('='))
      .reduce((acc, [key, value]) => {
        acc[key] = value
        return acc
      }, {})
    context.logger.info('【parseCookie】解析 cookie 成功.')
    return data
  } catch (err) {
    context.logger.error('【parseCookie】解析 cookie 出错', err)
    return {}
  }
}

/**
 * 电局登录 cookie 设置页
 */
function makeTpassUrl(province) {
  return 'https://tpass.' + (province || 'guangdong') + '.chinatax.gov.cn:8443/#/userCenter/baseInfoEE'
}

/**
 * 电局首页
 */
function makeEtaxUrl(province) {
  return 'https://etax.' + (province || 'guangdong') + '.chinatax.gov.cn:8443/loginb/'
}
