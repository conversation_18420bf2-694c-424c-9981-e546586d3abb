const configTemp = {
  "科目信息表":{
      "subjectCodeColNum": 3,
      "subjectFullNameFlag": true,
      "subjectNameColNum": 2,
      "auxiliaryTypeColNum": 5,
      "auxiliaryTypeSeparator": ";",
      "unitColNum": 7,
      "currencyNameColNum": 6,
      "currencyNameSeparator": ";",
      "sheetName": "My Sheet",
      "sheetIndex": 0,
      "titleRowNum": 1,
  },
  "辅助核算":{
          "auxiliaryTypeColNum": 5,
          "auxiliaryCodeColNum": 1,
          "auxiliaryNameColNum": 2,
          "sheetName": "My Sheet", 
          "sheetIndex": 0,
          "titleRowNum": 1,
      },
  "科目余额表":{
    "period": 202403,
    "subjectCodeColNum": 1,
    "subjectNameColNum": 2,
    "auxiliarySource": 1,
    "auxiliaryNameSeparator": "<$辅助核算编码$ $辅助核算名称$>",
    "currencyName": "CNY",
    "initDebitAmountColNum": 3,
    "initCreditAmountColNum": 4,
    "currDebitAmountColNum": 5,
    "currCreditAmountColNum": 6,
    "endingDebitAmountColNum": 9,
    "endingCreditAmountColNum": 10,
    "yearDebitAmountColNum": 7,
    "yearCreditAmountColNum": 8,
    "sheetName": "科目余额表",
    "sheetIndex": 0,
    "titleRowNum": 4,
    "fileName": ""
},
      '科目余额表(外币)':{
        "period": 202308,
        "subjectCodeColNum": 1,
        "subjectNameColNum": 2,
        "auxiliarySource": 1,
        "auxiliaryNameSeparator": "<$辅助核算编码$ $辅助核算名称$>",
        "currencyName": "",
        "initDebitAmountColNum": 4,
        "initCreditAmountColNum": 6,
        "initDebitAmountOColNum": 3,
        "initCreditAmountOColNum": 5,
        "currDebitAmountColNum": 8,
        "currCreditAmountColNum": 10,
        "currDebitAmountOColNum": 7,
        "currCreditAmountOColNum": 9,
        "endingDebitAmountColNum": 16,
        "endingCreditAmountColNum": 18,
        "endingDebitAmountOColNum": 15,
        "endingCreditAmountOColNum": 17,
        "yearDebitAmountColNum": 12,
        "yearCreditAmountColNum": 14,
        "yearDebitAmountOColNum": 11,
        "yearCreditAmountOColNum": 13,
        "sheetName": "Sheet1",
        "sheetIndex": 0,
        "titleRowNum": 4,
        "fileName": ""
      },
      '数量金额科目余额表': {
        "period": 202403,
        "subjectCodeColNum": 1,
        "subjectNameColNum": 2,
        "auxiliaryNameSeparator": "<$辅助核算编码$ $辅助核算名称$>",
        "currencyName": "CNY",
        "unitColNum": 3,
        "priceColNum": 18,
        "initDirectionColNum": 4,
        "initAmountColNum": 7,
        "initQtyColNum": 5,
        "currDebitAmountColNum": 9,
        "currCreditAmountColNum": 11,
        "currDebitQtyColNum": 8,
        "currCreditQtyColNum": 10,
        "endingDirectionColNum": 16,
        "endingAmountColNum": 19,
        "endingQtyColNum": 17,
        "yearDebitAmountColNum": 13,
        "yearCreditAmountColNum": 15,
        "yearDebitQtyColNum": 12,
        "yearCreditQtyColNum": 14,
        "auxiliarySource": 1,
        "sheetName": "数量科目余额表",
        "sheetIndex": 0,
        "titleRowNum": 4,
        "fileName": ""
      },
  "凭证表":{
          "voucherDateColNum": 1 ,
          "voucherDateFormat": "yyyy-MM-dd" ,
          "voucherNumColNum": 2 ,
          "voucherAbstractColNum": 3 ,
          "subjectCodeNameColNum": 4 ,
          "subjectCodeNameSeparator": "`$^" ,
          "auxiliarySource": 1 ,
          "auxiliaryNameSeparator": "<$辅助核算编码$ $辅助核算名称$>" ,
          "currencyNameColNum": 7 ,
          "debitAmountColNum": 10 ,
          "creditAmountColNum": 11 ,
          "qtyColNum": 5 ,
          "amountOColNum": 8 ,
          "exchangeRateColNum": 9 ,
          "sheetName": "凭证列表" ,
          "sheetIndex": 0 ,
          "titleRowNum": 3 ,
      },
      "资产卡片": {
        "departmentOne2more": false,
        "assetSubjectFormatter": 2,
        "deprSubjectAndRateSameCol": false,
        "deprLimitCycle": 2,
        "assetCodeColNum": "2",
        "assetCategoryColNum": "1",
        "assetNameColNum": "3",
        "modelSizeColNum": "",
        "assetQtyColNum": "",
        "businessTimeColNum": "",
        "usedDateColNum": "6",
        "dateFormat": "yyyy-MM-dd",
        "departmentNameColNum": "4",
        "originalValueSubjectColNum": "17",
        "deprSubjectColNum": "16",
        "deprSubjectRateColNum": "",
        "deprSubjectRateSeparator": "",
        "disposeSubjectColNum": "",
        "accuDeprSubjectColNum": "18",
        "depreciationSubjectColNum": "",
        "assetSubjectNameSeparator": "_",
        "addMethodColNum": "",
        "deprMethodColNum": "15",
        "deprLimitColNum": "7",
        "surplusValueRateColNum": "",
        "originalValueColNum": "8",
        "accuDeprColNum": "11",
        "depreciationColNum": "",
        "storagePlaceColNum": "",
        "userColNum": "",
        "remarkColNum": "",
        "statusColNum": "",
        "assetAddedVoucherColNum": "",
        "assetDisposeVoucherColNum": "",
        "sheetName": "My Sheet",
        "sheetIndex": 0,
        "titleRowNum": "1",
      }
}


// 定义轮询间隔时间（毫秒）
const POLL_INTERVAL = 1000;
// 定义最大重试次数
const MAX_RETRIES = 200;

// 异步任务接口方法
async function executeAsyncTask(taskid, type, callback) {
    let status = 'PENDING'; // 初始化任务状态为待处理
    let retries = 0; // 初始化重试次数为0
    let result
    while (status !== 'SUCCESS') { // 不断轮询直到任务状态成功或者达到最大重试次数
        // 调用查询任务状态的接口
        try {
          const fileRsp = await taskWindow.request({
            url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/account/TaskCenter/fileList',
            params: {taskid, type},
            method: 'get',
          })
          result = fileRsp.data.result
          console.log(fileRsp.data)
        } catch (error) {
            console.error('文件导出任务出错:', error, taskid);
            throw new Error('文件导出任务状态查询出错');
        }

        // 如果任务状态成功，则执行回调任务
        if (result?.status === 1 && result?.url) {
            status = 'SUCCESS';
            try {
                // 执行回调任务
                await callback(result);
                console.log('任务成功响应，开始执行下载');
                return; // 结束异步任务接口
            } catch (error) {
                console.error('执行下载任务失败', error);
                throw new Error('执行下载任务失败');
            }
        }

        // 如果任务状态不是成功且重试次数未超出最大限制，等待一段时间后再次轮询
        if (retries < MAX_RETRIES) {
            retries++;
            await new Promise(resolve => setTimeout(resolve, POLL_INTERVAL));
        } else {
            console.error('任务超时');
            throw new Error('任务超时');
        }
    }
}

function splitDateRangeByYear(startDate, endDate) {
  const result = [];
  let currentYear = parseInt(startDate.substring(0, 4));
  const endYear = parseInt(endDate.substring(0, 4));

  while (currentYear <= endYear) {
      let startOfYear = currentYear.toString() + '01';
      let endOfYear = currentYear.toString() + '12';
      
      if (currentYear === parseInt(startDate.substring(0, 4))) {
          // If it's the start year, adjust startOfMonth
          startOfYear = startDate;
      }
      
      if (currentYear === parseInt(endDate.substring(0, 4))) {
          // If it's the end year, adjust endOfMonth
          endOfYear = endDate;
      }
      
      result.push([startOfYear, endOfYear]);
      currentYear++;
  }

  return result;
}

async function decodeJSON(str) {
  const objJson = await taskWindow.page.evaluate((str) => {
      window.Utf8Key = window.CryptoJS.enc.Utf8.parse("fintaxfintaxfint")
      window.decodeJSON =  function b(C) {
        var T = C
          , k = window.CryptoJS.AES.decrypt(T, Utf8Key, {
            iv: "",
            mode: window.CryptoJS.mode.ECB,
            padding: window.CryptoJS.pad.Pkcs7
        })
          , B = k.toString(window.CryptoJS.enc.Utf8)
          , P = B.toString();
        return P
    }
    return window.decodeJSON(str)
  }, str);
  return JSON.parse(objJson)
}


await taskWindow.loadURL('https://sso.yunzhangfang.com/login.html')
let ztdm = ''
let ztmc = ''
let qyid = ''
let startTime = '' // 建账月
let endTime = ''
let kjnd = ''
let kjqj = ''
let yhid = ''
let infoObject = {}
let useBZList = [0] // 已使用的外币列表
let bzList = []
let pageGlobalData = {}
let lastBookClosingMonth = '' // 最后一个结账月
taskWindow.page.on('response', async (response) => {
    const url = response.url()

    // 查询账套代码的接口
    if(/\/api\/fintax\/application\/dz\/company\/index\/\d+/.test(url)) {
      const data = await response.json()
      console.log(data, '')
      ztdm = data.result.ztdm
      ztmc = data.result.ztVO.name
      qyid = data.result.ztVO.qyid
      startTime = data.result.startTime
      endTime = data.result.endTime
      kjnd = data.result.kjnd
      kjqj = data.result.kjqj
      infoObject = data;
      lastBookClosingMonth = await getLastJieZhang(startTime, endTime)
      taskWindow.sendShowForm({
        companyName: ztmc,
        lastBookClosingMonth,
        startTime: dayjs(startTime).format('YYYYMM')
      })
    }
    console.log(url)
})
// 获取最后一个结账月
const getLastJieZhang = async (startDate, endDate) => {
  const getJieZhang = async (kjnd) => {
    const rsp = await taskWindow.request({
      url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/account/ztqjzt/query/list',
      data: {
        ztdm,
        kjnd,
      },
      method: 'post'
    })
    const result = rsp.data.result || [];
    return result.findLast(item => item.zt === 9)
  }
  let year = dayjs(endDate).year()
  let startYear = dayjs(startDate).year()
  let jz
  while(year >= startYear) {
    jz = await getJieZhang(year)
    if(jz) {
      break;
    }
    year--
  }
  if(jz) {
    return dayjs().year(jz.kjnd).month(jz.kjqj - 1).format('YYYYMM')
  } else {
    return dayjs(startDate).format('YYYYMM')
  }
}
// 导出科目表
async function exportSubject ({kjnd}) {
  const [bzRsp, kmRsp, unitRsp] = await Promise.all([await taskWindow.request({
    url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/account/bz/selectAll',
    data: {
      ztdm,
      kjnd,
    },
    method: 'post'
  }), taskWindow.request({
    url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/account/ztkm/selectAllgzipZtkm',
    data: {
      ztdm,
      kjnd,
      kmmcOrkmbm: '',
      kmlx: null,
      start: 1,
      limit: 99999
    },
    method: 'POST'
  }),
  taskWindow.request({
    url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/api/stock/manage/stock/balance/list',
    data: {
      "pageNumber":1,
      "pageSize":99999,
      "nameCodeFuzzy":"",
      "stockTypeId":"",
      "balance":true,
      "conventUnit":true,
      "editUnit":true,
      kjnd,
      kjqj,
      ztdm
    },
    method: 'post'
  })
])
  bzList = bzRsp.data.result
  let data = kmRsp.data.result || []
  const unitList = unitRsp.data?.result?.list || []

  console.log(unitList.length, {
    ztdm,
    kjnd,
    kjqj,
  })
  data = data.map(item => {
    const unit = unitList.find((row) => row.subjectCode === item.kmbm && !!row.unit)?.unit || ''
    if(item?.bzidList && item?.bzidList?.length > 0) {
      useBZList = useBZList.concat(item?.bzidList)
    }
    return {
      unit,
      kmId: item.id,
      kmmc: item.kmmc,
      kmqc: item.kmqc,
      kmbm: item.kmbm,
      kmfx: item.kmfx === 1 ? '借' : '贷',
      fzlx: item?.currentFzlxCodes.map(item => item.fzlxName).join(';') || '',
      zbxx: item?.bzidList?.filter(item => item !== 0).map(item => bzList.find(bz => bz.bzid === item)?.bzjc).join(';') || ''
    }
  })
  await taskWindow.downloadExcelByData(data, [
    {key: 'kmmc', header: '科目名称'},
    {key: 'kmqc', header: '科目全称'},
    {key: 'kmbm', header: '科目编码'},
    {key: 'kmfx', header: '余额方向'},
    {key: 'fzlx', header: '辅助核算'},
    {key: 'zbxx', header: '外币'},
    {key: 'unit', header: '单位'}
  ], `${ztmc}_科目表.xlsx`, '科目信息表', configTemp['科目信息表'])
  return data
}

// 导出资产卡片
async function exportAssetsCard({kjnd, kjqj}) {
  // 请求科目列表
  const subjectRsp = await taskWindow.request({
    url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/account/ztkm/selectLastlevelKm',
    method: 'post',
    data: {
      kjnd,
      ztdm
    }
  })
  const subjectData = subjectRsp.data.result
  // 请求资产类别列表
  const assetType0Rsp = await taskWindow.request({
    url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/asset/assetInfo/zclb',
    params: {
      type: 0
    },
    method: 'get'
   })
   // 请求资产类别列表
  const assetType1Rsp = await taskWindow.request({
    url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/asset/assetInfo/zclb',
    params: {
      type: 1
    },
    method: 'get'
   })
   
   
  const assetTypeList = [...assetType0Rsp.data.result, ...assetType1Rsp.data.result]
  // 请求资产列表
 const rsp0 = await taskWindow.request({
  url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/asset/assetInfo/list/page',
  params: {
    currentPage: 1,
    kjnd,
    kjqj,
    pageSize: 9999,
    qyid,
    type: 0
  },
  method: 'get'
 })
 const rsp1 = await taskWindow.request({
  url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/asset/assetInfo/list/page',
  params: {
    currentPage: 1,
    kjnd,
    kjqj,
    pageSize: 9999,
    qyid,
    type: 1
  },
  method: 'get'
 })
 const statusList  = [
  {value: 1, label: '正常'},
  {value: 2, label: '暂停'},
  {value: 3, label: '全部变动'},
  {value: 4, label: '折旧完成'},
  {value: 5, label: '部分变动'},
 ]
 const result0 = await decodeJSON(rsp0.data.result)
 const result1 = await decodeJSON(rsp1.data.result)
 const list = [...(result0?.data || []), ...(result1?.data || [])]
 const cardList = []
 console.log(list.length)
 for(let i = 0; i < list.length; i++) {
  const card = list[i];
  // 请求资产卡片列表
  const cardRsp = await taskWindow.request({
    url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/asset/assetInfo/detail',
    params: {
      id: card.id,
      kjnd,
      kjqj,
      qyid,
    },
    method: 'get'
  })
  const cardDetail = cardRsp.data.result
  card.cbfykm = subjectData.find(subject => subject.id === cardDetail.cbfykmId)?.kmqc
  card.ljzjkm = subjectData.find(subject => subject.id === cardDetail.ljzjkmId)?.kmqc
  card.yzkm = subjectData.find(subject => subject.id === cardDetail.yzkmId)?.kmqc
  card.zclb = assetTypeList.find(assetType => assetType.code === cardDetail.zclb)?.sysName
  card.statusText = statusList.find(status => status.value === card.status)?.label
  card.zjff = card.sfid === 1 ? '平均年限法' : '未知'
  card.index = i + 1
  card.sybm = '行政部门'
  card.name = card.name.slice(0, 50)
  cardList.push(card)
  await new Promise(resolve => setTimeout(resolve, POLL_INTERVAL));
 }
 await taskWindow.downloadExcelByData(cardList, [
  
  {key: 'zclb', header: '资产类别'},
  {key: 'index', header: '资产编码'},
  {key: 'name', header: '资产名称'},
  {key: 'sybm', header: '使用部门'},
  {key: 'num', header: '数量'},
  {key: 'rzrq', header: '入账日期'},
  {key: 'yjsyyf', header: '可使用月份'},
  {key: 'yz', header: '原值'},
  {key: 'yzje', header: '本期折旧额'},
  {key: 'bnljzj', header: '本年折旧额'},
  {key: 'totalJtje', header: '累计折旧'},
  {key: 'jz', header: '期末净值'},
  {key: 'cz', header: '残值'},
  {key: 'czl', header: '残值率%'},
  {key: 'zjff', header: '折旧方法'},
  {key: 'cbfykm', header: '成本费用科目'},
  {key: 'yzkm', header: '原值科目'},
  {key: 'ljzjkm', header: '累计折旧科目'},
  {key: 'statusText', header: '状态'},
], `${ztmc}_资产卡片.xlsx`, `资产卡片`, configTemp[`资产卡片`])
//  console.log('----------cardList', cardList, '----------cardList')
}
// 导出辅助信息
async function exportFuZhuXinXi ({kjnd}) {
  const typeRsp = await taskWindow.request({
    url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/account/fzhs/fzlx/selectList',
    data: {
      ztdm,
      kjnd,
    },
    method: 'post'
  })
  const customList = typeRsp.data?.result?.customList || []
  const fzlxList = typeRsp.data?.result?.fzlxList || []
  const typeList = [...customList, ...fzlxList]
  console.log(typeRsp)
  const allFzhsRsp = await Promise.all(typeList.map(item => taskWindow.request({
    url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/account/fzhs/aggre/selectList',
    data: {
      ztdm,
      "fzlxCode": item.fzlxCode,
      "bmOrMc": "",
      kjnd
    },
    method: 'post'
  })))
  
  const allFzhsList = allFzhsRsp.reduce((result, itemRsp) => {
    console.log(itemRsp.data?.result, 'itemRsp.data?.result', result)
    return result.concat(itemRsp.data?.result || [])
  }, [])

  
  const data = allFzhsList.map(item => {
    return {
      bm: item.bm,
      mc: item.mc,
      bz: item.bz,
      zt: item.zt === 1 ? '启用' : '停用',
      fzlx: typeList.find(type => type.fzlxCode === item.fzlxCode)?.fzlxName || '',
    }
  })
  await taskWindow.downloadExcelByData(data, [
    {key: 'bm', header: '编码'},
    {key: 'mc', header: '名称'},
    {key: 'bz', header: '备注'},
    {key: 'zt', header: '状态'},
    {key: 'fzlx', header: '类型'}
  ], `${ztmc}_辅助核算.xlsx`, '辅助核算', configTemp['辅助核算'])
}
// 检查是否有科目余额数据
async function checkHasYuData(params) {
  const rsp = await taskWindow.request({
    url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/account/zm/selectZmKmye',
    data: params,
    method: 'post'
  })
  console.log('科目余额数据状态------', rsp.data?.message)
  // 此处返回结果为加密字符串，通过字符串长度判断是否有数据
  return rsp.data?.result?.length > 50
}
// 导出余额表
async function exportKeMuYuE({
  fromPeriod,
  toPeriod,
  bzid
}) {
  // 通过evaluate函数在浏览器中执行JavaScript代码
  const pageGlobalData = await taskWindow.page.evaluate(() => {
    // 在这里可以编写你的 JavaScript 代码来获取页面中的对象
    // 例如，获取标题
    return window.YZF.GlobalData;
  });
  // console.log(pageGlobalData)
  // const rows = $('#kmyeb_datagrid').datagrid('getRows')
  // if (rows.length === 0) {
  //   YZF.Public.tips({content:'暂无数据', type:2})
  //   return 
  // }

  const currencyName = bzList.find(item => item.bzid === bzid)?.bzjc
  // 账期区间
  const qsnf = fromPeriod.substring(0, 4)
  const qsyf = fromPeriod.substring(4) * 1
  const kjnd = fromPeriod.substring(0, 4)
  const kjqj = fromPeriod.substring(4) * 1
  const jsnf = toPeriod.substring(0, 4)
  const jsyf = toPeriod.substring(4) * 1
  // 企业名称
  const qymc = pageGlobalData.QyData.qymc
  // 企业id
  const qyid = pageGlobalData.QyData.qyid
  // 用户id
  const userId = pageGlobalData.UserData.yhid

  const zq = `${qsnf}年${qsyf}月-${jsnf}年${jsyf}月`
  // 是否显示本年累计
  const showTotalOfYear = true

  const data = {
    // 根据币种选择模板
    modelName: bzid === null || bzid === -1 || bzid === undefined || bzid === 0 ? 'kmye.xls' : 'kmye-wb.xls',
    fileName: qymc + '_' + zq + '_科目余额',
    yhid: pageGlobalData.QyData.qyid,
    excelTypeEnum: 'XLS',
    // 是否显示本年累计 0：显示 1：不显示
    displayAccountItemDetail:  0,
    dataMap: {
      company: qymc,
      zq: zq,
    },
    // TODO: 兼容性
    bzid: bzid === -1 || bzid === null || bzid === undefined ? 0 : bzid,
    // TODO: 兼容性
    sfzhbwb: bzid === null || bzid === -1 || bzid === undefined ? true : false,
  }

  const supplyParam = {
    // 账套代码
    ztdm: pageGlobalData.ztxxData.ztxx.ztdm,
    // 会计期间
    startKjnd: qsnf,
    startKjqj: qsyf,
    endKjnd: jsnf,
    endKjqj: jsyf,
    // 起始科目
    qskmBm: '',
    // 起始科目层级
    // TODO: 兼容性
    qskmcj: '1',
    // 截至科目
    jzkmBm: '',
    // 截至科目层级
    // TODO: 兼容性
    jzkmcj: String(pageGlobalData.ztxxData.ztxx.kKmcj),
    // 币别
    // TODO: 兼容性
    bzid: bzid === -1 || bzid === null || bzid === undefined ? 0 : bzid,
    // 隐藏无本期发生额
    wfsebxs: false,
    // 隐藏无本年累计发生额
    wbnljbxs: false,
    // 隐藏无期末余额
    wyebxs: false,
    // 展示末级一致科目
    showSameKmmc: false,
    // 展示期末余额为负数科目
    showYeFs: false,
    // 显示核算项目明细
    sfxsfzhs: true,
    // 是否综合本位币
    // TODO: 兼容性
    sfzhbwb: bzid === null || bzid === -1 || bzid === undefined ? true : false,
    version: "2.0",
    searchKey: ''
  }

  if(!(await checkHasYuData(supplyParam))) {
    console.log(bzid + '   此币种无数据')
    return 
  }

  let fileName
  if (qsnf === jsnf && qsyf === jsyf) {
    fileName = `${qymc}_${jsnf}年第${jsyf}期_科目余额${bzid === 0 ? '(人民币)' : ''}.xls`
  } else {
    fileName = `${qymc}_${qsnf}年第${qsyf}期-${jsnf}年第${jsyf}期_科目余额.xls`
  }

  const param = {
    qyid,
    userId,
    qymc,
    kjnd: kjnd,
    kjqj: kjqj,
    ztdm: ztdm,
    fileType: 'EXCEL',
    moduleType: 'TASK_CENTER_ZHANGMU',
    reportType: 'LEDGER',
    moduleName: '账簿财务处理',
    type: 'kmye',
    fileName,
    taskCenterCommonDto:{
        kjnd: kjnd,
        kjqj: kjqj,
        qsnf,
        qsyf,
        jsnf,
        jsyf
    },
    supplyParam: JSON.stringify(supplyParam),
    jxlsExcelDTO: JSON.stringify(data)
  }
  const exportTaskRsp = await taskWindow.request({
    url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/account/TaskCenter/export',
    data: param,
    method: 'post',
    contentType: 'application/json;charset=UTF-8'
  })
  if(exportTaskRsp.data.code === '0') {
    await downloadFileByTaskId(exportTaskRsp.data.result[0], !data.sfzhbwb ? '科目余额表(外币)' : '科目余额表', !data.sfzhbwb ? {currencyName, period: fromPeriod} : {period: fromPeriod})
  } else {
    console.error('余额表导出报错', exportTaskRsp.data.message)
  }

}
// 检查是否有科目余额数据
async function checkHasShuLiangYuData(params) {
  const rsp = await taskWindow.request({
    url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/account/zm/selectZmSlhs',
    data: params,
    method: 'post'
  })
  console.log('科目余额数据状态------', rsp.data.message)
  // 此处返回结果为加密字符串，通过字符串长度判断是否有数据
  return rsp.data?.result?.length > 50
}

// 导出数量金额余额表 
async function exportShuLiangYuE({
  fromPeriod,
  toPeriod,
  bzid
}) {
  // 通过evaluate函数在浏览器中执行JavaScript代码
  const pageGlobalData = await taskWindow.page.evaluate(() => {
    // 在这里可以编写你的 JavaScript 代码来获取页面中的对象
    // 例如，获取标题
    return window.YZF.GlobalData;
  });

  // 账期区间
  const qsnf = fromPeriod.substring(0, 4)
  const qsyf = fromPeriod.substring(4) * 1
  const kjnd = fromPeriod.substring(0, 4)
  const kjqj = fromPeriod.substring(4) * 1
  const jsnf = toPeriod.substring(0, 4)
  const jsyf = toPeriod.substring(4) * 1
  // 企业名称
  const qymc = pageGlobalData.QyData.qymc
  // 企业id
  const qyid = pageGlobalData.QyData.qyid
  // 用户id
  const userId = pageGlobalData.UserData.yhid

  const zq = `${qsnf}年${qsyf}月-${jsnf}年${jsyf}月`

  const data = {
    modelName: "slkmye.xls",
    fileName: qymc + '_' + zq + '_科目余额',
    yhid: pageGlobalData.QyData.qyid,
    excelTypeEnum: 'XLS',
    // 是否显示本年累计 0：显示 1：不显示
    displayAccountItemDetail: 0,
    dataMap: {
      company: qymc,
      zq: zq,
    },
  }

  const supplyParam = {
    // 账套代码
    ztdm: pageGlobalData.ztxxData.ztxx.ztdm,
    // 会计期间
    startKjnd: qsnf,
    startKjqj: qsyf,
    endKjnd: jsnf,
    endKjqj: jsyf,
    // 起始科目
    qskmBm: '',
    // 起始科目层级
    // TODO: 兼容性
    qskmcj: '1',
    // 截至科目
    jzkmBm: '',
    // 截至科目层级
    // TODO: 兼容性
    jzkmcj: String(pageGlobalData.ztxxData.ztxx.kKmcj),
    // 币别
    // TODO: 兼容性
    bzid: 0,
    // 隐藏无本期发生额
    wfsebxs: false,
    // 隐藏无本年累计发生额
    wbnljbxs: false,
    // 隐藏无期末余额
    wyebxs: false,
    // 展示末级一致科目
    showSameKmmc: false,
    // 展示期末余额为负数科目
    showYeFs: false,
    // 显示核算项目明细
    sfxsfzhs: true,
    // 是否综合本位币
    // TODO: 兼容性
    sfzhbwb: bzid === null || bzid === -1 || bzid === undefined ? true : false,
    version: "2.0",
    searchKey: '',
    // 单价保留两位小数
    djMxXsWs: 2,
  }
  

  if(!(await checkHasShuLiangYuData(supplyParam))) {
    console.log(bzid + '   此币种无数据')
    return 
  }

  let fileName
      if (qsnf === jsnf && qsyf === jsyf) {
        fileName = `${qymc}_${jsnf}年第${jsyf}期_数量金额科目余额.xls`
      } else {
        fileName = `${qymc}_${qsnf}年第${qsyf}期-${jsnf}年第${jsyf}期_数量金额科目余额.xls`
      }

  const param = {
    qyid,
    userId,
    qymc,
    kjnd: kjnd,
    kjqj: kjqj,
    ztdm: ztdm,
    fileType: 'EXCEL',
    moduleType: 'TASK_CENTER_ZHANGMU',
    reportType: 'LEDGER',
    moduleName: '账簿财务处理',
    type: 'sljekmyeb',
    fileName,
    taskCenterCommonDto:{
        kjnd: kjnd,
        kjqj: kjqj,
        qsnf,
        qsyf,
        jsnf,
        jsyf
    },
    supplyParam: JSON.stringify(supplyParam),
    jxlsExcelDTO: JSON.stringify(data)
  }
  console.log(param)
  const exportTaskRsp = await taskWindow.request({
    url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/account/TaskCenter/export',
    data: param,
    method: 'post',
    contentType: 'application/json;charset=UTF-8'
  })
  console.log(exportTaskRsp.data)
  if(exportTaskRsp.data.code === '0') {
    await downloadFileByTaskId(exportTaskRsp.data.result[0], '数量金额科目余额表', {period: fromPeriod})
  } else {
    console.error('数量金额科目余额表导出报错', exportTaskRsp.data.message)
  }
}
// 导出凭证
const exportVoucher = async ({
  kjnd,
  kjqj,
  voucherFromPeriod,
  voucherToPeriod,
}) => {
  // 通过evaluate函数在浏览器中执行JavaScript代码
  const pageGlobalData = await taskWindow.page.evaluate(() => {
    // 在这里可以编写你的 JavaScript 代码来获取页面中的对象
    // 例如，获取标题
    return window.YZF.GlobalData;
  });
  // 企业名称
  const qymc = pageGlobalData.QyData.qymc
  // 企业id
  const qyid = pageGlobalData.QyData.qyid
  // 用户id
  const userId = pageGlobalData.UserData.yhid
   // 账期区间
  const qsnf = voucherFromPeriod.substring(0, 4)
  const qsyf = voucherFromPeriod.substring(4) * 1
  const jsnf = voucherToPeriod.substring(0, 4)
  const jsyf = voucherToPeriod.substring(4) * 1
  const supplyParam ={
    "companyName": qymc,
    "yhid": userId,
    "ztdm": ztdm,
    kjnd,
    kjqj,
    kjndStart: qsnf,
    kjqjStart: qsyf,
    kjndEnd: jsnf,
    kjqjEnd: jsyf,
    "kmId": "",
    "lshStart": "",
    "lshEnd": "",
    "jeStart": "",
    "jeEnd": "",
    "zy": "",
    "zt": "",
    "yhjc": "",
    "page": 1,
    "rows": 99999,
    "yhzhId": null
}
let file
  if (qsnf === jsnf && qsyf === jsyf) {
    file = `${qymc}_${jsnf}年第${jsyf}期_凭证.xls`
  } else {
    file = `${qymc}_${qsnf}年第${qsyf}期-${jsnf}年第${jsyf}期_凭证.xls`
  }
  const param = {
    qyid: pageGlobalData.QyData.qyid,
    userId: pageGlobalData.UserData.yhid,
    qymc: pageGlobalData.QyData.qymc,
    kjnd,
    kjqj,
    ztdm,
    fileType: 'EXCEL',
    moduleType: 'TASK_CENTER_ZHANGMU',
    reportType: 'LEDGER',
    moduleName: '账簿财务处理',
    type: 'pz',
    fileName: file,
    supplyParam: JSON.stringify(supplyParam),
    jxlsExcelDTO: "{}"
  }
  const exportTaskRsp = await taskWindow.request({
    url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/account/TaskCenter/export',
    data: param,
    method: 'post',
    contentType: 'application/json;charset=UTF-8'
  })
  console.log(exportTaskRsp.data)
  if(exportTaskRsp.data.code === '0') {
    await downloadFileByTaskId(exportTaskRsp.data.result[0], '凭证表')
  } else {
    console.error('凭证导出报错', exportTaskRsp.data.message)
  }
}
// 导出资产   type： 0-固定资产   1-无形资产
const exportAssets = async ({kjnd, kjqj, type}) => {
  // 通过evaluate函数在浏览器中执行JavaScript代码
  const pageGlobalData = await taskWindow.page.evaluate(() => {
    // 在这里可以编写你的 JavaScript 代码来获取页面中的对象
    // 例如，获取标题
    return window.YZF.GlobalData;
  });
  // 企业名称
  const qymc = pageGlobalData.QyData.qymc
  // 企业id
  const qyid = pageGlobalData.QyData.qyid
  // 用户id
  const userId = pageGlobalData.UserData.yhid
  const fileName = `${qymc}_${kjnd}年第${kjqj}期_${type === 0 ? '固定' : '无形'}资产表.xls`
  const param = {
    qyid,
    userId,
    qymc,
    kjnd: kjnd,
    kjqj: kjqj,
    ztdm: ztdm,
    "fileType": "EXCEL",
    "moduleType": "TASK_CENTER_ZHANGMU",
    "reportType": "LEDGER",
    "moduleName": "账簿清单处理",
    "fileName": fileName,
    "type": "taskCenterAssets",
    "taskCenterCommonDto": {
        "type": type
    }
  }
  const exportTaskRsp = await taskWindow.request({
    url: 'https://daizhang99.yunzhangfang.com/api/fintax/application/dz/account/TaskCenter/export',
    data: param,
    method: 'post',
    contentType: 'application/json;charset=UTF-8'
  })
  console.log(exportTaskRsp.data)
  if(exportTaskRsp.data.code === '0') {
    await downloadFileByTaskId(exportTaskRsp.data.result[0], `${type === 0 ? '固定' : '无形'}资产表`)
  } else {
    console.error('资产导出报错', exportTaskRsp.data.message)
  }
}

async function downloadFileByTaskId(taskid, type, assignConfig = {}) {
  await executeAsyncTask(taskid, '', async (result) => {
    console.log('文件下载信息', result)
    await taskWindow.downloadFile(result.url, {}, result.fileName + '', type, {...configTemp[type], ...assignConfig})
  })
 console.log('文件下载完成');
}

taskWindow.setTask(async(task) => {
  console.log(task)
  const {period,  voucherToPeriod, isMoveVoucher, isAllVoucher} = task
  
  
  const pageGlobalData = await taskWindow.page.evaluate(() => {
    // 在这里可以编写你的 JavaScript 代码来获取页面中的对象
    // 例如，获取标题
    return window.YZF.GlobalData;
  });
  const startDate = isAllVoucher ? String(pageGlobalData.ztxxData.ztxx.sysStartTime) : period
  const kEndTime = isMoveVoucher ? voucherToPeriod : period
  const kjnd = kEndTime.slice(0, 4);
  const kjqj = kEndTime.slice(4, 6);
  taskWindow.importConfig.setPeriod(startDate)
  try{
    

  // 导出科目表
  await exportSubject({kjnd})
  await exportAssetsCard({
    kjnd,
    kjqj,
  })
  const bzList = [null, ...Array.from(new Set(useBZList))]
  for(let i = 0; i < bzList.length; i++) {
    if(bzList[i] === 0) continue
    await exportKeMuYuE({
      fromPeriod: startDate,
      toPeriod: startDate,
      bzid: bzList[i],
    })
  }
  await exportShuLiangYuE({
    fromPeriod: startDate,
    toPeriod: startDate,
    bzid: 0,
  })
  // 凭证
  if(isMoveVoucher) {
    const endDate = voucherToPeriod
    await exportVoucher({
      kjnd,
      kjqj,
      voucherFromPeriod: startDate,
      voucherToPeriod: endDate,
    })
  }
  await exportFuZhuXinXi({
    kjnd
  })
}catch(e) {
  console.log(e, '导出过程错误')
  throw Error(e)
}
  const fileSavePath = await taskWindow.packZip(`${ztmc}-旧账文件`)
  if(fileSavePath) {
    console.log('文件打包成功，路径：', fileSavePath)
  } else {
    console.log('文件打包失败')
  }
  
  return {
    status: 'success',
    fileSavePath,
  }
})
