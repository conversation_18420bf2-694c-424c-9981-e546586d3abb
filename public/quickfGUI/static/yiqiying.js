const configTemp = {
  "科目信息表":{
      "subjectCodeColNum": 3,
      "subjectFullNameFlag": true,
      "subjectNameColNum": 2,
      "auxiliaryTypeColNum": 5,
      "auxiliaryTypeSeparator": ";",
      "unitColNum": 7,
      "currencyNameColNum": 6,
      "currencyNameSeparator": ";",
      "sheetName": "My Sheet",
      "sheetIndex": 0,
      "titleRowNum": 1,
  },
  "辅助核算":{
          "auxiliaryTypeColNum": 5,
          "auxiliaryCodeColNum": 1,
          "auxiliaryNameColNum": 2,
          "sheetName": "My Sheet", 
          "sheetIndex": 0,
          "titleRowNum": 1,
      },
  "科目余额表":{
          "subjectCodeColNum": 1,
          "subjectNameColNum": 2,
          "auxiliarySource": 1,
          "auxiliaryNameSeparator": "<$辅助核算编码$ $辅助核算名称$>",
          "currencyName": "CNY" ,
          "endingDebitAmountColNum": 9,
          "endingCreditAmountColNum": 10,
          "yearDebitAmountColNum": 7,
          "yearCreditAmountColNum": 8,
          "sheetName": "科目余额表", 
          "sheetIndex": 0,
          "titleRowNum": 4,
      },
      '科目余额表(外币)':{
          "subjectCodeColNum": 1,
          "subjectNameColNum": 2,
          "auxiliarySource": 1,
          "auxiliaryNameSeparator": "<$辅助核算编码$ $辅助核算名称$>",
          "currencyName": "" ,
          "endingDebitAmountColNum": 16,
          "endingCreditAmountColNum": 18,
          "yearDebitAmountColNum": 12,
          "yearCreditAmountColNum": 14,
          "endingDebitAmountOColNum": 15,
          "endingCreditAmountOColNum": 17,
          "yearDebitAmountOColNum": 11,
          "yearCreditAmountOColNum": 13,
          "sheetName": "Sheet1", 
          "sheetIndex": 0,
          "titleRowNum": 4,
      },
      '数量金额科目余额表': {
          "subjectCodeColNum": 1,
          "subjectNameColNum": 2,
          "auxiliaryNameSeparator": "<$辅助核算编码$ $辅助核算名称$>",
          "currencyName": "CNY",
          "unitColNum": 3,
          "priceColNum": 18,
          "endingDirectionColNum": 16,
          "endingAmountColNum": 19,
          "yearDebitAmountColNum": 13,
          "yearCreditAmountColNum": 15,
          "endingQtyColNum": 17,
          "yearDebitQtyColNum": 12,
          "yearCreditQtyColNum": 14,
          "auxiliarySource": 1,
          "sheetName": "数量科目余额表",
          "sheetIndex": 0,
          "titleRowNum": 4,
      },
  "凭证表":{
          "voucherDateColNum": 1 ,
          "voucherDateFormat": "yyyy-MM-dd" ,
          "voucherNumColNum": 2 ,
          "voucherAbstractColNum": 3 ,
          "subjectCodeNameColNum": 4 ,
          "subjectCodeNameSeparator": "`$^" ,
          "auxiliarySource": 1 ,
          "auxiliaryNameSeparator": "<$辅助核算编码$ $辅助核算名称$>" ,
          "currencyNameColNum": 7 ,
          "debitAmountColNum": 10 ,
          "creditAmountColNum": 11 ,
          "qtyColNum": 5 ,
          "amountOColNum": 8 ,
          "exchangeRateColNum": 9 ,
          "sheetName": "凭证列表" ,
          "sheetIndex": 0 ,
          "titleRowNum": 3 ,
      },
      "资产卡片": {
        "departmentOne2more": false,
        "assetSubjectFormatter": 2,
        "deprSubjectAndRateSameCol": false,
        "deprLimitCycle": 2,
        "assetCodeColNum": "2",
        "assetCategoryColNum": "1",
        "assetNameColNum": "3",
        "modelSizeColNum": "",
        "assetQtyColNum": "",
        "businessTimeColNum": "",
        "usedDateColNum": "6",
        "dateFormat": "yyyy-MM-dd",
        "departmentNameColNum": "4",
        "originalValueSubjectColNum": "17",
        "deprSubjectColNum": "16",
        "deprSubjectRateColNum": "",
        "deprSubjectRateSeparator": "",
        "disposeSubjectColNum": "",
        "accuDeprSubjectColNum": "18",
        "depreciationSubjectColNum": "",
        "assetSubjectNameSeparator": "_",
        "addMethodColNum": "",
        "deprMethodColNum": "15",
        "deprLimitColNum": "7",
        "surplusValueRateColNum": "",
        "originalValueColNum": "8",
        "accuDeprColNum": "11",
        "depreciationColNum": "",
        "storagePlaceColNum": "",
        "userColNum": "",
        "remarkColNum": "",
        "statusColNum": "",
        "assetAddedVoucherColNum": "",
        "assetDisposeVoucherColNum": "",
        "sheetName": "My Sheet",
        "sheetIndex": 0,
        "titleRowNum": "1",
      }
}

let companyInfo = {
  "systemAccountId": 1,
  "systemAccountTypeCode": 100,
  "systemAccountName": null,
  "customerId": *************,
  "customerName": "广州多尔玛电器有限公司",
  "customerShortName": "广州多尔玛电器有限公司",
  "accountSetId": ********,
  "createPeriod": "202102",
  "currentPeriod": "202401",
  "lastPeriod": "202401",
  "newlyClosedPeriod": "202312",
  "makerName": "188****0383",
  "reclassifyFlag": "N",
  "bizVersion": null,
  "incomeTaxCollectionWay": "a",
  "remark": null,
  "appKey": "yqdz",
  "terminalType": "client",
  "authEnable": true,
  "authCode": "SUCCESS",
  "companyId": ************,
  "locationCode": "440000",
  "taxRegionCode": "440100",
  "originalRegionCode": "440000",
  "authLocationCode": "440000",
  "addedTaxType": "2",
  "taxNo": "91440101MA5CQDFH9X",
  "financeCheckMaxPeriod": null,
  "enableFinanceCheckAndOnClosedForSecurity": null,
  "isInventoryPro": false,
  "isSalaryPro": true,
  "enableExpense": true,
  "enableInvoicePro": false,
  "signStr": null,
  "maxDepreciationPeriod": null,
  "enableAssetPro": false,
  "token": "1HQRN38UO8IU734D5C0A00000A4A5FCE00",
  "maxHistoryClosedPeriod": "202312",
  "accountId": "20180918010532030018010000004",
  "fondsId": null,
  "key": "bklXV2dmaUZrdWZhNmxkUg==",
  "iv": "Wlg4Q2I4ZmhIcEJDMzBCVg==",
  "titleLevelPattern": "4-2-2-2-2-2",
  "bigAssistantTitleBalanceCustomer": false
}

let requestHeader = {
  host: 'www.17dz.com',
  Referer: 'https://www.17dz.com/',
  Accountsettoken: '',
  accept: 'application/json, text/plain, */*',
  'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
  'cache-control': 'no-cache',
  pragma: 'no-cache',
  'sec-ch-ua': '"Google Chrome";v="123", "Not:A-Brand";v="8", "Chromium";v="123"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"macOS"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
}
await taskWindow.loadURL('https://www.17dz.com/home/<USER>')



async function loadCompanyInfo() {
  const rsp = await taskWindow.request({
    url: 'https://www.17dz.com/financeweb/finance/account/session/accountSet',
    method: 'get',
    headers: requestHeader
  })
  console.log(rsp.data)
  companyInfo = rsp.data.body
  requestHeader.Accountsettoken = companyInfo.token
}
taskWindow.currentWin.webContents.setWindowOpenHandler(async details => {
  // shell.openExternal(details.url)
  console.log(details.url)
  if(details.url.indexOf('/finance-web-pc/index.html') !== -1) {
    await taskWindow.loadURL(details.url)
  requestHeader.Referer = details.url
  await loadCompanyInfo()
taskWindow.sendShowForm({
  companyName: companyInfo.customerName,
  lastBookClosingMonth: companyInfo.newlyClosedPeriod,
  startTime: companyInfo.createPeriod
})
  }
  return { action: 'deny' }
})
function flattenTree(treeNode, result = []) {
  if (!treeNode) {
      return result;
  }

  result.push({
      ...treeNode,
      children: undefined
      // Add other properties as needed
  });
  console.log(treeNode)
  if (treeNode.children && treeNode.children.length > 0) {
      treeNode.children.forEach(child => {
          flattenTree(child, result);
      });
  }

  return result;
}
async function exportSubject() {
  const now = new Date().getTime()
  const rsp = await taskWindow.request({
    url: 'https://www.17dz.com/financeweb/finance/customerAccountTitles/all',
    params: {
      _: now,
      customerId: companyInfo.customerId,
    },
    headers: requestHeader,
    method: 'get'
  })
  console.log(rsp.data.body.length)
  
  let data = rsp.data?.body?.reduce((result, item) => flattenTree(item, result), []) || [];
  console.log(data)
  const fzlxMap = {
    'c': '客户',
    's': '供应商',
    'p': '项目',
    'd': '部门',
    'i': '存货',
    'e': '员工'
  }
  data = data.map(item => {
    return {
      unit: item.unit,
      kmId: item.id,
      kmmc: item.name,
      kmqc: item.fullName,
      kmbm: item.code,
      kmfx: item.direction === 1 ? '借' : '贷',
      fzlx: fzlxMap[item.assistantType] || '',
      zbxx: item.fcurCode
    }
  })
  await taskWindow.downloadExcelByData(data, [
    {key: 'kmmc', header: '科目名称'},
    {key: 'kmqc', header: '科目全称'},
    {key: 'kmbm', header: '科目编码'},
    {key: 'kmfx', header: '余额方向'},
    {key: 'fzlx', header: '辅助核算'},
    {key: 'zbxx', header: '外币'},
    {key: 'unit', header: '单位'}
  ], `${companyInfo.customerName}_科目表.xlsx`, '科目信息表', configTemp['科目信息表'])
}

async function exportFuZhuXinXi() {
  const params =  {
    accountSetId: companyInfo.accountSetId,
    customerId: companyInfo.customerId,
    key: '',
    pageNo: 0,
    pageSize: 9999
  }
  const fzxx = [{url: 'https://www.17dz.com/financeweb/finance/clients/list/page', type: '客户'},
  {url: 'https://www.17dz.com/financeweb/finance/suppliers/list/page', type: '供应商'},
  {url: 'https://www.17dz.com/financeweb/finance/inventories/list/page', type: '存货'},
  {url: 'https://www.17dz.com/financeweb/finance/projects/list/page', type: '项目'},
  {url: 'https://www.17dz.com/financeweb/finance/department/list/page', type: '部门'},
  {url: 'https://www.17dz.com/financeweb/finance/person/list/page', type: '员工'},
  ]
  const results = await Promise.all(fzxx.map(async (item) => {
    const rsp = await taskWindow.request({
      url: item.url,
      params,
      headers
    })
    return {
      data: rsp.data.body,
      type: item.type
    }
  }))

}
async function exportAssetsCard(period) {
 const rsp = await taskWindow.request({
  url: 'https://www.17dz.com/xqy-print/print/erp/fixedassets/export',
  params: {
    key: '',
    state: '',
    depreciateMonth: period
  },
  headers: requestHeader
 })
 if(rsp.data.body.success) {
  await taskWindow.downloadFile(rsp.data.body.entity, {}, '资产卡片', '资产卡片', {Cookie: ''})
 } else {
  taskWindow.logger.log('资产卡片导出失败', rsp.data.head.description)
 }
}


async function exportKeMuYuE({
  fromPeriod,
  toPeriod,
  fcurCode = ''
}) {
  const params = {
    "beginPeriod": fromPeriod,
    "endPeriod": toPeriod,
    "beginTitleCode": "",
    "endTitleCode": "",
    "showYearAccumulated": "true",
    "assistantId": "",
    "assistantType": "",
    "showAssistant": "true",
    "firstAccountTitle": "false",
    "titleLevel": "6",
    "showEndBalance0": "true",
    "showQuantity": "false",
    "fcurCode": fcurCode,
    "integrativeFcur": "false",
    "titleName": "",
    "pageType": "NaN",
    "printDate": "",
    "_rnd_": Math.random()
  }
  const rsp = await taskWindow.request({
    url: 'https://www.17dz.com/xqy-print/print/finance/accountBalanceSheet/export',
    params,
    headers: requestHeader,
    method: 'get'
  })
  const type = fcurCode ? '科目余额表(外币)' : '科目余额表'
  console.log(rsp.data)
  if(rsp.data.head.status === 'Y') {
    await taskWindow.downloadFile(rsp.data.body, {}, type, type, {Cookie: ''})
   } else {
    taskWindow.logger.log(`${type}[${fcurCode}]`, rsp.data.head.msg)
   }
}
async function exportShuLiangKeMuYuE({
  fromPeriod,
  toPeriod,
}) {
  const params = {
    "beginPeriod": fromPeriod,
    "endPeriod": toPeriod,
    "beginTitleCode": "",
    "endTitleCode": "",
    "showYearAccumulated": "true",
    "assistantId": "",
    "assistantType": "",
    "showAssistant": "true",
    "firstAccountTitle": "false",
    "titleLevel": "6",
    "showEndBalance0": "true",
    "showQuantity": "true",
    "fcurCode": '',
    "integrativeFcur": "false",
    "titleName": "",
    "pageType": "NaN",
    "printDate": "",
    "_rnd_": Math.random()
  }
  const rsp = await taskWindow.request({
    url: 'https://www.17dz.com/xqy-print/print/finance/accountBalanceSheet/export',
    params,
    headers: requestHeader,
    method: 'get'
  })
  console.log(rsp.data)
  if(rsp.data.head.status === 'Y') {
    await taskWindow.downloadFile(rsp.data.body.entity, {}, '数量金额科目余额表', '数量金额科目余额表', {Cookie: ''})
   } else {
    taskWindow.logger.log('数量金额科目余额表', rsp.data.head.msg)
   }
}
taskWindow.setTask(async(task) => {
  console.log(task)
  try{
    const {period, voucherFromPeriod, voucherToPeriod, isMoveVoucher, isAllVoucher} = task;

    await exportSubject()
    await exportAssetsCard(period)
    await exportKeMuYuE({
      toPeriod: period,
      fromPeriod: period,
    })
    await exportShuLiangKeMuYuE({
      toPeriod: period,
      fromPeriod: period,
    })
  } catch(e) {
    console.log(e)
    taskWindow.logger.log('导出任务报错', e)
    throw Error(e)
  }
  
  console.log(taskWindow.files)
  const fileSavePath = await taskWindow.packZip(`${companyInfo.customerName}-旧账文件`)
  if(fileSavePath) {
    console.log('文件打包成功，路径：', fileSavePath)
  } else {
    console.log('文件打包失败')
  }
  
  return {
    status: 'success',
    fileSavePath,
  }
})