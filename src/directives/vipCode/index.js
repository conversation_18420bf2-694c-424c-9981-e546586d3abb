import store from '@/store';
import { setDisabled } from '@/utils/dom';

function removeElement(el) {
  if (el.parentNode) el.parentNode.removeChild(el);
}
function checkShow(el, binding) {
  const { value, modifiers } = binding;
  const vipCodeBkCom = store.getters['user/vipCodeBkCom'];

  if (value && value instanceof Array) {
    if (value.length > 0) {
      const hasVipCode = value.some((item) => vipCodeBkCom.includes(item));
      if (!hasVipCode) {
        // 禁用模式
        if (modifiers.disabled) {
          setDisabled(el);
        } else {
          removeElement(el);
        }
      }
    }
  } else {
    throw new Error('need vipCode! Like v-vipCode="[\'admin\',\'editor\']"');
  }
}

const install = function install(Vue) {
  Vue.directive('vipCode', {
    inserted(el, binding, vnode) {
      checkShow(el, binding, vnode);
    },
    update(el, binding, vnode) {
      checkShow(el, binding, vnode);
    },
  });
};

export default { install };
