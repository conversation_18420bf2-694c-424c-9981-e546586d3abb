import store from '@/store';
import { get } from 'lodash-es';
import { setDisabled } from '@/utils/dom';

function removeElement(el) {
  if (el.parentNode) el.parentNode.removeChild(el);
}
function checkShow(el, binding) {
  const { value, modifiers } = binding;
  const { appConfig } = store.state.app;

  if (value) {
      const state = get(appConfig, value);
      if (state === 'hide') {
        // 禁用模式
        if (modifiers.disabled) {
          setDisabled(el);
        } else {
          removeElement(el);
        }
      }
  } else {
    throw new Error('need config PATH! Like v-showWithConfig="home.quickAction"');
  }
}

const install = function install(Vue) {
  Vue.directive('showWithConfig', {
    inserted(el, binding, vnode) {
      checkShow(el, binding, vnode);
    },
    update(el, binding, vnode) {
      checkShow(el, binding, vnode);
    },
  });
};

export default { install };
