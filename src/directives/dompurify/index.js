import DOMPurify from 'dompurify';

// 处理 HTML 净化的核心函数
const sanitizeHtml = (value, config = {}) => {
  if (value === null || value === undefined) return '';
  return DOMPurify.sanitize(value, typeof config === 'object' ? config : {});
};

// 错误处理包装函数
const handleSanitizeError = (fn) => (...args) => {
    try {
      return fn(...args);
    } catch (error) {
      console.error('DOMPurify sanitize error:', error);
      return '';
    }
  };

// 安全的 HTML 处理函数
const processSafeHtml = handleSanitizeError((el, value, config) => {
  const sanitizedHtml = sanitizeHtml(value, config);
  el.innerHTML = sanitizedHtml;
});

const directive = {
  bind(el, binding) {
    const { value, arg = {} } = binding;
    processSafeHtml(el, value, arg);
  },

  update(el, binding) {
    if (binding.value !== binding.oldValue) {
      const { value, arg = {} } = binding;
      processSafeHtml(el, value, arg);
    }
  },
};

const install = function install(Vue) {
  Vue.directive('dompurify-html', directive);
};

export default { install };
