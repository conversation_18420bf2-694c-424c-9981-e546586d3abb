import store from '@/store';
import { setDisabled } from '@/utils/dom';

function removeElement(el) {
  if (el.parentNode) el.parentNode.removeChild(el);
}
function checkPermission(el, binding) {
  const { value, modifiers } = binding;
  const hasFontentPermission = store.getters['user/hasFontentPermission'];

  if (value && value instanceof Array) {
    if (value.length > 0) {
      const permissionRoles = value;

      const hasPermission = hasFontentPermission(permissionRoles, modifiers.BK);
      if (!hasPermission) {
        // 禁用模式
        if (modifiers.disabled) {
          setDisabled(el);
        } else {
          removeElement(el);
        }
      }
    }
  } else {
    throw new Error('need roles! Like v-permission="[\'admin\',\'editor\']"');
  }
}

export default {
  inserted(el, binding, vnode) {
    checkPermission(el, binding, vnode);
  },
  update(el, binding, vnode) {
    checkPermission(el, binding, vnode);
  },
};
