import store from '@/store';
import router from 'RouteConfig/route';

function checkPermissionWithPage(el, binding) {
  const { value } = binding;
  const hasFontentPermission = store.getters['user/hasFontentPermission'];

  if (value && value instanceof Array) {
    if (value.length > 0) {
      const hasPermission = value.some((pageRoute) => {
        if (typeof pageRoute === 'object') {
          const route = router.match(pageRoute);
          if (route.path === '/notFound' || route.matched.length === 0) {
            console.error(`PermissionWithPage 当前路由找不到${JSON.stringify(pageRoute)}`);
            return false;
          }
          const isBK = route.path.indexOf('/controlCenter') === 0;
          if (!route?.meta.frontendSeqCode) return true;
          return hasFontentPermission([`${route?.meta.frontendSeqCode}-chaxun`], isBK);
        }
        console.error(`PermissionWithPage 必须传入一个路由对象，用于查找对应的路由${pageRoute}`);
        return true;
      });
      if (!hasPermission) {
       if (el.parentNode) el.parentNode.removeChild(el);
      }
    }
  } else {
    throw new Error('need roles! Like v-permissionWithPage="[{name: \'inputTax\'}]"');
  }
}

export default {
  inserted(el, binding) {
    checkPermissionWithPage(el, binding);
  },
  update(el, binding) {
    checkPermissionWithPage(el, binding);
  },
};
