import Vue from 'vue';
import store from '@/store/index';
import isEmpty from 'lodash-es/isEmpty';
import intersection from 'lodash-es/intersection';
import dayjs from 'dayjs';
import permission from './permission';
import showWithConfig from './showWithConfig';
import vipCode from './vipCode';
import dompurify from './dompurify';

Vue.use(permission);
Vue.use(showWithConfig);
Vue.use(vipCode);
Vue.use(dompurify);

// 注册一个全局自定义指令 v-focus
Vue.directive('focus', {
  // 当绑定元素插入到 DOM 中。
  inserted(el) {
    // 聚焦元素
    el.querySelector('input').focus();
  },
});
// 注册一个全局滚动指令 v-scroll
Vue.directive('scroll', {
  bind(el, binding) {
    const $wrap = $('.el-scrollbar__wrap', el);
    const always = function always() {
      this.scrollTop = this.dataset.scrollTop || 0;
      this.dataset.loadDisabled = 'false';
    }.bind($wrap[0]);
    $wrap.scroll(function scroll() {
      if (
        el.dataset.allLoaded === 'false'
        && this.dataset.loadDisabled !== 'true'
        && Math.ceil(this.scrollTop + this.clientHeight) >= this.scrollHeight
      ) {
        this.dataset.loadDisabled = 'true';
        binding.value().then(always, always);
        this.dataset.scrollTop = this.scrollTop;
      }
    });
  },
  unbind(el) {
    const $wrap = $('.el-scrollbar__wrap', el);
    $wrap.off('scroll');
  },
});
// 注册一个全局滚动指令 v-scroll, pl-table使用，暂时这样
Vue.directive('scroll-pl', {
  bind(el, binding) {
    const $wrap = $('.el-table__body-wrapper', el);
    const always = function always() {
      this.scrollTop = this.dataset.scrollTop || 0;
      this.dataset.loadDisabled = 'false';
    }.bind($wrap[0]);
    $wrap.scroll(function scroll() {
      if (
        el.dataset.allLoaded === 'false'
        && this.dataset.loadDisabled !== 'true'
        && Math.ceil(this.scrollTop + this.clientHeight) >= this.scrollHeight
      ) {
        this.dataset.loadDisabled = 'true';
        binding.value().then(always, always);
        this.dataset.scrollTop = this.scrollTop;
      }
    });
  },
  unbind(el) {
    const $wrap = $('.el-table__body-wrapper', el);
    $wrap.off('scroll');
  },
});

function upDownFocus(e) {
  console.log(e);
  const { keyCode } = e;
  if (keyCode !== 40 && keyCode !== 38) {
    return;
  }
  const el = e.target;
  const parentRow = $(el).parents('tr');
  const foucsRow = keyCode === 40 ? parentRow.next() : parentRow.prev();
  if (foucsRow.length !== 0) {
    const inputIndex = $(el)
      .parents('td')
      .index();
    const foucsTd = foucsRow.children('td')[inputIndex];
    $(foucsTd)
      .find('input')
      .focus();
  }
  e.preventDefault();
}
// 表格上下键切换聚焦
Vue.directive('upDownFocus', {
  // 当绑定元素插入到 DOM 中。
  inserted(el) {
    // 聚焦元素
    // console.log(el.querySelector('input'), el);
    el.querySelector('input').addEventListener('keydown', upDownFocus);
  },
  unbind(el) {
    el.querySelector('input').removeEventListener('keydown', upDownFocus);
  },
});

function changeElOrVMDisable(el, disabled) {
  if (disabled) {
    $(el).addClass('is-disabled');
  } else {
    $(el).removeClass('is-disabled');
  }
  // eslint-disable-next-line no-underscore-dangle
  if (el.__vue__) {
    // 这里会报VUE 不能修改prop值得错误，但不影响正常使用
    // eslint-disable-next-line no-underscore-dangle
    el.__vue__.disabled = disabled;
  }
  el.disabled = disabled;
}
// 结账月禁用按钮
function setElDisable(el, binding) {
  const currentAccountPeriod = binding.value;
  // 对于原本就带有禁用按钮机制的，优先采取原有机制的禁用功能
  if (currentAccountPeriod === true) {
    changeElOrVMDisable(el, true);
    return;
  }
  const { enableDataLock, companyTimeDeadline } = store.state.user.companyInfo;
  // 判断公司是否已经到期
  const isOutOfDate = store.getters.isComOutOfDate;
  // 判断账套是否为只读模式
  const { isReadOnlyCom } = store.getters;
  const lockDataDisabled = store.getters.isLockDataMonth(currentAccountPeriod);
  if ((enableDataLock === 1 && lockDataDisabled) || isOutOfDate || isReadOnlyCom) {
    changeElOrVMDisable(el, true);
  } else {
    changeElOrVMDisable(el, false);
  }
}
// 使用这个指令后不能使用组件自带的disabled属性或者原生的disabled属性
Vue.directive('lockDataDisabled', {
  bind: setElDisable,
  update: setElDisable,
});

// 在select 和 级联选择器组件里添加按钮
Vue.directive('addBtn', (el, binding) => {
  // 前端权限控制
  const permissionCodes = binding.value?.permission;
  if (!isEmpty(permissionCodes)) {
    if (!store.getters['user/hasFontentPermission'](permissionCodes)) return;
  }
  // console.log(el, binding, vnode);
  if (binding.value !== binding.oldValue && binding.value) {
    const popper = el.querySelector('.el-popper');
    if (!popper) return;
    const oldAddBtn = popper.querySelector('#add-btn');
    if (oldAddBtn) {
      oldAddBtn.remove();
    }
    const addBtn = document.createElement('div');
    addBtn.className = 'add-btn';
    addBtn.id = 'add-btn';
    addBtn.style.textAlign = 'center';
    addBtn.style.color = '#fff';
    addBtn.style.background = '#66ccff';
    addBtn.style.cursor = 'pointer';
    addBtn.innerText = typeof binding.value.text === 'string' ? binding.value.text : '添加';
    addBtn.addEventListener('click', binding.value.handle);
    // 点击完后需要关闭弹窗
    addBtn.addEventListener('click', () => { popper.style.display = 'none'; });
    popper.appendChild(addBtn);
  }
});

//  旭日专用/收费专用 显示隐藏指令
Vue.directive('vipShow', (el, binding) => {
  const userAuthority = binding.value;
  const { vipCodeBkCom } = store.state.user.userInfo;
  if (isEmpty(userAuthority) || isEmpty(vipCodeBkCom)) return;
  const hasSame = intersection(userAuthority, vipCodeBkCom);
  if (isEmpty(hasSame)) {
    el.style.display = 'none';
  } else {
    el.style.display = 'block';
  }
});
