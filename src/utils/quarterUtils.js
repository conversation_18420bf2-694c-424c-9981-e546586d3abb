import dayjs from 'dayjs';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';

// 扩展 dayjs 以支持季度相关的操作
dayjs.extend(quarterOfYear);

/**
 * @typedef {object} ParsedQuarter
 * @property {number} year - 年份
 * @property {number} quarter - 季度 (1-4)
 */

/**
 * 内部辅助函数：解析并验证季度字符串。
 * @param {string} quarterString - 格式为 "YYYYQN" 的季度字符串 (例如 "2025Q1").
 * @returns {ParsedQuarter | null} 如果格式有效，则返回包含年份和季度的对象，否则返回 null。
 * @private
 */
const _parseQuarterString = (quarterString) => {
  if (typeof quarterString !== 'string') {
    console.error("Invalid input: quarterString must be a string.");
    return null;
  }
  const match = quarterString.match(/^(\d{4})Q([1-4])$/);
  if (!match) {
    console.error(`Invalid quarter string format: "${quarterString}". Expected format is "YYYYQN".`);
    return null;
  }
  return {
    year: parseInt(match[1], 10),
    quarter: parseInt(match[2], 10),
  };
};

/**
 * 将季度字符串转换为其代表的月份区间。
 * @param {string} quarterString - 格式为 "YYYYQN" 的季度字符串 (例如 "2025Q1").
 * @returns {Date[] | null} 一个包含该季度开始月份和结束月份的数组 (格式为 [Date, Date])，如果输入无效则返回 null。
 * @example
 * // returns [Date(2025, 0, 1), Date(2025, 2, 31)]
 * getMonthsInQuarter("2025Q1");
 * @example
 * // returns [Date(2024, 9, 1), Date(2024, 11, 31)]
 * getMonthsInQuarter("2024Q4");
 * @example
 * // returns null
 * getMonthsInQuarter("2025Q5");
 */
export const getMonthsInQuarter = (quarterString) => {
  const parsed = _parseQuarterString(quarterString);
  if (!parsed) {
    return null;
  }

  // dayjs().quarter(Q) 将日期设置为该季度的第一个月的第一天
  const startDate = dayjs(`${parsed.year}-01-01`).quarter(parsed.quarter);
  const endDate = startDate.endOf('quarter');

  return [startDate.toDate(), endDate.toDate()];
};

/**
 * 计算给定季度字符串增加指定季度数后的新季度字符串。
 * @param {string} quarterString - 格式为 "YYYYQN" 的季度字符串 (例如 "2025Q1").
 * @param {number} quartersToAdd - 要增加的季度数量 (必须为非负整数).
 * @returns {string | null} 计算后的新季度字符串，如果输入无效则返回 null。
 * @example
 * // returns "2025Q2"
 * addQuarters("2025Q1", 1);
 * @example
 * // returns "2026Q1" (跨年)
 * addQuarters("2025Q4", 1);
 * @example
 * // returns "2026Q1"
 * addQuarters("2025Q2", 3);
 */
export const addQuarters = (quarterString, quartersToAdd) => {
  const parsed = _parseQuarterString(quarterString);
  if (!parsed) {
    return null;
  }
  if (typeof quartersToAdd !== 'number' || !Number.isInteger(quartersToAdd) || quartersToAdd < 0) {
      console.error("Invalid input: quartersToAdd must be a non-negative integer.");
      return null;
  }


  // 创建一个代表该季度第一天的 dayjs 对象
  const startDate = dayjs(`${parsed.year}-01-01`).quarter(parsed.quarter);

  // 增加指定的季度数
  const newDate = startDate.add(quartersToAdd, 'quarter');

  // 从新日期中获取年份和季度，并格式化为 "YYYYQN"
  return `${newDate.year()}Q${newDate.quarter()}`;
};

/**
 * 计算给定季度字符串减少指定季度数后的新季度字符串。
 * @param {string} quarterString - 格式为 "YYYYQN" 的季度字符串 (例如 "2025Q1").
 * @param {number} quartersToSubtract - 要减少的季度数量 (必须为非负整数).
 * @returns {string | null} 计算后的新季度字符串，如果输入无效则返回 null。
 * @example
 * // returns "2024Q4" (跨年)
 * subtractQuarters("2025Q1", 1);
 * @example
 * // returns "2024Q2"
 * subtractQuarters("2025Q3", 5);
 * @example
 * // returns "2025Q1"
 * subtractQuarters("2025Q1", 0);
 */
export const subtractQuarters = (quarterString, quartersToSubtract) => {
  const parsed = _parseQuarterString(quarterString);
  if (!parsed) {
    return null;
  }
    if (typeof quartersToSubtract !== 'number' || !Number.isInteger(quartersToSubtract) || quartersToSubtract < 0) {
      console.error("Invalid input: quartersToSubtract must be a non-negative integer.");
      return null;
  }

  // 创建一个代表该季度第一天的 dayjs 对象
  const startDate = dayjs(`${parsed.year}-01-01`).quarter(parsed.quarter);
  console.log(startDate, startDate.subtract(quartersToSubtract, 'quarter').format('YYYY-MM'));
  // 减少指定的季度数
  const newDate = startDate.subtract(quartersToSubtract, 'quarter');

  // 从新日期中获取年份和季度，并格式化为 "YYYYQN"
  return `${newDate.year()}Q${newDate.quarter()}`;
};

/**
 * 将给定的日期格式化为 "YYYYQN" 格式的季度字符串。
 * @param {string | Date | dayjs.Dayjs} [date=new Date()] - 需要格式化的日期。可以是日期字符串、Date 对象或 dayjs 实例。如果留空，则使用当前日期。
 * @returns {string | null} 格式为 "YYYYQN" 的季度字符串 (例如 "2025Q3")，如果输入日期无效则返回 null。
 * @example
 * // 使用 Date 对象
 * // returns "2025Q3"
 * formatQuarter(new Date('2025-07-02'));
 * @example
 * // 使用日期字符串
 * // returns "2026-01"
 * formatQuarter('2026-01-15');
 * @example
 * // 使用 dayjs 实例
 * // returns "2024Q4"
 * formatQuarter(dayjs('2024-11-11'));
 * @example
 * // 不提供参数，使用当前日期 (假设今天是 2025年7月2日)
 * // returns "2025Q3"
 * formatQuarter();
 * @example
 * // 处理无效输入
 * // returns null
 * formatQuarter('not a valid date');
 */
export const formatQuarter = (date = new Date()) => {
    const dayjsInstance = dayjs(date);
  
    if (!dayjsInstance.isValid()) {
      console.error(`Invalid date input: "${date}"`);
      return null;
    }
  
    const year = dayjsInstance.year();
    const quarter = dayjsInstance.quarter();
  
    return `${year}Q${quarter}`;
  };