<!--
 * @Page: 资产部门
 * @Maintainer: 郑晓荣
 * @Modifier: 郑晓荣
 * @Description: 资产部门
-->
<template>
  <div class="flex-v">
    <edit-line-table
      :data="tableData"
      border
      ref="table"
      :rowTemplate="addRow"
      :saveRowFn="postSave"
      :validRowFn="validRowFn"
      :deleteRowFn="postDelete"
      :disabled="disabled"
      height="100%"
      style="margin-top: 16px"
      class="flex-fluid">
      <el-table-column
        label="使用部门"
        prop="departmentName"
        :modify-width="200" >
        <template slot-scope="{ row }">
          <el-input
            v-if="$refs.table.isEditingRow(row)"
            placeholder="请输入使用部门"
            :disabled="row.source === 1"
            v-model="row.departmentName" >
          </el-input>
          <div v-else>{{row.departmentName}}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="来源"
        prop="source"
        :modify-width="50"
        :formatter="sourceFormatter">
      </el-table-column>
      <el-table-column
        label="创建人"
        prop="createUserName"
        :width="300" >
      </el-table-column>
      <el-table-column
        label="创建日期"
        :width="$store.state.ColsWidth.w10"
        prop="createTime" >
        <template slot-scope="{ row }">
          <span>{{ row.createTime|date}}</span>
        </template>
      </el-table-column>
    </edit-line-table>
  </div>
</template>
<script>
import {
  getDepartment, postDepartment, putDepartment, delDepartment,
} from '../api/index';

export default {
  data() {
    return {
      tableData: [],
      addRow: {
        departmentName: '',
      },
    };
  },
  mounted() {
    this.initData();
  },
  computed: {
    disabled() {
      return !this.$store.getters['user/hasFontentPermission'](['assets-assetsDepartment-bianji']);
    },
  },
  methods: {
    async initData() {
      await getDepartment().then((res) => {
        this.tableData = res.data.data || [];
      });
    },
    validRowFn(row) {
      if (!row.departmentName) {
        this.$message.warning('请输入使用部门名称！');
        return false;
      }
      if (row?.departmentName.length > 20) {
        this.$message.warning('使用部门名称长度不得大于20');
        return false;
      }
      return true;
    },
    // 新/ 改
    async postSave(row) {
      console.log(row);
      let rsp = '';
      const { departmentId, departmentName } = row;
      const params = {
        departmentName,
        source: 2,
      };
      if (departmentId) {
        this.$set(params, 'departmentId', departmentId);
        rsp = await putDepartment(params);
      } else {
        rsp = await postDepartment(params);
      }
      this.$message.success(rsp.data.returnMessage);
      await this.initData();
    },
    // 删
    postDelete(row) {
      if (row.source === 1) {
        this.$message.warning('系统来源的部门不能删除');
        return;
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        delDepartment(row.departmentId).then((res) => {
          this.$message.success(res.data.returnMessage);
          this.initData();
        });
      });
    },
    sourceFormatter(row) {
      return ['', '系统', '用户'][row.source];
    },
  },
};
</script>
