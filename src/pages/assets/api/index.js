/*
 * @Description:
 * @version:
 * @Company: 海闻软件
 * @Author: 启旭
 * @Date: 2019-09-03 10:03:10
 * @LastEditors: 启旭
 * @LastEditTime: 2020-06-16 14:50:07
 */
import request from '@/utils/request';

/**
 * 获得资产总量信息
 * http://frontend.quickf.net/api/0399/swagger-ui.html#/fixed-assets-merge-controller/getFixedAssetsTotalListUsingGET
 * @param {Object} params
 */
export function fetchFixedAssetsTotalList(params = {}) {
  const API = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/total';
  const config = { params };
  return request.get(API, config);
}

/**
 * 查询资产类别
 * @swagger
 * http://frontend.quickf.net/api/e10399/swagger-ui.html#/fixed-assets-merge-part-controller/findFixedAssetsTypeListUsingGET
 *
 * @export
 * @param {Object} params 请求参数
 * @param {string} params.assetTypeCode 资产类型编码
 * @param {string} params.assetTypeName 资产名称
 * @param {number} params.categoryType 资产类别（0：固定资产、1：无形资产）
 */
export function fetchFixedAssetsTypes(params = { page: 1, pageSize: 9999 }) {
  const API = '/rest/proxy/account/companyAccount/merge/fixedAssetsCard/v0.1/typeList';
  const config = { params };
  return request.get(API, config);
}

/**
 * 获得资产折旧信息
 * @swagger
 * http://frontend.quickf.net/api/0399/swagger-ui.html#/fixed-assets-merge-controller/getDepreciationListUsingGET
 *
 * @export
 * @param {Object} params 查询参数
 */
export function fetchFixedAssetsDepreciation(params = {}) {
  const API = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/depreciation';
  const config = { params };
  return request.get(API, config);
}
/**
 * 查询资产折旧明细表合计
 * @swagger
 * http://frontend.quickf.net/api/0399/swagger-ui.html#/fixed-assets-merge-controller/getDepreciationListUsingGET
 *
 * @export
 * @param {Object} params 查询参数
 */
export function fetchFixedAssetsDepreciationTotal(params = {}) {
  const API = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/depreciation/total';
  const config = { params };
  return request.get(API, config);
}

/**
 * 添加资产类别(0:是不成功,非0:成功)
 * http://frontend.quickf.net/api/e10399/swagger-ui.html#/fixed-assets-merge-part-controller/insertFixedAssetsTypeUsingPOST
 */
export function createAssetsType(data) {
  const API = '/rest/proxy/account/companyAccount/merge/fixedAssetsCard/v0.1/typeAdd';
  return request.post(API, data);
}

/**
 * 修改资产类别(0:是不成功,非0:成功)
 * http://frontend.quickf.net/api/e10399/swagger-ui.html#/fixed-assets-merge-part-controller/updateFixedAssetsTypeUsingPUT
 */
export function updateAssetsType(data) {
  const API = '/rest/proxy/account/companyAccount/merge/fixedAssetsCard/v0.1/typeUpdate';
  return request.put(API, data);
}

/**
 * 获得资产处置信息和折旧合计
 * http://frontend.quickf.net/api/0399/swagger-ui.html#/fixed-assets-merge-controller/getDispostalListSummaryUsingGET
 */
export function fetchDispostalListSummary(params) {
  const API = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/dispostal/total';
  const config = { params };
  return request.get(API, config);
}

/**
 * 获得资产处置信息和折旧信息
 * http://frontend.quickf.net/api/0399/swagger-ui.html#/fixed-assets-merge-controller/getDispostalListUsingGET
 */
export function fetchDispostals(params) {
  const API = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/dispostal';
  const config = { params };
  return request.get(API, config);
}

/**
 * 根据条件查找进项发票列表
 * http://frontend.quickf.net/api/0399/swagger-ui.html#/purchase-checking-merge-controller/findInvoiceListUsingGET
 */
export function fetchInvoices(params) {
  const API = '/rest/companyAccount/merge/invoice/v1.0';
  const config = { params };
  return request.get(API, config);
}

/**
 * 查询资产卡片列表
 * http://frontend.quickf.net/api/0399/swagger-ui.html#/fixed-assets-merge-controller/findFixedAssetsCardListUsingGET
 */
export function fetchFixedAssetsCards(params) {
  const API = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/list';
  const config = { params };
  return request.get(API, config);
}
/**
 * 查询资产卡片列表-资产折旧明细账用的新接口
 */
export function fetchFixedAssetsCardsCode(params) {
  const API = '/rest/companyAccount/merge/v2.0/asset/findAssetCardDetailsOnly';
  const config = { params };
  return request.get(API, config);
}

/**
 * 删除资产信息
 * http://frontend.quickf.net/api/0399/swagger-ui.html#/fixed-assets-merge-controller/deleteFixedAssetsCardUsingDELETE
 * @param {string} fixedAssetsCardIds 资产ID 批量删除 用逗号隔开
 */
export function deleteFixedAssetsCards(fixedAssetsCardIds) {
  const API = `/rest/companyAccount/merge/fixedAssetsCard/v0.1/delete?fixedAssetsCards=${fixedAssetsCardIds}`;
  return request.delete(API);
}

/**
 * 查询资产折旧明细
 * http://frontend.quickf.net/api/0399/swagger-ui.html#/fixed-assets-merge-controller/findUsingGET
 * @param {number} accountPeriod yyyyMM
 */
export function fetchDepreciationDetails(accountPeriod, params) {
  const API = `/rest/companyAccount/merge/depreciation/v1.0/list/${accountPeriod}`;
  const config = { params };
  return request.get(API, config);
}

/**
 * 查询入账类型包含固定资产或无形资产的发票
 */
export function fetchVoucherList(params) {
  const API = '/rest/proxy/account/fixedAsset/v1.0/listInvoice';
  const config = { params };
  return request.get(API, config);
}

/**
 * 根据凭证号生成资产卡片
 */
export function createAssetCardByVoucherId(params) {
  const API = '/rest/proxy/account/fixedAsset/v1.0/insertBathByInvoiceId';
  return request.post(API, params);
}

/**
 * 生成资产类别编号
 * http://frontend.quickf.net/api/0399/swagger-ui.html#/fixed-assets-merge-controller/getFixedAssetsCodeUsingGET
 */
export function fetchFixedAssetsCode(assetTypeCode, number) {
  const API = `/rest/companyAccount/merge/fixedAssetsCard/v0.1/assetCode/${assetTypeCode}/${number}`;
  return request.get(API);
}

/**
 * 获得单类资产信息
 * http://frontend.quickf.net/api/0399/swagger-ui.html#/fixed-assets-merge-controller/getFixedAssetsCardByIdUsingGET
 * @param {number} assetId 单类资产信息ID
 */
export function fetchAssetCardById(assetId) {
  const API = `/rest/companyAccount/merge/fixedAssetsCard/v0.1/assetcardpo/${assetId}`;
  return request.get(API);
}

/**
 * 添加资产信息
 * http://frontend.quickf.net/api/0399/swagger-ui.html#/fixed-assets-merge-controller/insertFixedAssetsCardUsingPOST
 * @param {Object} params
 * @param {number} params.type 0 保存草稿 1 确认新增
 * @param {boolean} params.isCreateVoucher true 生成凭证，false 不生成凭证
 */
export function createFixedAssetsCard(params, data) {
  if (typeof data === 'object') data = JSON.stringify(data);
  const API = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/add';
  const config = {
    params,
  };
  return request.post(API, data, config);
}

/**
 * 修改资产信息
 * http://frontend.quickf.net/api/0399/swagger-ui.html#/fixed-assets-merge-controller/updateFixedAssetsCardUsingPUT
 * @param {Object} params
 * @param {number} params.type 0 保存草稿 1 确认新增
 * @param {boolean} params.isCreateVoucher true 生成凭证，false 不生成凭证
 */
export function updateFixedAssetsCard(params, data) {
  if (typeof data === 'object') data = JSON.stringify(data);
  const API = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/update';
  const config = {
    params,
  };
  return request.put(API, data, config);
}
/**
 * 添加'资产-凭证'关系
 *
 * @export
 * @param {Array} data {"relType":关联类型, "assetId":资产id, "assetItemsId":资产项目id, "voucherId":凭证id}
 * @returns
 */
export function updateAssetItemVoucherRel(data) {
  const API = '/rest/companyAccount/merge/v0.1/voucher/rel';
  return request.post(API, data);
}
/**
 * 获得资卡片信息
 * http://frontend.quickf.net/api/0399/swagger-ui.html#/fixed-assets-merge-controller/getAssetsCardDetailByIdUsingGET
 * @param {} assetItemsId 资产明细信息ID
 */
export function fetchAssetCardDetail(assetItemsId) {
  const API = `/rest/companyAccount/merge/fixedAssetsCard/v0.1/assetcarddetailpo/${assetItemsId}`;
  return request.get(API);
}

/**
 * 删除资产类别
 * @param {Object} params 删除参数
 * @param {string} params.fixedAssetsTypeIds 资产类别 id 列表，支持多个 id 用英文逗号分割
 */
export function deleteAssetType(params) {
  const API = '/rest/proxy/account/companyAccount/merge/fixedAssetsCard/v0.1/typeDelete';
  return request.delete(API, { params });
}

/**
 * 获取计算当月折旧额
 * @param {Object} data 请求参数，为资产的数据
 */
export function calcMonthlyDepreciation(data) {
  const API = '/rest/companyAccount/merge/depreciation/v1.0/monthDepreciation';
  return request.post(API, data);
}

/**
 * 预览凭证：折旧资产
 * http://frontend.quickf.net/api/0399/swagger-ui.html#/fixed-assets-merge-controller/preViewAssetDepreciationVoucherUsingPOST
 * @param {string} accountPeriod yyyyMM
 */
export function makeAssetDepreciationVoucherPreview(data) {
  if (typeof data === 'object') data = JSON.stringify(data);
  console.log('data', data);
  const API = '/rest/companyAccount/merge/depreciation/v1.0/preview/voucher';
  return request.post(API, data);
}
/**
 * 手动折旧资产
 * http://frontend.quickf.net/api/0399/swagger-ui.html#/fixed-assets-merge-controller/createAssetDepreciationUsingPOST
 */
export function createAssetDepreciation(data) {
  if (typeof data === 'object') data = JSON.stringify(data);
  const API = '/rest/companyAccount/merge/depreciation/v1.0/insert';
  return request.post(API, data);
}
// 手动折旧资产(变更)
export function putAssetDetail(list) {
  const API = '/rest/companyAccount/merge/depreciation/v1.0/asset/detail';
  return request.put(API, list);
}

/**
 * 预览凭证：处置资产
 * @param hasOutputInvoice {boolean} - 销项已生成凭证
 */
export function previewVoucherForAssetDisposal(data) {
  if (typeof data === 'object') data = JSON.stringify(data);
  const API = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/disposal/preview/voucher';
  return request.post(API, data);
}

/**
 * 添加资产处置信息
 * http://frontend.quickf.net/api/0399/swagger-ui.html#/fixed-assets-merge-controller/insertAssetDisposalUsingPOST
 * @param hasOutputInvoice {boolean} - 销项已生成凭证
 */
export function saveAssetDisposalInfo(data) {
  if (typeof data === 'object') data = JSON.stringify(data);
  const API = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/disposal';
  return request.post(API, data);
}

/**
 * 预览凭证：新增资产
 */
export function previewVoucherForAssetCreation(data) {
  if (typeof data === 'object') data = JSON.stringify(data);
  const API = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/preview/voucher';
  return request.post(API, data);
}

/**
 * 资产折旧明细账
 *
 * @export
 * @param {*} params
 * @returns
 */
export function getDepreciationAccountList(assetItemsId, params) {
  return request.get(`/rest/companyAccount/merge/v3.0/asset/depreciationDetailAccountlist/${assetItemsId}`, { params });
}
/**
 * 资产折旧明细账打印
 *
 * @export
 * @param {*} params
 * @returns
 */
export function getDepreciationAccountPrintList(params) {
  return request.get('/rest/companyAccount/merge/v3.0/asset/depreciationDetailAccountlist/pdf/export', { params });
}

// 查询资产部门列表:
export function getDepartment() {
  return request.get('/rest/companyAccount/merge/department/v1.0', { params: { page: 1, pageSize: 99999 } });
}

// 添加资产部门
// data{
// "departmentName": "营业部",
// "source": 2
// }
export function postDepartment(data) {
  return request.post('/rest/companyAccount/merge/department/v1.0', data);
}
// 修改资产部门
// data{
// "departmentId": xxxx,
// "departmentName": "营业部",
// "source": 2
// }
export function putDepartment(data) {
  return request.put('/rest/companyAccount/merge/department/v1.0', data);
}
// 删除资产部门:
export function delDepartment(departmentId) {
  return request.delete(`/rest/companyAccount/merge/department/v1.0/${departmentId}`);
}

// 查询在建工程
export function getConstructionList(params) {
  return request.get('/rest/companyAccount/merge/construction/v1.0', { params });
}

// 新增在建工程
export function postConstruction(params) {
  return request.post('/rest/companyAccount/merge/construction/v1.0', params);
}

// 修改在建工程
export function putConstruction(params) {
  return request.put('/rest/companyAccount/merge/construction/v1.0', params);
}

// 删除在建工程
export function delConstruction(constructionIds) {
  return request.delete(`/rest/companyAccount/merge/construction/v1.0?constructionIds=${constructionIds}`);
}

// 进项发票生成在建工程:生成在建工程
export function postConstructionInvoice(invoiceIds) {
  return request.post(`/rest/companyAccount/merge/construction/v1.0/from/invoice?invoiceIds=${invoiceIds}`);
}
// 进项发票生成在建工程:查询进项发票
export function getInvoiceList(params) {
  return request.get('/rest/companyAccount/merge/construction/v1.0/listInvoice', { params });
}

/**
 * 在建工程生成资产
 * @param {Object} params
 * @param {number} params.constructionIds 在建工程id
 * @param {number} params.type 0 保存草稿 1 确认新增
 * @param {boolean} params.isCreateVoucher true 生成凭证，false 不生成凭证
 */
export function postConstructionAsset(params, data) {
  const API = '/rest/companyAccount/merge/construction/v1.0/to/asset';
  const config = {
    params,
  };
  return request.post(API, data, config);
}

// 资产变更:查询历史变更记录
export function getAssetsRecordTable(params) {
  const API = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/change/history';
  return request.get(API, { params });
}
// 资产变更:查询资产信息
export function getAssetsChange(params) {
  return request.get('/rest/companyAccount/merge/fixedAssetsCard/v0.1/change', { params });
}
// 资产变更:切换变更月份
export function assetsChangeMonth(params) {
  return request.put('/rest/companyAccount/merge/fixedAssetsCard/v0.1/change/month', params);
}
// 资产变更：确认变更
export function doChangeAsset(params) {
  return request.post('/rest/companyAccount/merge/fixedAssetsCard/v0.1/change', params);
}
// 资产变更:打开修改页面
export function editGetAssetsChange(params) {
  return request.put('/rest/companyAccount/merge/fixedAssetsCard/v0.1/change/prev', params);
}
// 资产变更:修改变更
export function sureEditAssetsChange(params) {
  return request.put('/rest/companyAccount/merge/fixedAssetsCard/v0.1/change', params);
}
