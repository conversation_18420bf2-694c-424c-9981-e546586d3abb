<template>
  <div class="flex-v">
    <!-- 操作栏头部 -->
    <div class="toolbar flex-fixed">
      <el-form ref="searchForm" :model="searchForm" :inline="true" class="qf_query">
        <el-form-item>
          <more-search showSearch @search="doSearch" :search-form-data="searchForm" class="search">
            <el-input clearable slot="date" v-model="searchForm.assetName" placeholder="资产名称"></el-input>
            <el-form-item prop="assetTypeId" label="资产类别">
              <el-cascader filterable placeholder="请选择资产类别" v-model="searchForm.assetTypeId" :options="assetTypeOptions" :props="{ emitPath: false }" clearable />
            </el-form-item>
            <el-form-item prop="assetName" label="资产名称">
              <el-input v-model="searchForm.assetName" placeholder="请输入资产名称"></el-input>
            </el-form-item>
            <el-form-item prop="assetCode" label="资产编号">
              <el-input v-model="searchForm.assetCode" placeholder="请输入资产编号"></el-input>
            </el-form-item>
          </more-search>
        </el-form-item>
      </el-form>
      <div class="action-wrapper">
        <el-button type="primary" @click="changeRecordExport">导出</el-button>
      </div>
      <!-- <el-button type="primary" @click="doPrint">打印</el-button> -->
    </div>
    <!-- 表格主体 -->
    <pl-table
      use-virtual
      :row-height="30"
      class="flex-fluid"
      ref="table"
      :data="tableData"
      border
      height="100%"
      highlight-current-row
      @sort-change="sortChange"
      :cell-class-name="changeRecordClassName"
    >
      <pl-table-column label="变更日期" align="center" :width="$store.getters.columnSize.Buildin.Date" fixed prop="accountPeriod">
        <template slot-scope="{ row }">
          <span> {{ row.accountPeriod }} </span>
        </template>
      </pl-table-column>
      <pl-table-column label="变更原因" align="center" :width="$store.getters.columnSize.SizeForCount(6)" fixed prop="reason">
        <template slot-scope="{ row }">
          <span> {{ row.reason }} </span>
        </template>
      </pl-table-column>
      <pl-table-column label="资产类别" prop="assetTypeName" align="center" :width="$store.getters.columnSize.SizeForCount(7)" sortable="custom" fixed>
        <template slot-scope="{ row }">
          <span> {{ row.assetTypeName }} </span>
        </template>
      </pl-table-column>
      <pl-table-column label="资产名称" align="center" :width="$store.state.ColsWidth.w9" fixed prop="assetName">
        <template slot-scope="{ row }">
          <span> {{ row.assetName }} </span>
        </template>
      </pl-table-column>
      <pl-table-column label="资产编号" align="center" prop="assetCode" :width="$store.getters.columnSize.SizeForCount(10)" sortable="custom" fixed>
        <template slot-scope="{ row }">
          <span> {{ row.assetCode }} </span>
        </template>
      </pl-table-column>
      <pl-table-column label="变更前" align="center">
        <pl-table-column label="原值" align="right" :width="$store.state.ColsWidth.w9" class-name="prevOriginalValue" prop="prevOriginalValue">
          <template slot-scope="{ row }">
            <span> {{ row.prevOriginalValue | moneyFilter }} </span>
          </template>
        </pl-table-column>
        <pl-table-column label="折旧期限" align="right" :width="$store.state.ColsWidth.w9" class-name="prevDepreciationMonths" prop="prevDepreciationMonths">
          <template slot-scope="{ row }">
            <span> {{ row.prevDepreciationMonths }} </span>
          </template>
        </pl-table-column>
        <pl-table-column label="残值率" align="center" :width="$store.state.ColsWidth.w9" class-name="prevSurplusValueRate" prop="prevSurplusValueRate">
          <template slot-scope="{ row }">
            <span>{{ row.prevSurplusValueRate * 100 }}%</span>
          </template>
        </pl-table-column>
        <pl-table-column label="使用部门" align="center" :width="$store.state.ColsWidth.w9" class-name="prevDepartment" prop="prevDepartment">
          <template slot-scope="{ row }">
            <span> {{ row.prevDepartment }} </span>
          </template>
        </pl-table-column>
        <pl-table-column label="折旧/摊销费用科目" align="center" :width="$store.getters.columnSize.SizeForCount(20)" class-name="prevNewAmortizationFeeText" prop="prevNewAmortizationFeeText">
          <template slot-scope="{ row }">
            <span> {{ row.prevNewAmortizationFeeText }} </span>
          </template>
        </pl-table-column>
        <pl-table-column label="折旧方法" align="center" :width="$store.state.ColsWidth.w9" class-name="prevDepreciationMethod" prop="prevDepreciationMethod">
          <template slot-scope="{ row }">
            <span> {{ depreciationMethodFilter(row.prevDepreciationMethod) }} </span>
          </template>
        </pl-table-column>
        <pl-table-column label="累计减值准备" align="right" :width="$store.getters.columnSize.SizeForCount(8)" prop="totalDecreaseReady">
          <template slot-scope="{ row }">
            <span> {{ row.totalDecreaseReady }} </span>
          </template>
        </pl-table-column>
        <pl-table-column label="已计提折旧" align="right" :width="$store.getters.columnSize.SizeForCount(6)" prop="totalDepreciation">
          <template slot-scope="{ row }">
            <span> {{ row.totalDepreciation }} </span>
          </template>
        </pl-table-column>
      </pl-table-column>
      <pl-table-column label="变更后" align="center">
        <pl-table-column label="原值" align="right" :width="$store.state.ColsWidth.w9" class-name="originalValue" prop="originalValue">
          <template slot-scope="{ row }">
            <span> {{ row.originalValue | moneyFilter }} </span>
          </template>
        </pl-table-column>
        <pl-table-column label="折旧期限" align="right" :width="$store.state.ColsWidth.w9" class-name="depreciationMonths" prop="depreciationMonths">
          <template slot-scope="{ row }">
            <span> {{ row.depreciationMonths }} </span>
          </template>
        </pl-table-column>
        <pl-table-column label="残值率" align="center" :width="$store.state.ColsWidth.w9" class-name="surplusValueRate" prop="surplusValueRate">
          <template slot-scope="{ row }">
            <span> {{ row.surplusValueRate * 100 }}%</span>
          </template>
        </pl-table-column>
        <pl-table-column label="使用部门" align="center" :width="$store.state.ColsWidth.w9" class-name="department" prop="department">
          <template slot-scope="{ row }">
            <span> {{ row.department }} </span>
          </template>
        </pl-table-column>
        <pl-table-column label="折旧/摊销费用科目" align="center" :width="$store.getters.columnSize.SizeForCount(20)" class-name="newAmortizationFeeText" prop="newAmortizationFeeText">
          <template slot-scope="{ row }">
            <span> {{ row.newAmortizationFeeText }} </span>
          </template>
        </pl-table-column>
        <pl-table-column label="折旧方法" align="center" :width="$store.state.ColsWidth.w9" class-name="depreciationMethod" prop="depreciationMethod">
          <template slot-scope="{ row }">
            <span> {{ depreciationMethodFilter(row.depreciationMethod) }} </span>
          </template>
        </pl-table-column>
      </pl-table-column>
    </pl-table>
    <!-- 打印视图 -->
    <!-- <print v-if="printVisible" @hide="printVisible = false" :tableData="tableData" :isPortrait="false" /> -->
  </div>
</template>
<script>
import doExport from '@/mixins/doExport';
import { cloneDeep } from 'lodash-es';
import { fetchFixedAssetsTypes, getAssetsRecordTable } from '../api';

// 打印视图
// import Print from './components/Print.vue';

export default {
  // components: {
  //   Print,
  // },
  mixins: [doExport],
  data() {
    return {
      tableData: [],
      searchForm: {
        assetTypeId: '',
        assetName: '',
        assetCode: '',
        page: 1,
        pageSize: 999999,
      },
      assetCategoryOptions: {},
      assetTypeList: [],
    };
  },
  computed: {
    // 资产类别选项
    assetTypeOptions() {
      const options = [
        {
          value: '0',
          label: '固定资产',
          children: this.assetTypeList
            .filter((item) => item.categoryType === 0)
            .map((item) => ({
              value: item.assetTypeId,
              label: item.assetTypeName,
            })),
        },
        {
          value: '1',
          label: '无形资产',
          children: this.assetTypeList
            .filter((item) => item.categoryType === 1)
            .map((item) => ({
              value: item.assetTypeId,
              label: item.assetTypeName,
            })),
        },
      ];
      return options;
    },
  },
  created() {
    this.init(false);
  },
  methods: {
    // isSortChange 当为true时，说明该排序是根据点击的时候进行升降序排序，false时是根据默认的变更日期、修改时间进行排序
    async init(isSortChange) {
      const params = cloneDeep(this.searchForm);
      if (!isSortChange) {
        Object.assign(params, {
          'sd[0].field': 'accountPeriod',
          'sd[0].dir': 'asc',
          'sd[1].field': 'createTime',
          'sd[1].dir': 'asc',
        });
      }
      const res = await getAssetsRecordTable(params);
      this.tableData = res.data.data;
      // 展示变更前折旧/摊销费用科目
      this.tableData.forEach((e) => {
        const prevNewAmortizationFeeText = e.prevAssetSubjects?.map((item) => `${item.subjectFullName} ${item.rate * 100}%`).join(',') || '';
        const newAmortizationFeeText = e.assetSubjects?.map((item) => `${item.subjectFullName} ${item.rate * 100}%`).join(',') || '';
        e.prevNewAmortizationFeeText = prevNewAmortizationFeeText;
        e.newAmortizationFeeText = newAmortizationFeeText;
      });
      await this.getAssetCategoryOptions();
    },
    // 导出
    async changeRecordExport() {
      const params = cloneDeep(this.searchForm);
      const url = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/change/history/export';
      await this.doExport(url, { params });
    },
    // 查询
    doSearch() {
      this.init(false);
    },
    sortChange({ prop, order }) {
      Object.assign(this.searchForm, {
        'sd[0].field': prop,
        'sd[0].dir': order.indexOf('asc') > -1 ? 'asc' : 'desc',
      });
      this.init(true);
    },
    // 获取资产类别
    async getAssetCategoryOptions() {
      const response = await fetchFixedAssetsTypes();
      this.assetTypeList = response.data.data;
    },
    // 折旧方法过滤器
    depreciationMethodFilter(data) {
      if (data === 1) {
        return '平均年限法';
      }
      if (data === 2) {
        return '一次性折旧';
      }
      if (data === 5) {
        return '不折扣';
      }
      return '';
    },
    // 是否已经变更的属性
    isChangeProp(prevProp, Prop) {
      if (prevProp === Prop) {
        return false;
      }
      return true;
    },
    changeRecordClassName({ row, column }) {
      const classNameList = ['originalValue', 'depreciationMonths', 'surplusValueRate', 'department', 'newAmortizationFeeText', 'depreciationMethod'];
      if (classNameList.indexOf(column.property) !== -1) {
        // 变更前的cellName
        const prevCell = `prev${column.property.slice(0, 1).toUpperCase()}${column.property.slice(1)}`;
        if (this.isChangeProp(row[prevCell], row[column.property])) {
          return 'table_font_blue';
        }
        return '';
      }
      return false;
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-table{
  .table_font_blue{
    color: rgb(0, 175, 252);
  }
}
</style>
