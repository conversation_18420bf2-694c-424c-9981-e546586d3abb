<!--
 * @Page: 资产变更记录（打印界面）
 * @Maintainer: 卓海斌
 * @Modifier: 卓海斌
 * @Description:
 -->
<template>
  <div class="box" id="printTable1" style="width: 1200px;">
    <!-- 表格页面 -->
    <div class="A4" style="width: 100%">
      <!-- 表头 -->
      <h2>资产变更信息记录表</h2>

      <p class="time" style="position: relative;">
        <!-- 左 -->
        <span style="position: absolute;left: 0;">
          <span>编制单位：</span>
          <span>{{ $store.state.user.userInfo.companyName }}</span>
        </span>
      </p>

      <!-- 表格主体 -->
      <div style="padding-top:32px;">
        <!-- 表格主体 -->
        <el-table class="flex-fluid" ref="table" :data="tableData" border height="100%" highlight-current-row row-class-name="hoverRow">
          <el-table-column label="变更日期" align="center">
            <template slot-scope="{ row }">
              <span> {{ row.assetTypeName }} </span>
            </template>
          </el-table-column>
          <el-table-column label="变更原因" align="center">
            <template slot-scope="{ row }">
              <span> {{ row.assetTypeName }} </span>
            </template>
          </el-table-column>
          <el-table-column label="变更方式" align="center">
            <template slot-scope="{ row }">
              <span> {{ row.assetTypeName }} </span>
            </template>
          </el-table-column>
          <el-table-column label="资产类别" align="center">
            <template slot-scope="{ row }">
              <span> {{ row.assetTypeName }} </span>
            </template>
          </el-table-column>
          <el-table-column label="资产名称" align="center">
            <template slot-scope="{ row }">
              <span> {{ row.assetTypeName }} </span>
            </template>
          </el-table-column>
          <el-table-column label="资产编号" align="center">
            <template slot-scope="{ row }">
              <span> {{ row.assetTypeName }} </span>
            </template>
          </el-table-column>
          <el-table-column label="变更前" align="center">
            <el-table-column label="原值" align="center">
              <template slot-scope="{ row }">
                <span> {{ row.assetTypeName }} </span>
              </template>
            </el-table-column>
            <el-table-column label="折旧期限" align="center">
              <template slot-scope="{ row }">
                <span> {{ row.assetTypeName }} </span>
              </template>
            </el-table-column>
            <el-table-column label="残值率" align="center">
              <template slot-scope="{ row }">
                <span> {{ row.assetTypeName }} </span>
              </template>
            </el-table-column>
            <el-table-column label="使用部门" align="center">
              <template slot-scope="{ row }">
                <span> {{ row.assetTypeName }} </span>
              </template>
            </el-table-column>
            <el-table-column label="折旧/摊销" align="center">
              <template slot-scope="{ row }">
                <span> {{ row.assetTypeName }} </span>
              </template>
            </el-table-column>
            <el-table-column label="折旧方法" align="center">
              <template slot-scope="{ row }">
                <span> {{ row.assetTypeName }} </span>
              </template>
            </el-table-column>
            <el-table-column label="累计减值准备" align="center" :width="$store.getters.columnSize.SizeForCount(6)">
              <template slot-scope="{ row }">
                <span> {{ row.assetTypeName }} </span>
              </template>
            </el-table-column>
            <el-table-column label="已计提折旧" align="center">
              <template slot-scope="{ row }">
                <span> {{ row.assetTypeName }} </span>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="变更后" align="center">
            <el-table-column label="原值" align="center">
              <template slot-scope="{ row }">
                <span> {{ row.assetTypeName }} </span>
              </template>
            </el-table-column>
            <el-table-column label="折旧期限" align="center">
              <template slot-scope="{ row }">
                <span> {{ row.assetTypeName }} </span>
              </template>
            </el-table-column>
            <el-table-column label="残值率" align="center">
              <template slot-scope="{ row }">
                <span> {{ row.assetTypeName }} </span>
              </template>
            </el-table-column>
            <el-table-column label="使用部门" align="center">
              <template slot-scope="{ row }">
                <span> {{ row.assetTypeName }} </span>
              </template>
            </el-table-column>
            <el-table-column label="折旧/摊销" align="center">
              <template slot-scope="{ row }">
                <span> {{ row.assetTypeName }} </span>
              </template>
            </el-table-column>
            <el-table-column label="折旧方法" align="center">
              <template slot-scope="{ row }">
                <span> {{ row.assetTypeName }} </span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>

      <!-- 表格脚步
      <div class="printFlooter clearfix">
        <span class="fr">操作员：{{ $store.getters.getManagerNameByJob('账本-操作员') }}</span>
      </div> -->
    </div>
  </div>
</template>

<script>
import BasePrint from '@/components/BasePrint.vue';

export default {
  extends: BasePrint,
};
</script>
