<!--
 * @Description: 进项发票生成在建工程
 * @version: 过验证eslint
 * @Company: 海闻软件
 * @Author: 晓荣
 * @LastEditors: 晓荣
-->
<template>
  <div class="BuildingAssetDlgStyle">
    <el-dialog
      width="75%"
      title="生成资产"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      @close="hide">
      <h4>请选择固定资产的基本信息</h4>
      <el-form inline :model="dlgFormData" :rules="formRules" ref="assetForm" label-width="100px">
        <el-row :gutter="10">
            <el-col :span="10" :offset="1">
              <el-form-item label="资产类别" prop="assetTypeId">
                <el-select v-model="dlgFormData.assetTypeId" placeholder="请选择">
                  <el-option
                    v-for="item in assetTypeList"
                    :key="item.assetTypeId"
                    :label="item.assetTypeName"
                    :value="item.assetTypeId">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10" :offset="1">
              <el-form-item label="资产名称" prop="assetName">
                <el-input v-model.trim="dlgFormData.assetName" placeholder="请输入资产名称"></el-input>
              </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="10">
            <el-col :span="10" :offset="1">
              <el-form-item label="录入日期" prop="businessTime">
                <el-date-picker
                  type="date"
                  placeholder="选择录入日期"
                  v-model="dlgFormData.businessTime"
                  :editable="false"
                  :clearable="false"
                  :default-value="$store.state.user.accountPeriod"
                  :picker-options="$store.getters.pickerOptionsForAccountDateAndNowDate"
                  />
              </el-form-item>
            </el-col>
            <el-col :span="10" :offset="1">
              <el-form-item label="增加方式" prop="addMethod">
                <el-select v-model="dlgFormData.addMethod" disabled>
                  <el-option
                    v-for="item in fixedAssetsAddMethod"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
        <el-button  @click="hide">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">生成资产</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import get from 'lodash-es/get';
import { cloneDeep } from 'lodash-es';
import { fixedAssetsAddMethod } from '@/pages/assets/enums/index';
import { fetchFixedAssetsTypes } from '@/pages/assets/api';
import { insertTagTypeAndTagName } from '@/api/company';

export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    buildingAsset: {
      type: Array,
    },
  },
  data() {
    return {
      fixedAssetsAddMethod,
      assetTypeList: [],
      dlgFormData: {
        assetTypeId: '',
        assetName: '',
        businessTime: '',
        addMethod: 5,
      },
      formRules: {
        assetTypeId: [{ required: true, message: '请输入资产类别', trigger: ['blur', 'change'] }],
        assetName: [{ required: true, message: '请输入资产名称', trigger: ['blur', 'change'] }],
        businessTime: [{ required: true, message: '请输入录入日期', trigger: ['blur', 'change'] }],
      },

    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(isVisible) {
        if (this.visible && !isVisible) {
          this.$emit('hide', false);
        }
      },
    },
  },
  watch: {
    dialogVisible(newVal) {
      if (newVal) {
        this.loadAssetTypes();
      }
    },
  },
  methods: {
    hide() {
      if (this.$refs.assetForm) {
        this.$refs.assetForm.resetFields();
      }
      this.$emit('update:visible', false);
    },
    handleConfirm() {
      this.$refs.assetForm.validate(async (valid) => {
        if (!valid) return;
        const { dlgFormData, buildingAsset } = this;
        const paramsList = [
          {
            tagTypeName: '固定资产名称',
            tagFullName: dlgFormData.assetName,
          },
        ];
        await insertTagTypeAndTagName(paramsList).then((rsp) => {
          const [rspData] = get(rsp, 'data.data');
          const routerParams = {
            dlgFormData: cloneDeep(dlgFormData),
            buildingAsset,
            tagId: rspData.tagId,
          };
          this.$emit('update:visible', false);
          this.$router.push({
            name: 'card',
            params: {
              origin: 'BuildingEngineering',
              data: routerParams,
            },
          });
        });
      });
    },
    async loadAssetTypes() {
      try {
        // categoryType 0：固定资产
        const params = { page: 1, pageSize: 9999, categoryType: 0 };
        const response = await fetchFixedAssetsTypes(params);
        this.assetTypeList = get(response, 'data.data');
      } catch (error) {
        console.error(error);
      }
    },
  },
};
</script>
<style lang="scss">
  .BuildingAssetDlgStyle {
    .el-form .el-form-item__label {
      line-height: 36px;
    }
    .el-form .el-input {
      width: 280px;
    }
  }
</style>
