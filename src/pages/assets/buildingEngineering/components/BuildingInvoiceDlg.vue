<!--
 * @Description: 进项发票生成在建工程
 * @version: 过验证eslint
 * @Company: 海闻软件
 * @Author: 晓荣
 * @LastEditors: 晓荣
-->
<template>
  <div class="BuildingInvoiceDlgStyle">
    <el-dialog
      width="90%"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      @close="hide">
      <template slot="title">
        <span>生成在建工程</span>
        <span class="cl-red">(只有生成凭证的发票才能生成资产)</span>
      </template>
      <el-form inline :model="searchForm" ref="searchForm" >
        <el-form-item label="销方名称" prop="sellerName">
          <el-autocomplete
            placeholder="销方名称"
            v-model.trim="searchForm.sellerName"
            clearable
            value-key="supplierName"
            popper-class="sellerNameAutocompleteStyle"
            :fetch-suggestions="querySearch"
          ></el-autocomplete>
        </el-form-item>
        <el-form-item label="开票日期" prop="invoiceDate">
          <el-date-picker
            v-model="searchForm.invoiceDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="$store.getters.pickerOptionsForAccountDateAndNowDate">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="会计周期">
            <el-date-picker
              v-model="searchForm.crossMonth"
              type="monthrange"
              :editable="false"
              :clearable="false"
              align="right"
              :picker-options="pickerOptions"
            ></el-date-picker>
        </el-form-item>
        <el-form-item label="发票号码" prop="invoiceNumber">
          <el-input v-model.trim="searchForm.invoiceNumber" placeholder="请输入发票号码"></el-input>
        </el-form-item>
        <el-form-item >
          <el-button type="primary" icon="el-icon-search" @click="initData"></el-button>
        </el-form-item>
      </el-form>
      <el-table
        ref="table"
        border
        :data="tableData"
        :max-height='360'
        @selection-change="setSelection">
        <el-table-column type="selection"  :selectable="disableSelect"/>
        <el-table-column prop="voucherCode" label="凭证号" align="center" :width="$store.getters.columnSize.Buildin.VoucherCode + 40">
          <template slot-scope="{ row }">
            <el-button  v-for="(vouchersItem, index) in row.vouchers"
              :key="vouchersItem.voucherId"
              type="text"
              class="editCol"
              title="查看详情"
              @click="showVoucher(vouchersItem, row.vouchers);">
              {{ vouchersItem.voucherCode }}
              <span v-if="index+1 !== row.vouchers.length">，</span>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceNumber" label="发票号码" align="center" :width="$store.getters.columnSize.Buildin.InvoiceNumber"/>
        <af-table-column prop="sellerName" label="销方名称"  />
        <el-table-column prop="invoiceDate" label="开票日期" align="center" :width="$store.getters.columnSize.Buildin.Date">
            <template slot-scope="{ row }">
            <span> {{ row.invoiceDate | date}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="notaxCostAmount" label="不含税金额" align="right" class-name="table_font_black" :formatter="$moneyFormatter" :width="$store.getters.columnSize.Buildin.Money"/>
        <el-table-column prop="inputTaxAmount" label="税额" align="right" class-name="table_font_black" :formatter="$moneyFormatter" :width="$store.getters.columnSize.Buildin.Money"/>
        <el-table-column prop="totalAmount" label="价税合计" align="right" class-name="table_font_black" :formatter="$moneyFormatter" :width="$store.getters.columnSize.Buildin.Money"/>
        <el-table-column prop="constructionCode" label="在建工程编号" align="center" />
      </el-table>
      <Voucher
        class="trade-voucher"
        v-if="visibleCard === 'detail'"
        :voucherId="currentVoucherId"
        :allVoucherIds="allVoucherIds"
        @hide="visibleCard = null"
      />
      <span slot="footer">
        <el-button  @click="hide">取 消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="!checkedRows.length">确 认</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import get from 'lodash-es/get';
import dayjs from 'dayjs';
import isEmpty from 'lodash-es/isEmpty';
import Voucher from '@/pages/finance/voucherDetails/components/Voucher.vue';
import { postConstructionInvoice, getInvoiceList } from '@/pages/assets/api';

export default {
  components: { Voucher },
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      searchForm: {
        sellerName: '',
        invoiceDate: [],
        crossMonth: [this.$store.state.user.accountPeriod, this.$store.state.user.accountPeriod],
        invoiceNumber: '',
      },
      pickerOptions: Object.freeze({
        disabledDate: (time) => {
          const accountDate = dayjs(this.$store.state.user.companyInfo.accountDate);
          const nowMonthlastDay = dayjs().endOf('month');
          return !dayjs(time).isBetween(accountDate, nowMonthlastDay, null, '[');
        },
        onPick: ({ minDate, maxDate }) => {
          if (minDate && maxDate) {
            const start = dayjs(minDate);
            const end = dayjs(maxDate);
            if ((end.year() > start.year() && end.month() >= start.month())) {
              this.$nextTick(() => {
                this.$message.warning('搜索的日期区间不可超过12个月！');
                this.searchForm.crossMonth = [this.$store.state.user.accountPeriod, this.$store.state.user.accountPeriod];
              });
            }
          }
        },
      }),
      tableData: [],
      checkedRows: [],

      visibleCard: null, // 凭证弹窗
      allVoucherIds: [], // 当月全部凭证id
      currentVoucherId: '', // 当前展示的凭证id
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(isVisible) {
        if (this.visible && !isVisible) {
          this.$emit('hide', false);
        }
      },
    },
  },
  watch: {
    async dialogVisible(newVal) {
      if (newVal) {
        await this.$store.dispatch('selectData/getSupplierList');
        this.initData();
      }
    },
  },
  methods: {
    async initData() {
      const { searchForm } = this;
      const params = {
        invoiceNumber: searchForm.invoiceNumber,
        sellerName: searchForm.sellerName,
        invoiceDateFrom: isEmpty(searchForm.invoiceDate) ? '' : dayjs(searchForm.invoiceDate[0]).format('YYYY-MM-DD'),
        invoiceDateTo: isEmpty(searchForm.invoiceDate) ? '' : dayjs(searchForm.invoiceDate[1]).format('YYYY-MM-DD'),
        accountPeriodFrom: dayjs(searchForm.crossMonth[0]).format('YYYYMM'),
        accountPeriodTo: dayjs(searchForm.crossMonth[1]).format('YYYYMM'),
      };
      console.log('params', params);
      try {
        const response = await getInvoiceList(params);
        this.tableData = get(response, 'data.data');
      } catch (error) {
          console.error(error);
      }
    },
    hide() {
      if (this.$refs.searchForm) {
        this.$refs.searchForm.resetFields();
      }
      this.$emit('update:visible', false);
    },
    handleConfirm() {
      const invoiceIds = this.checkedRows.map((item) => item.invoiceId);
      postConstructionInvoice(invoiceIds).then((response) => {
        this.$message.success(response.data.returnMessage);
        this.$emit('success');
        this.hide();
      });
    },
    // 勾选
    setSelection(selection) {
      this.checkedRows = selection;
    },
    // 禁用勾选
    disableSelect(row) {
      return !row.constructionCode;
    },
    // 销方名称模糊查询
    querySearch(queryString, callback) {
      const suppliers = this.$store.state.selectData.supplierList;
      queryString = queryString.trim();
      callback(
        queryString
          ? suppliers.filter(
              (item) => item.supplierName.indexOf(queryString) > -1,
            )
          : suppliers,
      );
    },

    // 表格凭证号 显示凭证
    showVoucher(row, allRow) {
      this.visibleCard = 'detail';
      this.allVoucherIds = allRow.map((item) => item.voucherId);
      this.currentVoucherId = row.voucherId;
    },
  },
};
</script>
<style lang="scss">
  .sellerNameAutocompleteStyle{
    width: 380px !important;
  }
  .BuildingInvoiceDlgStyle {
    .el-form  {
      .el-input {
        width: 180px;
      }
      .el-date-editor {
        width: 140px;
      }
      .el-range-editor {
        width: 220px;
        margin-top: 5px;
      }
      .el-form-item__label,.el-form-item__content {
        line-height: 36px;
      }
      .el-button {
        margin-top: 5px;
      }
    }

  }
</style>
