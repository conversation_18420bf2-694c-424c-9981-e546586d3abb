<!--
 * @Description: 在建工程新增/修改
 * @version: 过验证eslint
 * @Company: 海闻软件
 * @Author: 晓荣
 * @LastEditors: 晓荣
-->
<template>
  <div class="buildingDialogStyle">
    <el-dialog
      width="75%"
      :title="title"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      @close="hide">
      <h4>在建工程基础信息</h4>
      <el-form ref="buildingForm" label-width="120px"
          :model="formData"
          :rules="formRules">
          <el-row :gutter="10">
            <el-col :span="10" :offset="1">
              <el-form-item label="编号" prop="constructionCode">
                <el-input v-model.trim="formData.constructionCode"  disabled />
              </el-form-item>
            </el-col>
            <el-col :span="10" :offset="1">
              <el-form-item label="在建工程名称" prop="constructionName">
                <el-input placeholder="请输入在建工程名称" v-model.trim="formData.constructionName"  :disabled="isReadMode"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="10" :offset="1">
              <el-form-item label="不含税金额" prop="notaxAmount">
                <q-nb-input :min="0" :max="*********.99" v-model="formData.notaxAmount" @change="hangdleComputed" :disabled="isReadMode"/>
              </el-form-item>
            </el-col>
            <el-col :span="10" :offset="1">
              <el-form-item label="税额" prop="taxAmount">
                <q-nb-input :min="0" :max="*********.99" v-model="formData.taxAmount" @change="hangdleComputed" :disabled="isReadMode"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="10" :offset="1">
              <el-form-item label="金额" prop="amount">
                <q-nb-input :min="0" :max="*********.99" v-model="formData.amount" :disabled="true"/>
              </el-form-item>
            </el-col>
            <el-col :span="10" :offset="1">
              <el-form-item label="入库时间" prop="storageDate">
                <el-date-picker
                  type="date"
                  placeholder="选择日期"
                  v-model="formData.storageDate"
                  :editable="false"
                  :clearable="false"
                  :picker-options="$store.getters.pickerOptionsForAccountDateAndNowDate"
                  :disabled="isReadMode"
                  />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="10" :offset="1">
              <el-form-item label="备注" prop="remark">
                <el-input placeholder="请输入备注" v-model.trim="formData.remark"  :disabled="isReadMode"/>
              </el-form-item>
            </el-col>
            <el-col :span="10" :offset="1">
              <el-form-item label="在建工程科目" prop="subjectFullName">
                <subject-select
                  :filterSubjectCodes="['1604']"
                  :noParent="false"
                  :show-levels="[2,3]"
                  no-disabled-item
                  :hasLocalMemory="false"
                  :disabled="isReadMode"
                  :value.sync="formData.subjectFullName" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="10" :offset="1">
              <el-form-item label="固定资产名称" prop="assetName">
                <el-input placeholder="固定资产名称" v-model.trim="formData.assetName"  disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="10" :offset="1">
              <el-form-item label="转固定资产日期" prop="transformDate">
                <el-date-picker
                  type="date"
                  v-model="formData.transformDate"
                  placeholder="转固定资产日期"
                  disabled
                  />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="10" :offset="1">
              <el-form-item label="固定资产编号" prop="assetCode">
                <el-input placeholder="固定资产编号" v-model.trim="formData.assetCode" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="10" :offset="1" v-if="isReadMode">
              <el-form-item label="累计折旧" prop="depreciationAmount">
                <q-nb-input v-model="formData.depreciationAmount" disabled/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10"  v-if="isReadMode">
            <el-col :span="10" :offset="1">
              <el-form-item label="净值" prop="netAmount">
                <q-nb-input v-model="formData.netAmount" disabled/>
              </el-form-item>
            </el-col>
          </el-row>
      </el-form>
      <span slot="footer">
        <el-button  @click="hide">取 消</el-button>
        <ac-button :comCode="['assets-buildingEngineering-bianji']" type="primary" @click="handleConfirm">确 认</ac-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import isEmpty from 'lodash-es/isEmpty';
import SubjectSelect from '@/components/commonSelect/SubjectSelect.vue';
import dayjs from 'dayjs';
import { postConstruction, putConstruction } from '../../api/index';

export default {
  components: {
    SubjectSelect,
  },
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    dialogType: {
      type: String,
      default: 'add',
    },
    buildingDialogDate: {
      type: Object,
    },
  },
  data() {
    return {
      formData: {
        constructionCode: '',
        constructionName: '',
        notaxAmount: 0,
        taxAmount: 0,
        amount: 0,
        storageDate: '',
        remark: '',
        assetName: '',
        assetCode: '',
        transformDate: '',
        subjectFullName: '',
      },
      formRules: {
        constructionName: [
          { required: true, message: '请输入在建工程名称', trigger: ['blur', 'change'] },
          { max: 100, message: '最多100字的在建工程名称', trigger: ['blur', 'change'] },
        ],
        notaxAmount: [
          { required: true, message: '请输入不含税金额', trigger: ['blur', 'change'] },
        ],
        taxAmount: [
          { required: true, message: '请输入税额', trigger: ['blur', 'change'] },
        ],
        storageDate: [
          { required: true, message: '请选择入库时间', trigger: ['blur', 'change'] },
        ],
        remark: [
          { max: 100, message: '最多100字的备注', trigger: ['blur', 'change'] },
        ],
        subjectFullName: [
          { required: true, message: '请选择在建工程科目', trigger: ['blur', 'change'] },
        ],
      },
    };
  },
  watch: {
    dialogVisible(newVal) {
      if (newVal && !isEmpty(this.buildingDialogDate)) {
        console.log('DialogDate', this.buildingDialogDate);
        this.setFormData();
      }
    },
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(isVisible) {
        if (this.visible && !isVisible) {
          this.$emit('hide', false);
        }
      },
    },
    title() {
      let titleText;
      switch (this.dialogType) {
          case 'add': titleText = '新增';
              break;
          case 'edit': titleText = '修改';
              break;
          case 'read': titleText = '查看';
              break;
          default: titleText = '新增';
      }
      return titleText;
    },
    // 是否 read 模式
    isReadMode() {
      return this.dialogType === 'read';
    },

    // 是否新增模式
    isCreateMode() {
      return this.dialogType === 'create';
    },

    // 是否编辑模式
    isEditMode() {
      return this.dialogType === 'edit';
    },

    subjectList() {
      return this.$store.state.selectData.subjectList;
    },

  },
  methods: {
    hangdleComputed() {
      const { notaxAmount, taxAmount } = this.formData;
      this.formData.amount = notaxAmount.add(taxAmount);
    },
    setFormData() {
      this.formData = { ...this.buildingDialogDate };
    },
    // 点击保存按钮时
    handleConfirm() {
      this.$refs.buildingForm.validate(async (valid) => {
        if (!valid) return;
        let rsp;
        const { formData, subjectList } = this;
        const node = subjectList.find((item) => item.subjectCode === formData.subjectFullName);
        const subjectFullName = node?.subjectFullName || '';
        const params = {
          ...formData,
          subjectFullName,
          storageDate: dayjs(formData.storageDate).format('YYYY-MM-DD'),
        };
        this.$delete(params, 'constructionCode');
        this.$delete(params, 'depreciationAmount');
        if (params.assetCode) {
          this.$message.warning('在建工程已生成固定资产，不能修改');
          return;
        }
        if (this.isCreateMode) {
          // 新增
          rsp = await postConstruction(params);
        } else {
          // 修改
          rsp = await putConstruction(params);
        }
        this.$message.success(rsp.data.returnMessage);
        this.$emit('success');
        this.hide();
      });
    },
    // 隐藏对话框
    hide() {
      this.formData = {
        constructionCode: '',
        constructionName: '',
        notaxAmount: 0,
        taxAmount: 0,
        amount: 0,
        storageDate: '',
        remark: '',
        assetName: '',
        assetCode: '',
        transformDate: '',
        subjectFullName: '',
      };
      this.$refs.buildingForm.resetFields();
      this.$emit('update:visible', false);
    },

  },
};
</script>
<style lang="scss">
  .buildingDialogStyle {
    .el-input,.el-select {
      width: 100%;
    }
  }
</style>
