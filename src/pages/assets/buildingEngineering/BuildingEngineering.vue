<!--
 * @Description:在建工程
 * @version:
 * @Company: 海闻软件
 * @Author: 晓荣
 * @LastEditors: 晓荣
-->
<template>
  <div class="flex-v">
    <page-tabs class="flex-fixed" v-model="buuildingEngineerType" :tabs="[
      {
        label: '在建工程(未结转)',
        value: 'project'
      }, {
        label: '固定资产明细(在建工程结转部分)',
        value: 'assetDetails'
      }
    ]"></page-tabs>
    <div class="toolbar flex-fixed">
      <el-form ref="searchForm" :model="searchForm" :inline="true" class="qf_query">
        <el-form-item>
          <more-search showSearch @search="initData" :search-form-data="searchForm">
            <el-input
              slot="date"
              clearable
              v-model.trim="searchForm.constructionName"
              placeholder="在建工程名称"></el-input>
            <el-form-item label="编号" prop="constructionCode">
              <el-input v-model.trim="searchForm.constructionCode" placeholder="请输入编号"></el-input>
            </el-form-item>
            <el-form-item label="在建工程名称" prop="constructionName">
              <el-input v-model.trim="searchForm.constructionName" placeholder="请输入在建工程名称"></el-input>
            </el-form-item>
            <el-form-item label="入库时间" prop="storageDate">
              <el-date-picker type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                v-model="searchForm.storageDate" :editable="false"
                :picker-options="$store.getters.pickerOptionsForAccountDateAndNowDate" />
            </el-form-item>
            <el-form-item label="转固定资产日期" prop="transformDate" v-if="!isProject">
              <el-date-picker type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                v-model="searchForm.transformDate" :editable="false"
                :picker-options="$store.getters.pickerOptionsForAccountDateAndNowDate" />
            </el-form-item>
            <el-form-item label="固定资产编号" prop="assetCode" v-if="!isProject">
              <el-input v-model.trim="searchForm.assetCode" placeholder="请输入固定资产编号"></el-input>
            </el-form-item>
            <el-form-item label="固定资产名称" prop="assetName" v-if="!isProject">
              <el-input v-model.trim="searchForm.assetName" placeholder="请输入固定资产名称"></el-input>
            </el-form-item>
          </more-search>
        </el-form-item>
      </el-form>
      <div class="action-wrapper">
        <ac-button :comCode="['assets-buildingEngineering-bianji']" type="primary" v-if="isProject" @click="hangleShowPanel('create', {})">新增</ac-button>
        <ac-button :comCode="['assets-buildingEngineering-bianji']" type="primary" v-if="isProject" @click="handleGenerate">根据进项生成在建工程</ac-button>
        <el-button type="primary" @click="hangleExport">导出</el-button>
      </div>
      <row-operate :selectionIng="checkedRows">
        <ac-button :comCode="['assets-buildingEngineering-bianji']" size="small" type="text" @click="hangleShowPanel('edit', checkedRows[0])"
          :disabled="checkedRows.length !== 1">
          <i class="fa fa-pencil" aria-hidden="true"> 编辑</i>
        </ac-button>

        <ac-button :comCode="['assets-buildingEngineering-bianji']" size="small" type="text" @click="hangleShowPanel('generate', checkedRows)"><i class="fa share"
            aria-hidden="true">结转固定资产</i></ac-button>

        <ac-button :comCode="['assets-buildingEngineering-bianji']" size="small" type="text" @click="hangleDelete">
          <i class="fa fa-trash" aria-hidden="true"> 删除</i>
        </ac-button>
      </row-operate>
    </div>
    <el-table ref="table" border :data="tableData" @sort-change="sortChange" @selection-change="setSelection"
      height="100%" class="flex-fluid">
      <el-table-column type="selection" :selectable="disableSelect" align="center" />
      <template v-if="!isProject">
        <el-table-column prop="assetCode" label="固定资产编号" sortable="custom" key="assetCode" />
        <el-table-column prop="assetName" label="固定资产名称" key="assetName" />
      </template>
      <el-table-column prop="constructionCode" label="编号" align="center" key="constructionCode">
        <template slot-scope="{ row }">
          <span class="editCol">
            <span @click="hangleShowPanel('edit', row);">{{ row.constructionCode }}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="constructionName" :label="isProject ? '在建工程名称' : '固定资产明细名称'" align="center"
        key="constructionName" />
      <el-table-column prop="notaxAmount" label="不含税金额" align="right" key="notaxAmount" class-name="table_font_black"
        :formatter="$moneyFormatter" :width="$store.getters.columnSize.Buildin.Money" />
      <el-table-column prop="taxAmount" label="税额" align="right" key="taxAmount" class-name="table_font_black"
        :formatter="$moneyFormatter" :width="$store.getters.columnSize.Buildin.Money" />
      <el-table-column prop="amount" label="金额" align="right" key="amount" class-name="table_font_black"
        :formatter="$moneyFormatter" :width="$store.getters.columnSize.Buildin.Money" />
      <el-table-column prop="storageDate" label="入库时间" :width="$store.getters.columnSize.Buildin.Date" key="storageDate">
        <template slot-scope="{ row }">
          <span> {{ row.storageDate | date }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" key="remark" />
      <template v-if="!isProject">
        <el-table-column prop="depreciationAmount" label="累计折旧" align="right" class-name="table_font_black"
          :formatter="$moneyFormatter" key="depreciationAmount" :width="$store.getters.columnSize.Buildin.Money" />
        <el-table-column prop="netAmount" label="净值" align="right" class-name="table_font_black" key="netAmount"
          :formatter="$moneyFormatter" :width="$store.getters.columnSize.Buildin.Money" />
        <el-table-column prop="transformDate" label="转固定资产日期" :width="$store.getters.columnSize.Buildin.Date + 20"
          key="transformDate">
          <template slot-scope="{ row }">
            <span> {{ row.transformDate | date }}</span>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <pagination class="flex-fixed" :current-page="searchForm.page" :total="searchForm.total"
      :page-size.sync="searchForm.pageSize" :page-sizes="[10, 15, 20, 30, 50, 100]" @current-change="onPageChange"
      @size-change="onPageSizeChange" layout="total, sizes, prev, pager, next, slot">
    </pagination>

    <building-dlg :dialogType="dialogType" :visible.sync="showBuildingDialog" :buildingDialogDate="buildingDialogDate"
      @success="initData" @hide="showBuildingDialog = false">
    </building-dlg>
    <building-asset-dlg :visible.sync="showBuildingAssetDialog" :buildingAsset="buildingAsset" @success="initData"
      @hide="showBuildingAssetDialog = false">
    </building-asset-dlg>
    <building-invoice-dlg :visible.sync="showBuildingInvoiceDialog" @success="initData"
      @hide="showBuildingInvoiceDialog = false">
    </building-invoice-dlg>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import get from 'lodash-es/get';
import isEmpty from 'lodash-es/isEmpty';
import doExport from '@/mixins/doExport';
import { getConstructionList, delConstruction } from '../api/index';
import BuildingDlg from './components/BuildingDlg.vue';
import BuildingAssetDlg from './components/BuildingAssetDlg.vue';
import BuildingInvoiceDlg from './components/BuildingInvoiceDlg.vue';

export default {
  mixins: [doExport],
  components: {
    BuildingDlg,
    BuildingAssetDlg,
    BuildingInvoiceDlg,
  },
  data() {
    return {
      buuildingEngineerType: 'project', // project, assetDetails
      searchForm: {
        constructionCode: '',
        constructionName: '',
        storageDate: [],
        transformDate: [],
        assetCode: '',
        assetName: '',
        page: 1,
        total: 0,
        pageSize: this.$store.state.defaultPageSize,
      },
      tableData: [],
      checkedRows: [],
      showBuildingDialog: false,
      dialogType: 'create',
      buildingDialogDate: {},

      showBuildingInvoiceDialog: false,
      showBuildingAssetDialog: false,
      buildingAsset: [],
    };
  },
  computed: {
    isProject() {
      return this.buuildingEngineerType === 'project';
    },
  },
  watch: {
    buuildingEngineerType() {
      this.initData();
    },
  },
  mounted() {
    const { origin, assetCode } = this.$route.params;
    if (origin === 'card') {
      this.buuildingEngineerType = 'assetDetails';
      this.searchForm.assetCode = assetCode;
    }
    this.initData();
  },
  methods: {
    getParams() {
      const { searchForm, isProject } = this;
      const queryScope = isProject ? 2 : 1;
      const params = {
        ...searchForm,
        queryScope,
        storageDateFrom: isEmpty(searchForm.storageDate) ? '' : dayjs(searchForm.storageDate[0]).format('YYYY-MM-DD'),
        storageDateTo: isEmpty(searchForm.storageDate) ? '' : dayjs(searchForm.storageDate[1]).format('YYYY-MM-DD'),
        transformDateFrom: isEmpty(searchForm.transformDate) ? '' : dayjs(searchForm.transformDate[0]).format('YYYY-MM-DD'),
        transformDateTo: isEmpty(searchForm.transformDate) ? '' : dayjs(searchForm.transformDate[1]).format('YYYY-MM-DD'),
      };
      this.$delete(params, 'storageDate');
      this.$delete(params, 'transformDate');
      return params;
    },
    async initData() {
      const params = this.getParams();
      getConstructionList(params).then((response) => {
        this.tableData = get(response, 'data.data');
        this.searchForm.total = get(response, 'data.total');
      });
    },
    sortChange({ prop, order }) {
      this.$set(this.searchForm, 'sort[0][field]', prop);
      this.$set(this.searchForm, 'sort[0][dir]', order.indexOf('asc') > -1 ? 'asc' : 'desc');
      this.initData();
    },
    // 处理页大小改变
    onPageSizeChange(pageSize) {
      this.searchForm.pageSize = pageSize;
      this.searchForm.page = 1;
      this.initData();
    },
    // 处理页码改变
    onPageChange(page) {
      this.searchForm.page = page;
      this.initData();
    },
    // 根据销进项生成在建工程
    handleGenerate() {
      this.showBuildingInvoiceDialog = true;
      // this.showBuildingAssetDialog = true;
    },
    // 禁用勾选
    disableSelect(row) {
      return !row.assetCode;
    },
    // 勾选
    setSelection(selection) {
      this.checkedRows = selection;
    },
    // 勾选操作： 编辑 / 生成固定资产
    hangleShowPanel(type, data) {
      if (type !== 'generate') {
        // 编辑 / 新增
        this.dialogType = data.assetCode ? 'read' : type;
        this.showBuildingDialog = true;
        this.buildingDialogDate = data;
      } else {
        // 固定资产
        console.log(data);
        this.showBuildingAssetDialog = true;
        this.buildingAsset = data;
      }
    },
    hangleDelete() {
      const constructionIds = this.checkedRows.map((item) => item.constructionId).join(',');
      this.$confirm('此操作将会删除该在建工程', '是否删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        delConstruction(constructionIds).then(async (res) => {
          this.$message.success(res.data.returnMessage || '操作成功');
          this.initData();
        });
      });
    },
    // 导出
    hangleExport() {
      if (this.tableData.length === 0) {
        this.$message.warning('当前条件没有可以导出的记录。');
        return;
      }
      const url = '/rest/companyAccount/merge/construction/v1.0/export?';
      const params = this.getParams();
      this.doExport(url, { params });
    },
  },
};
</script>
