<!--
 * @Component: 预览凭证侧滑面板（折旧资产时）
 * @Maintainer: 阮小文
 * @Modifier: 阮小文
 * @Description:
-->
<template>
  <base-voucher-preview-create-side
    class="VoucherPreviewSlidePanelForDepreciation"
    :visible="visible"
    :voucherData="voucherData"
    :businessTypeStr="businessTypeStr"
    @update:visible="updateVisible"
    @createVoucher="doConfirm"
  />
</template>

<script>
import get from 'lodash-es/get';

import dayjs from 'dayjs';

import BaseVoucherPreviewCreateSide from '@/components/BaseVoucherPreviewCreateSide.vue';
import {
  makeAssetDepreciationVoucherPreview,
  createAssetDepreciation,
  putAssetDetail,
} from '../../api';

// 凭证预览组件

const logError = console.error.bind(console);

export default {
  name: 'VoucherPreviewSlidePanelForDepreciation',

  components: {
    BaseVoucherPreviewCreateSide,
  },

  props: {
    // 当前对话框是否展示
    visible: {
      type: Boolean,
      required: true,
    },
  },

  data() {
    return {

      voucherData: {
        // 折旧固定资产记录数量
        total: 0,
        // 预计生成凭证记录数量
        voucherTotal: 0,
        // 凭证数据
        voucher: [],
      },
      previewParams: {},
      tagTypeParams: {},
    };
  },

  computed: {
    // 展示在顶部提示文字中的业务类型
    businessTypeStr() {
      return '折旧固定资产';
    },
  },

  watch: {
    // 对话框隐藏后，重置数据
    visible(value) {
      if (!value) {
        this.resetData();
      }
    },
  },

  methods: {
    hide() {
      this.$emit('update:visible', false);
    },

    updateVisible(value) {
      if (!value) {
        this.hide();
      }
    },

    // 通过折旧信息加载列表数据
    async loadMainTableData(depreciationInfo) {
      const {
        selectionIng,
        accountPeriod,
        mergeCredit,
        mergeDebit,
        tagTypeIds2,
        tagTypeIds4,
      } = depreciationInfo;
      try {
        this.previewParams = {
          accountPeriod: dayjs(accountPeriod).format('YYYYMM'),
          mergeCredit,
          mergeDebit,
          tagTypeMap: { 2: tagTypeIds2, 4: tagTypeIds4 },
          depreciations: selectionIng,
        };
        const response = await makeAssetDepreciationVoucherPreview(this.previewParams);
        const data = get(response, 'data.data[0]');
        this.voucherData = data;
        this.tagTypeParams = {
          tagTypeIds2,
          tagTypeIds4,
        };
        // 如果返回的结果中，还携带了提示信息，则打印出来
        if (data.warningMsg) {
          this.$alert(data.warningMsg);
        }
      } catch (error) {
        console.error(error);
      }
    },

    // 重置数据
    resetData() {
      this.voucherData.voucherTotal = 0;
      this.voucherData.total = 0;
      this.voucherData.voucher = [];
    },

    // 确认
    async doConfirm() {
      try {
        const params = {
          ...this.previewParams,
          vouchers: this.voucherData.vouchers,
        };
        console.log('params', params);
        await createAssetDepreciation(params).then(() => {
          putAssetDetail(params.depreciations);
          this.$message.success('操作成功！');
          this.$store.dispatch('optionHistory/setAssetsDepreciationTagType', this.tagTypeParams);
          this.$emit('confirm');
          this.hide();
        });
      } catch (error) {
        logError(error);
      }
    },

    formatterLeaf(row, column, cellValue) {
      return row.isLeaf ? '' : cellValue;
    },
  },
};
</script>
