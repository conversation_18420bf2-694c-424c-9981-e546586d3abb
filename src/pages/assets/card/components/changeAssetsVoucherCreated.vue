<template>
  <edit-voucher-preview origin="assetChange" :visible="voucherPreviewVisible" :voucherData="voucherData" @hide="hide"> </edit-voucher-preview>
</template>
<script>
import dayjs from 'dayjs';
import EditVoucherPreview from '@/components/EditVoucherPreview.vue';

export default {
  components: {
    EditVoucherPreview,
  },
  props: {
    // 当前对话框是否展示
    visible: {
      type: Boolean,
      required: true,
    },
    // 凭证日期
    voucherTime: {
      type: String,
      required: true,
    },
    // 变更后原值是否大于变更前原值（true为是）
    isGreater: {
      type: <PERSON>olean,
      required: true,
    },
    // 资产原值科目
    subjectFullName: {
      type: String,
      required: true,
    },
    // 凭证的金额
    subjectAmount: {
      type: Number,
      required: true,
    },
    // 资产的Id
    assetId: {
      type: String,
      required: true,
    },
    // 资产细表的ID
    assetItemsId: {
      type: String,
      required: true,
    },
    // 资产类别
    categoryType: {
      type: Number,
      required: true,
    },
    // 变更ID
    changeId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      voucherData: {
        items: [],
      },
      voucherPreviewVisible: false,
    };
  },
  watch: {
    // 对话框隐藏后，重置数据
    visible(value) {
      if (!value) {
        this.voucherPreviewVisible = false;
        this.resetData();
      } else {
        this.loadingVoucherData();
      }
    },
  },
  created() {
    this.loadingVoucherData();
  },
  computed: {
    functionalCurrency() {
      return this.$store.state.user.companyInfo.functionalCurrency;
    },
  },
  methods: {
    async loadingVoucherData() {
      console.log(this.voucherTime);
      const voucherTime = dayjs(this.voucherTime)
        .endOf('month')
        .format('YYYY-MM-DD');
      // 获取凭证号voucherCode
      const rsp = await this.$http.get(`/rest/companyAccount/merge/voucher/basic/manual/${voucherTime}/v1.0`);
      const voucher = {
        // 凭证日期
        voucherTime,
        // 凭证ID
        voucherId: '',
        // 凭证编号
        voucherCode: rsp.data.data[0].voucherCode || '',
        creationMode: 2,
        items: [],
      };
      const { subjectHashTable } = this.$store.state.selectData;
      const subjectCode = (subjectHashTable.items(this.subjectFullName) && subjectHashTable.items(this.subjectFullName).subjectCode) || '';
      if (this.isGreater) {
        const debitItem = {
          subjectAmount: this.subjectAmount,
          subjectFullName: this.subjectFullName,
          voucherDirection: 1,
          subjectCode,
          currencyCode: this.functionalCurrency,
        };
        const creditItem = {
          subjectAmount: 0,
          subjectFullName: '',
          voucherDirection: 2,
        };
        voucher.items.push(debitItem, creditItem);
      } else {
        const debitItem = {
          subjectAmount: 0,
          subjectFullName: '',
          voucherDirection: 1,
        };
        const creditItem = {
          subjectAmount: this.subjectAmount,
          subjectFullName: this.subjectFullName,
          voucherDirection: 2,
          subjectCode,
          currencyCode: this.functionalCurrency,
        };
        voucher.items.push(debitItem, creditItem);
      }
      voucher.assetId = this.assetId;
      voucher.assetItemsId = this.assetItemsId;
      voucher.categoryType = this.categoryType;
      voucher.changeId = this.changeId;
      voucher.isGreater = this.isGreater;
      this.voucherData = voucher;
      console.log(this.voucherData);
      this.voucherPreviewVisible = true;
    },
    hide() {
      this.$emit('update:visible', false);
    },
    // 重置数据
    resetData() {
      this.voucherData.items = [];
    },
  },
};
</script>
<style lang="scss" scoped></style>
