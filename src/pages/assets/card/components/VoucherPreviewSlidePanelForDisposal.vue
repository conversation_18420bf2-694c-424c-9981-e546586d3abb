<!--
 * @Component: 预览凭证侧滑面板（处置资产时）
 * @Maintainer: 阮小文
 * @Modifier: 阮小文
 * @Description:
-->
<template>
  <base-voucher-preview-create-side
    class="VoucherPreviewSlidePanelForDisposal"
    :visible="visible"
    :voucherData="voucherData"
    :showResetRule="false"
    :businessTypeStr="businessTypeStr"
    @update:visible="updateVisible"
    @createVoucher="doConfirm"
  />
</template>

<script>
import get from 'lodash-es/get';

import BaseVoucherPreviewCreateSide from '@/components/BaseVoucherPreviewCreateSide.vue';
import {
  previewVoucherForAssetDisposal,
  saveAssetDisposalInfo,
} from '../../api';

// 凭证预览组件

export default {
  name: 'VoucherPreviewSlidePanelForDisposal',

  components: {
    BaseVoucherPreviewCreateSide,
  },

  props: {
    // 当前对话框是否展示
    visible: {
      type: Boolean,
      required: true,
    },
    data: {
      type: Object,
    },
  },

  data() {
    return {
      voucherData: {
        // 折旧固定资产记录数量
        total: 0,
        // 预计生成凭证记录数量
        voucherTotal: 0,
        // 凭证数据
        voucher: [],
      },
      previewParams: {},
      tagTypeParams: {},
      xxvisible: false,
    };
  },

  computed: {
    // 展示在顶部提示文字中的业务类型
    businessTypeStr() {
      return '固定资产';
    },
  },

  watch: {
    // 对话框隐藏后，重置数据
    visible(value) {
      if (!value) {
        this.resetData();
      }
    },

    data() {
      this.loadMainTableData();
    },
  },

  methods: {
    hide() {
      this.$emit('update:visible', false);
    },

    updateVisible(value) {
      if (!value) {
        this.hide();
      }
    },

    // 通过折旧信息加载列表数据
    async loadMainTableData() {
      if (!this.data) return;
      const {
        accountPeriod,
        hasOutputInvoice,
        tagTypeIds1,
        tagTypeIds2,
        tagTypeIds3,
        disposalDetailPoList,
      } = this.data;
      const { vatType } = this.$store.state.user.taxInfo;
      this.previewParams = {
        accountPeriod,
        disposal: { ...this.data, disposalDetails: disposalDetailPoList },
        hasOutputInvoice,
        tagTypeMap: { 1: tagTypeIds1, 2: tagTypeIds2, 3: tagTypeIds3 },
        vatType,
      };
      try {
        const response = await previewVoucherForAssetDisposal(this.previewParams);
        const vouchers = get(response, 'data.data');
        this.tagTypeParams = {
          tagTypeIds1,
          tagTypeIds2,
          tagTypeIds3,
        };
        this.voucherData.voucher = vouchers;
        this.voucherData.total = this.data.disposalDetailPoList.length;
        this.voucherData.voucherTotal = vouchers.length;
      } catch (error) {
        console.error(error);
      }
    },

    // 重置数据
    resetData() {
      this.voucherData.voucherTotal = 0;
      this.voucherData.total = 0;
      this.voucherData.vouchers = [];
    },

    // 确认
    async doConfirm() {
      const params = {
          ...this.previewParams,
          vouchers: this.voucherData.voucher,
        };
      await saveAssetDisposalInfo(params);
      this.$message.success('操作成功！');
      this.$store.dispatch('optionHistory/setAssetsDisposalTagType', this.tagTypeParams);
      this.$emit('confirm');
      this.hide();
    },

    formatterLeaf(row, column, cellValue) {
      return row.isLeaf ? '' : cellValue;
    },
  },
};
</script>
