<!-- 用于显示 资产相关的凭证号，资产处置的凭证号内容 -->
<template>
  <div class="voucherWithAssetsItem-wrapper">
    <div class="voucherWithAssetsItem-itemBox">
      <span v-if="Object.keys(voucherWithMonth).length === 0">无</span>
      <div class="voucherWithAssetsItem-item" v-for="(item, month) in voucherWithMonth" :key="month">
        <div class="period">{{month | periodToMonth}}：</div>
        <div class="voucher-code">
          {{item | vouchersToCodes}}
        </div>
      </div>
    </div>

    <div class="operate" v-if="isCanEdit">
      <el-button @click="handleClickEdit" type="text">编辑</el-button>
    </div>
  </div>

</template>
<script>
import dayjs from 'dayjs';

export default {
  name: 'voucherWithAssetsItem',
  props: {
    vouchers: {
      type: Array,
      default: () => [],
    },
    isCanEdit: Boolean,
    relType: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {

    };
  },
  filters: {
    periodToMonth(val) {
      return dayjs(String(val)).format('YYYY年MM月');
    },
    vouchersToCodes(vouchers) {
      return vouchers.map((voucher) => voucher.voucherCode).join('、');
    },
  },
  computed: {
    voucherWithMonth() {
      const result = {};
      this.vouchers.forEach((item) => {
        if (item.relType !== this.relType) return;
        if (result[item.voucherPeriod]) {
          result[item.voucherPeriod].push(item);
        } else {
          result[item.voucherPeriod] = [item];
        }
      });
      return result;
    },
  },
  methods: {
    handleClickEdit() {
      this.$emit('edit');
    },
  },
};
</script>
<style lang="scss" scoped>
.voucherWithAssetsItem-wrapper{
  display: flex;
  .voucherWithAssetsItem-itemBox{
    flex: 1;
    line-height: 28px;
    .voucherWithAssetsItem-item{
      display: flex;
      .voucher-code{
        flex: 1;
        white-space: pre-line;
      }
    }
  }
  .operate{
    width: 30px;
    line-height: 30px;
  }
}
</style>
