<template>
    <div class="flex-v">
      <div class="toolbar flex-fixed">
        <!-- 勾选行操作区 -->
        <row-operate :selectionIng="listState.selection" :total="listState.records.length">
  
          <!-- <el-button type="text" @click="copyLastMonthIncomeDialog.open">批量</el-button> -->
          <el-button type="text" @click="preFillDialog.open">批量获取收入</el-button>
          <el-button type="text" @click="calculateTaxDialog.open">批量计算税款</el-button>
          <el-button :disabled="isFutureMonth" type="text" @click="declareDialog.open">批量申报</el-button>
          <el-button :disabled="isFutureMonth" type="text" @click="getPaymentCertDialog.open">批量获取完税凭证</el-button>
        </row-operate>
        <el-form :model="listState.queryObject" class="qf_query" inline @submit.native.prevent>
          <el-form-item>
            <el-input placeholder="账套名称" v-model="listState.queryObject.companyName"
              @keyup.enter.native="listMethods.loadListRecords" />
          </el-form-item>
          <el-form-item>
            <more-search @search="listMethods.loadListRecords" @reset-from="listMethods.resetQuery"
              :search-form-data="listState.queryObject">
               <DateTypePicker slot="date" :allowedTypes="['quarter', 'year']" v-model="listState.queryObject.declarationPeriod" @change="listMethods.loadListRecords" />
              <el-form-item label="申报状态">
                <el-select v-model="listState.queryObject.declarationState" clearable>
                  <el-option v-for="item in personTaxDeclareStatusEnum" :key="item.value" :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="绑定状态">
                <el-select v-model="listState.queryObject.bindStatus" clearable>
                  <el-option label="未绑定" :value="0" />
                  <el-option label="已绑定" :value="2" />
                </el-select>
              </el-form-item>
            </more-search>
          </el-form-item>
          <!-- <el-form-item label="税款所属期">{{ declarePeriod }}</el-form-item> -->
          <el-form-item>
            <el-checkbox :false-label="''" :true-label="'true'" v-model="listState.queryObject.whetherJoin"
              @change="listMethods.loadListRecords">
              仅显示已加入的账套
            </el-checkbox>
          </el-form-item>
        </el-form>
        <div class="action-wrapper">
        
          <el-button type="primary" @click="preFillDialog.open">批量获取收入</el-button>
          <el-button type="primary" @click="calculateTaxDialog.open">批量计算税款</el-button>
          <el-button :disabled="isFutureMonth" type="primary" @click="declareDialog.open">批量申报</el-button>
          <el-button :disabled="isFutureMonth" type="primary" @click="getPaymentCertDialog.open">获取完税凭证</el-button>

          <!-- <el-dropdown trigger="click">
            <el-button type="primary">
              更多操作<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <div :class="{disabled: isFutureMonth}" @click="!isFutureMonth && getPaymentCertDialog.open()">批量获取完税凭证</div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->
        </div>
      </div>
  
      <el-table ref="table" :data="listState.records" border stripe height="100%" class="flex-fluid"
        @selection-change="listMethods.onSelectionChange" v-loading="listState.loading">
        <el-table-column fixed="left" :selectable="canSelection" type="selection" align="center" />
        <el-table-column :min-width="240" prop="companyName" label="账套名称">
          <template slot-scope="{row}">
            <CompanyNameCell page="personTax" :companyInfo="row" :companyCommonTipList="companyCommonTipList" />
          </template>
        </el-table-column>
        <el-table-column prop="withholdAgentName" label="申报主体" min-width="180">
          <template slot-scope="{row}">
            <template v-if="hasTaxInfo(row)">
              {{ row.withholdAgentName }}
            </template>
            <el-button v-else type="text" @click="jumpToTaxInfo(row)">
              绑定
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="申报信息">
          <el-table-column prop="bblx" label="报送类型" :width="$store.getters.columnSize.SizeForCount(6)">
            <template slot-scope="{row}" v-if="hasTaxInfo(row)">
              {{ getDeclareTypeName(row.bblx) }}
            </template>
          </el-table-column>
          <el-table-column prop="numberOfTaxpayers" label="纳税人数" :width="$store.getters.columnSize.SizeForCount(8)">
            <template slot-scope="{row}">
              <template v-if="hasTaxInfo(row)">
                <template v-if="row.numberOfTaxpayers">
                  <el-button type="text" @click="jumpToDetail(row)">
                    {{ row.numberOfTaxpayers }}
                  </el-button>
                </template>
                <template v-else-if="hasTaxInfo">
                  <div style="display: flex; justify-content: space-between; align-items: center">
                    <div>未填写</div>
                    <div>
                      <el-button type="text" @click="jumpToDetail(row)">
                        填写
                      </el-button>
                    </div>
                  </div>
                </template>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="srze" label="收入总额" align="right" :width="$store.getters.columnSize.Buildin.Money">
            <template slot-scope="{row}" v-if="hasTaxInfo(row)">
              {{ row.srze !== null ? formatMoney(row.srze) : '' }}
            </template>
          </el-table-column>
  
          <!-- <el-table-column prop="specialAddOnUpdatesProcessStatus" label="预填扣除"
            :width="$store.getters.columnSize.SizeForCount(9)">
            <template slot-scope="{row}">
              <div v-if="hasTaxInfo(row)">
                <div style="display: flex; justify-content: space-between">
                  <div>
                    <el-tooltip placement="top">
                      <div slot="content">
                        <p>更新时间：{{ row.updateDate || '无' }}</p>
                      </div>
                      <i class="el-icon-timer" style="font-size: 14px;"></i>
                    </el-tooltip>
                    {{ getPreFillStatus(row.specialAddOnUpdatesProcessStatus) }}
                    <el-tooltip v-if="row.specialAddOnUpdatesProcessStatus === 3" :content="row.failExplain"
                      placement="top">…
                      <i class="Danger el-icon-warning-outline"></i>
                    </el-tooltip>
                  </div>
                  <el-button v-if="canPreFill(row)" type="text" @click="startPreFill(row)">
                    获取
                  </el-button>
                </div>
              </div>
            </template>
          </el-table-column> -->
          <el-table-column label="税款总额" :width="$store.getters.columnSize.SizeForCount(14)">
            <template slot-scope="{row}">
              <div v-if="hasTaxInfo(row)">
                <div style="display: flex; justify-content: space-between">
                  <div style="width: 110px">
                    <el-tooltip placement="top">
                      <div slot="content">
                        <p>更新时间：{{ row.updateDate || '无' }}</p>
                      </div>
                      <i class="el-icon-timer" style="font-size: 14px;"></i>
                    </el-tooltip>
                    {{ getTaxCalculateStatus(row.taxCalculationsProcessStatus) }}
                    <el-tooltip v-if="row.taxCalculationsProcessStatus === 3" :content="row.failExplain" placement="top">
                      <i class="Danger el-icon-warning-outline"></i>
                    </el-tooltip>
                    <el-button v-if="canCalculate(row)" type="text" @click="calculateTax(row)">
                      {{ row.taxCalculationsProcessStatus !== 0 ? '重算' : '计算' }}
                    </el-button>
                  </div>
                  <div v-if="row.ttIndividualIncomeTaxBusinessTotal !== null">
                    {{ formatMoney(row.ttIndividualIncomeTaxBusinessTotal.ybtseTotal) }}
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column prop="declarationState" label="申报状态" :width="$store.getters.columnSize.SizeForCount(10)">
          <template slot-scope="{row}">
            <div v-if="hasTaxInfo(row)" style="display: flex; justify-content: space-between">
              <div>
                <el-tooltip placement="top">
                  <div slot="content">
                    <p>更新时间：{{ row.updateDate || '无' }}</p>
                  </div>
                  <i class="el-icon-timer" style="font-size: 14px;"></i>
                </el-tooltip>
                {{ formatIndividualIncomeTaxDeclarationState(row.declarationState) || "未填写" }}
                <el-tooltip v-if="row.individualProcessStatus === 3" :content="row.failExplain" placement="top">
                  <i class="Danger el-icon-warning-outline"></i>
                </el-tooltip>
              </div>
              <el-button v-if="canDeclare(row)" type="text" @click="startDeclare(row)">
                申报
              </el-button>
              <el-button v-if="canPay(row)" type="text" @click="jumpToPayment(row)">
                缴款
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="declarationState" label="缴款状态" :width="$store.getters.columnSize.SizeForCount(10)">
          <template slot-scope="{row}">
            <div v-if="hasTaxInfo(row)" style="display: flex; justify-content: space-between">
              <div>
                <el-tooltip placement="top">
                  <div slot="content">
                    <p>更新时间：{{ row.updateDate || '无' }}</p>
                  </div>
                  <i class="el-icon-timer" style="font-size: 14px;"></i>
                </el-tooltip>
                {{ formatIndividualIncomeTaxPayTotalState(row.ttIndividualIncomeTaxBusinessTotal?.payTotalState) }}
                <el-tooltip v-if="row.individualProcessStatus === 3" :content="row.failExplain" placement="top">
                  <i class="Danger el-icon-warning-outline"></i>
                </el-tooltip>
              </div>
              <div>
                <!-- <el-popover width="300px">
                  <el-button  slot="reference" type="text"><i class="el-icon-tickets"></i>明细</el-button>
                    <el-table width="300px" :data="row.ttIndividualIncomeTaxBusinessItemsVoList">
                      <el-table-column width="100px" prop="xm" label="姓名">
                      </el-table-column>
                      <el-table-column width="100px" prop="payState" label="缴款状态">
                        <template slot-scope="{row}">
                          {{ formatIndividualIncomeTaxPersonPayState(row.payState) }}
                        </template>
                      </el-table-column>
                      <el-table-column width="100px" prop="payState" label="完税凭证">
                        <template slot-scope="{row}">
                          <el-button type="text" @click="showFileViewer(row.wszmBaseStr)">查看</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                </el-popover> -->

              <el-button v-if="canPay(row)" type="text" @click="jumpToPayment(row)">
                缴款
              </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="人员明细" align="center" :width="$store.getters.columnSize.SizeForCount(6)">
          <template slot-scope="{row}">
            <el-popover width="300px" v-if="row.ttIndividualIncomeTaxBusinessItemsVoList">
              <el-button slot="reference" type="text"><i class="el-icon-tickets"></i>明细</el-button>
                <el-table width="300px" :data="row.ttIndividualIncomeTaxBusinessItemsVoList">
                  <el-table-column width="100px" prop="xm" label="姓名">
                  </el-table-column>
                  <el-table-column width="100px" prop="payState" label="缴款状态">
                    <template slot-scope="{row}">
                      {{ formatIndividualIncomeTaxPersonPayState(row.payState) }}
                    </template>
                  </el-table-column>
                  <el-table-column width="100px" prop="payState" label="完税凭证">
                    <template slot-scope="{row}">
                      <el-button type="text" @click="showFileViewer(row.wszmBaseStr)">查看</el-button>
                    </template>
                  </el-table-column>
                </el-table>
            </el-popover>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="taxOfficeState" label="电局状态" :width="$store.getters.columnSize.SizeForCount(11)">
          <template slot-scope="{row}">
            <div v-if="hasTaxInfo(row)" style="display: flex; justify-content: space-between">
              <div>
                {{ formatPersonTaxElectricStatus(row.taxOfficeState) || '未获取'}}
              </div>
              <el-button v-if="canGetElectricStatus(row)" type="text" @click="getElectricStatus(row)">
                获取
              </el-button>
            </div>
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="完税凭证" :width="$store.getters.columnSize.SizeForCount(9)">
          <template slot-scope="{row}">
            <div v-if="hasTaxInfo(row)" style="display: flex; justify-content: space-between">
              {{ formatPersonTaxCertificateStatus(row.issuedStatus) }}
              <el-button v-if="canGetPaymentCert(row)" type="text" @click="getPaymentCert(row)">
                获取
              </el-button>
              <el-button v-if="canViewPaymentCert(row)" type="text" @click="viewPaymentCert(row)">
                下载
              </el-button>
            </div>
          </template>
        </el-table-column> -->
      </el-table>
  
      <pagination class="flex-fixed" @size-change="listMethods.onListPageSizeChange"
        @current-change="listMethods.onListPageChange" :current-page="listState.queryObject.page"
        :page-size="listState.queryObject.pageSize" layout="total, sizes, prev, pager, next"
        :total="listState.totalCount" />
  
      <!-- 批量复制上月 -->
      <BatchOperationDialog title="批量复制上月" content-title="批量复制以下账套的上月个税收入"
        :visible.sync="copyLastMonthIncomeDialog.visible.value" :model="copyLastMonthIncomeDialog.data"
        :has-selection="!!listState.selection.length" :confirm-method="handleSubmitCopyLastMonth" />
  
      <!-- 批量获取收入 -->
      <BatchOperationDialog title="批量获取收入" content-title="批量获取以下账套的收入信息" :visible.sync="preFillDialog.visible.value"
        :model="preFillDialog.data" :has-selection="!!listState.selection.length" :confirm-method="handleSubmitPreFill" />
  
      <!-- 批量计算税款 -->
      <BatchOperationDialog title="批量计算税款" content-title="批量计算以下账套的税款" :visible.sync="calculateTaxDialog.visible.value"
        :model="calculateTaxDialog.data" :has-selection="!!listState.selection.length"
        :confirm-method="handleSubmitCalculateTax" />
  
      <!-- 批量申报 -->
      <BatchOperationDialog title="批量申报" content-title="批量申报以下账套的个税" :visible.sync="declareDialog.visible.value"
        :model="declareDialog.data" :has-selection="!!listState.selection.length" :confirm-method="handleSubmitDeclare" />
  
      <!-- 批量缴款 -->
      <BatchOperationDialog title="批量缴款" content-title="批量缴纳以下账套的个税" :visible.sync="payDialog.visible.value"
        :model="payDialog.data" :has-selection="!!listState.selection.length" :confirm-method="handleSubmitPay" />
  
      <!-- 批量获取完税凭证 -->
      <BatchOperationDialog title="批量获取完税凭证" content-title="批量获取以下账套的完税凭证"
        :visible.sync="getPaymentCertDialog.visible.value" :model="getPaymentCertDialog.data"
        :has-selection="!!listState.selection.length" :confirm-method="handleSubmitGetPaymentCert" />
        
      <!-- 批量获取电局状态 -->
      <BatchOperationDialog title="批量获取电局状态" content-title="批量获取以下账套的电局状态"
        :visible.sync="getElectricStatusDialog.visible.value" :model="getElectricStatusDialog.data"
        :has-selection="!!listState.selection.length" :confirm-method="handleSubmitGetElectricStatus" />
        <!-- viewImage组件 -->
      <FileViewerDialog :visible="fileViewerState.visible" :url="fileUrl" :fileType="fileType" @hide="hide">
      </FileViewerDialog>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, computed, watch } from 'vue';
  import { Message, MessageBox } from 'element-ui';
  import dayjs from 'dayjs';
  import quarterOfYear from "dayjs/plugin/quarterOfYear";
  
  import DateTypePicker from '@/components/DateTypePicker.vue';
  
  import { omit, get } from 'lodash-es';
  import { useRouter } from 'vue-router/composables';
  import store from '@/store';
  import {
    getBkCompanyIndividualIncomeTaxBusinessList_IndividualIncomeTaxBusinessMerge_UsingGET,
    batchCheckTaxCalculation_IndividualIncomeTaxBusinessMerge_UsingPUT,
    batchOnlineDeclarationStatus_IndividualIncomeTaxBusinessMerge_UsingPUT,
    batchRevenueExpenseAndPrepaymentInfo_IndividualIncomeTaxBusinessMerge_UsingPUT,
    getbatchWszmBase_IndividualIncomeTaxBusinessMerge_UsingPUT
  } from '@/pages/tax/personTax/api/individualIncomeTax';
  
  import { formatMoney } from '@/assets/Utils';
  import { useList } from '@/hooks/useList';
  import doExport from '@/utils/exportFile';
  import { formatIndividualIncomeTaxZsfs, formatIndividualIncomeTaxPersonPayState,  formatIndividualIncomeTaxDeclarationState,
    formatPersonTaxCertificateStatus, formatPersonTaxElectricStatus, formatIndividualIncomeTaxPayTotalState
   } from '@/pages/tax/helper/formaters'
  import { personTaxDeclareStatusEnum } from '@/pages/tax/helper/enums';
  import BatchOperationDialog from '@/components/BatchOperationDialog/BatchOperationDialog.vue';
  import useDialogControl from '@/components/BatchOperationDialog/useDialogControl';
  import { useCompanyCommonTip } from '@/pages/controlCenter/tax/useCompanyCommonTip';
  import CompanyNameCell from '@/pages/controlCenter/components/CompanyNameCell.vue';
  import FileViewerDialog from '@/pages/tax/components/FileViewerDialog.vue';
  import useFileViewer from '@/pages/tax/components/useFileViewer';
  import { subtractQuarters, formatQuarter, getMonthsInQuarter } from '@/utils/quarterUtils';
  
  
  dayjs.extend(quarterOfYear);
  
  const router = useRouter();
  const table = ref(null);
  
  // 使用useList hook
  const { listState, listMethods } = useList({
    initQuery: {
      // declarationYearmonth: new Date(),
      declarationPeriod: subtractQuarters(formatQuarter(new Date()), 1),
      companyName: '',
      whetherJoin: 'true',
      declarationState: '',
      bindStatus: '',
      page: 1,
      pageSize: 50,
    },
    loadMethod: async (queryObject) => {
      // const declarationYearmonth = dayjs(queryObject.declarationYearmonth).format('YYYYMM');
      const rsp = await getBkCompanyIndividualIncomeTaxBusinessList_IndividualIncomeTaxBusinessMerge_UsingGET(queryObject);
      // 移除无用的收入明细数据， 避免数据量过大，影响响应式性能
      rsp.data.data = rsp.data.data?.map((item) => {
        item.numberOfTaxpayers = item.ttIndividualIncomeTaxBusinessItemsVoList?.length || 0;
        return item;
      }) || [];
      return rsp;
    },
  });

  const {
    showFileViewer, hide, fileType, fileUrl, fileViewerState,
  } = useFileViewer();
  
  const getFileUrl = (row) => {
    const API = `/rest/companytax/merge/individualIncome/declaration/v1.0/downloadFile/${row.bblx}/${row.declarationYearmonth}/${row.companyId}`;
    return API
  };
  
  // 申报月份是否为未来月份
  const isFutureMonth = computed(() => {
    const now = dayjs();
    const declarationYearmonth = dayjs(listState.queryObject.declarationPeriod);
    return now.isBefore(declarationYearmonth, 'month');
  });
  // 选择账套
  const selectCompany = async (companyId) => {
    await store.dispatch('user/changeCompany', { companyId });
  };
  // 获取申报类型名称
  function getDeclareTypeName(type) {
    const types = {
      1: '预缴纳税申报',
      2: '年度汇繳申报',
    };
    return types[type] || '';
  }
  
  // 获取预填状态
  function getPreFillStatus(status) {
    const statusMap = {
      0: '未获取',
      1: '已获取',
      2: '获取中',
      3: '获取失败',
    };
    return statusMap[status] || '未获取';
  }
  
  // 附表填写状态  fillInStatus 填写状态 1 无需填写 2已填写 3未填写
  function getAttachmentStatus(status) {
    const statusMap = {
      1: '无需填写',
      2: '已填写',
      3: '未填写',
    };
    return statusMap[status] || '';
  }
  
  // 获取税款计算状态
  function getTaxCalculateStatus(status) {
    const statusMap = {
      0: '暂未计算',
      1: '计算完成',
      2: '正在计算',
      3: '计算失败',
    };
    return statusMap[status] || '未获取';
  }
  // 是否已绑定个税信息
  function hasTaxInfo(row) {
    return row.bindStatus === 2;
  }
  // 只要加入了账套的才能勾选税种进行操作
  const { companyCommonTipList, getCompanyCommonTipList, isOverdue } = useCompanyCommonTip();
  // 是否允许操作
  function canOperation(row) {
    return row.whetherJoin && hasTaxInfo(row) && !isOverdue(row.companyId);
  }
  // 只要加入了账套的才能勾选税种进行操作
  const canSelection = (row) => row.whetherJoin && hasTaxInfo(row) && !isOverdue(row.companyId);
  // 判断是否可以计算税款
  function canCalculate(row) {
    return canOperation(row) && row.numberOfTaxpayers && [1, 4].includes(row.declarationState);
  }
  // 判断是否可以预填
  function canPreFill(row) {
    return row.withholdAgentName
      && canOperation(row)
      && [0, 1, 3].includes(row.specialAddOnUpdatesProcessStatus)
      && [0, 1, 4].includes(row.declarationState);
  }
  // 判断是否可以申报
  function canDeclare(row) {
    return !isFutureMonth.value && canOperation(row) && [1, 4].includes(row.declarationState) && row.taxCalculationsProcessStatus === 1;
  }
  
  // 判断是否可以缴款
  function canPay(row) {
    return canOperation(row) && [3, 7].includes(row.declarationState) && row.ttIndividualIncomeTaxBusinessTotal?.payTotalState !== 1;
  }
  
  // 获取完税凭证状态
  function getPaymentStatus(row) {
    if (!hasTaxInfo(row) || !row.numberOfTaxpayers) return '';
    if (row.wszmUrl) return '已获取';
    if (row.gettingCert) return '获取中';
    return '未获取';
  }
  
  // 判断是否可以获取完税凭证
  function canGetPaymentCert(row) {
    return canOperation(row) && row.ybtse > 0 && [2, 3].includes(row.declarationState);
  }
  
  // 判断是否可以获取电局状态
  function canGetElectricStatus(row) {
    return !dayjs(row.declarationYearmonth).isAfter(dayjs(), 'month');
  }
  
  // 判断是否可以查看完税凭证
  function canViewPaymentCert(row) {
    return row.issuedStatus === 1;
  }
  
  // 判断是否需要填写附表
  function canAttachment(row) {
    return canOperation(row) && row.numberOfTaxpayers && [0, 3].includes(row.attachmentStatus);
  }
  
  // 跳转到附表页面
  function jumpToAttachment(row) {
    // TODO: 实现跳转逻辑
  }
  
  // 跳转到税务信息页面
  async function jumpToTaxInfo(row) {
    // 先选中对应的账套
    await selectCompany(row.companyId);
    // 跳转到申报信息页面
    router.push({
      name: 'taxHome',
    });
  }
  // 跳转到个税详情页面
  async function jumpToDetail(row) {
    // TODO: 实现跳转逻辑
    await selectCompany(row.companyId);
    router.push({
      name: 'personTax',
      params: {
        declarationPeriod: listState.queryObject.declarationPeriod,
        tab: '经营所得',
      },
    });
  }
  // 所有批量操作的接口操作成功后的通用处理
  async function handleBatchOperationSuccess(title, rsp) {
    const reslut = get(rsp, 'data.data[0]');
    const MessageContent = `
          <div>${reslut}</div>
        `;
    MessageBox({
      title,
      message: MessageContent,
      dangerouslyUseHTMLString: true,
    });
    listMethods.loadListRecords();
  }
  
  // 根据弹窗表单数据对象生成批量操作所需的参数的 通用处理方法
  function getBatchOperationParams(data) {
    let params = {
    };
    if (data.contentScope === 2) {
      params = {
        ...omit(listState.queryObject, ['page', 'pageSize', 'declarationPeriod']),
      };
    } else if (data.contentScope === 3) {
      params = {
        companyIds: listState.selection.map((item) => item.companyId),
      };
    }
    params.selectType = data.contentScope;
    return { params, declarationPeriod: listState.queryObject.declarationPeriod };
  }
  
  // 复制上月收入 -start
  const copyLastMonthIncomeDialog = useDialogControl({
    contentScope: 1,
  });
  
  const handleSubmitCopyLastMonth = async () => {
    const { params, declarationPeriod } = getBatchOperationParams(copyLastMonthIncomeDialog.data);
    const rsp = await batchCopyDataOfThePreviousMonthIndividualIncomeTax(params, { declarationPeriod });
    handleBatchOperationSuccess('复制上月数据任务发起成功', rsp);
  };
  
  const onCopySingleRow = async (row) => {
    // TODO: 实现复制逻辑
    const params = {
      companyIds: [row.companyId],
      selectType: 3,
    };
    const declarationPeriod = listState.queryObject.declarationPeriod;
    const rsp = await batchCopyDataOfThePreviousMonthIndividualIncomeTax(params, { declarationPeriod });
    handleBatchOperationSuccess('复制上月数据任务发起成功', rsp);
  };
  
  // 复制上月收入 -end
  
  // 开始预填
  const preFillDialog = useDialogControl({
    contentScope: 1,
  });
  
  const handleSubmitPreFill = async () => {
    const { params, declarationPeriod } = getBatchOperationParams(preFillDialog.data);
    const rsp = await batchRevenueExpenseAndPrepaymentInfo_IndividualIncomeTaxBusinessMerge_UsingPUT(params, { declarationPeriod });
    handleBatchOperationSuccess('预填扣除信息任务发起成功', rsp);
  };
  
  function startPreFill(row) {
    if (!row) {
      preFillDialog.open();
      return;
    }
    const params = {
      companyIds: [row.companyId],
      selectType: 3,
    };
    const declarationPeriod = listState.queryObject.declarationPeriod;
    batchRevenueExpenseAndPrepaymentInfo_IndividualIncomeTaxBusinessMerge_UsingPUT(params, { declarationPeriod })
      .then((rsp) => handleBatchOperationSuccess('预填扣除信息任务发起成功', rsp));
  }
  
  // 计算税款
  const calculateTaxDialog = useDialogControl({
    contentScope: 1,
  });
  
  const handleSubmitCalculateTax = async () => {
    const { params, declarationPeriod } = getBatchOperationParams(calculateTaxDialog.data);
    const rsp = await batchCheckTaxCalculation_IndividualIncomeTaxBusinessMerge_UsingPUT(params, { declarationPeriod });
    handleBatchOperationSuccess('计算税款任务发起成功', rsp);
  };
  
  function calculateTax(row) {
    if (!row) {
      calculateTaxDialog.open();
      return;
    }
    const params = {
      companyIds: [row.companyId],
      selectType: 3,
    };
    const declarationPeriod = listState.queryObject.declarationPeriod;
    batchCheckTaxCalculation_IndividualIncomeTaxBusinessMerge_UsingPUT(params, { declarationPeriod })
      .then((rsp) => handleBatchOperationSuccess('计算税款任务发起成功', rsp));
  }
  
  // 开始申报
  const declareDialog = useDialogControl({
    contentScope: 1,
  });
  
  const handleSubmitDeclare = async () => {
    const { params, declarationPeriod } = getBatchOperationParams(declareDialog.data);
    const rsp = await batchSubmissionDeclarationIndividualIncomeTax(params, { declarationPeriod });
    handleBatchOperationSuccess('申报任务发起成功', rsp);
  };
  
  function startDeclare(row) {
    if (!row) {
      declareDialog.open();
      return;
    }
    // 添加确认操作
    MessageBox.confirm('确认要发起申报任务吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      const params = {
        companyIds: [row.companyId],
        selectType: 3,
      };
      const declarationPeriod = listState.queryObject.declarationPeriod;
      batchSubmissionDeclarationIndividualIncomeTax(params, { declarationPeriod })
        .then((rsp) => handleBatchOperationSuccess('申报任务发起成功', rsp));
    }).catch(() => {
      Message({
        type: 'info',
        message: '已取消',
      });
    });
  }
  
  // 开始缴款
  const payDialog = useDialogControl({
    contentScope: 1,
  });
  
  const handleSubmitPay = async () => {
    const { params, declarationPeriod } = getBatchOperationParams(payDialog.data);
    const rsp = await updateBatchPaymentOfTaxesIndividualIncomeTax(params, { declarationPeriod });
    handleBatchOperationSuccess('缴款任务发起成功', rsp);
  };
  
  async function jumpToPayment(row) {
    await selectCompany(row.companyId);
    router.push({
      name: 'individualIncomeTaxPayment',
      params: {
        declarationPeriod: listState.queryObject.declarationPeriod,
      },
    })
  }
  
  // 获取完税凭证
  const getPaymentCertDialog = useDialogControl({
    contentScope: 1,
  });
  
  const handleSubmitGetPaymentCert = async () => {
    const { params, declarationPeriod } = getBatchOperationParams(getPaymentCertDialog.data);
    return getbatchWszmBase_IndividualIncomeTaxBusinessMerge_UsingPUT(params, { declarationPeriod })
      .then(rsp => handleBatchOperationSuccess('获取完税凭证任务发起成功', rsp));
  };
  
  // 获取电局状态
  const getElectricStatusDialog = useDialogControl({
    contentScope: 1,
  });
  
  const getElectricStatus = (row) => {
    if (!row) {
      getElectricStatusDialog.open();
      return;
    }
    MessageBox.confirm('确认要获取电局状态吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      const params = {
        companyIds: [row.companyId],
        selectType: 3,
      };
      const declarationPeriod = listState.queryObject.declarationPeriod;
      batchOnlineDeclarationStatus_IndividualIncomeTaxBusinessMerge_UsingPUT(params, { declarationPeriod })
        .then((rsp) => handleBatchOperationSuccess('获取电局状态任务发起成功', rsp));
    }).catch(() => {
      Message({
        type: 'info',
        message: '已取消',
      });
    });
  };
  
  const handleSubmitGetElectricStatus = () => {
    const { params, declarationPeriod } = getBatchOperationParams(getElectricStatusDialog.data);
    getElectricStatusDialog.close();
    return batchOnlineDeclarationStatus_IndividualIncomeTaxBusinessMerge_UsingPUT(params, { declarationPeriod })
      .then(rsp => handleBatchOperationSuccess('获取电局状态任务发起成功', rsp));
  };
  
  function getPaymentCert(row) {
    if (!row) {
      getPaymentCertDialog.open();
      return;
    }
    const params = {
      companyIds: [row.companyId],
      selectType: 3,
    };
    const declarationPeriod = listState.queryObject.declarationPeriod;
    getbatchWszmBase_IndividualIncomeTaxBusinessMerge_UsingPUT(params, { declarationPeriod })
      .then((rsp) => handleBatchOperationSuccess('获取完税凭证任务发起成功', rsp));
  }
  
  // 查看完税凭证
  function viewPaymentCert(row) {
    // TODO: 实现查看逻辑
    if (row.issuedStatus === 1) {
      doExport(`/rest/companytax/merge/individualIncome/declaration/v1.0/downloadTheFile/${row.bblx}/${listState.queryObject.declarationPeriod}/${row.companyId}`);
    }
  }
  
  watch(() => listState.records, async () => {
    getCompanyCommonTipList(listState.records.map((item) => item.companyId));
  });
  
  onMounted(() => {
    listMethods.loadListRecords();
  });
  </script>
  
  <style lang="scss" scoped>.disabled {
    color: #c0c4cc;
    cursor: not-allowed;
  }
  </style>
  