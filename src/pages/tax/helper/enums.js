// 申报周期（5:次 4:月 3:季 2:半年 1:年）
export const declareCycleEnum = Object.freeze([
  Object.freeze({ label: '年', value: 1 }),
  Object.freeze({ label: '半年', value: 2 }),
  Object.freeze({ label: '季', value: 3 }),
  Object.freeze({ label: '月', value: 4 }),
  Object.freeze({ label: '次', value: 5 }),
]);
// 申报状态 finalStatus 0、'草稿', 1、'已存档未申报', 2、'申报中', 3、'申报失败', 4、'申报成功' 5、 '已准备'
export const TaxfinalStatusEnum = Object.freeze([
  Object.freeze({ label: '草稿', value: 0 }),
  Object.freeze({ label: '已存档未申报', value: 1 }),
  Object.freeze({ label: '申报中', value: 2 }),
  Object.freeze({ label: '申报失败', value: 3 }),
  Object.freeze({ label: '申报成功', value: 4 }),
  Object.freeze({ label: '已准备', value: 5 }),
]);
//  合并后的申报状态；0-未申报 1-已申报  3: 申报失败(转换) 5: 申报准备 (转换)   9-申报中 10-导入中（仅电子税局异步申报有这个状态） 77 获取失败 88 获取中 99 未获取
export const afterProDeclarationStateEnum = Object.freeze([
  { label: '未申报', value: 0 },
  { label: '已申报', value: 1 },
  { label: '申报失败', value: 3 },
  { label: '已准备', value: 5 },
  { label: '申报中', value: 9 },
  { label: '导入中', value: 10 },
  { label: '获取失败', value: 77 },
  { label: '获取中', value: 88 },
  { label: '未获取', value: 99 },
]);
// 清册申报状态 0-未申报 1-已申报 9-申报中 10-导入中
export const TaxDeclareStatusEnum = Object.freeze([
  Object.freeze({ label: '未申报', value: 0 }),
  Object.freeze({ label: '已申报', value: 1 }),
  Object.freeze({ label: '申报中', value: 9 }),
  Object.freeze({ label: '导入中', value: 10 }),
]);
// 电局电局期初获取状态0、未获取 1 已获取（后面显示具体时间）2、获取中
export const TaxInitDataStatusEnum = Object.freeze([
  { label: '未获取', value: 0 },
  { label: '已获取', value: 1 },
  { label: '获取中', value: 2 },
  { label: '获取失败', value: 3 },
]);
// 申报准备状态 1、未准备2、准备中 3、已准备（后面显示具体时间）4 异常
export const DeclareReadyStatusEnum = Object.freeze([
  { label: '未准备', value: 1 },
  { label: '准备中', value: 2 },
  { label: '已准备', value: 3 },
  { label: '异常', value: 4 },
]);
// 本地申报状态  1、未申报。当本地当期申报税款所属期无记录，或存在草稿记录时，显示为“未申报”；2、申报中：本地记录提交申报，则显示为“申报中”3、申报成功：（后面显示具体时间）4、申报失败： 5异常
export const LocalTaxDeclareStatusEnum = Object.freeze([
  { label: '未申报', value: 1 },
  { label: '申报中', value: 2 },
  { label: '申报成功', value: 3 },
  { label: '申报失败', value: 4 },
  { label: '异常', value: 5 },
]);
// 缴款状态 -1无需缴款，0未缴款，1已缴款，9缴款中
export const TaxPaymentStatusEnum = Object.freeze([
  { label: '无需缴款', value: -1 },
  { label: '未缴款', value: 0 },
  { label: '已缴款', value: 1 },
  { label: '缴款中', value: 9 },
]);
// 合并后的缴款状态  -1无需缴款，0未交款，1已缴款，9缴款中 77 获取失败 88 获取中 99 未获取
export const afterProPaymentStatusEnum = Object.freeze([
  { label: '无需缴款', value: -1 },
  { label: '未缴款', value: 0 },
  { label: '已缴款', value: 1 },
  { label: '缴款中', value: 9 },
  { label: '获取失败', value: 77 },
  { label: '获取中', value: 88 },
  { label: '未获取', value: 99 },
]);
// 税种列表
export const taxTypeList = Object.freeze([
  { label: '增值税及附加税(适用于小规模纳税人)', value: 'BDA0610611' },
  { label: '增值税及附加税(适用于一般纳税人)', value: 'BDA0610606' },
  { label: '消费税及附加税费申报表', value: 'BDA0611111' },
  { label: '企业所得税A类（2021年版A类）（查账征收）', value: 'BDA0611159' },
  { label: '企业所得税年报（汇算清缴）', value: 'BDA0610994' },
  { label: '企业所得税月（季）度申报表（B类，2018年版）', value: 'BDA0611038' },
  { label: '印花税纳税申报表', value: 'BDA0610794' },
  { label: '文化事业建设费', value: 'BDA0610334' },
  { label: '工会经费等通用申报', value: 'BDA0610100' },
  { label: '个人所得税代扣代缴', value: 'BDA0610135' },
  { label: '综合所得个人所得税预扣预缴申报', value: 'BDA0611062' },
  { label: '分类所得个人所得税代扣代缴报告表', value: 'BDA0611065' },
  { label: '非居民个人所得税代扣代缴报告表', value: 'BDA0611068' },
  { label: '残保金', value: 'BDA0610857' },
  { label: '社保费', value: 'BDA0610222' },
  { label: '环境保护税纳税申报表', value: 'BDA0610980' },
  { label: '城镇土地使用税', value: 'BDA0610790' },
  { label: '房产税', value: 'BDA0610791' },
  { label: '城镇土地使用税税源采集', value: 'BDA0610790SYCJ' },
  { label: '房产税税源采集', value: 'BDA0610791SYCJ' },
  { label: '资源税', value: 'BDA0610036' },
  { label: '水资源税', value: 'BDA0610987' },
  { label: '车辆购置税', value: 'BDA0610128' },
  { label: '车船税', value: 'BDA0610005' },
  { label: '契税', value: 'BDA0610108' },
  { label: '非税收入垃圾处理费', value: 'BDA0611054' },
  { label: '企业年度关联业务往来报告表', value: 'BDA0610068' },
  { label: '财务报表申报（月季报）', value: 'CWBBSB' },
  { label: '财务报表年度申报（年报）', value: 'CWBBNDSB' },
  { label: '定期定额纳税申报表', value: 'BDA0610642' },
]);
// 会计准则
export const accountRuleList = Object.freeze([
  { label: '企业会计准则', value: 101 },
  { label: '小企业会计准则', value: 102 },
  { label: '企业会计制度', value: 201 },
]);
// 资料报送小类类型（zlbsxlDm）
export const accountSmallClassList = Object.freeze([
  { label: '企业会计准则（一般企业）财务报表报送与信息采集', value: 'ZL1001001' },
  { label: '企业会计制度财务报表报送与信息采集', value: 'ZL1001002' },
  { label: '小企业会计准则财务报表报送与信息采集', value: 'ZL1001003' },
  { label: '行业会计制度（运输[交通]）财务报表报送与信息采集', value: 'ZL1001010' },
  { label: '企业会计制度（民航企业会计核算办法）财务报表报送与信息采集', value: 'ZL1001012' },
  { label: '农民专业合作社财务会计制度财务报表报送与信息采集', value: 'ZL1001015' },
  { label: '勘察设计企业会计制度财务报表报送与信息采集', value: 'ZL1001016' },
  { label: '企业会计制度（保险中介公司会计核算办法）财务报表报送与信息采集', value: 'ZL1001017' },
  { label: '企业会计准则（商业银行）财务报表报送与信息采集', value: 'ZL1001018' },
  { label: '企业会计准则（证券公司）财务报表报送与信息采集', value: 'ZL1001019' },
  { label: '企业会计准则（保险公司）财务报表报送与信息采集', value: 'ZL1001020' },
  { label: '企业会计准则（典当企业）财务报表报送与信息采集', value: 'ZL1001021' },
  { label: '企业会计准则（担保企业会计核算办法）财务报表报送与信息采集', value: 'ZL1001022' },
  { label: '民间非营利组织会计制度财务报表报送与信息采集', value: 'ZL1001023' },
  { label: '事业单位会计制度财务报表报送与信息采集', value: 'ZL1001024' },
  { label: '中小学校会计制度财务报表报送与信息采集', value: 'ZL1001025' },
  { label: '高等学校会计制度财务报表报送与信息采集', value: 'ZL1001026' },
  { label: '医院会计制度财务报表报送与信息采集', value: 'ZL1001027' },
  { label: '科学事业单位会计制度财务报表报送与信息采集', value: 'ZL1001028' },
  { label: '测绘事业单位会计制度财务报表报送与信息采集', value: 'ZL1001029' },
  { label: '文化事业单位会计制度财务报表报送与信息采集', value: 'ZL1001030' },
  { label: '广播事业单位会计制度财务报表报送与信息采集', value: 'ZL1001031' },
  { label: '体育事业单位会计制度财务报表报送与信息采集', value: 'ZL1001032' },
  { label: '文物事业单位会计制度财务报表报送与信息采集', value: 'ZL1001033' },
  { label: '人口和计划生育事业单位会计制度财务报表报送与信息采集', value: 'ZL1001034' },
  { label: '银行类金融企业财务报表报送与信息采集', value: 'ZL1001036' },
  { label: '证券类金融企业财务报表报送与信息采集', value: 'ZL1001037' },
  { label: '保险类金融企业财务报表报送与信息采集', value: 'ZL1001038' },
  { label: '担保类金融企业财务报表报送与信息采集', value: 'ZL1001039' },
  { label: '金融控股集团公司类金融企业财务报表报送与信息采集', value: 'ZL1001040' },
  { label: '金融资产管理公司类金融企业财务报表报送与信息采集', value: 'ZL1001041' },
  { label: '彩票机构会计制度财务报表报送与信息采集', value: 'ZL1001042' },
  { label: '村集体经济组织财务报表报送与信息采集', value: 'ZL1001043' },
  { label: '个体工商户财务报表报送与信息采集', value: 'ZL1001044' },
  { label: '企业集团合并财务报表报送与信息采集', value: 'ZL1001045' },
  { label: '行政单位会计制度财务报表报送与信息采集', value: 'ZL1001046' },
  { label: '基层医疗卫生机构会计制度财务报表资料报送与信息采集', value: 'ZL1001047' },
]);

// 纳税人性质
export const vatTypeList = Object.freeze([
  { label: '一般纳税人', value: 1 },
  { label: '小规模纳税人', value: 2 },
]);
// 清册状态0获取成功 1获取中 2获取失败
export const TaxInventoryStatusEnum = Object.freeze([
  { label: '未获取', value: null },
  { label: '获取成功', value: 0 },
  { label: '获取中', value: 1 },
  { label: '获取失败', value: 2 },
]);

// 个税收入类型枚举
export const personIncomeTypeEnum = Object.freeze([
  { label: '正常工资资金所得', value: 1, code: '0101' },
  { label: '全年一次性将奖金收入', value: 2, code: '0103' },
  { label: '内退一次性补偿金', value: 4, code: '0107' },
  { label: '年金领取', value: 5, code: '0110' },
  { label: '解除劳动合同一次性补偿金', value: 6, code: '0108' },
  { label: '央企负责人绩效薪金延期兑现收入和任期奖励', value: 7, code: '' },
  { label: '单位低价向职工售房', value: 8, code: '' },
  { label: '劳务报酬（适用累计预扣法）', value: 9, code: '0411' },
  { label: '劳务报酬（不适用累计预扣法）', value: 10, code: '0412' },
  { label: '稿酬所得', value: 11, code: '0500' },
  { label: '特许权使用费所得', value: 12, code: '0600' },
  { label: '提前退休一次性补贴', value: 13, code: '0111' },
  { label: '个人股权激励收入（非居民）', value: 14, code: '0119' },
  { label: '税收递延型商业养老金', value: 15, code: '' }
]);
// 个税申报状态  0未填写 1已填写未申报 2申报中 3申报成功 4申报失败 5缴款中 6缴款成功 7缴款失败 8作废中 9作废成功 10作废失败 99 更正作废
export const personTaxDeclareStatusEnum = Object.freeze([
  { label: '未填写', value: 0 },
  { label: '已填写未申报', value: 1 },
  { label: '申报中', value: 2 },
  { label: '申报成功', value: 3 },
  { label: '申报失败', value: 4 },
  { label: '缴款中', value: 5 },
  { label: '缴款成功', value: 6 },
  { label: '缴款失败', value: 7 },
  { label: '作废中', value: 8 },
  { label: '作废成功', value: 9 },
  { label: '作废失败', value: 10 },
  { label: '更正作废', value: 99 },
]);
// 个税电局状态  0未填写 1已填写未申报 2申报中 3申报成功 4申报失败 5缴款中 6缴款成功 7缴款失败 8作废中 9作废成功 10作废失败 11 申报成功,无需缴款 12申报成功,未缴款
export const personTaxElectricStatusEnum = Object.freeze([
  { label: '未填写', value: 0 },
  { label: '已填写未申报', value: 1 },
  { label: '申报中', value: 2 },
  { label: '申报成功', value: 3 },
  { label: '申报失败', value: 4 },
  { label: '缴款中', value: 5 },
  { label: '缴款成功', value: 6 },
  { label: '缴款失败', value: 7 },
  { label: '作废中', value: 8 },
  { label: '作废成功', value: 9 },
  { label: '作废失败', value: 10 },
  { label: '申报成功,无需缴款', value: 11 },
  { label: '申报成功,未缴款', value: 12 },
]);

// 个税完税证明状态 [issuedStatus] - 0未开具  1已开具  2开具中 3开具失败
export const personTaxCertificateStatusEnum = Object.freeze([
  { label: '未开具', value: 0 },
  { label: '已开具', value: 1 },
  { label: '开具中', value: 2 },
  { label: '开具失败', value: 3 },
]);

// 经营所得申报状态 declarationState 申报状态 0未填写 1已填写未申报 2申报中 3申报成功 4申报失败 8作废中 9作废成功 10作废失败
export const individualIncomeTaxDeclarationStateEnum = Object.freeze([
  { label: '未填写', value: 0 },
  { label: '已填写未申报', value: 1 },
  { label: '申报中', value: 2 },
  { label: '申报成功', value: 3 },
  { label: '申报失败', value: 4 },
  { label: '作废中', value: 8 },
  { label: '作废成功', value: 9 },
  { label: '作废失败', value: 10 },
]);
// 经营所得征收方式 zsfs 征收方式（1查账征收据实预缴、2查账征收上年应纳税所得额、3定期定额、4按税务机关认可的其他方式（所得率）、5核定征收核定应税所得额（能准确核算成本费用的）、6核定征收核定应纳税所得额）
export const individualIncomeTaxZsfsEnum = Object.freeze([
  { label: '查账征收据实预缴', value: 1 },
  { label: '查账征收上年应纳税所得额', value: 2 },
  { label: '定期定额', value: 3 },
  { label: '按税务机关认可的其他方式（所得率）', value: 4 },
  { label: '核定征收核定应税所得额（能准确核算成本费用的）', value: 5 },
  { label: '核定征收核定应纳税所得额', value: 6 },
]);

// 经营所得个人缴款状态 payState 缴款状态 -1无需缴款，0未缴款，1已缴款，9缴款中
export const individualIncomeTaxPersonPayStateEnum = Object.freeze([
  { label: '无需缴款', value: -1 },
  { label: '未缴款', value: 0 },
  { label: '已缴款', value: 1 },
  { label: '缴款中', value: 9 },
]);

// 经营所得缴款状态 payTotalState 缴款状态 -1无需缴款 0未交款 1已缴款 2部分缴款 3缴款失败
export const individualIncomeTaxPayTotalStateEnum = Object.freeze([
  { label: '无需缴款', value: -1 },
  { label: '未缴款', value: 0 },
  { label: '已缴款', value: 1 },
  { label: '部分缴款', value: 2 },
  { label: '缴款失败', value: 3 },
]);
