import request from '@/utils/request';

/** 
 * @description 个税模块-税款计算检查 
 * @param {Object} pathParams
 * @param {number} pathParams.bblx - bblx
 * @param {number} pathParams.declarationYearmonth - declarationYearmonth  
**/
export const checkTaxCalculation_IndividualIncomeTaxBusinessMerge_UsingPUT = ({ bblx, declarationYearmonth }) =>
    request.put(`/rest/companytax/merge/individualIncome/business/v1.0/checkTaxCalculation/${bblx}/${declarationYearmonth}`);
/** 
* @description 个税模块-更正申报 
* @param {Object} pathParams
* @param {number} pathParams.bblx - bblx
* @param {number} pathParams.declarationYearmonth - declarationYearmonth  
**/
export const correctionOfDeclarations_IndividualIncomeTaxBusinessMerge_UsingPUT = (
    { bblx, declarationYearmonth }
) =>
    request.put(`/rest/companytax/merge/individualIncome/business/v1.0/correctionOfDeclarations/${bblx}/${declarationYearmonth}`);

/** 
 * @description 个税模块-生产经营-查询申报信息 
 * @param {Object} params 
 * @param {number} params.batchOperationParamsList[0].bblx - 报表类型
 * @param {number} params.batchOperationParamsList[0].companyId - 账套ID
 * @param {number} params.bblx - 申报表类型，1、预缴纳税；2、年度汇缴
 * @param {number} params.bindStatus - 绑定状态
 * @param {number} params.companyId - 账套ID
 * @param {string} params.companyName - 账套名称
 * @param {number} params.declarationState - 申报状态
 * @param {number} params.declarationYearmonth - 申报年月
 * @param {string} params.group[0].dir - 排序类型，如果接口支持自定义排序才会往效
 * @param {string} params.group[0].field - 排序序名称名，如果接口支持自定义排序才会往效
 * @param {number} params.page - 分页-第几页
 * @param {number} params.pageSize - 分页-每页记录数
 * @param {string} params.sd[0].dir - 排序类型，如果接口支持自定义排序才会往效
 * @param {string} params.sd[0].field - 排序序名称名，如果接口支持自定义排序才会往效
 * @param {number} params.tagStatus - 默认查询不作废
 * @param {boolean} params.whetherJoin - 是否加入公司   
**/
export const getIndividualIncomeTaxBusinessList_IndividualIncomeTaxBusinessMerge_UsingGET = (params) =>
    request.get(`/rest/companytax/merge/individualIncome/business/v1.0/getIndividualIncomeTaxBusinessList`, { params });
/** 
* @description 个税模块-撤销更正申报 
* @param {Object} pathParams
* @param {number} pathParams.bblx - bblx
* @param {number} pathParams.declarationYearmonth - declarationYearmonth  
**/
export const revocationOfTheCorrectionDeclaration_IndividualIncomeTaxBusinessMerge_UsingPUT = (
    { bblx, declarationYearmonth }
) =>
    request.put(`/rest/companytax/merge/individualIncome/business/v1.0/revocationOfTheCorrectionDeclaration/${bblx}/${declarationYearmonth}`);
/** 
* @description 个税模块-申报报送 
* @param {Object} pathParams
* @param {number} pathParams.bblx - bblx
* @param {number} pathParams.declarationYearmonth - declarationYearmonth  
**/
export const submissionDeclaration_IndividualIncomeTaxBusinessMerge_UsingPUT = (
    { bblx, declarationYearmonth }
) =>
    request.put(`/rest/companytax/merge/individualIncome/business/v1.0/submissionDeclaration/${bblx}/${declarationYearmonth}`);
/** 
 * @description 个税模块-修改指定月份报表 
 * @param {TtIndividualIncomeTaxBusinessVo} params - ttIndividualIncomeTaxBusinessEditVo 
 * @param {Object} pathParams
 * @param {number} pathParams.bblx - bblx
 * @param {number} pathParams.declarationYearmonth - declarationYearmonth  
**/
export const updateBusiness_IndividualIncomeTaxBusinessMerge_UsingPUT = (params, { bblx, declarationYearmonth }) =>
    request.put(`/rest/companytax/merge/individualIncome/business/v1.0/updateBusiness/${bblx}/${declarationYearmonth}`, params);
/** 
 * @description 个税模块-修改指定月份报表明细 
 * @param {TtIndividualIncomeTaxBusinessItemsVo[]} params - items 
 * @param {Object} pathParams
 * @param {number} pathParams.bblx - bblx
 * @param {number} pathParams.declarationYearmonth - declarationYearmonth  
**/
export const updateBusinessItems_IndividualIncomeTaxBusinessMerge_UsingPUT = (params, { bblx, declarationYearmonth }) =>
    request.put(`/rest/companytax/merge/individualIncome/business/v1.0/updateBusinessItems/${bblx}/${declarationYearmonth}`, params);
/** 
* @description 个税模块-申报作废 
* @param {Object} pathParams
* @param {number} pathParams.bblx - bblx
* @param {number} pathParams.declarationYearmonth - declarationYearmonth  
**/
export const updateDeclarationIsInvalid_IndividualIncomeTaxBusinessMerge_UsingPUT = (
    { bblx, declarationYearmonth }
) =>
    request.put(`/rest/companytax/merge/individualIncome/business/v1.0/updateDeclarationIsInvalid/${bblx}/${declarationYearmonth}`);
/** 
* @description 个税模块-生产经营获取预缴信息 
* @param {Object} pathParams
* @param {number} pathParams.bblx - bblx
* @param {number} pathParams.declarationYearmonth - declarationYearmonth  
**/
export const updateRevenueExpenseAndPrepaymentInfo_IndividualIncomeTaxBusinessMerge_UsingPUT = (
    { bblx, declarationYearmonth }
) =>
    request.put(`/rest/companytax/merge/individualIncome/business/v1.0/updateRevenueExpenseAndPrepaymentInfo/${bblx}/${declarationYearmonth}`);
/** 
 * @description 个税模块-生产经营-缴款 
 * @param {Object} pathParams
 * @param {number} pathParams.bblx - bblx
 * @param {number} pathParams.businessItemId - businessItemId
 * @param {string} pathParams.declarationPeriod - declarationPeriod
 * @param {number} pathParams.paymentType - paymentType  
**/
export const updatePayment_IndividualIncomeTaxBusinessMerge_UsingPUT = (
    { bblx, businessItemId, declarationPeriod, paymentType }
) =>
    request.put(`/rest/companytax/merge/individualIncome/business/v1.0/updatePayment/${businessItemId}/${paymentType}/${bblx}/${declarationPeriod}`);

/** 
 * @description 个税模块-生产经营-确认支付 
 * @param {Object} pathParams
 * @param {number} pathParams.bblx - bblx
 * @param {number} pathParams.businessItemId - businessItemId
 * @param {string} pathParams.declarationPeriod - declarationPeriod
 * @param {number} pathParams.paymentType - paymentType  
**/
export const updateConfirmPayment_IndividualIncomeTaxBusinessMerge_UsingPUT = (
    { bblx, businessItemId, declarationPeriod, paymentType }
) =>
    request.put(`/rest/companytax/merge/individualIncome/business/v1.0/updateConfirmPayment/${businessItemId}/${paymentType}/${bblx}/${declarationPeriod}`);
/** 
 * @description 个税模块-生产经营-查询申报信息 
 * @param {Object} pathParams
 * @param {string} pathParams.declarationPeriod - declarationPeriod  
 * @returns {Promise<{data: {data: TtIndividualIncomeTaxBusinessTotal[]}}>} - aa
**/
export const getIndividualIncomeTaxBusinessTotal_IndividualIncomeTaxBusinessMerge_UsingGET = (
    { declarationPeriod }
) =>
    request.get(`/rest/companytax/merge/individualIncome/business/v1.0/getIndividualIncomeTaxBusinessTotal/${declarationPeriod}`);

/** 
 * @description 个税模块-批量计算 
 * @param {IndividualIncomeTaxBusinessQueryVo} params - individualIncomeTaxBusinessQueryVo 
 * @param {Object} pathParams
 * @param {string} pathParams.declarationPeriod - declarationPeriod  
**/
export const batchCheckTaxCalculation_IndividualIncomeTaxBusinessMerge_UsingPUT = (params, { declarationPeriod }) =>
    request.put(`/rest/companytax/merge/individualIncome/business/v1.0/batchCheckTaxCalculation/${declarationPeriod}`, params);

/** 
* @description 个税模块-批量获取电局申报状态 
* @param {IndividualIncomeTaxBusinessQueryVo} params - individualIncomeTaxBusinessQueryVo 
* @param {Object} pathParams
* @param {string} pathParams.declarationPeriod - declarationPeriod  
**/
export const batchOnlineDeclarationStatus_IndividualIncomeTaxBusinessMerge_UsingPUT = (params, { declarationPeriod }) =>
    request.put(`/rest/companytax/merge/individualIncome/business/v1.0/batchOnlineDeclarationStatus/${declarationPeriod}`, params);
/** 
 * @description 个税模块-批量获取预填信息 
 * @param {IndividualIncomeTaxBusinessQueryVo} params - individualIncomeTaxBusinessQueryVo 
 * @param {Object} pathParams
 * @param {string} pathParams.declarationPeriod - declarationPeriod  
**/
export const batchRevenueExpenseAndPrepaymentInfo_IndividualIncomeTaxBusinessMerge_UsingPUT = (
    params,
    { declarationPeriod }
) =>
    request.put(`/rest/companytax/merge/individualIncome/business/v1.0/batchRevenueExpenseAndPrepaymentInfo/${declarationPeriod}`, params);
/** 
 * @description 个税模块-生产经营查询账套申报信息 
 * @param {Object} params 
 * @param {number} params.batchOperationParamsList[0].bblx - 报表类型
 * @param {number} params.batchOperationParamsList[0].companyId - 账套ID
 * @param {number} params.bblx - 申报表类型，1、预缴纳税；2、年度汇缴
 * @param {number} params.bindStatus - 绑定状态
 * @param {number} params.companyId - 账套ID
 * @param {number} params.companyIds - 公司集合
 * @param {string} params.companyName - 账套名称
 * @param {string} params.declarationPeriod - 申报属期  季度 yyyyQ1 yyyyQ2 yyyyQ3 yyyyQ4  年 yyyy
 * @param {string} params.declarationPeriods - 申报属期值
 * @param {number} params.declarationState - 申报状态
 * @param {number} params.declarationYearmonth - 申报年月
 * @param {string} params.group[0].dir - 排序类型，如果接口支持自定义排序才会往效
 * @param {string} params.group[0].field - 排序序名称名，如果接口支持自定义排序才会往效
 * @param {number} params.page - 分页-第几页
 * @param {number} params.pageSize - 分页-每页记录数
 * @param {string} params.sd[0].dir - 排序类型，如果接口支持自定义排序才会往效
 * @param {string} params.sd[0].field - 排序序名称名，如果接口支持自定义排序才会往效
 * @param {number} params.selectType - 选中类型 1全部 2搜索 3选中
 * @param {number} params.tagStatus - 默认查询不作废
 * @param {boolean} params.whetherJoin - 是否加入公司   
**/
export const getBkCompanyIndividualIncomeTaxBusinessList_IndividualIncomeTaxBusinessMerge_UsingGET = (params) =>
    request.get(`/rest/companytax/merge/individualIncome/business/v1.0/getBkCompanyIndividualIncomeTaxList`, { params });
/** 
 * @description 个税模块-批量获取完税证明 
 * @param {IndividualIncomeTaxBusinessQueryVo} params - individualIncomeTaxBusinessQueryVo 
 * @param {Object} pathParams
 * @param {string} pathParams.declarationPeriod - declarationPeriod  
**/
export const getbatchWszmBase_IndividualIncomeTaxBusinessMerge_UsingPUT = (params, { declarationPeriod }) =>
    request.put(`/rest/companytax/merge/individualIncome/business/v1.0/getbatchWszmBase/${declarationPeriod}`, params);



/**
* @typedef {Object} TtIndividualIncomeTaxBusinessTotal
* @property {number} [alreadyPaymentPersonnelNum] 已缴款人数
* @property {number} [bblx] 申报表类型，1、预缴纳税；2、年度汇缴
* @property {number} [declarationState] 申报状态 0未填写 1已填写未申报 2申报中 3申报成功 4申报失败 8作废中 9作废成功 10作废失败
* @property {number} [failPaymentPersonnelInfoNum] 缴款失败缴款人数
* @property {number} [individualIncomeTaxBusinessId] 主键ID
* @property {number} [notPaymentPersonnelInfoNum] 未交缴款人数
* @property {number} [payTotalState] 缴款状态 -1无需缴款 0未交款 1已缴款 2部分缴款 3缴款失败
* @property {number} [paymentPersonnelInfoNum] 缴款人数
* @property {number} [personnelInfoNum] 投资人人数
* @property {number} [ybtseTotal] 应补退总税额
* @property {number} [zsfs] 征收方式（1查账征收据实预缴、2查账征收上年应纳税所得额、3定期定额、4核定征收核定应税所得额（能准确核算收入总额的）、5核定征收核定应税所得额（能准确核算成本费用的）、6核定征收核定应纳税所得额）
*/

/**
 * 个税-生产经营所得明细附表-编辑表单
 * @typedef {Object} TtIndividualIncomeTaxBusinessAnnexEditVo
 * @property {number} [annexType] 附表类型 0 减免税额附表 1 准予扣除的捐赠额附表<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">[附表类型 0 减免税额附表 1 准予扣除的捐赠额附表]不允许为空</span>
 * @property {number} [businessAnnexId] id
 * @property {number} [businessItemId] 经营所得明细id<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">[经营所得明细id]不允许为空</span>
 * @property {number} [companyId] 账套ID<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">[账套ID]不允许为空</span>
 * @property {string} [createDate] 创建时间<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">[创建时间]不允许为空</span>
 * @property {number} [createUserId] 创建人员<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">[创建人员]不允许为空</span>
 * @property {string} [jmsxdm] 减免事项<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">[减免事项]不允许超出{max}个字符</span><br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">[减免事项]不允许为空</span>
 * @property {string} [jmxzdm] 减免性质<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">[减免性质]不允许超出{max}个字符</span><br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">[减免性质]不允许为空</span>
 * @property {number} [sjkcje] 实际扣除金额<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">[实际扣除金额]不能超出9位整数与2位小数</span><br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">[实际扣除金额]不允许为空</span>
 */

/** 个税-生产经营所得主表
 * @typedef {Object} TtIndividualIncomeTaxBusinessVo
 * @property {number} [asyTaskId] - 异步任务id
 * @property {number} [bblx] - 申报表类型，1、预缴纳税；2、年度汇缴
 * @property {number} [bindStatus] - 绑定状态
 * @property {number} [bkklss] - 成本费用 - 财务报表自然灾害等不可抗力因素造成的损失
* @property {string} [bz] - 备注
* @property {number} [cbfy] - 
* @property {number} [companyId] - 账套ID
* @property {string} [createDate] - 创建时间
* @property {number} [createUserId] - 创建人员
* @property {number} [cwfy] - 财务费用
* @property {number} [declarationState] - 申报状态  0未填写 1已填写未申报 2申报中 3申报成功 4申报失败  8作废中 9作废成功 10作废失败  *
* @property {number} [declarationYearmonth] - 申报年月
* @property {string} [failExplain] - 失败说明
* @property {number} [fj] - 成本费用 - 罚金、罚款和被没收财务的损失
* @property {number} [fpjsze] - 成本费用 - 发票接收总额
* @property {number} [fpsrze] - 收入总额 (按发票开具情况测算)
* @property {number} [glfy] - 管理费用
* @property {number} [gzkjhj] - 成本费用 - 工资扣缴合计
* @property {number} [gzlxsr] - 国债利息收入
* @property {string} [hdqxq] - 核定期限起
* @property {string} [hdqxz] - 核定期限止
* @property {string} [hdrq] - 核定日期
* @property {string} [hdsl] - 核定税率
* @property {number} [hzss] - 成本费用 - 财务报表坏账损失
* @property {number} [individualIncomeTaxBusinessId] - 主键ID
* @property {string} [individualProcessCode] - 个税流程编码  枚举 IndividualBusinessProcessCodeEnum
* @property {number} [individualProcessStatus] - 个税更新状态 0 未获取 1 以获取 2 获取中 3 获取失败
* @property {number} [issuedStatus] - 0未开具 1已开具 2开具中 3开具失败
* @property {string} [jsbs] - 计税标识
* @property {number} [mbyqndks] - 弥补以前年度亏损
* @property {string} [nsqx] - 纳税期限
* @property {number} [qtzc] - 其他支出
* @property {number} [qysb] - 成本费用 - 社保（企业承担部分）
* @property {number} [sblx] - 申报类型，0 正常申报 1更正申报
* @property {string} [sdxmdm] - 所得项目
* @property {number} [sj] - 税金
* @property {string} [skssqq] - 税款所属期起
* @property {string} [skssqz] - 税款所属期止
* @property {number} [srze] - 收入总额
* @property {number} [ss] - 损失
* @property {number} [ssznj] - 成本费用 - 税收滞纳金
* @property {number} [tagStatus] - 是否是备份 0 不是 1是 备份
* @property {number} [taxOfficeState] - 电局申报状态  3申报成功 4申报失败  8作废中 9作废成功 10作废失败  *
* @property {string} [taxOfficeUpdateDate] - 创建时间
* @property {TtIndividualIncomeTaxBusinessItemsVo[]} [ttIndividualIncomeTaxBusinessItemsVoList] - 
* @property {TtIndividualPersonnelInfoVo[]} [ttIndividualPersonnelInfoVoList] - 
* @property {string} [updateDate] - 更新时间
* @property {number} [updateUserId] - 更新时间
* @property {boolean} [whetherJoin] - 是否加入公司
* @property {string} [wszmFile] - 完税证明文件
* @property {string} [wszmUrl] - 完税证明url
* @property {number} [yycb] - 营业成本
* @property {number} [yyfy] - 营业费用
* @property {number} [yywzc] - 成本费用 - 财务报表营业外支出
* @property {number} [zsfs] - 征收方式（1查账征收据实预缴、2查账征收上年应纳税所得额、3定期定额、4核定征收核定应税所得额（能准确核算收入总额的）、5核定征收核定应税所得额（能准确核算成本费用的）、6核定征收核定应纳税所得额）
* @property {number} [zzssrze] - 收入总额 (按增值税申报情况测算)
*/
/**
 * 个税-生产经营所得明细表
 * @typedef {Object} TtIndividualIncomeTaxBusinessItemsVo
 * @property {number} [businessItemId] - 主键ID
 * @property {number} [companyId] - 账套ID
 * @property {string} [createDate] - 创建时间
 * @property {number} [createUserId] - 创建人员
 * @property {number} [fpbl] - 投资者信息-分配比例
 * @property {number} [individualIncomeTaxBusinessId] - 主表ID
 * @property {number} [jbylaobx] - 基本养老保险费: (1查账征收据实预缴可填, 其他不可填)
 * @property {number} [jbylbx] - 基本医疗保险费: (1查账征收据实预缴可填, 其他不可填)
 * @property {number} [jmse] - 减免税额
 * @property {number} [payState] - 缴款状态 -1无需缴款，0未交款，1已缴款，9缴款中
 * @property {number} [qtkc] - 其他扣除: (1查账征收据实预缴可填, 其他不可填)
 * @property {number} [sfjcfy] - 投资者信息-是否使用投资者减除费用: (1使用, 2不使用; 仅查账征收据实预缴可填)
 * @property {number} [sl] - 税率
 * @property {number} [sskcs] - 速算扣除数
 * @property {number} [sybx] - 失业保险费: (1查账征收据实预缴可填, 其他不可填)
 * @property {number} [syjkbx] - 商业健康保险: (仅第四季度或12月所属期填写有效)
 * @property {number} [syylbx] - 税延养老保险: (仅第四季度或12月所属期填写有效)
 * @property {string} [updateDate] - 更新时间
 * @property {number} [updateUserId] - 更新时间
 * @property {string} [xm] - 投资者信息-姓名
 * @property {number} [ybtse] - 应补退税额
 * @property {number} [yijse] - 已缴税额
 * @property {number} [ynse] - 应纳税额
 * @property {number} [ynssde] - 应纳税所得额: (仅查账征收上年应纳税所得额时必填)
 * @property {number} [yssdl] - 应税所得率
 * @property {number} [zfgjj] - 住房公积金: (1查账征收据实预缴可填, 其他不可填)
 * @property {string} [zjhm] - 证件号码
 * @property {string} [zjlx] - 证件类型，枚举见个税附录
 * @property {number} [zykcdjze] - 准予扣除的捐赠额: (1查账征收据实预缴可填, 其他不可填)
 * @property {TtIndividualIncomeTaxBusinessAnnexEditVo[]} [ttIndividualIncomeTaxBusinessAnnexList] - 减免事项明细
 */
/**
 * 个税人员信息表
 * @typedef {Object} TtIndividualPersonnelInfoVo
 * @property {string} [bmbh] - 部门编号
 * @property {string} [bz] - 备注
 * @property {string} [cjzh] - 残疾证号
 * @property {string} [cjzjlx] - 残疾证件类型: 1 残疾证, 2 残疾军人证, 3 伤残人民警察证, 4 残疾消防救援人员证, 5 伤残预备役人员、伤残民兵民工证, 6 因公伤残人员证
 * @property {number} [companyId] - 账套ID
 * @property {string} [createTime] - 创建时间
 * @property {number} [createUserId] - 创建人员
 * @property {string} [csd] - 出生地
 * @property {string} [csny] - 出生日期
 * @property {string} [dzyx] - 邮箱
 * @property {string} [failMessage] - 失败信息
 * @property {string} [gh] - 工号
 * @property {string} [gj] - 国籍
 * @property {number} [grgbbl] - 个人投资比例
 * @property {number} [grgbze] - 个人投资总额
 * @property {string} [hjszdJd] - 户籍街道
 * @property {string} [hjszdQx] - 户籍区县
 * @property {string} [hjszdSheng] - 户籍省份
 * @property {string} [hjszdShi] - 户籍城市
 * @property {string} [hjszdXxdz] - 户籍详细地址
 * @property {number} [individualPersonnelInfoId] - 个税人员信息记录ID
 * @property {string} [khyh] - 开户银行
 * @property {string} [khyhsfmz] - 开户银行省份
 * @property {string} [lszh] - 烈属证号
 * @property {string} [lxdh] - 电话号码
 * @property {string} [lxdz] - 居住详细地址
 * @property {string} [lxdzJd] - 居住街道
 * @property {string} [lxdzQx] - 居住区县
 * @property {string} [lxdzSheng] - 居住省份
 * @property {string} [lxdzShi] - 居住城市
 * @property {string} [lzrq] - 离职日期
 * @property {string} [modifyTime] - 修改时间
 * @property {number} [modifyUserId] - 修改人员
 * @property {string} [nsrzt] - 人员状态: 0 非正常, 1 正常
 * @property {string} [qtzzhm] - 其他证件号码
 * @property {string} [qtzzlx] - 其他证件类型: 208 外国护照, 245 港澳居民居住证, 244 台湾居民居住证, 233 外国人永久居留身份证, 241 外国人工作许可证（A类）, 242 外国人工作许可证（B类）, 243 外国人工作许可证（C类）, 210 港澳居民来往内地通行证, 213 台湾居民来往大陆通行证
 * @property {string} [rybscl] - 人员报送策略
 * @property {string} [rydq] - 人员地区: 0 境内, 1 境外
 * @property {string} [rzndjyqk] - 入职年度就业情形: 0 无, 1 当年首次入职学生, 2 当年首次入职其他人员
 * @property {string} [rzsgrq] - 受雇日期
 * @property {string} [rzzt] - 身份验证状态: 0 验证不通过, 1 验证通过, 2 验证中, 3 暂不验证, N 待验证
 * @property {string} [sbzt] - 报送状态: 0 报送失败, 1 报送成功, 2 待反馈, N 待报送
 * @property {string} [scrjsj] - 首次入境时间
 * @property {string} [sfcj] - 是否残疾 0否 1是
 * @property {string} [sfgl] - 是否孤老 0否 1是
 * @property {string} [sfgy] - 任职受雇类型: 0 雇员, 1 保险营销员, 2 证券经纪人, 3 其他, 4 实习学生（全日制学历教育）
 * @property {string} [sfls] - 是否烈属 0否 1是
 * @property {string} [sfzdw] - 是否扣除减除费用 0否 1是
 * @property {number} [skssq] - 所属期
 * @property {string} [sssx] - 涉税事由
 * @property {string} [wjrlxdzJd] - 联系地街道
 * @property {string} [wjrlxdzQx] - 联系地区县
 * @property {string} [wjrlxdzSheng] - 联系地省份
 * @property {string} [wjrlxdzShi] - 联系地城市
 * @property {string} [wjrlxdzXxdz] - 联系地详细地址
 * @property {string} [xb] - 性别: 1 男, 2 女
 * @property {string} [xl] - 学历: 0 研究生, 1 大学本科, 2 大学本科以下
 * @property {string} [xm] - 姓名
 * @property {string} [xmzw] - 中文名
 * @property {string} [yhzh] - 银行账号
 * @property {string} [yjljsj] - 预计离境时间
 * @property {string} [zw] - 职务: 0 高层, 1 普通
 * @property {string} [zzhm] - 证件号码
 * @property {string} [zzlx] - 证件类型: 201 居民身份证, 227 中国护照, 210 港澳居民来往内地通行证, 213 台湾居民来往大陆通行证, 245 港澳居民居住证, 244 台湾居民居住证, 208 外国护照, 233 外国人永久居留身份证, 241 外国人工作许可证（A类）, 242 外国人工作许可证（B类）, 243 外国人工作许可证（C类）
 */
