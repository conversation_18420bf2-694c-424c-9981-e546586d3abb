<template>
  <div class="flex-v" ref="loadingWrapper">
    <!-- 顶部筛选区 -->
    <div class="toolbar flex-fixed">
      <div class="header-left">
        <div class="header-picker">
          <DateTypePicker :allowedTypes="['quarter']" v-model="declarationPeriod" @change="fetchMainInfo" />
        </div>
        <div class="header-title">
          
          {{ title }}
        </div>
        <div class="status-info">
          <el-alert v-if="mainInfo.failExplain" :title="mainInfo.failExplain" type="error" />
          <el-tag :type="getStatusTagType(mainInfo.declarationState)" size="medium">
            {{ formatIndividualIncomeTaxDeclarationState(mainInfo.declarationState) }}
          </el-tag>
          <el-tag v-if="can.computedTax" size="medium" :type="isComputedTax ? 'success' : 'info'" style="margin-left: 8px;">
            {{ isComputedTax ? '已算税' : '未算税' }}
          </el-tag>
          <span v-if="mainInfo.declarationState === 3" style="margin-left: 8px;display: inline-block;">
            <el-tag v-if="mainInfo.payTotalState === 0" type="warning" size="medium" style="margin-left: 8px">未缴款</el-tag>
            <el-tag v-else-if="mainInfo.payTotalState === 1" type="success" size="medium" style="margin-left: 8px">已缴款</el-tag>
            <el-tag v-else-if="mainInfo.payTotalState === 3" type="info" size="medium" style="margin-left: 8px">无需缴款</el-tag>
          </span>
        </div>
      </div>
      <div class="action-wrapper">
      <!-- <el-button :disabled="!can.getRevenueExpense" @click="handleGetRevenueExpense">获取收入/费用</el-button> -->
      <el-button type="primary" :disabled="!can.computedTax" @click="handleCheckTax">算税</el-button>
      <el-button :disabled="!can.submit" @click="handleSubmit">提交申报</el-button>
      <el-button v-if="mainInfo.sblx === 0" :disabled="!can.correction" @click="handleCorrection">更正申报</el-button>
      <el-button v-if="mainInfo.sblx === 1" :disabled="!can.revokeCorrection" @click="handleRevokeCorrection">撤销更正</el-button>
      <el-button type="danger" :disabled="!can.invalid" @click="handleInvalid">作废</el-button>
      </div>
    </div>

    <!-- 公共信息区 -->
    <div class="flex-fixed">
        <CommonInfo :mainInfo="mainInfo" :can-edit="can.commonInfo" @confirm="handleConfirmChangeMainInfo" />
        
    </div>

    <!-- 投资者明细表格（已实现） -->
    <el-table :data="investorList" border v-loading="loading" class="flex-fluid">
      <el-table-column label="操作" fixed="left" width="100">
        <template #default="scope">
          <el-button type="text" @click="handleShowIndividualTaxForm(scope.row)">查看</el-button>
          <el-button type="text" v-if="can.editItem" @click="handleEditIndividualTaxForm(scope.row)">修改</el-button>
          
        </template>
      </el-table-column>
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column prop="xm" label="姓名" :width="$store.getters.columnSize.Buildin.Name" />
      <el-table-column prop="zjlx" label="证件类型" :width="$store.getters.columnSize.SizeForCount(10)" />
      <el-table-column prop="zjhm" label="证件号码" :width="$store.getters.columnSize.SizeForCount(12)" />
      <el-table-column prop="sblx" align="center" label="申报类型" :width="$store.getters.columnSize.SizeForCount(5)"></el-table-column>
      <el-table-column prop="sbzt" label="申报状态" align="center" :width="$store.getters.columnSize.SizeForCount(9)" />
      <el-table-column prop="fpbl" align="right" label="分配比例" :width="$store.getters.columnSize.SizeForCount(17)">
        <template #header>
          <el-button type="text" :disabled="!can.editItem" @click="handleModifyAllocationRatio">合伙企业合伙个人分配比例（%） <i class="el-icon-edit"></i></el-button>
        </template>
        <template #default="scope">
          {{ scope.row.fpbl != null ? scope.row.fpbl : 0 | percent }}
        </template>
      </el-table-column>
      <el-table-column prop="ynse" align="right" label="应纳税额" :width="$store.getters.columnSize.Buildin.Money" />
      <el-table-column prop="jmse" align="right" label="减免税额" :width="$store.getters.columnSize.Buildin.Money" />
      <el-table-column prop="yijse" align="right" label="已缴税额" :width="$store.getters.columnSize.Buildin.Money" />
      <el-table-column prop="ybtse" align="right" label="应补退税额" :width="$store.getters.columnSize.Buildin.Money" />
      <!-- <el-table-column prop="sbrq" label="申报日期" /> -->
    </el-table>
    <modify-allocation-ratio-dialog
    :visible.sync="showModifyAllocationRatioDialog"
    :investor-list="investorList"
    @confirm="handleConfirmModifyAllocationRatio"
  />
  <IndividualTaxForm
    :visible.sync="showIndividualTaxFormDialog"
    :formData="formData"
    :readonly="modelMode === 'view'"
    :mainInfo="mainInfo"
    @confirm="handleConfirmIndividualTaxForm"
  />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import {
  getIndividualIncomeTaxBusinessList_IndividualIncomeTaxBusinessMerge_UsingGET,
  updateRevenueExpenseAndPrepaymentInfo_IndividualIncomeTaxBusinessMerge_UsingPUT,
  checkTaxCalculation_IndividualIncomeTaxBusinessMerge_UsingPUT,
  submissionDeclaration_IndividualIncomeTaxBusinessMerge_UsingPUT,
  updateBusiness_IndividualIncomeTaxBusinessMerge_UsingPUT,
  correctionOfDeclarations_IndividualIncomeTaxBusinessMerge_UsingPUT,
  revocationOfTheCorrectionDeclaration_IndividualIncomeTaxBusinessMerge_UsingPUT,
  updateDeclarationIsInvalid_IndividualIncomeTaxBusinessMerge_UsingPUT,
  updateBusinessItems_IndividualIncomeTaxBusinessMerge_UsingPUT
} from '@/pages/tax/personTax/api/individualIncomeTax.js'
import {pick} from 'lodash-es'
import { Message, MessageBox } from 'element-ui'
import CommonInfo from './components/commonInfo.vue'
import dayjs from 'dayjs'
import { individualIncomeTaxDeclarationStateFormatter, formatIndividualIncomeTaxDeclarationState } from '@/pages/tax/helper/formaters'
import zjlxdmMap from '@/pages/tax/personTax/helper/zjlxdmMap'
import ModifyAllocationRatioDialog from './components/ModifyAllocationRatioDialog.vue';
import DateTypePicker from '@/components/DateTypePicker.vue';
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import IndividualTaxForm from './components/IndividualTaxForm.vue';
import { useRoute } from 'vue-router/composables'
import { individualProcessCodeEmun, checkProcessStatus } from './individualProcessCode';
import { useAsyncResult } from '@/pages/tax/hooks/useAsyncResult';
import { isEmpty } from '@hw/pl-table/lib/utils/util'
import { subtractQuarters, formatQuarter, getMonthsInQuarter } from '@/utils/quarterUtils';
dayjs.extend(quarterOfYear);



const { loadingWrapper, watchAsyncResult } = useAsyncResult();
const declarationPeriod = ref(subtractQuarters(formatQuarter(new Date()), 1))
const declarationYearmonth = computed(() => {
  const [startDate, endDate] = getMonthsInQuarter(declarationPeriod.value)
  // 返回 YYYYMM 格式
  return dayjs(endDate).add(1, 'month').format('YYYYMM')
})
const title = computed(() => {
  const [year, quarter] = declarationPeriod.value.split('Q').map(Number)
  return `${year}年第${quarter}季度 经营所得预缴纳申报`
})
const route = useRoute()
// 主表信息
const mainInfo = ref({})
const investorList = ref([])
const loading = ref(false)
const btnLoading = reactive({
  getRevenue: false,
  checkTax: false,
  submit: false,
  update: false,
  correction: false,
  invalid: false,
})


const sblxMap = {
  0: '正常申报',
  1: '更正申报',
}
// 判断是否已经算税
const isComputedTax = computed(() => {
  return checkProcessStatus(mainInfo.value.individualProcessCode, mainInfo.value.individualProcessStatus, 'taxCalculations')
})

// 判断是否已经申报
const isSubmitted = computed(() => {
  return checkProcessStatus(mainInfo.value.individualProcessCode, mainInfo.value.individualProcessStatus, 'submissionDeclaration')
})

// 判断是否已经缴税
const isPaid = computed(() => {
  return checkProcessStatus(mainInfo.value.individualProcessCode, mainInfo.value.individualProcessStatus, 'paymentOfTaxes')
})

// 判断是否已经开具完税证明
const isIssued = computed(() => {
  return checkProcessStatus(mainInfo.value.individualProcessCode, mainInfo.value.individualProcessStatus, 'issuanceOfTaxPaymentCertificate')
})

// 获取状态标签类型
const getStatusTagType = (status) => {
  const statusMap = {
    0: 'info',     // 未申报
    1: 'primary',  // 申报中
    2: 'warning',  // 申报失败
    3: 'success',  // 申报成功
    4: 'danger',   // 申报作废
  }
  return statusMap[status] || 'info'
}

// 判断是否已经作废
const isInvalid = computed(() => {
  return checkProcessStatus(mainInfo.value.individualProcessCode, mainInfo.value.individualProcessStatus, 'DeclarationIsInvalid')
})
const can = computed(() => {
  return {
    computedTax: [1, 4].includes(mainInfo.value.declarationState),
    // 是否能修改明细 暂时只支持编辑 查账征收据实预缴 的明细项
    editItem: true,
    // 是否可以修改公共信息
    commonInfo: [1, 4].includes(mainInfo.value.declarationState),
    submit: [1, 4].includes(mainInfo.value.declarationState),
    correction: [3].includes(mainInfo.value.declarationState),
    revokeCorrection: [1, 4].includes(mainInfo.value.declarationState),
    // 校验申报状态=申报成功，申报状态=申报成功/缴款状态=“无需缴款”，申报状态=申报成功/缴款状态=“未缴款”的状态才能作废
    invalid: [3].includes(mainInfo.value.declarationState),
  }
})
// 获取主表及投资者明细
const fetchMainInfo = async () => {
  loading.value = true
  try {
    // 这里需要根据实际业务传递参数，如公司ID、申报年月等
    const params = {
      page: 1,
      pageSize: 50,
      declarationPeriod: declarationPeriod.value,
      declarationYearmonth: declarationYearmonth.value
    }
    const res = await getIndividualIncomeTaxBusinessList_IndividualIncomeTaxBusinessMerge_UsingGET(params)
    const main = res.data.data[0] || {}
    mainInfo.value = main
    investorList.value = (main.ttIndividualIncomeTaxBusinessItemsVoList || []).map(item => {
      return {
        ...item,
        sblx: sblxMap[mainInfo.value.sblx],
        sbzt: formatIndividualIncomeTaxDeclarationState(mainInfo.value.declarationState),
        zjlx: zjlxdmMap[item.zjlx],
      }
    })
    waitAsyncTask()
  } finally {
    loading.value = false
  }
}
// 等待并监听正在执行的异步任务
const waitAsyncTask = () => {
  // 判断当前是否存在正在执行的异步任务
  if (mainInfo.value.individualProcessStatus === 2) {
    const currentProcessText = individualProcessCodeEmun.find(
      (item) => item.code === mainInfo.value.individualProcessCode,
    ).label;
    watchAsyncResult({
        taskId: mainInfo.value.asyTaskId,
        loadingText: `正在执行${currentProcessText}。。。`,
        callback: (status, error) => {
          if (status === 'success') {
            // 当任务成功时执行某些操作。
            // eslint-disable-next-line no-use-before-define
            fetchMainInfo();
          }
          if (status === 'error') {
              // 当任务失败时执行某些操作。
              MessageBox.alert(error.message, '异步任务执行错误');
            }
        },
      });
  }
};

const showModifyAllocationRatioDialog = ref(false)
const handleModifyAllocationRatio = () => {
  showModifyAllocationRatioDialog.value = true
}  

const showIndividualTaxFormDialog = ref(false)
const modelMode = ref('view')
const formData = ref({})
const handleShowIndividualTaxForm = (row) => {
  formData.value = {
    ...row,
    srze: mainInfo.value.srze,
    cbfy: mainInfo.value.cbfy,
    mbyqndks: mainInfo.value.mbyqndks,
  }
  modelMode.value = 'view'
  showIndividualTaxFormDialog.value = true
}

const handleEditIndividualTaxForm = (row) => {
  formData.value = {
    ...row,
    srze: mainInfo.value.srze,
    cbfy: mainInfo.value.cbfy,
    mbyqndks: mainInfo.value.mbyqndks,
  } 
  modelMode.value = 'edit'
  showIndividualTaxFormDialog.value = true
}

const handleConfirmIndividualTaxForm = async (data = {}) => {
  await updateBusinessItems_IndividualIncomeTaxBusinessMerge_UsingPUT([data], { bblx: 1, declarationYearmonth: declarationYearmonth.value })
  showIndividualTaxFormDialog.value = false
  Message.success('操作成功')
  fetchMainInfo()
}

const handleConfirmModifyAllocationRatio = async (data = []) => {
  await updateBusinessItems_IndividualIncomeTaxBusinessMerge_UsingPUT(data.map(item => {
    return {
      ...pick(item, ['businessItemId', 'fpbl', 'individualIncomeTaxBusinessId', 'companyId']),
    }
  }), { bblx: 1, declarationYearmonth: declarationYearmonth.value })
  Message.success('操作成功')
  showModifyAllocationRatioDialog.value = false
  fetchMainInfo()
}
// 修改公共信息
const handleConfirmChangeMainInfo = async (data = {}) => {
    await updateBusiness_IndividualIncomeTaxBusinessMerge_UsingPUT(data, { bblx: 1, declarationYearmonth: declarationYearmonth.value })
    Message.success('操作成功')
    fetchMainInfo()
}
// 获取收入/费用
const handleGetRevenueExpense = async () => {
    // 需传递 bblx, declarationYearmonth
    await updateRevenueExpenseAndPrepaymentInfo_IndividualIncomeTaxBusinessMerge_UsingPUT({
      bblx: 1,
      declarationYearmonth: declarationYearmonth.value
    })
    Message.success('获取收入/费用成功')
    fetchMainInfo()
}
// 校验投资人分配比例字段是否合法
const validateInvestorRatios = () => {
  // 检查是否有分配比例小于等于0的投资人
  const invalidInvestor = investorList.value.find(item => {
    return !item.fpbl || Number(item.fpbl) <= 0;
  });

  if (invalidInvestor) {
    Message.error(`提示：${invalidInvestor.xm}【合伙企业合伙个人分配比例（%）】不能小于等于0，按实际情况填入【合伙企业合伙个人分配比例（%）】`);
    return false;
  }

  // 检查分配比例总和是否超过1（100%）
  const totalRatio = investorList.value.reduce((sum, item) => {
    return sum + (Number(item.fpbl) || 0);
  }, 0);

  if (totalRatio > 1) {
    Message.error('提示：所有投资人的【合伙企业合伙个人分配比例（%）】总和不能超过100%');
    return false;
  }

  return true;
};

// 算税
const handleCheckTax = async () => {
  // 先校验分配比例
  if (!validateInvestorRatios()) {
    handleModifyAllocationRatio()
    return;
  }

  await checkTaxCalculation_IndividualIncomeTaxBusinessMerge_UsingPUT({
      bblx: 1,
      declarationYearmonth: declarationYearmonth.value
    })
    fetchMainInfo()
}

// 提交申报
const handleSubmit = async () => {
  // 提交前先校验分配比例
  if (!isComputedTax.value) {
    Message.warning('请先点击【算税】， 在提交申报')
    return;
  }
  
  await submissionDeclaration_IndividualIncomeTaxBusinessMerge_UsingPUT({
      bblx: 1,
      declarationYearmonth: declarationYearmonth.value
    })
    Message.success('提交申报成功')
    fetchMainInfo()
}

// 更新申报
const handleUpdate = async () => {
    await updateBusiness_IndividualIncomeTaxBusinessMerge_UsingPUT(mainInfo.value, {
      bblx: 1,
      declarationYearmonth: declarationYearmonth.value
    })
    Message.success('更新申报成功')
    fetchMainInfo()
}

// 更正申报
const handleCorrection = async () => {
    await correctionOfDeclarations_IndividualIncomeTaxBusinessMerge_UsingPUT({
      bblx: 1,
      declarationYearmonth: declarationYearmonth.value
    })
    Message.success('更正申报成功')
    fetchMainInfo()
}

// 撤销更正
const handleRevokeCorrection = async () => {
    await revocationOfTheCorrectionDeclaration_IndividualIncomeTaxBusinessMerge_UsingPUT({
      bblx: 1,
      declarationYearmonth: declarationYearmonth.value
    })
    Message.success('撤销更正成功')
    fetchMainInfo()
}

// 作废
const handleInvalid = async () => {
    await MessageBox.confirm('作废申报将作废所有投资人的申报，是否继续？', '提示', { type: 'warning' })
    await updateDeclarationIsInvalid_IndividualIncomeTaxBusinessMerge_UsingPUT({
      bblx: 1,
      declarationYearmonth: declarationYearmonth.value
    })
    Message.success('已发起作废申报，正在处理中')
    fetchMainInfo()
}

onMounted(async () => {
  if(route.query?.declarationPeriod){
    declarationPeriod.value = route.query?.declarationPeriod
  }
  await fetchMainInfo()
  // 当获取不到申报记录时，自动执行一次获取 获取 收入/费用
  if(isEmpty(mainInfo.value)) {
    await handleGetRevenueExpense()
  }
})
</script>
<style lang="scss" scoped>
.header-left {
  display: flex;
  align-items: center;
}
.header-title {
  font-size: 18px;
  font-weight: bold;
  margin-right: 18px;
}
.header-picker{
  margin-right: 18px;
}
.status-info {
  display: flex;
  align-items: center;
}
</style>