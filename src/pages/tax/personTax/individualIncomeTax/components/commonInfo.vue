<template>
    <div class="PublicInfoComponent">
        <div class="card-header">
            <span class="card-title">公共信息</span>
            <div>
                <el-button type="text" @click="handleCheckTax" v-if="canEdit">修改公共信息</el-button>
                <el-button type="text" @click="handleCheckTax" v-if="!canEdit">查看公共信息</el-button>
            </div>
        </div>
        <div class="card-content">
            <div class="info-item">
                <span class="label">征收方式: </span>
                <span class="value">{{ formatIndividualIncomeTaxZsfs(mainInfo.zsfs) || '-' }}</span>
            </div>
            <div class="info-class">
                <span class="info-class-title">利润信息</span>
                <div class="info-class-content">
                    <div class="info-item">
                        <span class="label">收入总额: </span>
                        <span class="value">{{ formatMoney(mainInfo.srze || 0) }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">成本费用: </span>
                        <span class="value">{{ formatMoney(mainInfo.cbfy || 0) }}</span>
                    </div>
                    <div class="info-item">
                        <!-- 利润总额=收入总额-成本费 -->
                        <span class="label">利润总额: </span>
                        <span class="value">{{ formatMoney(lrzl) }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">弥补以前年度亏损: </span>
                        <span class="value">{{ formatMoney(mainInfo.mbyqndks || 0) }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">被投资单位经营所得: </span>
                        <span class="value">{{ formatMoney(bezdjs) }}</span>
                    </div>
                </div>
            </div>
        </div>
        <el-dialog
      title="修改公共信息"
      :visible.sync="dialogVisible"
      width="800px"
      :close-on-click-modal="false"
      class="edit-dialog"
    >
      <div class="dialog-content">
        <!-- 说明文字 -->
        <div class="notice-box">
          <el-alert  :closable="false">
            根据您开具发票信息以及其他税种的申报信息，系统测算出本期收入总额和成本费用提供参考，数据存在一定误差，如您取得了股权转让所得收益以及从其他营业主体的股息红利等经营所得应税所得。请您如实填报。购入固定资产或无形资产等需要折旧或者摊销的，请如实调减系统测算数据。
            <el-button type="text" style="padding: 0;" @click="showRevenueCostMeasurement">
              <i class="el-icon-link"></i>
              查看详情
            </el-button>
          </el-alert>
          
        </div>

        <!-- 表单内容 -->
        <el-form :model="editFormData" label-width="140px" class="edit-form" :disabled="!canEdit">
          <!-- 征收方式 -->
          <el-form-item label="征收方式">
            <el-radio-group v-model="editFormData.zsfs" disabled>
              <div  v-for="item in zsfsOptions" :key="item.value">
                <el-radio :label="item.value">{{ item.label }}</el-radio>
              </div>
            </el-radio-group>
          </el-form-item>

          <!-- 财务信息 -->
          <div class="form-grid">
            <el-form-item label="收入总额" class="form-item">
              <q-nb-input
                v-model="editFormData.srze"
                placeholder="请输入收入总额"
              >
              </q-nb-input>
            </el-form-item>

            <el-form-item label="成本费用" class="form-item">
              <q-nb-input
                v-model="editFormData.cbfy"
                :disabled="isHdzs"
                placeholder="请输入成本费用"
              >
              </q-nb-input>
            </el-form-item>

            <el-form-item label="利润总额" class="form-item">
              <q-nb-input
                v-model="editFormDataLrzl"
                disabled
                class="calculated-field"
              >
              </q-nb-input>
              <div class="calculation-hint" v-if="!isHdzs">利润总额=收入总额-成本费用</div>
            </el-form-item>

            <el-form-item label="弥补以前年度亏损" class="form-item">
              <q-nb-input
                v-model="editFormData.mbyqndks"
                :disabled="isHdzs"
                placeholder="请输入弥补以前年度亏损"
              >
              </q-nb-input>
            </el-form-item>

            <el-form-item label="被投资单位经营所得" class="form-item">
              <q-nb-input
                v-model="editFormDataBezdjs"
                disabled
              >
              </q-nb-input>
              <div class="calculation-hint" v-if="!isHdzs">被投资单位经营所得=利润总额-弥补以前年度亏损</div>
            </el-form-item>
            <el-form-item label="所得率" class="form-item" v-if="isHdzsSdr">
              <q-nb-input
                disabled
                v-model="editFormData.hdsl"
              >
              </q-nb-input>
            </el-form-item>

            <el-form-item label="备注" class="form-item full-width">
              <el-input
                type="textarea"
                v-model="editFormData.bz"
                placeholder="请输入备注信息"
                :rows="3"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave" v-if="canEdit">确定</el-button>
      </div>
    </el-dialog>
    
    <!-- 收入及成本测算情况弹窗 -->
    <revenue-cost-measurement-dialog
      :visible.sync="showRevenueCostDialog"
      :main-info="mainInfo"
    />
  </div>
</template>

<script setup>
import { ref, computed, defineEmits } from 'vue';
import { formatIndividualIncomeTaxZsfs } from '@/pages/tax/helper/formaters'
import RevenueCostMeasurementDialog from './RevenueCostMeasurementDialog.vue';
import { formatMoney } from '@/assets/Utils';
import { ElMessage } from 'element-ui';
import {updateBusiness_IndividualIncomeTaxBusinessMerge_UsingPUT} from '@/pages/tax/personTax/api/individualIncomeTax'

import { cloneDeep } from 'lodash-es';

const props = defineProps({
    mainInfo: {
        type: Object,
        default: () => ({})
    },
    canEdit: {
      type: Boolean,
      default: true
    }
})
const emit = defineEmits(['confirm'])

// 收入及成本测算情况弹窗
const showRevenueCostDialog = ref(false)
const showRevenueCostMeasurement = () => {
  showRevenueCostDialog.value = true
}

// 是否为大类 核定征收 4， 5，6
const isHdzs = computed(() => {
  return [4, 5, 6].includes(props.mainInfo.zsfs)
})
// 是否为 核定征收 (所得率)
const isHdzsSdr = computed(() => {
  return props.mainInfo.zsfs === 4
})

// 利润总额
const lrzl = computed(() => {
  // 核定征收(所得率) 4   利润总额默认为 0
  if(isHdzs.value){
        return 0
    }
    return props.mainInfo.srze?.sub(props.mainInfo.cbfy || 0) || 0
})

// 被投资单位经营所得
const bezdjs = computed(() => {
    if(isHdzs.value) {
      return props.mainInfo.srze
    }
    return lrzl.value?.sub(props.mainInfo.mbyqndks || 0) || 0
})

const dialogVisible = ref(false)
const editFormData = ref({})
// 编辑表单的利润总额
const editFormDataLrzl = computed(() => {
  // 核定征收(所得率) 4   利润总额默认为 0
    if(isHdzs.value){
        return 0
    }
    return editFormData.value.srze?.sub(editFormData.value.cbfy || 0) || 0
})

// 编辑表单的被投资单位经营所得
const editFormDataBezdjs = computed(() => {
  if(isHdzs.value) {
      return editFormData.value.srze
    }
    return editFormDataLrzl.value?.sub(editFormData.value.mbyqndks || 0) || 0
})
// 若返回的征收方式=【查账征收】则默认"据实预繳”可修改选项【据实预缴】和【上年应纳税所得额】
// 征收方式=【定期定额】则无选项
// 征收方式=【核定征收】选项：核定应税所得额（能佳确核算收入总额的）、应稅所得额（能准确核算成本费用的）、核定应纳税所得额
const zsfsOptions = computed(() => {
    if (props.mainInfo.zsfs <= 2) {
        return [
            { label: '据实预缴', value: 1 },
            { label: '上年应纳税所得额', value: 2 }
        ]
    } else if (props.mainInfo.zsfs > 3) {
        return [
            { label: '按税务机关认可的其他方式（所得率）', value: 4 },
            { label: '核定征收核定应税所得额（能准确核算成本费用的）', value: 5 },
            { label: '核定征收核定应纳税所得额', value: 6 }
        ]
    } else if (props.mainInfo.zsfs === 3) {
        return [ { label: '定期定额', value: 3 },]
    }
    return []
})
const handleCheckTax = () => {
    editFormData.value = cloneDeep(props.mainInfo)
    dialogVisible.value = true
}

const handleSave = async () => {
    emit('confirm', editFormData.value)
    dialogVisible.value = false
}
</script>

<style scoped lang="scss">
.PublicInfoComponent {
    background-color: #fff;
    margin-bottom: 8px;
    .edit-form{
      margin-top: 8px;
    }
    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px;
        border-bottom: 1px solid #ccc;
        .card-title {
            font-weight: bold;
            font-size: 16px;
        }
    }
    .card-content {
        padding: 10px 10px 0 10px;
        .info-class {
            margin-bottom: 10px;
            .info-class-title {
                font-weight: bold;
                margin-bottom: 10px;
                margin-right: 12px;
                display: block;
            }
            .info-class-content {
                display: flex;
                flex-direction: row;
                
            }
            
        }
        .info-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 10px;
                    width: 30%;
                    .label {
                        margin-right: 10px;
                    }
                    .value {
                        flex: 1;
                    }
                }
    }
}
</style>
