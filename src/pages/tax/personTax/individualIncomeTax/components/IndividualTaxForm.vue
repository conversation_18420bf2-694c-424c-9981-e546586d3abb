<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="800px"
    top="40px"
    custom-class="individual-tax-dialog"
    :close-on-click-modal="false"
    @close="handleClose"
    :before-close="handleClose"
  >
    <div class="individual-tax-form">
      
      <!-- 基本信息区域 -->
      <div class="basic-info-section">
        <el-descriptions title="纳税人信息">
            <el-descriptions-item label="姓名">{{ formData.xm }}</el-descriptions-item>
            <el-descriptions-item label="证件类型">{{formData.zjlx}}</el-descriptions-item>
            <el-descriptions-item label="证件号码">{{formData.zjhm}}</el-descriptions-item>
            <el-descriptions-item label="税款所属期">{{ mainInfo.declarationPeriod }}</el-descriptions-item>
            <el-descriptions-item label="征收方式">{{ formatIndividualIncomeTaxZsfs(mainInfo.zsfs) }}</el-descriptions-item>
        </el-descriptions>
      
      </div>

      <!-- 申报表格区域 -->
      <div class="declaration-table-section">
        <el-table 
          :data="tableData" 
          border 
          stripe
        >
          <el-table-column prop="item" label="项目" />
          <el-table-column prop="lineNumber" label="行次" width="80" align="center" />
          <el-table-column label="金额（比例）" width="120" align="right">
            <template slot-scope="scope">
              <div>
                <q-nb-input
                :value="scope.row.value"
                class="right-align-input"
                @change="handleInputChange(scope.row.field, $event)"
                v-if="!readonly && !scope.row.readonly && scope.row.type === 'amount'"
              />
              <div  v-else-if="!readonly && !scope.row.readonly && scope.row.type === 'checkbox'">
                <span>{{scope.row.value === 1 ? sfjcfy : '0.00'}}</span>
                <el-checkbox
                  v-if="scope.row.readonly === false"
                  :value="scope.row.value"
                  :true-label="1"
                  :false-label="2"
                  @change="handleInputChange(scope.row.field, $event)"
                />
                
              </div>
              
              <PercentInput
                v-else-if="!readonly && !scope.row.readonly && scope.row.type === 'percent'"
                :value="scope.row.value"
                @change="handleInputChange(scope.row.field, $event)"
              />
              <el-button v-if="scope.row.type === 'annexList'" type="text" @click="handleAnnexListClick()">查看</el-button>
              <span v-else>{{ scope.row.displayValue }}</span>
              </div>
              
            </template>
          </el-table-column>
        </el-table>
      </div>
  </div>
  <template #footer>
    <el-button @click="handleClose">取消</el-button>
    <el-button type="primary" v-if="!readonly" @click="handleSave">保存</el-button>
  </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed, defineProps, onMounted } from 'vue';
import { Message, MessageBox } from 'element-ui';
import { formatIndividualIncomeTaxZsfs } from '@/pages/tax/helper/formaters';
import { createTaxFormManager } from './taxFormConfig';
import PercentInput from '@/components/PercentInput.vue'
import { formatMoney } from '@/assets/Utils'; 
import { pick } from 'lodash-es';

const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  mainInfo: {
    type: Object,
    required: true
  },
  visible: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

// 定义 emits
const emit = defineEmits(['update:visible', 'close', 'confirm']);

const title = computed(() => {
  return props.readonly ? '查看个人预缴申报详情' : '编辑个人预缴申报'
})
let taxManager = createTaxFormManager({
  zsfs: props.mainInfo.zsfs,
  quarter: 1
});

const tableData = ref(taxManager.getDisplayData());
// 投资者减除费用
const sfjcfy = computed(() => {
  if(props.formData.sfjcfy === 1){
    const quarter = Number(props.mainInfo.declarationPeriod?.split('Q')[1])
    return formatMoney(15000 * quarter)
  }
  return '0.00'
})
// 处理弹窗打开
const handleOpen = () => {
  taxManager = createTaxFormManager({
    zsfs: props.mainInfo.zsfs,
    quarter: Number(props.mainInfo.declarationPeriod?.split('Q')[1])
  });
  console.log(taxManager)
  // 初始化表格数据
  taxManager.setInitialData(props.formData);

  tableData.value = taxManager.getDisplayData();

};
// 当用户输入改变时，逻辑保持不变
const handleInputChange = (field, value) => {
  taxManager.updateField(field, value);
  tableData.value = taxManager.getDisplayData();
};

// 处理关闭
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

// Methods
const handleSave = () => {
  try {
    const formData = taxManager.getRawData();
     if(formData.fpbl === 0){
      Message.error('分配比例不能为0')
      return
     }
    emit('confirm', {
      ...formData,
      ...pick(props.formData, ['businessItemId', 'individualIncomeTaxBusinessId', 'companyId']),
      ynssde: 0,
    });
  } catch (error) {
  }
};

// 监听 visible 变化，处理弹窗打开/关闭
watch(() => props.visible, (newVal) => {
  if (newVal) {
    handleOpen();
  }
});

// 组件挂载时，如果 visible 为 true，则初始化数据
onMounted(() => {
  if (props.visible) {
    handleOpen();
  }
});
</script>


<style lang="scss" scoped>

.basic-info-section {
    margin-bottom: 12px;
    
    padding: 10px;
    background-color: #fafafa;
    border: 1px solid #e4e7ed;
  }
</style>
<style lang="scss">
.individual-tax-dialog{
 .el-dialog__body {
    max-height: 740px;
    overflow-y: auto;
  }
}
.right-align-input .el-input__inner{
  text-align: right;
}
@media screen and (max-height: 768px) {
  .individual-tax-dialog{
  .el-dialog__body {
      max-height: 530px;
      overflow-y: auto;
    }
  }
}

</style>