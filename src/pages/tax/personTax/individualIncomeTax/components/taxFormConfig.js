import { cloneDeep } from 'lodash-es';
import { formatMoney } from '@/assets/Utils'; 
import Decimal from 'decimal.js';

// ===================================================================================
// 1. 计算公式定义 (完整版)
// ===================================================================================

/**
 * @description 定义不同征收方式的类型常量，避免使用魔法数字。
 */
const ZSFS_TYPE = {
  CZZS: [1, 2, 3], // 查账征收
  HDZS: [4, 5, 6], // 核定征收
};

/**
 * @description 基础计算公式 (所有征收方式通用的部分)
 */
const baseCalculations = {
  // 专项扣除 = 基本养老保险费 + 基本医疗保险费 + 失业保险费 + 住房公积金
  specialDeduction: {
    calculate: ({ jbylaobx = 0, jbylbx = 0, sybx = 0, zfgjj = 0 }) =>
      new Decimal(jbylaobx).plus(jbylbx).plus(sybx).plus(zfgjj).toNumber(),
    dependencies: ['jbylaobx', 'jbylbx', 'sybx', 'zfgjj']
  },

  // 其他扣除 = 商业健康保险 + 税延养老保险 + 其他扣除
  otherDeduction: {
    calculate: ({ syjkbx = 0, syylbx = 0, qtkc = 0 }) =>
      new Decimal(syjkbx).plus(syylbx).plus(qtkc).toNumber(),
    dependencies: ['syjkbx', 'syylbx', 'qtkc']
  },

  /**
   * 允许扣除的个人费用及其他扣除 = 投资者减除费用 + 专项扣除 + 其他扣除
   * @param {object} formData - 当前的表单数据。
   * @param {object} context - 外部上下文对象，包含如 `quarter` 等动态参数。
   */
  totalDeduction: {
    calculate: ({ sfjcfy = 0, specialDeduction = 0, otherDeduction = 0 }, context = {}) => {
      const { quarter = 1 } = context; 
      const sfjcfyAmount = sfjcfy === 1 ? new Decimal(15000).times(quarter).toNumber() : 0;
      return new Decimal(sfjcfyAmount).plus(specialDeduction).plus(otherDeduction).toNumber();
    },
    dependencies: ['sfjcfy', 'specialDeduction', 'otherDeduction']
  },

  // 应纳税额 = 应纳所得额 * 税率 - 速算扣除数 (结果小于0 时， 返回0)
  ynse: {
    calculate: ({ ynssde = 0, sl = 0, sskcs = 0 }) => {
        const result = new Decimal(ynssde).times(sl).minus(sskcs).toNumber();
        return new Decimal(result).isNegative() ? 0 : result;
    },
    dependencies: ['ynssde', 'sl', 'sskcs']
  },

  // 应补(退)税额 = 应纳税额 - 减免税额 - 已缴税额 (结果小于0 时， 返回0)
  ybtse: {
    calculate: ({ ynse = 0, jmse = 0, yijse = 0 }) => {
      const result = new Decimal(ynse).minus(jmse).minus(yijse).toNumber();
      return Math.max(0, result);
    },
    dependencies: ['ynse', 'jmse', 'yijse']
  },
};

/**
 * @description 查账征收 (CZZS) 的特定计算逻辑
 */
const calculateCzzs = {
  ...baseCalculations, // 继承所有基础公式
  // 利润总额 = 收入总额 - 成本费用
  profit: {
    calculate: ({ srze = 0, cbfy = 0 }) => new Decimal(srze).minus(cbfy).toNumber(),
    dependencies: ['srze', 'cbfy']
  },
  // 应纳所得额 = (利润总额 - 弥补以前年度亏损) * 分配比例 - 允许扣除的个人费用及其他扣除 - 准予扣除的捐赠额
  ynssde: {
    calculate: ({ profit = 0, mbyqndks = 0, fpbl = 0, totalDeduction = 0, zykcdjze = 0 }) => {
      const result = new Decimal(profit)
        .minus(mbyqndks)
        .times(fpbl)
        .minus(totalDeduction)
        .minus(zykcdjze)
        .toNumber();
      return Math.max(0, result);
    },
    dependencies: ['profit', 'mbyqndks', 'fpbl', 'totalDeduction', 'zykcdjze']
  },
};

/**
 * @description 核定征收 (HDZS) 的特定计算逻辑
 */
const calculateHdzs = {
  ...baseCalculations, // 继承所有基础公式
  // @override: 核定征收时，利润总额通常不参与计算，置为0
  profit: {
    calculate: () => 0,
    dependencies: ['srze', 'cbfy']
  },
  // @override: 核定征收的应纳所得额公式不同
  ynssde: {
    calculate: ({ srze = 0, mbyqndks = 0, fpbl = 0, totalDeduction = 0, yssdl = 1, zykcdjze = 0 }) => {
      const result = new Decimal(srze)
        .minus(mbyqndks)
        .times(yssdl) // 使用应税所得率
        .times(fpbl)
        .minus(totalDeduction)
        .minus(zykcdjze)
        .toNumber();
      return Math.max(0, result);
    },
    dependencies: ['srze', 'mbyqndks', 'fpbl', 'totalDeduction', 'zykcdjze', 'yssdl']
  },
};

// ===================================================================================
// 2. 表格配置与配置生成函数 (完整版)
// ===================================================================================
const tableConfig = [
  // --- 输入项 ---
   { item: '一、收入总额', lineNumber: 1, readonly: true, field: 'srze', type: 'amount' },
   { item: '二、成本费用', lineNumber: 2, readonly: true, field: 'cbfy', type: 'amount' },
   { item: '四、弥补以前年度亏损', lineNumber: 4, readonly: true, field: 'mbyqndks', type: 'amount' },
   { item: '五、应税所得率（%）', lineNumber: 5, readonly: true, field: 'yssdl', type: 'percent' },
   { item: '六、合伙企业个人合伙人配比例（%）', lineNumber: 6, readonly: false, field: 'fpbl', type: 'percent' },
   { item: '（一）投资者减除费用', lineNumber: 8, readonly: true, field: 'sfjcfy', type: 'checkbox' },
   { item: '1、基本养老保险费', lineNumber: 10, readonly: false, field: 'jbylaobx', type: 'amount' },
   { item: '2、基本医疗保险费', lineNumber: 11, readonly: false, field: 'jbylbx', type: 'amount' },
   { item: '3、失业保险费', lineNumber: 12, readonly: false, field: 'sybx', type: 'amount' },
   { item: '4、住房公积金', lineNumber: 13, readonly: false, field: 'zfgjj', type: 'amount' },
   { item: '1、商业健康保险', lineNumber: 15, readonly: true, field: 'syjkbx', type: 'amount' },
   { item: '2、税延养老保险', lineNumber: 16, readonly: true, field: 'syylbx', type: 'amount' },
   { item: '3、其他扣除', lineNumber: 17, readonly: false, field: 'qtkc', type: 'amount' },
   { item: '八、准予扣除的捐赠额', lineNumber: 18, readonly: true, field: 'zykcdjze', type: 'amount' },
   { item: '十、税率（%）', lineNumber: 20, readonly: true, field: 'sl', type: 'percent' },
   { item: '十一、速算扣除数', lineNumber: 21, readonly: true, field: 'sskcs', type: 'amount' },
   { item: '十三、减免税额', lineNumber: 23, readonly: true, field: 'jmse', type: 'annexList' },
   { item: '十四、已缴税额', lineNumber: 24, readonly: true, field: 'yijse', type: 'amount' },

  // --- 计算项 ---
   { item: '三、利润总额', lineNumber: 3, readonly: true, field: 'profit', type: 'amount', },
   { item: '（二）专项扣除（9=10+11+12+13）', lineNumber: 9, readonly: true, field: 'specialDeduction', type: 'amount'},
   { item: '（三）依法确定的其他扣除（14=15+16+17）', lineNumber: 14, readonly: true, field: 'otherDeduction', type: 'amount' },
   { item: '七、允许扣除的个人费用及其他扣除（7=8+9+14）', lineNumber: 7, readonly: true, field: 'totalDeduction', type: 'amount' },
   { item: '九、应纳所得额', lineNumber: 19, readonly: true, field: 'ynssde', type: 'amount' },
   { item: '十二、应纳税额（22=19*20-21）', lineNumber: 22, readonly: true, field: 'ynse', type: 'amount' },
   { item: '十五、应补/退税额', lineNumber: 25, readonly: true, field: 'ybtse', type: 'amount' },
].sort((a, b) => a.lineNumber - b.lineNumber);

const genConfigByZsfs = (zsfs) => {
  const config = cloneDeep(tableConfig);
  const calculationStrategy = ZSFS_TYPE.HDZS.includes(zsfs) ? calculateHdzs : calculateCzzs;

  Object.entries(calculationStrategy).forEach(([field, { calculate, dependencies }]) => {
    const configLine = config.find(item => item.field === field);
    if (configLine) {
      configLine.calculate = calculate;
      configLine.dependencies = dependencies;
    }
  });

  // 只有查账征收时， 可以填 投资者减除费用
  if(zsfs === 1) {
    const sfjcfyLine = config.find(item => item.field === 'sfjcfy');
    sfjcfyLine.readonly = false;
  }

  return config;
};

// ===================================================================================
// 3. TaxFormManager 核心类 (完整版)
// ===================================================================================
class TaxFormManager {
  /**
   * @param {object} initialContext - 初始的外部上下文，必须包含 `zsfs`，可包含 `quarter` 等。
   */
  constructor(initialContext = {}) {
    const defaultContext = { zsfs: ZSFS_TYPE.CZZS[0], quarter: 1 };
    this.context = { ...defaultContext, ...initialContext };

    this._initializeConfig(this.context.zsfs);
    this.formData = this._initFormData();
  }

  /**
   * @description 根据征收方式(zsfs)初始化或重置计算相关的所有配置。
   * @param {number} zsfs - 征收方式代码。
   */
  _initializeConfig(zsfs) {
    this.config = genConfigByZsfs(zsfs);
    this.configByField = new Map(this.config.map(item => [item.field, item]));
    this.calculatedFields = this.config.filter(item => item.calculate);
    
    this.dependentsMap = this._buildDependentsMap();
    this.calculationOrder = this._getCalculationOrder();
  }

  _initFormData() {
    const data = {};
    for (const item of this.config) {
      data[item.field] = 0;
    }
    return data;
  }
  
  _buildDependentsMap() {
    const map = {};
    for (const field of this.config.map(item => item.field)) {
      map[field] = [];
    }
    
    for (const item of this.calculatedFields) {
      for (const dep of item.dependencies) {
        if (map[dep]) {
          map[dep].push(item.field);
        } else {
          console.warn(`Field "${item.field}" has a non-existent dependency: "${dep}".`);
        }
      }
    }
    return map;
  }

  _getCalculationOrder() {
    const order = [];
    const visited = new Set();
    const visiting = new Set(); 

    const visit = (field) => {
      if (visited.has(field)) return;
      if (visiting.has(field)) {
        console.error('Circular dependency detected:', Array.from(visiting).join(' -> '), `-> ${field}`);
        throw new Error('Circular dependency detected.');
      }

      visiting.add(field);
      
      const configItem = this.configByField.get(field);
      if (configItem?.dependencies) {
        configItem.dependencies.forEach(dep => visit(dep));
      }

      visiting.delete(field);
      visited.add(field);

      if (configItem?.calculate) {
        order.push(field);
      }
    };

    try {
        this.calculatedFields.forEach(item => visit(item.field));
    } catch (e) {
        console.error("Failed to build calculation order:", e.message);
        return [];
    }
    return order;
  }
  
  /**
   * @description 设置一组初始或默认值，并触发一次完整计算。
   * @param {Object} initialData - 包含字段和其初始值的对象。
   * @returns {TaxFormManager} - 返回 manager 实例以支持链式调用。
   */
  setInitialData(initialData = {}) {
    Object.entries(initialData).forEach(([field, value]) => {
      if (this.configByField.has(field)) {
        const configItem = this.configByField.get(field);
        const parsedValue = (configItem.type === 'amount' || configItem.type === 'percent')
          ? parseFloat(value) || 0
          : value;
        this.formData[field] = parsedValue;
      }
    });

    this._recalculate();
    return this;
  }

  /**
   * @description 更新外部上下文。如果`zsfs`发生变化，则重建整个计算配置。
   * @param {object} newContext - 新的上下文对象。
   */
  setContext(newContext) {
    const oldZsfs = this.context.zsfs;
    
    this.context = { ...this.context, ...newContext };
    const newZsfs = this.context.zsfs;

    if (newZsfs !== oldZsfs) {
      console.log(`征收方式已从 ${oldZsfs} 切换到 ${newZsfs}。正在重新初始化计算配置...`);
      this._initializeConfig(newZsfs);
    }

    this._recalculate();
    return this;
  }

  updateField(field, value) {
    if (!this.formData.hasOwnProperty(field)) {
      console.warn(`Field "${field}" does not exist in formData.`);
      return this.getRawData();
    }
    
    const configItem = this.configByField.get(field);
    const parsedValue = configItem.type === 'amount' || configItem.type === 'percent'
      ? parseFloat(value) || 0
      : value;

    if (this.formData[field] === parsedValue) {
        return this.getRawData();
    }

    this.formData[field] = parsedValue;
    this._recalculate();
    
    return this.getRawData();
  }

  _recalculate() {
    for (const field of this.calculationOrder) {
      const configItem = this.configByField.get(field);
      if (configItem.calculate) {
        this.formData[field] = configItem.calculate(this.formData, this.context);
      }
    }
  }

  getDisplayData() {
    return this.config.map(item => ({
      ...item,
      value: this.formData[item.field],
      displayValue: TaxFormManager.formatValue(this.formData[item.field], item.type, item.field, this.context),
    }));
  }
  
  getRawData() {
    return { ...this.formData };
  }

  static formatValue(value, type, field, context = {}) {
    if (field === 'sfjcfy') {
      const { quarter = 1 } = context;
      const amount = value === 1 ? new Decimal(15000).times(quarter).toNumber() : 0;
      return formatMoney(amount);
    }
    if (value === null || value === undefined) return '';
    switch (type) {
      case 'percent':
        return value !== 0 ? `${new Decimal(value).times(100).toFixed(2)}%` : '0%';
      case 'amount':
      default:
        return formatMoney(value);
    }
  }
}

// ===================================================================================
// 4. 导出 (完整版)
// ===================================================================================

/**
 * @description 导出的工厂函数，完全由上下文驱动。
 * @param {object} initialContext - 初始上下文，例如 { zsfs: 4, quarter: 2 }。
 * @returns {TaxFormManager}
 */
export const createTaxFormManager = (initialContext) => new TaxFormManager(initialContext);