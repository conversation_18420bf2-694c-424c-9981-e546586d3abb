<template>
  <el-dialog
    title="收入及成本测算情况"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleClose"
    class="income-cost-dialog"
  >
    <el-tabs v-model="activeTab" class="dialog-tabs">
      <!-- 收入信息 Tab -->
      <el-tab-pane label="收入信息" name="income">
        <div class="tab-content">
          <div class="info-alert">
            <el-alert
              type="info"
              :closable="false"
              show-icon
            >
              <template slot="title">
                收入总额根据增值税申报情况及发票开具情况分别测算，统计范围为当前申报税款所属期内，如您当期增值税尚未申报，可先办理申报后再办理经营所得个人所得税申报。上述信息不包含股权转让、股息红利等收入，请您如实补充后续操作。
              </template>
            </el-alert>
          </div>
          
          <div class="calculation-table">
            <div class="table-header">
              <span class="label">测算类型</span>
              <span class="amount">金额</span>
            </div>
            
            <div class="table-row">
              <span class="label">收入总额（按增值税申报情况测算）</span>
              <span class="amount">{{ formatMoney(mainInfo.zzssrze) }}</span>
            </div>
            
            <div class="table-row">
              <span class="label">收入总额（按发票开具情况测算）</span>
              <span class="amount">{{ formatMoney(mainInfo.fpsrze) }}</span>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 成本信息 Tab -->
      <el-tab-pane label="成本信息" name="cost" v-if="!isHdzs">
        <div class="tab-content">
          <div class="info-alert">
            <el-alert
              type="info"
              :closable="false"
              show-icon
            >
              <template slot="title">
                成本费用根据发票，其他支出和财务报表数据分别测算，购入商品或产成无形资产等费用进行或准确的，请如实调减系统测算数据，统计范围为当前申报税款所属期内，如该数据会企业当期经营所得增值税和增值税报表务申报这，可先办理后再办理经营所得个人所得税申报
              </template>
            </el-alert>
          </div>

          <!-- 成本费用（按发票及其他数据情况测算）合计 -->
          <div class="cost-section">
            <div class="section-title">
              <span>成本费用（按发票及其他数据情况测算）合计</span>
              <span class="total-amount">{{ formatMoney(getTotalInvoiceCost) }}</span>
            </div>
            
            <div class="cost-items">
              <div class="item-header">
                <span>成本项目</span>
                <span>金额（元）</span>
              </div>
              
              <div class="cost-item">
                <span>发票接受总额</span>
                <span>{{ formatMoney(mainInfo.fpjsze) }}</span>
              </div>
              
              <div class="cost-item">
                <span>工资扣缴合计</span>
                <span>{{ formatMoney(mainInfo.gzkjhj) }}</span>
              </div>
              
              <div class="cost-item">
                <span>社保（企业承担部分）</span>
                <span>{{ formatMoney(mainInfo.qysb) }}</span>
              </div>
            </div>
          </div>

          <!-- 成本费用（按财务报表申报情况测算）合计 -->
          <div class="cost-section">
            <div class="section-title">
              <span>成本费用（按财务报表申报情况测算）合计</span>
              <span class="total-amount">{{ formatMoney(getFinancialReportCost) }}</span>
            </div>
            
            <div class="cost-items">
              <div class="item-header">
                <span>成本项目</span>
                <span>金额（元）</span>
              </div>
              
              <div class="cost-item">
                <span>财务报表-营业费用</span>
                <span>{{ formatMoney(mainInfo.yyfy) }}</span>
              </div>
              
              <div class="cost-item">
                <span>财务报表-营业成本</span>
                <span>{{ formatMoney(mainInfo.yycb) }}</span>
              </div>
              
              <div class="cost-item">
                <span>财务报表-管理费用</span>
                <span>{{ formatMoney(mainInfo.glfy) }}</span>
              </div>
              
              <div class="cost-item">
                <span>财务报表-财务费用</span>
                <span>{{ formatMoney(mainInfo.cwfy) }}</span>
              </div>
              
              <div class="cost-item">
                <span>财务报表-营业外支出</span>
                <span>{{ formatMoney(mainInfo.yywzc) }}</span>
              </div>
              
              <div class="cost-item">
                <span>财务报表-税金</span>
                <span>{{ formatMoney(mainInfo.sj) }}</span>
              </div>
              
              <div class="cost-item sub-header">
                <span>其他财务报表申报信息</span>
                <span>金额（元）</span>
              </div>
              
              <div class="cost-item">
                <span>财务报表-环账损失</span>
                <span>{{ formatMoney(mainInfo.hzss) }}</span>
              </div>
              
              <div class="cost-item">
                <span>财务报表-自然灾害等不可抗力因素造成的损失</span>
                <span>{{ formatMoney(mainInfo.bkklss) }}</span>
              </div>
              
              <div class="cost-item">
                <span>税收滞纳金</span>
                <span>{{ formatMoney(mainInfo.ssznj) }}</span>
              </div>
              
              <div class="cost-item">
                <span>罚金、罚款和被没收财务的损失</span>
                <span>{{ formatMoney(mainInfo.fj) }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { formatMoney } from '@/assets/Utils'

// 定义 props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mainInfo: {
    type: Object,
    default: () => ({})
  }
})
// 是否为大类 核定征收 4， 5，6
const isHdzs = computed(() => {
  return [4, 5, 6].includes(props.mainInfo.zsfs)
})
// 定义 emits
const emit = defineEmits(['update:visible', 'close'])

// 响应式数据
const activeTab = ref('income')

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 方法
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

// const formatMoney = (amount) => {
//   if (amount === null || amount === undefined || amount === '') {
//     return '0'
//   }
//   return Number(amount).toFixed(2)
// }

const getTotalInvoiceCost = computed(() => {
  const { fpjsze = 0, gzkjhj = 0, qysb = 0 } = props.mainInfo
  return Number(fpjsze) + Number(gzkjhj) + Number(qysb)
})

const getFinancialReportCost = computed(() => {
  const {
    yyfy = 0, yycb = 0, glfy = 0, cwfy = 0, yywzc = 0, 
    sj = 0, hzss = 0, bkklss = 0, ssznj = 0, fj = 0
  } = props.mainInfo
  
  return Number(yyfy) + Number(yycb) + Number(glfy) + Number(cwfy) + 
         Number(yywzc) + Number(sj) + Number(hzss) + Number(bkklss) + 
         Number(ssznj) + Number(fj)
})

</script>

<style lang="scss" scoped>
.income-cost-dialog {
  .dialog-tabs {
    ::v-deep .el-tabs__header {
      margin-bottom: 20px;
    }
    
    ::v-deep .el-tabs__item {
      font-size: 16px;
      font-weight: 500;
    }
  }
  
  .tab-content {
    .info-alert {
      margin-bottom: 20px;
      
      ::v-deep .el-alert {
        border-radius: 4px;
        
        .el-alert__content {
          .el-alert__title {
            font-size: 14px;
            line-height: 1.5;
            color: #409eff;
          }
        }
      }
    }
    
    .calculation-table {
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      
      .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #e4e7ed;
        font-weight: 600;
        color: #303133;
      }
      
      .table-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid #e4e7ed;
        font-size: var(--base-font-size);
        &:last-child {
          border-bottom: none;
        }
        
        .label {
          color: #606266;
        }
        
        .amount {
          font-weight: 500;
          color: #303133;
        }
      }
    }
  }
  
  .cost-section {
    margin-bottom: 24px;
    
    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background-color: #f5f7fa;
      border: 1px solid #e4e7ed;
      border-radius: 4px 4px 0 0;
      font-weight: 600;
      color: #303133;
      
      .total-amount {
        color: #e6a23c;
        font-size: 16px;
      }
    }
    
    .cost-items {
      border: 1px solid #e4e7ed;
      border-top: none;
      border-radius: 0 0 4px 4px;
      
      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 16px;
        background-color: #fafafa;
        border-bottom: 1px solid #e4e7ed;
        font-weight: 500;
        color: #606266;
        font-size: 14px;
      }
      
      .cost-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 16px;
        border-bottom: 1px solid #f0f0f0;
        font-size: var(--base-font-size);
        &:last-child {
          border-bottom: none;
        }
        
        &.sub-header {
          background-color: #fafafa;
          font-weight: 500;
          color: #606266;
          border-bottom: 1px solid #e4e7ed;
        }
        
        span:first-child {
          color: #606266;
          flex: 1;
        }
        
        span:last-child {
          font-weight: 500;
          color: #303133;
          min-width: 100px;
          text-align: right;
        }
      }
    }
  }
  
  .dialog-footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;
  }
}
</style>