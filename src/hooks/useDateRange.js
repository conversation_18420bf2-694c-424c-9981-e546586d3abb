import dayjs from 'dayjs';
import { watch, ref } from 'vue';

/**
 * 日期范围
 */
export const useDateRange = ({
  syncTarget,
  startField,
  endField,
  format,
  initValue,
}) => {
  const dateRange = ref(initValue);
  watch(() => dateRange.value?.[0] + dateRange.value?.[1], () => {
    const startDate = dateRange.value?.[0];
    const endDate = dateRange.value?.[1];
    if (startDate && endDate) {
      syncTarget[startField] = dayjs(startDate).format(format ?? 'YYYY-MM-DD');
      syncTarget[endField] = dayjs(endDate).format(format ?? 'YYYY-MM-DD');
    } else {
      syncTarget[startField] = '';
      syncTarget[endField] = '';
    }
  });

  return { dateRange };
};

export default useDateRange;
