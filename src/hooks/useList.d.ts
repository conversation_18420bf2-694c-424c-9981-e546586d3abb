import type { UnwrapRef } from 'vue';
import type { AxiosResponse } from 'axios';

// IO 成功
interface IoResult<RecordType> {
  data: RecordType;
  page: number;
  pageSize: number;
  returnCode: number;
  returnMessage: string | null;
  total: number;
  totalData: any;
}

// IO 错误，例如服务器返回 400 的场景
interface IoError {
  Throwable: string;
  errorCode: string;
  errorId: string;
  errorLV: number;
  errorMessage: string;
  returnErrorTime: string;
}

// 列表查询条件通用字段
interface ListQuery {
  page?: number;
  pageSize?: number;
  [index: number]: any;
  [key: string]: any;
}

// 列表记录的通用字段
interface ListRecord {
  // 创建人员
  createUserId: number;
  // 创建时间
  createTime: string;
  // 修改人员
  modifyUserId: number;
  // 修改时间
  modifyTime: string;

  groupId: string;
}

/**
 * 通用列表管理 hook
 */
export function useList<T = unknown, U = any>(options: {
  /**
   * 请求参数初始化对象
   */
  initQuery?: U & ListQuery & Record<string, any>;

  /**
   * 获取列表数据的方法实现
   */
  loadMethod: (queryObject: ListQuery & U) => Promise<IoResult<T[]> | AxiosResponse<IoResult<T[]>>>;

  /**
   * 导出列表数据的方法实现
   */
  exportMethod?: (queryObject: ListQuery & U) => Promise<AxiosResponse<ArrayBuffer>>;
}): {
  listState: UnwrapRef<{
    records: T[];
    selection: T[];
    queryObject: ListQuery & U & Record<string, any>;
    totalCount: number;
    totalData: any;
    loading: boolean;
    exporting: boolean;
  }>;

  listMethods: {
    resetQuery: (resetOptions: {
      map?: (queryObject: ListQuery & U & Record<string, any>) => ListQuery & U & Record<string, any>;
      exclude?: string[];
      include?: string[];
      preventReload?: boolean
    }) => void;
    loadListRecords: (queryObject?: ListQuery & U & Record<string, any>) => Promise<IoResult<T[]> | AxiosResponse<IoResult<T[]>, any>>;
    onListPageChange: (page: number) => void;
    onListPageSizeChange: (pageSize: number) => void;
    summaryField: (path: ((record: object) => number) | number | string | (string | number)[]) => number;
    onSelectionChange: (selectedRows: T[]) => void;
  };
}

export function useListWithMapper<QIn, QOut, RIn, ROut>(options: {
  queryWrapper: (queryObject: QIn & ListQuery & Record<string, any>) => QOut & ListQuery & Record<string, any>;
  resultMapper: (item: RIn) => ROut;
}): typeof useList<ROut, QOut>
