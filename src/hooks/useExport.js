import isFunction from 'lodash-es/isFunction';
import isString from 'lodash-es/isString';
import isPlainObject from 'lodash-es/isPlainObject';
import endsWith from 'lodash-es/endsWith';
import forOwn from 'lodash-es/forOwn';
import result from 'lodash-es/result';
import get from 'lodash-es/get';
import { invokeClick } from '@/assets/Utils';

/**
 * 执行导出动作
 * @param {string} downloadUrl 导出请求地址
 * @param {Object} options 导出选项
 * @param {Object} options.params 导出请求参数
 * @param {()=>boolean | string} 校验方法或校验当前实例上的校验方法名
 */
function doExport(downloadUrl, options) {
  if (isPlainObject(downloadUrl)) {
    options = downloadUrl;
    downloadUrl = result(options, 'downloadUrl');
  }

  if (!downloadUrl) return;

  // 校验是否要继续导出
  const validate = get(options, 'validate');
  if (isFunction(validate)) {
    if (!validate()) return;
  }

  // 处理参数
  const params = result(options, 'params');
  if (params) {
    if (!endsWith(downloadUrl, '?')) downloadUrl += '?';
  }
  if (isString(params)) {
    downloadUrl += `?${params}`;
  } else if (isPlainObject(params)) {
    forOwn(params, (value, key) => {
      downloadUrl += `${key}=${value}&`;
    });
  }

  // 伪造 a 元素，并派发点击事件
  const anchorElement = document.createElement('a');
  anchorElement.setAttribute('href', downloadUrl);
  invokeClick(anchorElement);
}

export const useDoExport = () => doExport;

export const useExport = (downloadUrl, queryRef) => (plainQueryObject) => {
  if (!plainQueryObject) {
    doExport(downloadUrl, {
      params: plainQueryObject ?? queryRef.value,
    });
  }
};
