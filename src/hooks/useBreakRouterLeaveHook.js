import { onBeforeRouteLeave } from 'vue-router/composables';
import { MessageBox } from 'element-ui';
import { sleep } from '@/assets/Utils';
import NProgress from 'nprogress';

export async function onBreakFormByRouterLeave(to, from, next, message = '离开当前页面后表单当前编辑的信息会将不会被保存，请问要继续离开吗？') {
  if (to.params?.forceNext) {
    next();
    return;
  }
  // next(false);
  // 是否由浏览器自带后退触发，浏览器后退触发时，会直接将url改变
  const isBrowserBack = !window.location.href.includes(from.path);
  console.log(from.path, window.location.href, to.path);
  if (isBrowserBack) {
    // 先暂停执行，防止提示框被自动关闭
    await sleep(1);
  }
  NProgress.done();
  MessageBox.confirm(message, '提示', {
    closeOnClickModal: false,
  }).then(() => {
    NProgress.start();
    next();
  }).catch(() => {
    next(false);
  });
}
// eslint-disable-next-line import/prefer-default-export
export function useBreakRouterLeaveHook(message = '离开当前页面后表单当前编辑的信息会将不会被保存，请问要继续离开吗？') {
  onBeforeRouteLeave(async (to, from, next) => {
    onBreakFormByRouterLeave(to, from, next, message);
});
}
