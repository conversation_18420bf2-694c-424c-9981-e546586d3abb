import validCompanyState from '@/helper/company/validCompanyState';
import store from '@/store';
import { Loading, MessageBox } from 'element-ui';
import { computed, nextTick } from 'vue';
import { useRouter } from 'vue-router/composables';

/**
 * @param {string} companyId 公司 ID
 */
export default function useCompanySwitch() {
  const router = useRouter();

  const switchCompany = async (companyId) => {
    const activeCompanyId = computed(() => store.state.user.userInfo.selectCompanyId);

    if (companyId === activeCompanyId.value) {
      // 选公司操作
      return;
    }

    const loadingInstance = Loading.service({
      text: '正在切换账套中...',
      fullscreen: false,
    });

    // await store.dispatch('tagsView/delAllViews');
    const cacheCurrentAccountPeriod = store.state.user.accountPeriod;

    // 防止切换账套过程中当前页面触发一些不必要的事件，先把当前页面卸载掉
    router.replace('/redirect/loadingPage/loading').then(async () => {
      await store.dispatch('tagsView/delAllViews');
      await nextTick();

      try {
        await store.dispatch('user/changeCompany', { companyId });
        store.commit('app/SET_QUICK_ACCOUNT', 0);
        store.dispatch('user/setAccountPeriod', cacheCurrentAccountPeriod);
        // 刷新页面
        router.replace('/redirect/manager/guidance').finally(() => {
          validCompanyState(store.state.user.userInfo.selectCompanyId);
          loadingInstance.close();
        });
      } catch (e) {
        console.error(e);
        loadingInstance.close();
        MessageBox.alert('切换账套出错，请点击【返回工作台】', '切换账套失败', {
          confirmButtonText: '返回工作台',
          callback: (action) => {
            if (action === 'confirm') {
              router.push({
                name: 'booksSetList',
              });
            }
          },
        });
      }
    });
  };

  return {
    switchCompany,
  };
}
