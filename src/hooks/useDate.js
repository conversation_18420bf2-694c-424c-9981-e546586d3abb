import dayjs from 'dayjs';
import { watch, ref } from 'vue';

/**
 * 日期
 */
export const useDate = ({
  syncTarget,
  field,
  format,
  initValue,
}) => {
  const date = ref(initValue);
  watch(() => date.value, () => {
    if (date.value) {
      syncTarget[field] = dayjs(date.value).format(format ?? 'YYYY-MM-DD');
    } else {
      syncTarget[field] = '';
    }
  });
  return { date };
};

export default useDate;
