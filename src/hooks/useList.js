import { reactive } from 'vue';
import get from 'lodash-es/get';
import has from 'lodash-es/has';
import merge from 'lodash-es/merge';
import cloneDeep from 'lodash-es/cloneDeep';
import omitBy from 'lodash-es/omitBy';
import omit from 'lodash-es/omit';
import pick from 'lodash-es/pick';
import Decimal from 'decimal.js';
import { applyFieldMapperToApiResult } from '@/utils/fieldMapper';

// 排除掉对象中值为 undefined 或空字符串的属性
function compactFields(object) {
  return omitBy(object, (value) => value === undefined || value === '');
}

/**
 * 通用列表管理 hook
 */
export function useList(options) {
  // 记录初始化时的参数，可用于重置
  const initQuery = cloneDeep(options.initQuery) ?? {};

  const listState = reactive({
    records: [],
    selection: [], // 选中的记录
    // 不做对象拷贝，直接使用传入的对象，以便有机会在不同接口中共享查询参数
    queryObject: options.initQuery ?? {},
    totalCount: 0,
    totalData: null,
    loading: false,
    exporting: false,
  });

  // 没有设置 page、pageSize，则给默认值
  if (listState.queryObject.page === undefined) listState.queryObject.page = 1;
  if (listState.queryObject.pageSize === undefined) listState.queryObject.pageSize = 99999;

  // 显示指定 page、pageSize 为 null，则清空不传递
  if (listState.queryObject.page === null) delete listState.queryObject.page;
  if (listState.queryObject.pageSize === null) delete listState.queryObject.pageSize;

  // 是否 axios 标准响应对象
  function isAxiosResponse(axiosResponse) {
    return has(axiosResponse, 'status');
  }

  /**
   * 调用 API 获取列表数据
   */
  const loadListRecords = async (queryObject) => {
    const payload = merge({}, listState.queryObject, queryObject);

    listState.loading = true;
    let axiosResponse;
    try {
      axiosResponse = await options.loadMethod(compactFields(payload));
      let ioResult;
      if (isAxiosResponse(axiosResponse)) {
        ioResult = axiosResponse.data;
      } else {
        ioResult = axiosResponse;
      }
      listState.totalCount = ioResult.total;
      listState.totalData = ioResult.totalData;
      listState.records = ioResult.data;
      listState.loading = false;
    } catch (error) {
      listState.loading = false;
      throw error;
    }

    return axiosResponse;
  };

  /**
   * 页码更新
   */
  const onListPageChange = (page) => {
    if (listState.queryObject && listState.queryObject.page !== page) {
      listState.queryObject.page = page;
    }
    loadListRecords();
  };

  /**
   * 每页大小更新
   */
  const onListPageSizeChange = (pageSize) => {
    if (listState.queryObject) {
      listState.queryObject.page = 1;
      listState.queryObject.pageSize = pageSize;
    }
    loadListRecords();
  };

  /**
   * 重置查询条件
   */
  const resetQuery = (resetOptions = {}) => {
    let queryObject = cloneDeep(initQuery);

    // 手工调整
    if (typeof resetOptions.map === 'function') {
      queryObject = resetOptions.map(queryObject);
    }
    // 白名单
    if (Array.isArray(resetOptions.include)) {
      queryObject = pick(queryObject, resetOptions.include);
    }
    // 黑名单
    if (Array.isArray(resetOptions.exclude)) {
      queryObject = omit(queryObject, resetOptions.exclude);
    }

    Object.assign(listState.queryObject, queryObject);

    if (!resetOptions.preventReload) {
      console.log('reset, reload: ', cloneDeep(listState.queryObject));
      loadListRecords();
    }
  };

  /**
   * 合计某个字段的值
   */
  const summaryField = (path) => {
    const getValue = typeof path === 'function'
      ? path
      : (record) => get(record, path);

    return listState.records.reduce((acc, record) => {
      const value = getValue(record);
      const numValue = Number(value) || 0;
      return new Decimal(numValue).add(acc).toNumber();
    }, 0);
  };

  /**
   * 监听选中的记录，设置到 listState.selection 中
   */
  const onSelectionChange = (selection) => {
    listState.selection = selection;
  };

  const listMethods = {
    resetQuery,
    loadListRecords,
    onListPageChange,
    onListPageSizeChange,
    summaryField,
    onSelectionChange,
  };

  return { listState, listMethods };
}

/**
 * 通用列表管理 hook，可以传入查询参数映射函数，以及结果映射函数
 */
export function useListWithMapper({ queryMapper, resultMapper }) {
  if (!queryMapper && !resultMapper) {
    return useList;
  }

  return function wrappedUseList(options) {
    const rawLoadMethod = options.loadMethod;

    options.loadMethod = async function wrappedLoadMethod(queryObject) {
      if (queryMapper) {
        queryObject = queryMapper(queryObject);
      }

      const rawResult = await rawLoadMethod.call(options, queryObject);

      if (resultMapper) {
        return applyFieldMapperToApiResult(rawResult, resultMapper);
      }

      return rawResult;
    };

    return useList(options);
  };
}
