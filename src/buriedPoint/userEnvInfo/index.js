/* eslint-disable import/prefer-default-export */
/* eslint-disable arrow-body-style */
import store from '@/store';
import { withUserEnvInfo } from './collect';
import { sendUserEnvInfo } from './api';

function collectAndSendUserEnvInfoWhenLoggedIn() {
  store.watch((state) => state.user.userInfo.userId, (userId, oldUserId) => {
    if (userId && !oldUserId) {
      withUserEnvInfo((info) => {
        info.userId = userId;
        info.selectedOldGuidance = window.localStorage.getItem('selectedOldGuidance');
        sendUserEnvInfo(info);
      });
    }
  });
}

collectAndSendUserEnvInfoWhenLoggedIn();
