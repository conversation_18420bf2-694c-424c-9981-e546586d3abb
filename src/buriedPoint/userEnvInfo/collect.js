/* eslint-disable import/prefer-default-export */
/* eslint-disable no-useless-escape */
/* eslint-disable no-bitwise */
/* eslint-disable no-use-before-define */
/* eslint-disable no-restricted-syntax */
/* eslint-disable guard-for-in */
/* eslint-disable prefer-destructuring */
/* eslint-disable no-restricted-globals */
/**
 * 处理收集到的用户环境信息
 *
 * @param {(info: object) => any} callback 处理用户环境信息的回调
 * @return {Promise<any>}
 */
export async function withUserEnvInfo(callback) {
  return callback(collectUserEnvInfo());
}

// 收集用户环境信息
function collectUserEnvInfo() {
  const info = {};

  Object.assign(info,
    getBrowserInfo(),
    getScreenInfo(),
    getWindowInfo(),
    getOsInfo());

  return info;
}

// 获取操作系统信息
function getOsInfo() {
  const agent = navigator.userAgent;
  const clientStrings = [
    { s: 'Windows 10', r: /(Windows 10.0|Windows NT 10.0)/ },
    { s: 'Windows 8.1', r: /(Windows 8.1|Windows NT 6.3)/ },
    { s: 'Windows 8', r: /(Windows 8|Windows NT 6.2)/ },
    { s: 'Windows 7', r: /(Windows 7|Windows NT 6.1)/ },
    { s: 'Windows Vista', r: /Windows NT 6.0/ },
    { s: 'Windows Server 2003', r: /Windows NT 5.2/ },
    { s: 'Windows XP', r: /(Windows NT 5.1|Windows XP)/ },
    { s: 'Windows 2000', r: /(Windows NT 5.0|Windows 2000)/ },
    { s: 'Windows ME', r: /(Win 9x 4.90|Windows ME)/ },
    { s: 'Windows 98', r: /(Windows 98|Win98)/ },
    { s: 'Windows 95', r: /(Windows 95|Win95|Windows_95)/ },
    { s: 'Windows NT 4.0', r: /(Windows NT 4.0|WinNT4.0|WinNT|Windows NT)/ },
    { s: 'Windows CE', r: /Windows CE/ },
    { s: 'Windows 3.11', r: /Win16/ },
    { s: 'Android', r: /Android/ },
    { s: 'Open BSD', r: /OpenBSD/ },
    { s: 'Sun OS', r: /SunOS/ },
    { s: 'Chrome OS', r: /CrOS/ },
    { s: 'Linux', r: /(Linux|X11(?!.*CrOS))/ },
    { s: 'iOS', r: /(iPhone|iPad|iPod)/ },
    { s: 'Mac OS X', r: /Mac OS X/ },
    { s: 'Mac OS', r: /(Mac OS|MacPPC|MacIntel|Mac_PowerPC|Macintosh)/ },
    { s: 'QNX', r: /QNX/ },
    { s: 'UNIX', r: /UNIX/ },
    { s: 'BeOS', r: /BeOS/ },
    { s: 'OS/2', r: /OS\/2/ },
    { s: 'Search Bot', r: /(nuhk|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask Jeeves\/Teoma|ia_archiver)/ },
  ];
  let osName = 'Unknown';
  for (const id in clientStrings) {
    const cs = clientStrings[id];
    if (cs.r.test(agent)) {
      osName = cs.s;
      break;
    }
  }

  let osVersion = 'Unknown';
  if (/Windows/.test(osName)) {
    osVersion = /Windows (.*)/.exec(osName)[1];
    osName = 'Windows';
  }

  // eslint-disable-next-line default-case
  switch (osName) {
      case 'Mac OS':
      case 'Mac OS X':
      case 'Android':
        osVersion = /(?:Android|Mac OS|Mac OS X|MacPPC|MacIntel|Mac_PowerPC|Macintosh) ([\.\_\d]+)/.exec(agent)[1];
        break;
      case 'iOS':
        osVersion = /OS (\d+)_(\d+)_?(\d+)?/.exec(navigator.appVersion);
        osVersion = `${osVersion[1] }.${ osVersion[2] }.${ osVersion[3] | 0}`;
        break;
  }

  return {
    osName,
    osVersion,
  };
}

// 获取屏幕信息
function getScreenInfo() {
  return {
    screenWidth: screen.width,
    screenHeight: screen.height,
    screenAvailWidth: screen.availWidth,
    screenAvailHeight: screen.availHeight,
    screenColorDepth: screen.colorDepth,
    screenPixelDepth: screen.pixelDepth,
  };
}

// 获取窗口信息
function getWindowInfo() {
  return {
    windowWidth: window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth,
    windowHeight: window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight,
  };
}

// 获取浏览器信息
function getBrowserInfo() {
  const ua = navigator.userAgent;
  let tem;
  let M = ua.match(/(opera|chrome|safari|firefox|msie|trident(?=\/))\/?\s*(\d+)/i) || [];

  if (/trident/i.test(M[1])) {
    tem = /\brv[ :]+(\d+)/g.exec(ua) || [];
    return { name: 'IE', version: tem[1] || '' };
  }

  if (M[1] === 'Chrome') {
    tem = ua.match(/\bOPR|Edge\/(\d+)/);
    if (tem != null) {
      return { name: 'Opera', version: tem[1] };
    }
  }

  M = M[2] ? [M[1], M[2]] : [navigator.appName, navigator.appVersion, '-?'];

  // eslint-disable-next-line no-cond-assign
  if ((tem = ua.match(/version\/(\d+)/i)) != null) {
    M.splice(1, 1, tem[1]);
  }

  const browserName = M[0].toLowerCase();
  const browserVersion = M[1];

  return { browserName, browserVersion };
}
