/* eslint-disable */
import request from '@/utils/request';
/**
 * 新增委外加工 - insertUsingPOST_1
 * /rest/proxy/config/goods/process/v1.0/insert
 * @param prcess - 商品实体
 */
export const processInsert = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/process/v1.0/insert'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['prcess']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回全部或部态 - findUsingGET_1
 * /rest/proxy/config/goods/process/v1.0/list
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param processName - 加工名称
 * @param processStatus - 加工状态
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const processList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/process/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['processName'] = parameters['processName'];
  queryParameters['processStatus'] = parameters['processStatus'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改委外加工 - 
 * /rest/proxy/config/goods/process/v1.0/{processId}
 * @param prcess - 商品实体
 * @param processId - processId
 */
export const updateUsingPUT_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/process/v1.0/{processId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['prcess']
  path = path.replace('{processId}', `${parameters['processId']}`)
  if (parameters['processId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: processId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 获取商品代码 - getGoodsCodeUsingGET
 * /rest/proxy/config/goods/v1.0/generate/goodsCode
 */
export const goodsGenerate = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/generate/goodsCode'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导入 - importGoodsUsingPOST
 * /rest/proxy/config/goods/v1.0/import
 */
export const goodsImport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/import'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 新增商品 - insertUsingPOST
 * /rest/proxy/config/goods/v1.0/insert
 * @param cdGoods - 商品实体
 */
export const goodsInsert = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/insert'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['cdGoods']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 批量新增商品 - insertBatchUsingPOST
 * /rest/proxy/config/goods/v1.0/insertBatch
 * @param cdGoods - 商品实体
 */
export const goodsInsertbatch = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/insertBatch'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['cdGoods']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回全部或部分商品 - findUsingGET
 * /rest/proxy/config/goods/v1.0/list
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param goodsClassId - 商品分类id
 * @param goodsName - 商品名称
 * @param goodsStatus - 商品状态
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const goodsList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['goodsClassId'] = parameters['goodsClassId'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['goodsStatus'] = parameters['goodsStatus'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询构成材料 - findMaterialUsingGET
 * /rest/proxy/config/goods/v1.0/material/list
 */
export const goodsMaterial = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/material/list'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询计算了权重的构成材料 - findMaterialWithPercentUsingGET
 * /rest/proxy/config/goods/v1.0/material/percent
 */
export const findMaterialWithPercent = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/material/percent'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改原材料的权重 - updateMaterialPercentUsingPUT
 * /rest/proxy/config/goods/v1.0/material/percent/{accountPeriod}
 * @param accountPeriod - accountPeriod
 */
export const updateMaterialPercent = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/material/percent/{accountPeriod}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 单记录查询 - 
 * /rest/proxy/config/goods/v1.0/{goodsId}
 * @param goodsId - 商品ID
 */
export const getUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/{goodsId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{goodsId}', `${parameters['goodsId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改商品 - 
 * /rest/proxy/config/goods/v1.0/{goodsId}
 * @param cdGoods - 商品实体
 * @param goodsId - goodsId
 */
export const updateUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/{goodsId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['cdGoods']
  path = path.replace('{goodsId}', `${parameters['goodsId']}`)
  if (parameters['goodsId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: goodsId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 导出新旧科目对比数据 - exportDepreciationDetailUsingGET
 * /rest/proxy/config/imports/v0.1/getCompareBalance/export/{softwareId}
 * @param softwareId - softwareId
 */
export const importsGetcomparebalance = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v0.1/getCompareBalance/export/{softwareId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{softwareId}', `${parameters['softwareId']}`)
  if (parameters['softwareId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: softwareId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 初始化-查看数据对比Excel文件 - getCheckExcelUsingGET
 * /rest/proxy/config/imports/v1.0/data/check/excel
 */
export const importsData = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v1.0/data/check/excel'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 初始化-查看数据对比界面 - getCheckPageUsingGET
 * /rest/proxy/config/imports/v1.0/data/check/page
 */
export const getCheckPage = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v1.0/data/check/page'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 删除报表 - deleteStatementUsingPOST
 * /rest/proxy/config/imports/v1.0/delete
 * @param atReportsHistoryList - atReportsHistoryList
 */
export const importsDelete = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v1.0/delete'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['atReportsHistoryList']
  if (parameters['atReportsHistoryList'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: atReportsHistoryList'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查询单个报表 - findReportUsingGET
 * /rest/proxy/config/imports/v1.0/get/{reportsId}
 * @param reportsId - reportsId
 */
export const importsGet = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v1.0/get/{reportsId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{reportsId}', `${parameters['reportsId']}`)
  if (parameters['reportsId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: reportsId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 选择性复制复制科目返回科目编码 - getSubjectVoListUsingPOST
 * /rest/proxy/config/imports/v1.0/getCopySubjectList
 * @param oldSubjectCode - 旧科目编码
 * @param oldSubjectFullName - 旧科目全称
 * @param parentSubjectCode - 父级科目编码
 * @param parentSubjectName - 父级科目名称
 * @param subjectCode - 新科目编码(当科目对应关系修改时返回)
 * @param subjectName - 当前科目名称
 * @param subjectVoList - subjectVoList
 */
export const importsGetcopysubjectlist = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v1.0/getCopySubjectList'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['oldSubjectCode'] = parameters['oldSubjectCode'];
  queryParameters['oldSubjectFullName'] = parameters['oldSubjectFullName'];
  queryParameters['parentSubjectCode'] = parameters['parentSubjectCode'];
  queryParameters['parentSubjectName'] = parameters['parentSubjectName'];
  queryParameters['subjectCode'] = parameters['subjectCode'];
  queryParameters['subjectName'] = parameters['subjectName'];
  body = parameters['subjectVoList']
  if (parameters['subjectVoList'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: subjectVoList'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查询报表列表 - findReportListUsingGET
 * /rest/proxy/config/imports/v1.0/getReportList
 */
export const importsGetreportlist = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v1.0/getReportList'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 初始化导入软件版本获取 - getSoftwareVesionUsingGET
 * /rest/proxy/config/imports/v1.0/getSoftwareVesion
 */
export const importsGetsoftwarevesion = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v1.0/getSoftwareVesion'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获取新旧科目余额表对应 - getSubjectBalanceMapUsingGET
 * /rest/proxy/config/imports/v1.0/getSubjectBalanceMap/{softwareId}
 * @param softwareId - 软件版本Id
 */
export const importsGetsubjectbalancemap = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v1.0/getSubjectBalanceMap/{softwareId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{softwareId}', `${parameters['softwareId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获取套账AIS新旧科目关系 - getSubjectRalationsUsingPOST
 * /rest/proxy/config/imports/v1.0/getSubjectRalation/{softwareId}
 * @param softwareId - 软件版本Id
 */
export const importsGetsubjectralation = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v1.0/getSubjectRalation/{softwareId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{softwareId}', `${parameters['softwareId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 复制科目返回科目编码 - getSubjectVoUsingPOST
 * /rest/proxy/config/imports/v1.0/getSubjectVo
 * @param bankAccount - 银行账号
 * @param bankName - 开户行
 * @param chooseNameLevelList - 应付账款和应收账款选择科目层级:1、2、3代表二级科目的1，2，3级；合并的话同时传回,例如：[1,2,3]
 * @param oldSubjectCode - 旧科目编码
 * @param parentSubjectCode - 父级科目编码
 * @param parentSubjectName - 父级科目名称
 * @param subjectCode - 新科目编码(当科目对应关系修改时返回)
 * @param subjectName - 当前科目名称
 * @param subjectVo - subjectVo
 */
export const importsGetsubjectvo = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v1.0/getSubjectVo'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['bankAccount'] = parameters['bankAccount'];
  queryParameters['bankName'] = parameters['bankName'];
  queryParameters['chooseNameLevelList'] = parameters['chooseNameLevelList'];
  queryParameters['oldSubjectCode'] = parameters['oldSubjectCode'];
  queryParameters['parentSubjectCode'] = parameters['parentSubjectCode'];
  queryParameters['parentSubjectName'] = parameters['parentSubjectName'];
  queryParameters['subjectCode'] = parameters['subjectCode'];
  queryParameters['subjectName'] = parameters['subjectName'];
  body = parameters['subjectVo']
  if (parameters['subjectVo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: subjectVo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 套账数据迁移-导入套账AIS信息 - findAisCompanyCompareUsingPOST
 * /rest/proxy/config/imports/v1.0/importCompanyAIS/{softwareId}
 * @param file - 套账AIS文件
 * @param softwareId - 软件版本Id
 */
export const importsImportcompanyais = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v1.0/importCompanyAIS/{softwareId}'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['file'] = parameters['file'];
  path = path.replace('{softwareId}', `${parameters['softwareId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 初始化-科目文件上传 - initCompanySubjectDataUsingPOST
 * /rest/proxy/config/imports/v1.0/importCompanySubject
 * @param accessFile - 公司access文件上传
 * @param subjectFile - 科目excel文件上传
 */
export const importsImportcompanysubject = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v1.0/importCompanySubject'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accessFile'] = parameters['accessFile'];
  queryParameters['subjectFile'] = parameters['subjectFile'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 用户公司文件上传 - initUserCompanyDataUsingPOST
 * /rest/proxy/config/imports/v1.0/importUserCompany
 * @param companyFile - 公司Excel文件
 * @param userFile - 用户Excel文件
 */
export const importsImportusercompany = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v1.0/importUserCompany'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['companyFile'] = parameters['companyFile'];
  queryParameters['userFile'] = parameters['userFile'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 三大报表文件数据上传 - insertStatementUsingPOST
 * /rest/proxy/config/imports/v1.0/insert
 * @param atReportsHistoryList - atReportsHistoryList
 */
export const importsInsert = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v1.0/insert'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['atReportsHistoryList']
  if (parameters['atReportsHistoryList'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: atReportsHistoryList'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 判断初始化是否完成 - importIsCompleteUsingPOST
 * /rest/proxy/config/imports/v1.0/isComplete
 */
export const importsIscomplete = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v1.0/isComplete'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 套账数据迁移-数据迁移 - saveSubjectRelationVoUsingPOST
 * /rest/proxy/config/imports/v1.0/saveSubjectRalation/{softwareId}
 * @param cdSubjectMapList - cdSubjectMapList
 * @param companyId - 公司Id
 * @param currency - 币种
 * @param goodNature - 原材料商品性质(0、主要材料，1、关联材料，2、其他材料)
 * @param goodUnit - 商品单位
 * @param newSubjectCode - 新科目编码
 * @param newSubjectFullmame - 新科目全称
 * @param originalSubjectCode - 旧科目编码
 * @param originalSubjectFullmame - 旧科目全称
 * @param softwareId - 软件版本Id
 */
export const importsSavesubjectralation = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v1.0/saveSubjectRalation/{softwareId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['cdSubjectMapList']
  if (parameters['cdSubjectMapList'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: cdSubjectMapList'))
  }
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['currency'] = parameters['currency'];
  queryParameters['goodNature'] = parameters['goodNature'];
  queryParameters['goodUnit'] = parameters['goodUnit'];
  queryParameters['newSubjectCode'] = parameters['newSubjectCode'];
  queryParameters['newSubjectFullmame'] = parameters['newSubjectFullmame'];
  queryParameters['originalSubjectCode'] = parameters['originalSubjectCode'];
  queryParameters['originalSubjectFullmame'] = parameters['originalSubjectFullmame'];
  path = path.replace('{softwareId}', `${parameters['softwareId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 文件上传 - uploadTemplateFileUsingPOST
 * /rest/proxy/config/imports/v1.0/upload/templateFile
 * @param fixedAssetFile - 固定资产文件
 * @param subjectBalanceFile - 科目余额文件上传
 * @param subjectFile - 科目excel文件上传
 * @param voucherDetailsFile - 凭证文件上传
 */
export const importsUpload = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/imports/v1.0/upload/templateFile'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['fixedAssetFile'] = parameters['fixedAssetFile'];
  queryParameters['subjectBalanceFile'] = parameters['subjectBalanceFile'];
  queryParameters['subjectFile'] = parameters['subjectFile'];
  queryParameters['voucherDetailsFile'] = parameters['voucherDetailsFile'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查询项目信息 - getListUsingGET
 * /rest/proxy/config/project/v1.0/alllist
 * @param companyId - 公司ID
 * @param projectName - 项目名称
 * @param projectNameLike - 项目名称模糊
 * @param 项目状态（字典0、停用；1、启用；） - 
 */
export const projectAlllist = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/project/v1.0/alllist'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['projectName'] = parameters['projectName'];
  queryParameters['projectNameLike'] = parameters['projectNameLike'];
  queryParameters['项目状态（字典  0、停用；1、启用；）'] = parameters['项目状态（字典0、停用；1、启用；）'];
  if (parameters['项目状态（字典0、停用；1、启用；）'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: 项目状态（字典0、停用；1、启用；）'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 新增项目 - insertUsingPOST_2
 * /rest/proxy/config/project/v1.0/insert
 * @param info - 项目实体
 */
export const projectInsert = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/project/v1.0/insert'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['info']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查询有效项目信息 - getListByStatusUsingGET
 * /rest/proxy/config/project/v1.0/list
 * @param companyId - 公司ID
 * @param projectName - 项目名称
 * @param projectNameLike - 项目名称模糊
 * @param 项目状态（字典0、停用；1、启用；） - 
 */
export const projectList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/project/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['projectName'] = parameters['projectName'];
  queryParameters['projectNameLike'] = parameters['projectNameLike'];
  queryParameters['项目状态（字典  0、停用；1、启用；）'] = parameters['项目状态（字典0、停用；1、启用；）'];
  if (parameters['项目状态（字典0、停用；1、启用；）'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: 项目状态（字典0、停用；1、启用；）'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 单记录查询 - 
 * /rest/proxy/config/project/v1.0/{projectId}
 * @param projectId - 项目ID
 */
export const getUsingGET_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/project/v1.0/{projectId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{projectId}', `${parameters['projectId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改项目 - 
 * /rest/proxy/config/project/v1.0/{projectId}
 * @param info - 项目实体
 * @param projectId - projectId
 */
export const updateUsingPUT_2 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/project/v1.0/{projectId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['info']
  path = path.replace('{projectId}', `${parameters['projectId']}`)
  if (parameters['projectId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: projectId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 删除项目 - 
 * /rest/proxy/config/project/v1.0/{projectId}
 * @param projectId - projectId
 */
export const updateUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/project/v1.0/{projectId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{projectId}', `${parameters['projectId']}`)
  if (parameters['projectId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: projectId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}