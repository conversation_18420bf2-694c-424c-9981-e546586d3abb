/*
 * @Description: 财务模块相关api
 * @version:
 * @Company: 海闻软件
 * @Author: 肖泽涛
 * @Date: 2019-01-17 17:27:35
 * @LastEditors: 肖泽涛
 * @LastEditTime: 2019-05-31 16:22:35
 */
import request from '@/utils/request';

/**
 *快速做账进项生成凭证
 *
 * @export
 * @param {*} accountPeriod -属期（yyyyMM）
 */
export function quickVoucherInput(accountPeriod) {
  return request.post(`/rest/proxy/account/merge/quick/account/v1.0/invoice/1/vouchers/${accountPeriod}`, null, { headers: { noLoading: 'true' } });
}
/**
 *快速做账销项生成凭证
 *
 * @export
 * @param {*} accountPeriod -属期（yyyyMM）
 */
export function quickVoucherOutput(accountPeriod) {
  return request.post(`/rest/proxy/account/merge/quick/account/v1.0/invoice/2/vouchers/${accountPeriod}`, null, { headers: { noLoading: 'true' } });
}
/**
 *快速做账银行流水生成凭证
 *
 * @export
 * @param {*} accountPeriod -属期（yyyyMM）
 */
export function quickVoucherBank(accountPeriod) {
  return request.post(`/rest/proxy/account/merge/quick/account/v1.0/fund/1/vouchers/${accountPeriod}`, null, { headers: { noLoading: 'true' } });
}
/**
 *快速做账现金流水生成凭证
 *
 * @export
 * @param {*} accountPeriod -属期（yyyyMM）
 */
export function quickVoucherCash(accountPeriod) {
  return request.post(`/rest/proxy/account/merge/quick/account/v1.0/fund/2/vouchers/${accountPeriod}`, null, { headers: { noLoading: 'true' } });
}
/**
 *快速做账费用报销生成凭证
 *
 * @export
 * @param {*} accountPeriod -属期（yyyyMM）
 */
export function quickVoucherCost(accountPeriod) {
  return request.post(`/rest/proxy/account/merge/quick/account/v1.0/claim/vouchers/${accountPeriod}/${accountPeriod}`, null, { headers: { noLoading: 'true' } });
}

/**
 *查看工资凭证
 *
 * @export
 * @param {*} accountPeriod -属期（yyyyMM）
 */
export function getSalaryVoucher(accountPeriod) {
  return request.get(`/rest/companyAccount/merge/voucher/6001/${accountPeriod}/v1.0`);
}
/**
 *查看结转制造费凭证
 *
 * @export
 * @param {*} accountPeriod -属期（yyyyMM）
 */
export function getFabricateVoucher(accountPeriod) {
  return request.get(`/rest/companyAccount/merge/voucher/accountsType/9032/${accountPeriod}/v1.0`);
}
/**
 *预览结转制造费用凭证
 *
 * @export
 * @param {*} accountPeriod -属期（yyyyMM）
 */
export function previewFabricateVoucher(accountPeriod) {
  return request.get(`/rest/proxy/account/costing/voucher/preview/9032/${accountPeriod}/v1.0`);
}

/**
 *新增凭证
 *
 * @export
 * @param {*} voucher
 * @returns
 */
export function insertVoucher(voucher) {
  return request.post('/rest/companyAccount/merge/voucher/v1.0', voucher);
}

/**
 *
 *
 * @export
 * @param {*} {mainIds, subIds} subIds - 子凭证id mainIds - 主凭证id
 * @returns
 */
export function removeVoucher({ mainIds = '', subIds = '' }) {
  return request.delete('/rest/companyAccount/merge/voucher/batch/v1.0', { params: { mainIds, subIds } });
}
