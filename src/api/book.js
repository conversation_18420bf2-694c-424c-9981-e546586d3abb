/*
 * @Description:  账本相关api
 * @version:
 * @Company: 海闻软件
 * @Author: 晓荣
 * @LastEditors: 晓荣
 * @Date: 2019-02-20 14:16:32
 * @LastEditTime: 2019-08-26 14:43:00
 */
import request from '@/utils/request';

/**
 * 科目余额表
 * subjectParentCodes=top 时 获取一级科目
 * subjectParentCodes= 1001  获取该科目下的二三级科目余额
 * @export
 * @param {*} params
 */
export function getAccountBalanceSheet(params) {
  return request.get('/rest/statements/merge/financial/balance/v1.0/list', { params });
}

/**
 * 明细账
 *
 * @export
 * @param {*} params
 * @returns
 */
export function getSubAccountList(params) {
  return request.get('/rest/statements/merge/financial/detailAccount/v1.0/list', { params });
}

/**
 * 明细账 区间内有发生数据的科目
 *
 * @export
 * @param {*} params
 * @returns
 */
export function getSubAccountCode(params) {
  return request.get('/rest/statements/merge/financial/detailAccount/v1.0/codes', { params });
}

/**
 * 数量金额明细账
 *
 * @export
 * @param {*} params
 * @returns
 */
export function getAmountGeneralSubsidiary(params) {
  return request.get('/rest/proxy/stmt/merge/report/numDetails/v1.0/list', { params });
}

/**
 * 数量金额总账
 *
 * @export
 * @param {*} params
 * @returns
 */
export function getAmountGeneralLedger(params) {
  return request.get('/rest/proxy/stmt/merge/report/number/v2.0/list', { params });
}

/**
 * @description:  销售汇总表 查询
 * @param {type}
 * @return:
 */
export function getSalesSummaryList(params) {
  return request.get('/rest/statements/merge/sale/gathering/v1.0/list', { params });
}

/**
 * @description:  销售汇总表存货名称 查询
 * @param {type}
 * @return:
 */
export function getSalesSummaryNameList(params) {
  return request.get('/rest/statements/merge/sale/gathering/v1.0/names', { params });
}

/**
 * 销售汇总表 查询存货名称的科目编码
 *
 * @export
 * @param {*} params subjectCode 6001 / subjectName 存货名称
 * @returns
 */
export function getSubjectinfoCode(params) {
  return request.get('/rest/companyConfig/companyBasis/subjectinfo/v1.1/by', { params });
}

/**
 * @description:  收入统计表
 * @param {type}  fromPeriod YYYY-MM toPeriod YYYY-MM
 * @return:
 */
export function getIncomeSummaryList(params) {
  return request.get(`/rest/statements/merge/sale/summary/incomeSummary/${params.fromPeriod}/${params.toPeriod}/v1.0`, { params });
}

/**
 * @description:  采购统计表
 * @param {type}  fromPeriod YYYY-MM-dd toPeriod YYYY-MM-dd
 * @return:
 */
export function getProcurementStatisticList(params) {
  return request.get(`/rest/statements/merge/financial/procurementStatistic/v1.0/list/${params.fromPeriod}/${params.toPeriod}`, { params });
}

/**
 * @description:  生产成本表、管理费用表、销售费用表
 * @param {type}  businessTimeFrom YYYY-MM-DD businessTimeTo YYYY-MM-DD
 * categoryCode 1.生产成本，2.管理费用，3.销售费用
 * @return:
 */
export function getProductionList(params) {
  return request.get('/rest/proxy/stmt/merge/report/production/v1.0/list', { params });
}

/**
 * @description:  制造费用多栏明细账
 * @param {type}  businessTimeFrom YYYY-MM-DD businessTimeTo YYYY-MM-DD
 * @return:
 */
export function getManufactureCostList(params) {
  return request.get('/rest/proxy/stmt/merge/report/manufacture/cost/v1.0/list', { params });
}

/**
 * @description:  取数规则查询
 * @param {type}  params
 * lineId	行号
 * lineName	行代码
 * moduleId	模块ID（1：资产负债表-企业会计准则；2：利润表；3：资产负债表-小企业会计准则；
 * querySubject	是否查询科目信息：0：否；1：是
 *
 * @return:
 */
export function getRuleList(params) {
  return request.get('/rest/companyConfig/companyBasis/rule/v1.0/list', { params });
}

/**
 * @description:  取数规则新增
 * @param {type}  moduleId
 * @return:
 */
export function addRule(moduleId, list) {
  return request.post(`/rest/companyConfig/companyBasis/rule/v1.0/insert/${moduleId}`, list);
}

/**
 * @description:  取数规则删除
 * @param {type}  moduleId
 * @return:
 */
export function deleteRule(moduleId) {
  return request.delete(`/rest/companyConfig/companyBasis/rule/v1.0/delete/${moduleId}`);
}

/**
 * @description:  负债表取数查询
 * @return:
 */
 export function getBsList(params) {
  return request.get('/rest/statements/merge/financial/bs/v2.0/list', { params });
}
/**
 * @description:  负债表取数规则查询
 * @return:
 */
 export function getBsRulesList(params) {
  return request.get('/rest/statements/merge/financial/bs/v2.0/rules', { params });
}

/**
 * @description:  利润表取数查询
 * @return:
 */
 export function getPsList(params) {
  return request.get('rest/statements/merge/financial/ps/v2.0/list', { params });
}
/**
 * @description:  利润表取数规则查询
 * @return:
 */
 export function getPsRuleList(params) {
  return request.get('/rest/statements/merge/financial/ps/v2.0/rules', { params });
}

/**
 * @description:  现金流量表
 * @return:
 */
export function getCashFlowList(params) {
  return request.get('/rest/statements/merge/financial/cashflowex2/v3.1/list', { params });
}

/**
 * @description:  现金流量表取数规则查询
 * @return:
 */
export function getCashFlowRuleList() {
  return request.get('/rest/companyConfig/companyBasis/cash_rule/v1.0');
}

/**
 * @description:  仓帐 打印
 *
 * @return:
 */
export function getListAll(params) {
  return request.get('/rest/proxy/stmt/merge/report/goods/v1.0/listAll', { params });
}

/**
 * @description:  标签汇总表查询
 *
 * @return:
 */
export function getTagAggregationList(params, urlParams) {
  const API = '/rest/statements/merge/financial/tagAggregation/v1.0/list';
  const config = {
    params: urlParams,
  };
  return request.post(API, params, config);
}

/**
 * @description:  标签汇总表文件导出生成
 *
 * @return:
 */
export function postTagAggregationFile(params) {
  const API = '/rest/statements/merge/financial/tagAggregation/v1.0/file';
  return request.post(API, params);
}

/**
 * @description:  科目标签明细账查询
 *
 * @return:
 */
export function getTagAggregationDetailList(params) {
  const API = '/rest/statements/merge/financial/tagAggregationDetail/v1.0/list';
  return request.get(API, params);
}

/**
 * @description:  科目标签明细账文件导出生成
 *
 * @return:
 */
export function postTagAggregationDetailFile(params) {
  const API = '/rest/statements/merge/financial/tagAggregationDetail/v1.0/file';
  return request.post(API, params);
}

/**
 * @description:  s=查询明细账的凭证部分"
 *
 * @return:
 */
 export function getDetailAccountVoucherList(params) {
  const API = '/rest/statements/merge/financial/detailAccount/v1.0/voucher_list';
  return request.get(API, { params });
}
// 科目标签余额明细 按单组查询
export function getSubjectTagBalanceList(params) {
  const API = '/rest/statements/merge/financial/subjectTagBalance/v1.0/accountBookGroupOne';

  return request.get(API, { params });
}
/**
 * 获取科目标签余额明细，按多组查询。
 *
 * @param {Object} params - 查询参数
 * @param {string} params.accountPeriodBegin - 起始会计期间，格式为YYYYMM，例如202501
 * @param {string} params.accountPeriodEnd - 结束会计期间，格式为YYYYMM，例如202506
 * @param {string|null} params.currencyType - 货币类型，null表示所有货币
 * @param {boolean} params.reversalNegative - 是否反转负值
 * @param {Object[]} params.subjectTagTeam - 科目标签组数组
 * @param {string} params.subjectTagTeam[].subjectId - 科目标签组中的科目ID
 * @param {string[]} params.subjectTagTeam[].tagIds - 科目标签组中的标签ID数组
 * @returns {Promise} 返回包含请求结果的Promise对象
 */
export function getSubjectTagBalanceTeamList(params) {
  const API = '/rest/statements/merge/financial/subjectTagBalance/v1.0/accountBookGroupTeam';

  return request.post(API, params, { custom: { canRepeat: true } });
}

// 查询经营盈亏表
export function getLossList(params) {
  const API = '/rest/statements/merge/financial/loss/v1.0/list';
  return request.get(API, { params });
}

// 现金流量表 凭证取数规则弹窗
export function postCashflowVouchers(params) {
  const API = `/rest/statements/merge/financial/cashflowex2/v1.0/vouchers?page=${params.page}&pageSize=${params.pageSize}`;
  return request.post(API, params);
}

// 现金流量表 凭证取数规则弹窗
export function exportCashflowVouchers(params) {
  const API = '/rest/statements/merge/financial/cashflowex2/v1.0/vouchers/export';
  return request.post(API, params, {
    responseType: 'arraybuffer',
  });
}
