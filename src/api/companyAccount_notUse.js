/* eslint-disable */
import request from '@/utils/request';
/**
 * 添加或修改结转金额 - 
 * /rest/companyAccount/merge/carried/amount/v1.0
 * @param carried - carried
 */
export const insertOrUpdateCarriedUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/carried/amount/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['carried']
  if (parameters['carried'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: carried'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 查询结转外币汇率 - 
 * /rest/companyAccount/merge/carried/exchangeRate/v1.0
 * @param accountPeriod - 会计属期，YYYYMM
 * @param businessTimeFrom - 单据开始日期，YYYY-MM-DD
 * @param businessTimeTo - 单据结束日期，YYYY-MM-DD
 * @param categoryCode - 查询类型码：1.生产成本，2.管理费用，3.销售费用
 * @param categoryName - 查询类型名称
 */
export const findCarriedExchangeRateUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/carried/exchangeRate/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['categoryCode'] = parameters['categoryCode'];
  queryParameters['categoryName'] = parameters['categoryName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 新增结转外币汇率 - 
 * /rest/companyAccount/merge/carried/exchangeRate/v1.0
 * @param currencyList - currencyList
 */
export const insertCarriedExchangeRateUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/carried/exchangeRate/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['currencyList']
  if (parameters['currencyList'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: currencyList'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查询商品的销售状态数量 - 
 * /rest/companyAccount/merge/carried/saleGoodsQty/{accountPeriod}/v1.0
 * @param accountPeriod - accountPeriod
 */
export const findCarriedSaleCostQtyUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/carried/saleGoodsQty/{accountPeriod}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 保存商品的销售状态数量 - 
 * /rest/companyAccount/merge/carried/saleGoodsQty/{accountPeriod}/v1.0
 * @param accountPeriod - accountPeriod
 * @param qtySetting - qtySetting
 */
export const insertCarriedSaleCostQtyUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/carried/saleGoodsQty/{accountPeriod}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  body = parameters['qtySetting']
  if (parameters['qtySetting'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: qtySetting'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 更新商品的销售状态数量 - 
 * /rest/companyAccount/merge/carried/saleGoodsQty/{accountPeriod}/v1.0
 * @param accountPeriod - accountPeriod
 * @param qtySetting - qtySetting
 */
export const updateCarriedSaleCostQtyUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/carried/saleGoodsQty/{accountPeriod}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  body = parameters['qtySetting']
  if (parameters['qtySetting'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: qtySetting'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 重设商品的销售状态数量 - 
 * /rest/companyAccount/merge/carried/saleGoodsQty/{accountPeriod}/v1.0
 * @param accountPeriod - accountPeriod
 */
export const deleteCarriedSaleCostQtyUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/carried/saleGoodsQty/{accountPeriod}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 查询各个模块有没有结转 - 
 * /rest/companyAccount/merge/carried/simple/status/{accountPeriod}/v1.0
 * @param accountPeriod - accountPeriod
 */
export const getCarriedSimpleStatusUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/carried/simple/status/{accountPeriod}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 更新计提税金及附加的税率 - 
 * /rest/companyAccount/merge/carried/taxRate/v1.0
 * @param carriedTax - carriedTax
 */
export const updateCarriedTaxUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/carried/taxRate/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['carriedTax']
  if (parameters['carriedTax'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: carriedTax'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 更新计税依据 - 
 * /rest/companyAccount/merge/carried/taxation/{accountPeriod}/{taxationBasis}/v1.0
 * @param accountPeriod - accountPeriod
 * @param taxationBasis - taxationBasis
 */
export const updateTaxationBasisUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/carried/taxation/{accountPeriod}/{taxationBasis}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  path = path.replace('{taxationBasis}', `${parameters['taxationBasis']}`)
  if (parameters['taxationBasis'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: taxationBasis'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 查询结转信息 - 
 * /rest/companyAccount/merge/carried/{carriedType}/v1.0
 * @param accountPeriod - 会计属期，YYYYMM
 * @param businessTimeFrom - 单据开始日期，YYYY-MM-DD
 * @param businessTimeTo - 单据结束日期，YYYY-MM-DD
 * @param carriedType - 结转类型
 * @param tip - 不满足结转条件时是否弹出提示
 */
export const findCarriedListUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/carried/{carriedType}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['carriedType'] = parameters['carriedType'];
  queryParameters['tip'] = parameters['tip'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 添加结转信息 - 
 * /rest/companyAccount/merge/carried/{carriedType}/v1.0
 * @param carriedType - carriedType
 * @param criteria - criteria
 */
export const insertCarriedListUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/carried/{carriedType}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{carriedType}', `${parameters['carriedType']}`)
  if (parameters['carriedType'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: carriedType'))
  }
  body = parameters['criteria']
  if (parameters['criteria'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: criteria'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 导出资产折旧信息 - exportDepreciationDetailUsingGET_1
 * /rest/companyAccount/merge/depreciation/v0.1/detail/export
 * @param addMethod - 增加方式
 * @param assetCode - 资产编号
 * @param assetId - 资产ID
 * @param assetIdList - 
 * @param assetItemsId - 资产明细ID
 * @param assetName - 资产名称
 * @param assetStatusList - 
 * @param assetTypeId - 资产类型ID
 * @param assetTypeIdList - 
 * @param department - 使用部门
 * @param displayStatus - 显示状态（1：显示、0：不显示，默认为1）
 * @param disposeId - 处置主表ID
 * @param disposeIdList - 
 * @param disposeItemsId - 处置明细ID
 * @param disposeMode - 资产处置方式（1：清理、2：盘亏）
 * @param strBDateEnd - 本年结束使用日期 字符串类型
 * @param strBDateStart - 本年开始使用日期 字符串类型
 * @param strDateEnd - 结束使用日期 字符串类型
 * @param strDateStart - 开始使用日期 字符串类型
 * @param supplierId - 供应商ID
 * @param supplierName - 供应商名称
 * @param usedDateEnd - 结束使用日期 或者 处置日期
 * @param usedDateStart - 开始使用日期 或者 处置日期
 * @param usedStatus - 使用状态（1：正常、2：清理）
 */
export const depreciationDetail = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/depreciation/v0.1/detail/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['addMethod'] = parameters['addMethod'];
  queryParameters['assetCode'] = parameters['assetCode'];
  queryParameters['assetId'] = parameters['assetId'];
  queryParameters['assetIdList'] = parameters['assetIdList'];
  queryParameters['assetItemsId'] = parameters['assetItemsId'];
  queryParameters['assetName'] = parameters['assetName'];
  queryParameters['assetStatusList'] = parameters['assetStatusList'];
  queryParameters['assetTypeId'] = parameters['assetTypeId'];
  queryParameters['assetTypeIdList'] = parameters['assetTypeIdList'];
  queryParameters['department'] = parameters['department'];
  queryParameters['displayStatus'] = parameters['displayStatus'];
  queryParameters['disposeId'] = parameters['disposeId'];
  queryParameters['disposeIdList'] = parameters['disposeIdList'];
  queryParameters['disposeItemsId'] = parameters['disposeItemsId'];
  queryParameters['disposeMode'] = parameters['disposeMode'];
  queryParameters['strBDateEnd'] = parameters['strBDateEnd'];
  queryParameters['strBDateStart'] = parameters['strBDateStart'];
  queryParameters['strDateEnd'] = parameters['strDateEnd'];
  queryParameters['strDateStart'] = parameters['strDateStart'];
  queryParameters['supplierId'] = parameters['supplierId'];
  queryParameters['supplierName'] = parameters['supplierName'];
  queryParameters['usedDateEnd'] = parameters['usedDateEnd'];
  queryParameters['usedDateStart'] = parameters['usedDateStart'];
  queryParameters['usedStatus'] = parameters['usedStatus'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 手动折旧资产 - createAssetDepreciationUsingPOST
 * /rest/companyAccount/merge/depreciation/v1.0/insert/{accountPeriod}
 * @param accountPeriod - accountPeriod
 * @param depreciationDetailList - depreciationDetailList
 * @param isMerge - 是否合并明细科目
 */
export const depreciationInsert = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/depreciation/v1.0/insert/{accountPeriod}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  body = parameters['depreciationDetailList']
  if (parameters['depreciationDetailList'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: depreciationDetailList'))
  }
  body = parameters['isMerge']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查询资产折旧明细 - findUsingGET
 * /rest/companyAccount/merge/depreciation/v1.0/list/{accountPeriod}
 * @param accountPeriod - accountPeriod
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const depreciationList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/depreciation/v1.0/list/{accountPeriod}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 预览折旧资产凭证 - preViewAssetDepreciationVoucherUsingPOST
 * /rest/companyAccount/merge/depreciation/v1.0/preView/{accountPeriod}
 * @param accountPeriod - accountPeriod
 * @param depreciationDetailList - depreciationDetailList
 * @param isMerge - 是否合并明细科目
 */
export const depreciationPreview = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/depreciation/v1.0/preView/{accountPeriod}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  body = parameters['depreciationDetailList']
  if (parameters['depreciationDetailList'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: depreciationDetailList'))
  }
  body = parameters['isMerge']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查询费用分摊明细 - 
 * /rest/companyAccount/merge/expenses/apportion/v1.0
 * @param apportionStatus - 摊销状态(1：未启动，2：摊销中，3: 摊完)
 * @param businessTimeFrom - 单据开始日期
 * @param businessTimeTo - 单据结束日期
 * @param data - 
 * @param expensesName - 费用名称
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const findExpensesApportionListUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/expenses/apportion/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['apportionStatus'] = parameters['apportionStatus'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['data'] = parameters['data'];
  queryParameters['expensesName'] = parameters['expensesName'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询费用单列表 - 
 * /rest/companyAccount/merge/expenses/bill/v1.0
 * @param billStatus - 单据状态(1：草稿，2：已对账)
 * @param businessTimeFrom - 单据开始日期
 * @param businessTimeTo - 单据结束日期
 * @param data - 
 * @param expensesBill - 费用单号
 * @param expensesName - 费用名称
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param supplierId - 供应商ID
 * @param supplierName - 供应商名称
 * @param take - 
 */
export const findExpensesBillListUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/expenses/bill/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['billStatus'] = parameters['billStatus'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['data'] = parameters['data'];
  queryParameters['expensesBill'] = parameters['expensesBill'];
  queryParameters['expensesName'] = parameters['expensesName'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['supplierId'] = parameters['supplierId'];
  queryParameters['supplierName'] = parameters['supplierName'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 添加费用单 - 
 * /rest/companyAccount/merge/expenses/bill/v1.0
 * @param vo - vo
 */
export const insertExpensesBillUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/expenses/bill/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['vo']
  if (parameters['vo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 修改费用单 - 
 * /rest/companyAccount/merge/expenses/bill/v1.0
 * @param vo - vo
 */
export const updateExpensesBillUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/expenses/bill/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['vo']
  if (parameters['vo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 查询费用单详细信息 - 
 * /rest/companyAccount/merge/expenses/bill/v1.0/{expensesBillId}
 * @param expensesBillId - 费用单ID
 */
export const getExpensesBillDetailUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/expenses/bill/v1.0/{expensesBillId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{expensesBillId}', `${parameters['expensesBillId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 删除费用单 - 
 * /rest/companyAccount/merge/expenses/bill/v1.0/{expensesBillId}
 * @param expensesBillId - 费用单ID
 */
export const deleteExpensesBillUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/expenses/bill/v1.0/{expensesBillId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{expensesBillId}', `${parameters['expensesBillId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 查询费用类型 - 
 * /rest/companyAccount/merge/expenses/type/v1.0
 * @param data - 
 * @param expensesName - 费用名称
 * @param expensesType - 费用类型(1：财务费用、2：经营费用)
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const findExpensesTypeListUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/expenses/type/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['expensesName'] = parameters['expensesName'];
  queryParameters['expensesType'] = parameters['expensesType'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 添加费用类型 - 
 * /rest/companyAccount/merge/expenses/type/v1.0
 * @param vo - vo
 */
export const insertExpensesTypeUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/expenses/type/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['vo']
  if (parameters['vo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 批量删除费用类型 - 
 * /rest/companyAccount/merge/expenses/type/v1.0
 * @param ids - 费用ID, 用逗号分隔
 */
export const deleteExpensesTypeUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/expenses/type/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['ids'] = parameters['ids'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 费用单付款 - insertPaymentUsingPOST
 * /rest/companyAccount/merge/expenses/v1.0/addPayment
 * @param vo - vo
 */
export const expensesAddpayment = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/expenses/v1.0/addPayment'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['vo']
  if (parameters['vo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 添加资产信息 - insertFixedAssetsCardUsingPOST
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/add
 * @param isCreateVoucher - true 生成凭证，false 不生成凭证
 * @param po - po
 * @param type - 0 保存草稿 1 确认新增
 */
export const fixedAssetsCardAdd = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/add'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['isCreateVoucher']
  body = parameters['po']
  if (parameters['po'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: po'))
  }
  body = parameters['type']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 添加资产处置信息 - insertAssetDisposalUsingPOST
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/addDisposal
 * @param po - po
 */
export const fixedAssetsCardAdddisposal = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/addDisposal'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['po']
  if (parameters['po'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: po'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 保存资产付款信息 - insertAssetPaymentMergeVoUsingPOST
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/addPaymentMerge
 * @param po - po
 */
export const fixedAssetsCardAddpaymentmerge = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/addPaymentMerge'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['po']
  if (parameters['po'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: po'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 生成资产类别编号 - getFixedAssetsCodeUsingGET
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/assetCode/{assetTypeCode}/{number}
 * @param assetTypeCode - 资产类别代码
 * @param number - 数量
 */
export const fixedAssetsCardAssetcode = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/assetCode/{assetTypeCode}/{number}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{assetTypeCode}', `${parameters['assetTypeCode']}`)
  path = path.replace('{number}', `${parameters['number']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获得资卡片信息 - getAssetsCardDetailByIdUsingGET
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/assetcarddetailpo/{assetItemsId}
 * @param assetItemsId - 资产明细信息ID
 */
export const fixedAssetsCardAssetcarddetailpo = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/assetcarddetailpo/{assetItemsId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{assetItemsId}', `${parameters['assetItemsId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获得单类资产信息 - getFixedAssetsCardByIdUsingGET
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/assetcardpo/{assetId}
 * @param assetId - 单类资产信息ID
 */
export const fixedAssetsCardAssetcardpo = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/assetcardpo/{assetId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{assetId}', `${parameters['assetId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 删除资产信息 - deleteFixedAssetsCardUsingDELETE
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/delete
 * @param fixedAssetsCards - 资产ID 批量删除 用逗号隔开
 */
export const fixedAssetsCardDelete = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/delete'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['fixedAssetsCards'] = parameters['fixedAssetsCards'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 获得资产折旧信息 - getDepreciationListUsingGET
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/depreciation
 * @param addMethod - 增加方式
 * @param assetCode - 资产编号
 * @param assetId - 资产ID
 * @param assetIdList - 
 * @param assetItemsId - 资产明细ID
 * @param assetName - 资产名称
 * @param assetStatusList - 
 * @param assetTypeId - 资产类型ID
 * @param assetTypeIdList - 
 * @param data - 
 * @param department - 使用部门
 * @param displayStatus - 显示状态（1：显示、0：不显示，默认为1）
 * @param disposeId - 处置主表ID
 * @param disposeIdList - 
 * @param disposeItemsId - 处置明细ID
 * @param disposeMode - 资产处置方式（1：清理、2：盘亏）
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param strBDateEnd - 本年结束使用日期 字符串类型
 * @param strBDateStart - 本年开始使用日期 字符串类型
 * @param strDateEnd - 结束使用日期 字符串类型
 * @param strDateStart - 开始使用日期 字符串类型
 * @param supplierId - 供应商ID
 * @param supplierName - 供应商名称
 * @param take - 
 * @param usedDateEnd - 结束使用日期 或者 处置日期
 * @param usedDateStart - 开始使用日期 或者 处置日期
 * @param usedStatus - 使用状态（1：正常、2：清理）
 */
export const fixedAssetsCardDepreciation = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/depreciation'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['addMethod'] = parameters['addMethod'];
  queryParameters['assetCode'] = parameters['assetCode'];
  queryParameters['assetId'] = parameters['assetId'];
  queryParameters['assetIdList'] = parameters['assetIdList'];
  queryParameters['assetItemsId'] = parameters['assetItemsId'];
  queryParameters['assetName'] = parameters['assetName'];
  queryParameters['assetStatusList'] = parameters['assetStatusList'];
  queryParameters['assetTypeId'] = parameters['assetTypeId'];
  queryParameters['assetTypeIdList'] = parameters['assetTypeIdList'];
  queryParameters['data'] = parameters['data'];
  queryParameters['department'] = parameters['department'];
  queryParameters['displayStatus'] = parameters['displayStatus'];
  queryParameters['disposeId'] = parameters['disposeId'];
  queryParameters['disposeIdList'] = parameters['disposeIdList'];
  queryParameters['disposeItemsId'] = parameters['disposeItemsId'];
  queryParameters['disposeMode'] = parameters['disposeMode'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['strBDateEnd'] = parameters['strBDateEnd'];
  queryParameters['strBDateStart'] = parameters['strBDateStart'];
  queryParameters['strDateEnd'] = parameters['strDateEnd'];
  queryParameters['strDateStart'] = parameters['strDateStart'];
  queryParameters['supplierId'] = parameters['supplierId'];
  queryParameters['supplierName'] = parameters['supplierName'];
  queryParameters['take'] = parameters['take'];
  queryParameters['usedDateEnd'] = parameters['usedDateEnd'];
  queryParameters['usedDateStart'] = parameters['usedDateStart'];
  queryParameters['usedStatus'] = parameters['usedStatus'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获得资产处置信息和折旧信息 - getDispostalListUsingGET
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/dispostal
 * @param addMethod - 增加方式
 * @param assetCode - 资产编号
 * @param assetId - 资产ID
 * @param assetIdList - 
 * @param assetItemsId - 资产明细ID
 * @param assetName - 资产名称
 * @param assetStatusList - 
 * @param assetTypeId - 资产类型ID
 * @param assetTypeIdList - 
 * @param data - 
 * @param department - 使用部门
 * @param displayStatus - 显示状态（1：显示、0：不显示，默认为1）
 * @param disposeId - 处置主表ID
 * @param disposeIdList - 
 * @param disposeItemsId - 处置明细ID
 * @param disposeMode - 资产处置方式（1：清理、2：盘亏）
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param strBDateEnd - 本年结束使用日期 字符串类型
 * @param strBDateStart - 本年开始使用日期 字符串类型
 * @param strDateEnd - 结束使用日期 字符串类型
 * @param strDateStart - 开始使用日期 字符串类型
 * @param supplierId - 供应商ID
 * @param supplierName - 供应商名称
 * @param take - 
 * @param usedDateEnd - 结束使用日期 或者 处置日期
 * @param usedDateStart - 开始使用日期 或者 处置日期
 * @param usedStatus - 使用状态（1：正常、2：清理）
 */
export const fixedAssetsCardDispostal = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/dispostal'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['addMethod'] = parameters['addMethod'];
  queryParameters['assetCode'] = parameters['assetCode'];
  queryParameters['assetId'] = parameters['assetId'];
  queryParameters['assetIdList'] = parameters['assetIdList'];
  queryParameters['assetItemsId'] = parameters['assetItemsId'];
  queryParameters['assetName'] = parameters['assetName'];
  queryParameters['assetStatusList'] = parameters['assetStatusList'];
  queryParameters['assetTypeId'] = parameters['assetTypeId'];
  queryParameters['assetTypeIdList'] = parameters['assetTypeIdList'];
  queryParameters['data'] = parameters['data'];
  queryParameters['department'] = parameters['department'];
  queryParameters['displayStatus'] = parameters['displayStatus'];
  queryParameters['disposeId'] = parameters['disposeId'];
  queryParameters['disposeIdList'] = parameters['disposeIdList'];
  queryParameters['disposeItemsId'] = parameters['disposeItemsId'];
  queryParameters['disposeMode'] = parameters['disposeMode'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['strBDateEnd'] = parameters['strBDateEnd'];
  queryParameters['strBDateStart'] = parameters['strBDateStart'];
  queryParameters['strDateEnd'] = parameters['strDateEnd'];
  queryParameters['strDateStart'] = parameters['strDateStart'];
  queryParameters['supplierId'] = parameters['supplierId'];
  queryParameters['supplierName'] = parameters['supplierName'];
  queryParameters['take'] = parameters['take'];
  queryParameters['usedDateEnd'] = parameters['usedDateEnd'];
  queryParameters['usedDateStart'] = parameters['usedDateStart'];
  queryParameters['usedStatus'] = parameters['usedStatus'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获得资产处置信息和折旧合计 - getDispostalListSummaryUsingGET
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/dispostal/total
 * @param addMethod - 增加方式
 * @param assetCode - 资产编号
 * @param assetId - 资产ID
 * @param assetIdList - 
 * @param assetItemsId - 资产明细ID
 * @param assetName - 资产名称
 * @param assetStatusList - 
 * @param assetTypeId - 资产类型ID
 * @param assetTypeIdList - 
 * @param department - 使用部门
 * @param displayStatus - 显示状态（1：显示、0：不显示，默认为1）
 * @param disposeId - 处置主表ID
 * @param disposeIdList - 
 * @param disposeItemsId - 处置明细ID
 * @param disposeMode - 资产处置方式（1：清理、2：盘亏）
 * @param strBDateEnd - 本年结束使用日期 字符串类型
 * @param strBDateStart - 本年开始使用日期 字符串类型
 * @param strDateEnd - 结束使用日期 字符串类型
 * @param strDateStart - 开始使用日期 字符串类型
 * @param supplierId - 供应商ID
 * @param supplierName - 供应商名称
 * @param usedDateEnd - 结束使用日期 或者 处置日期
 * @param usedDateStart - 开始使用日期 或者 处置日期
 * @param usedStatus - 使用状态（1：正常、2：清理）
 */
export const getDispostalListSummaryUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/dispostal/total'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['addMethod'] = parameters['addMethod'];
  queryParameters['assetCode'] = parameters['assetCode'];
  queryParameters['assetId'] = parameters['assetId'];
  queryParameters['assetIdList'] = parameters['assetIdList'];
  queryParameters['assetItemsId'] = parameters['assetItemsId'];
  queryParameters['assetName'] = parameters['assetName'];
  queryParameters['assetStatusList'] = parameters['assetStatusList'];
  queryParameters['assetTypeId'] = parameters['assetTypeId'];
  queryParameters['assetTypeIdList'] = parameters['assetTypeIdList'];
  queryParameters['department'] = parameters['department'];
  queryParameters['displayStatus'] = parameters['displayStatus'];
  queryParameters['disposeId'] = parameters['disposeId'];
  queryParameters['disposeIdList'] = parameters['disposeIdList'];
  queryParameters['disposeItemsId'] = parameters['disposeItemsId'];
  queryParameters['disposeMode'] = parameters['disposeMode'];
  queryParameters['strBDateEnd'] = parameters['strBDateEnd'];
  queryParameters['strBDateStart'] = parameters['strBDateStart'];
  queryParameters['strDateEnd'] = parameters['strDateEnd'];
  queryParameters['strDateStart'] = parameters['strDateStart'];
  queryParameters['supplierId'] = parameters['supplierId'];
  queryParameters['supplierName'] = parameters['supplierName'];
  queryParameters['usedDateEnd'] = parameters['usedDateEnd'];
  queryParameters['usedDateStart'] = parameters['usedDateStart'];
  queryParameters['usedStatus'] = parameters['usedStatus'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出资产总量信息 - exportDetailAccountUsingGET
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/export
 * @param addMethod - 增加方式
 * @param assetCode - 资产编号
 * @param assetId - 资产ID
 * @param assetIdList - 
 * @param assetItemsId - 资产明细ID
 * @param assetName - 资产名称
 * @param assetStatusList - 
 * @param assetTypeId - 资产类型ID
 * @param assetTypeIdList - 
 * @param department - 使用部门
 * @param displayStatus - 显示状态（1：显示、0：不显示，默认为1）
 * @param disposeId - 处置主表ID
 * @param disposeIdList - 
 * @param disposeItemsId - 处置明细ID
 * @param disposeMode - 资产处置方式（1：清理、2：盘亏）
 * @param strBDateEnd - 本年结束使用日期 字符串类型
 * @param strBDateStart - 本年开始使用日期 字符串类型
 * @param strDateEnd - 结束使用日期 字符串类型
 * @param strDateStart - 开始使用日期 字符串类型
 * @param supplierId - 供应商ID
 * @param supplierName - 供应商名称
 * @param usedDateEnd - 结束使用日期 或者 处置日期
 * @param usedDateStart - 开始使用日期 或者 处置日期
 * @param usedStatus - 使用状态（1：正常、2：清理）
 */
export const fixedAssetsCardExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['addMethod'] = parameters['addMethod'];
  queryParameters['assetCode'] = parameters['assetCode'];
  queryParameters['assetId'] = parameters['assetId'];
  queryParameters['assetIdList'] = parameters['assetIdList'];
  queryParameters['assetItemsId'] = parameters['assetItemsId'];
  queryParameters['assetName'] = parameters['assetName'];
  queryParameters['assetStatusList'] = parameters['assetStatusList'];
  queryParameters['assetTypeId'] = parameters['assetTypeId'];
  queryParameters['assetTypeIdList'] = parameters['assetTypeIdList'];
  queryParameters['department'] = parameters['department'];
  queryParameters['displayStatus'] = parameters['displayStatus'];
  queryParameters['disposeId'] = parameters['disposeId'];
  queryParameters['disposeIdList'] = parameters['disposeIdList'];
  queryParameters['disposeItemsId'] = parameters['disposeItemsId'];
  queryParameters['disposeMode'] = parameters['disposeMode'];
  queryParameters['strBDateEnd'] = parameters['strBDateEnd'];
  queryParameters['strBDateStart'] = parameters['strBDateStart'];
  queryParameters['strDateEnd'] = parameters['strDateEnd'];
  queryParameters['strDateStart'] = parameters['strDateStart'];
  queryParameters['supplierId'] = parameters['supplierId'];
  queryParameters['supplierName'] = parameters['supplierName'];
  queryParameters['usedDateEnd'] = parameters['usedDateEnd'];
  queryParameters['usedDateStart'] = parameters['usedDateStart'];
  queryParameters['usedStatus'] = parameters['usedStatus'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询资产卡片列表 - findFixedAssetsCardListUsingGET
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/list
 * @param addMethod - 增加方式
 * @param assetCode - 资产编号
 * @param assetId - 资产ID
 * @param assetIdList - 
 * @param assetItemsId - 资产明细ID
 * @param assetName - 资产名称
 * @param assetStatusList - 
 * @param assetTypeId - 资产类型ID
 * @param assetTypeIdList - 
 * @param data - 
 * @param department - 使用部门
 * @param displayStatus - 显示状态（1：显示、0：不显示，默认为1）
 * @param disposeId - 处置主表ID
 * @param disposeIdList - 
 * @param disposeItemsId - 处置明细ID
 * @param disposeMode - 资产处置方式（1：清理、2：盘亏）
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param strBDateEnd - 本年结束使用日期 字符串类型
 * @param strBDateStart - 本年开始使用日期 字符串类型
 * @param strDateEnd - 结束使用日期 字符串类型
 * @param strDateStart - 开始使用日期 字符串类型
 * @param supplierId - 供应商ID
 * @param supplierName - 供应商名称
 * @param take - 
 * @param usedDateEnd - 结束使用日期 或者 处置日期
 * @param usedDateStart - 开始使用日期 或者 处置日期
 * @param usedStatus - 使用状态（1：正常、2：清理）
 */
export const fixedAssetsCardList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['addMethod'] = parameters['addMethod'];
  queryParameters['assetCode'] = parameters['assetCode'];
  queryParameters['assetId'] = parameters['assetId'];
  queryParameters['assetIdList'] = parameters['assetIdList'];
  queryParameters['assetItemsId'] = parameters['assetItemsId'];
  queryParameters['assetName'] = parameters['assetName'];
  queryParameters['assetStatusList'] = parameters['assetStatusList'];
  queryParameters['assetTypeId'] = parameters['assetTypeId'];
  queryParameters['assetTypeIdList'] = parameters['assetTypeIdList'];
  queryParameters['data'] = parameters['data'];
  queryParameters['department'] = parameters['department'];
  queryParameters['displayStatus'] = parameters['displayStatus'];
  queryParameters['disposeId'] = parameters['disposeId'];
  queryParameters['disposeIdList'] = parameters['disposeIdList'];
  queryParameters['disposeItemsId'] = parameters['disposeItemsId'];
  queryParameters['disposeMode'] = parameters['disposeMode'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['strBDateEnd'] = parameters['strBDateEnd'];
  queryParameters['strBDateStart'] = parameters['strBDateStart'];
  queryParameters['strDateEnd'] = parameters['strDateEnd'];
  queryParameters['strDateStart'] = parameters['strDateStart'];
  queryParameters['supplierId'] = parameters['supplierId'];
  queryParameters['supplierName'] = parameters['supplierName'];
  queryParameters['take'] = parameters['take'];
  queryParameters['usedDateEnd'] = parameters['usedDateEnd'];
  queryParameters['usedDateStart'] = parameters['usedDateStart'];
  queryParameters['usedStatus'] = parameters['usedStatus'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获得资产处置信息 - getOnlyDispostalDetailListUsingGET
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/onlyDispostalDetail
 * @param addMethod - 增加方式
 * @param assetCode - 资产编号
 * @param assetId - 资产ID
 * @param assetIdList - 
 * @param assetItemsId - 资产明细ID
 * @param assetName - 资产名称
 * @param assetStatusList - 
 * @param assetTypeId - 资产类型ID
 * @param assetTypeIdList - 
 * @param data - 
 * @param department - 使用部门
 * @param displayStatus - 显示状态（1：显示、0：不显示，默认为1）
 * @param disposeId - 处置主表ID
 * @param disposeIdList - 
 * @param disposeItemsId - 处置明细ID
 * @param disposeMode - 资产处置方式（1：清理、2：盘亏）
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param strBDateEnd - 本年结束使用日期 字符串类型
 * @param strBDateStart - 本年开始使用日期 字符串类型
 * @param strDateEnd - 结束使用日期 字符串类型
 * @param strDateStart - 开始使用日期 字符串类型
 * @param supplierId - 供应商ID
 * @param supplierName - 供应商名称
 * @param take - 
 * @param usedDateEnd - 结束使用日期 或者 处置日期
 * @param usedDateStart - 开始使用日期 或者 处置日期
 * @param usedStatus - 使用状态（1：正常、2：清理）
 */
export const fixedAssetsCardOnlydispostaldetail = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/onlyDispostalDetail'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['addMethod'] = parameters['addMethod'];
  queryParameters['assetCode'] = parameters['assetCode'];
  queryParameters['assetId'] = parameters['assetId'];
  queryParameters['assetIdList'] = parameters['assetIdList'];
  queryParameters['assetItemsId'] = parameters['assetItemsId'];
  queryParameters['assetName'] = parameters['assetName'];
  queryParameters['assetStatusList'] = parameters['assetStatusList'];
  queryParameters['assetTypeId'] = parameters['assetTypeId'];
  queryParameters['assetTypeIdList'] = parameters['assetTypeIdList'];
  queryParameters['data'] = parameters['data'];
  queryParameters['department'] = parameters['department'];
  queryParameters['displayStatus'] = parameters['displayStatus'];
  queryParameters['disposeId'] = parameters['disposeId'];
  queryParameters['disposeIdList'] = parameters['disposeIdList'];
  queryParameters['disposeItemsId'] = parameters['disposeItemsId'];
  queryParameters['disposeMode'] = parameters['disposeMode'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['strBDateEnd'] = parameters['strBDateEnd'];
  queryParameters['strBDateStart'] = parameters['strBDateStart'];
  queryParameters['strDateEnd'] = parameters['strDateEnd'];
  queryParameters['strDateStart'] = parameters['strDateStart'];
  queryParameters['supplierId'] = parameters['supplierId'];
  queryParameters['supplierName'] = parameters['supplierName'];
  queryParameters['take'] = parameters['take'];
  queryParameters['usedDateEnd'] = parameters['usedDateEnd'];
  queryParameters['usedDateStart'] = parameters['usedDateStart'];
  queryParameters['usedStatus'] = parameters['usedStatus'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获得单类资产未付金额 - getAssetsCardByIdForPayUsingGET
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/payment/{assetId}
 * @param assetId - 单类资产信息ID
 */
export const fixedAssetsCardPayment = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/payment/{assetId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{assetId}', `${parameters['assetId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获得资产总量信息 - getFixedAssetsTotalListUsingGET
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/total
 * @param addMethod - 增加方式
 * @param assetCode - 资产编号
 * @param assetId - 资产ID
 * @param assetIdList - 
 * @param assetItemsId - 资产明细ID
 * @param assetName - 资产名称
 * @param assetStatusList - 
 * @param assetTypeId - 资产类型ID
 * @param assetTypeIdList - 
 * @param data - 
 * @param department - 使用部门
 * @param displayStatus - 显示状态（1：显示、0：不显示，默认为1）
 * @param disposeId - 处置主表ID
 * @param disposeIdList - 
 * @param disposeItemsId - 处置明细ID
 * @param disposeMode - 资产处置方式（1：清理、2：盘亏）
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param strBDateEnd - 本年结束使用日期 字符串类型
 * @param strBDateStart - 本年开始使用日期 字符串类型
 * @param strDateEnd - 结束使用日期 字符串类型
 * @param strDateStart - 开始使用日期 字符串类型
 * @param supplierId - 供应商ID
 * @param supplierName - 供应商名称
 * @param take - 
 * @param usedDateEnd - 结束使用日期 或者 处置日期
 * @param usedDateStart - 开始使用日期 或者 处置日期
 * @param usedStatus - 使用状态（1：正常、2：清理）
 */
export const fixedAssetsCardTotal = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/total'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['addMethod'] = parameters['addMethod'];
  queryParameters['assetCode'] = parameters['assetCode'];
  queryParameters['assetId'] = parameters['assetId'];
  queryParameters['assetIdList'] = parameters['assetIdList'];
  queryParameters['assetItemsId'] = parameters['assetItemsId'];
  queryParameters['assetName'] = parameters['assetName'];
  queryParameters['assetStatusList'] = parameters['assetStatusList'];
  queryParameters['assetTypeId'] = parameters['assetTypeId'];
  queryParameters['assetTypeIdList'] = parameters['assetTypeIdList'];
  queryParameters['data'] = parameters['data'];
  queryParameters['department'] = parameters['department'];
  queryParameters['displayStatus'] = parameters['displayStatus'];
  queryParameters['disposeId'] = parameters['disposeId'];
  queryParameters['disposeIdList'] = parameters['disposeIdList'];
  queryParameters['disposeItemsId'] = parameters['disposeItemsId'];
  queryParameters['disposeMode'] = parameters['disposeMode'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['strBDateEnd'] = parameters['strBDateEnd'];
  queryParameters['strBDateStart'] = parameters['strBDateStart'];
  queryParameters['strDateEnd'] = parameters['strDateEnd'];
  queryParameters['strDateStart'] = parameters['strDateStart'];
  queryParameters['supplierId'] = parameters['supplierId'];
  queryParameters['supplierName'] = parameters['supplierName'];
  queryParameters['take'] = parameters['take'];
  queryParameters['usedDateEnd'] = parameters['usedDateEnd'];
  queryParameters['usedDateStart'] = parameters['usedDateStart'];
  queryParameters['usedStatus'] = parameters['usedStatus'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 资产类别 启用/停用 - updateAssetsTypeStatusUsingPUT
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/type/{assetTypeId}/{status}
 * @param assetTypeId - 资产类别ID
 * @param status - status
 */
export const fixedAssetsCardType = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/type/{assetTypeId}/{status}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{assetTypeId}', `${parameters['assetTypeId']}`)
  path = path.replace('{status}', `${parameters['status']}`)
  if (parameters['status'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: status'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 添加资产类别(0:是不成功,非0:成功) - insertFixedAssetsTypeUsingPOST
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/typeAdd
 * @param po - po
 */
export const fixedAssetsCardTypeadd = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/typeAdd'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['po']
  if (parameters['po'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: po'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 删除资产类别 - deleteFixedAssetsTypeUsingDELETE
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/typeDelete
 * @param fixedAssetsTypeIds - 资产IDs
 */
export const fixedAssetsCardTypedelete = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/typeDelete'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['fixedAssetsTypeIds'] = parameters['fixedAssetsTypeIds'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 查询资产类别 - findFixedAssetsTypeListUsingGET
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/typeList
 * @param assetTypeCode - 资产类型编码
 * @param assetTypeName - 资产名称
 * @param categoryType - 资产类别（0：固定资产、1：无形资产）
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const fixedAssetsCardTypelist = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/typeList'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['assetTypeCode'] = parameters['assetTypeCode'];
  queryParameters['assetTypeName'] = parameters['assetTypeName'];
  queryParameters['categoryType'] = parameters['categoryType'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改资产类别(0:是不成功,非0:成功) - updateFixedAssetsTypeUsingPUT
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/typeUpdate
 * @param po - po
 */
export const fixedAssetsCardTypeupdate = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/typeUpdate'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['po']
  if (parameters['po'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: po'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 修改资产信息 - updateFixedAssetsCardUsingPUT
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/update
 * @param isCreateVoucher - true 生成凭证，false 不生成凭证
 * @param po - po
 * @param type - 0 保存草稿 1 确认新增
 */
export const fixedAssetsCardUpdate = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/update'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['isCreateVoucher']
  body = parameters['po']
  if (parameters['po'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: po'))
  }
  body = parameters['type']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 修改资产处置信息 - updateAssetDisposalUsingPUT
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/updateDisposal
 * @param po - po
 */
export const fixedAssetsCardUpdatedisposal = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/updateDisposal'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['po']
  if (parameters['po'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: po'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 修改付款信息 - updateAssetPaymentMergeVoUsingPUT
 * /rest/companyAccount/merge/fixedAssetsCard/v0.1/updatePaymentMerge
 * @param po - po
 */
export const fixedAssetsCardUpdatepaymentmerge = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/fixedAssetsCard/v0.1/updatePaymentMerge'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['po']
  if (parameters['po'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: po'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 字典名(keyName):customer_type,account_type,... - 
 * /rest/companyAccount/merge/gdconfigs/v0.1/{keyName}
 * @param keyName - 字典名
 */
export const findByKeyNameUsingGET_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/gdconfigs/v0.1/{keyName}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{keyName}', `${parameters['keyName']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 根据条件查找进项发票列表 - 
 * /rest/companyAccount/merge/invoice/v1.0
 * @param certifyPeriodFrom - 属期开始
 * @param certifyPeriodTo - 属期结束
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param invoiceCode - 发票代码
 * @param invoiceDateFrom - 开票开始日期
 * @param invoiceDateTo - 开票结束日期
 * @param invoiceNumber - 发票号码
 * @param page - 
 * @param pageSize - 
 * @param sellerName - 销方名称
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 * @param tickStatus - 勾选状态，1：勾选、0：未勾选
 * @param usedStatus - 使用状态，1：使用、0：未使用
 */
export const findInvoiceListUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/invoice/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['certifyPeriodFrom'] = parameters['certifyPeriodFrom'];
  queryParameters['certifyPeriodTo'] = parameters['certifyPeriodTo'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['invoiceCode'] = parameters['invoiceCode'];
  queryParameters['invoiceDateFrom'] = parameters['invoiceDateFrom'];
  queryParameters['invoiceDateTo'] = parameters['invoiceDateTo'];
  queryParameters['invoiceNumber'] = parameters['invoiceNumber'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['sellerName'] = parameters['sellerName'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  queryParameters['tickStatus'] = parameters['tickStatus'];
  queryParameters['usedStatus'] = parameters['usedStatus'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 新增备忘邮箱 - 
 * /rest/companyAccount/merge/memo/conf/email/v0.1
 * @param adMemoEmail - 备忘邮箱实体
 */
export const insertMemoEmailUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/memo/conf/email/v0.1'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['adMemoEmail']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查询备忘邮箱 - findMemoEmailUsingGET
 * /rest/companyAccount/merge/memo/conf/email/v0.1/list
 */
export const emailList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/memo/conf/email/v0.1/list'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 删除备忘邮箱 - 
 * /rest/companyAccount/merge/memo/conf/email/v0.1/{memoEmailId}
 * @param memoEmailId - 备忘邮箱编号
 */
export const deleteMemoEmailUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/memo/conf/email/v0.1/{memoEmailId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{memoEmailId}', `${parameters['memoEmailId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 新增备忘定时任务 - 
 * /rest/companyAccount/merge/memo/conf/task/v0.1
 * @param adMemoTask - 备忘定时实体
 */
export const insertMemoTaskUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/memo/conf/task/v0.1'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['adMemoTask']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 根据任务代码查询备忘定时任务 - findMemoTaskUsingGET
 * /rest/companyAccount/merge/memo/conf/task/v0.1/list
 * @param taskCode - 任务代码（1：导出设置，2：核算设置）
 */
export const taskList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/memo/conf/task/v0.1/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['taskCode'] = parameters['taskCode'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 更新备忘定时任务 - 
 * /rest/companyAccount/merge/memo/conf/task/v0.1/{taskId}
 * @param adMemoTask - 备忘定时实体
 * @param taskId - 任务ID
 */
export const updateMemoTaskUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/memo/conf/task/v0.1/{taskId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['adMemoTask']
  path = path.replace('{taskId}', `${parameters['taskId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 备忘核算 - memoAccountsUsingPUT
 * /rest/companyAccount/merge/memo/memo/v0.1/accounts
 * @param atMemo - 备忘ID集合
 */
export const memoAccounts = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/memo/memo/v0.1/accounts'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['atMemo']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回关联核算数据 - getAccountsUsingGET
 * /rest/companyAccount/merge/memo/memo/v0.1/accounts/{originalBill}
 * @param originalBill - 原始的销售单单号
 */
export const getAccountsMemoList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/memo/memo/v0.1/accounts/{originalBill}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{originalBill}', `${parameters['originalBill']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出备忘 - exportMemoUsingGET
 * /rest/companyAccount/merge/memo/memo/v0.1/export
 */
export const memoExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/memo/memo/v0.1/export'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导入备忘 - importMemoUsingPOST
 * /rest/companyAccount/merge/memo/memo/v0.1/import
 * @param requestHeadersETag - 
 * @param requestHeadersAcceptCharset0Registered - 
 * @param requestHeadersAccept0CharSetRegistered - 
 * @param requestHeadersAccept0CharsetRegistered - 
 * @param requestHeadersAccept0Concrete - 
 * @param requestHeadersAccept0QualityValue - 
 * @param requestHeadersAccept0Subtype - 
 * @param requestHeadersAccept0Type - 
 * @param requestHeadersAccept0WildcardSubtype - 
 * @param requestHeadersAccept0WildcardType - 
 * @param requestHeadersAccessControlAllowCredentials - 
 * @param requestHeadersAccessControlAllowHeaders - 
 * @param requestHeadersAccessControlAllowMethods - 
 * @param requestHeadersAccessControlAllowOrigin - 
 * @param requestHeadersAccessControlExposeHeaders - 
 * @param requestHeadersAccessControlMaxAge - 
 * @param requestHeadersAccessControlRequestHeaders - 
 * @param requestHeadersAccessControlRequestMethod - 
 * @param requestHeadersAllow - 
 * @param requestHeadersCacheControl - 
 * @param requestHeadersConnection - 
 * @param requestHeadersContentLength - 
 * @param requestHeadersContentTypeCharSetRegistered - 
 * @param requestHeadersContentTypeCharsetRegistered - 
 * @param requestHeadersContentTypeConcrete - 
 * @param requestHeadersContentTypeQualityValue - 
 * @param requestHeadersContentTypeSubtype - 
 * @param requestHeadersContentTypeType - 
 * @param requestHeadersContentTypeWildcardSubtype - 
 * @param requestHeadersContentTypeWildcardType - 
 * @param requestHeadersDate - 
 * @param requestHeadersExpires - 
 * @param requestHeadersIfMatch - 
 * @param requestHeadersIfModifiedSince - 
 * @param requestHeadersIfNoneMatch - 
 * @param requestHeadersIfUnmodifiedSince - 
 * @param requestHeadersLastModified - 
 * @param requestHeadersLocationAbsolute - 
 * @param requestHeadersLocationAuthority - 
 * @param requestHeadersLocationFragment - 
 * @param requestHeadersLocationHost - 
 * @param requestHeadersLocationOpaque - 
 * @param requestHeadersLocationPath - 
 * @param requestHeadersLocationPort - 
 * @param requestHeadersLocationQuery - 
 * @param requestHeadersLocationRawAuthority - 
 * @param requestHeadersLocationRawFragment - 
 * @param requestHeadersLocationRawPath - 
 * @param requestHeadersLocationRawQuery - 
 * @param requestHeadersLocationRawSchemeSpecificPart - 
 * @param requestHeadersLocationRawUserInfo - 
 * @param requestHeadersLocationScheme - 
 * @param requestHeadersLocationSchemeSpecificPart - 
 * @param requestHeadersLocationUserInfo - 
 * @param requestHeadersOrigin - 
 * @param requestHeadersPragma - 
 * @param requestHeadersUpgrade - 
 * @param requestHeadersVary - 
 * @param requestMethod - 
 */
export const memoImport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/memo/memo/v0.1/import'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['requestHeaders.ETag'] = parameters['requestHeadersETag'];
  queryParameters['requestHeaders.acceptCharset[0].registered'] = parameters['requestHeadersAcceptCharset0Registered'];
  queryParameters['requestHeaders.accept[0].charSet.registered'] = parameters['requestHeadersAccept0CharSetRegistered'];
  queryParameters['requestHeaders.accept[0].charset.registered'] = parameters['requestHeadersAccept0CharsetRegistered'];
  queryParameters['requestHeaders.accept[0].concrete'] = parameters['requestHeadersAccept0Concrete'];
  queryParameters['requestHeaders.accept[0].qualityValue'] = parameters['requestHeadersAccept0QualityValue'];
  queryParameters['requestHeaders.accept[0].subtype'] = parameters['requestHeadersAccept0Subtype'];
  queryParameters['requestHeaders.accept[0].type'] = parameters['requestHeadersAccept0Type'];
  queryParameters['requestHeaders.accept[0].wildcardSubtype'] = parameters['requestHeadersAccept0WildcardSubtype'];
  queryParameters['requestHeaders.accept[0].wildcardType'] = parameters['requestHeadersAccept0WildcardType'];
  queryParameters['requestHeaders.accessControlAllowCredentials'] = parameters['requestHeadersAccessControlAllowCredentials'];
  queryParameters['requestHeaders.accessControlAllowHeaders'] = parameters['requestHeadersAccessControlAllowHeaders'];
  queryParameters['requestHeaders.accessControlAllowMethods'] = parameters['requestHeadersAccessControlAllowMethods'];
  queryParameters['requestHeaders.accessControlAllowOrigin'] = parameters['requestHeadersAccessControlAllowOrigin'];
  queryParameters['requestHeaders.accessControlExposeHeaders'] = parameters['requestHeadersAccessControlExposeHeaders'];
  queryParameters['requestHeaders.accessControlMaxAge'] = parameters['requestHeadersAccessControlMaxAge'];
  queryParameters['requestHeaders.accessControlRequestHeaders'] = parameters['requestHeadersAccessControlRequestHeaders'];
  queryParameters['requestHeaders.accessControlRequestMethod'] = parameters['requestHeadersAccessControlRequestMethod'];
  queryParameters['requestHeaders.allow'] = parameters['requestHeadersAllow'];
  queryParameters['requestHeaders.cacheControl'] = parameters['requestHeadersCacheControl'];
  queryParameters['requestHeaders.connection'] = parameters['requestHeadersConnection'];
  queryParameters['requestHeaders.contentLength'] = parameters['requestHeadersContentLength'];
  queryParameters['requestHeaders.contentType.charSet.registered'] = parameters['requestHeadersContentTypeCharSetRegistered'];
  queryParameters['requestHeaders.contentType.charset.registered'] = parameters['requestHeadersContentTypeCharsetRegistered'];
  queryParameters['requestHeaders.contentType.concrete'] = parameters['requestHeadersContentTypeConcrete'];
  queryParameters['requestHeaders.contentType.qualityValue'] = parameters['requestHeadersContentTypeQualityValue'];
  queryParameters['requestHeaders.contentType.subtype'] = parameters['requestHeadersContentTypeSubtype'];
  queryParameters['requestHeaders.contentType.type'] = parameters['requestHeadersContentTypeType'];
  queryParameters['requestHeaders.contentType.wildcardSubtype'] = parameters['requestHeadersContentTypeWildcardSubtype'];
  queryParameters['requestHeaders.contentType.wildcardType'] = parameters['requestHeadersContentTypeWildcardType'];
  queryParameters['requestHeaders.date'] = parameters['requestHeadersDate'];
  queryParameters['requestHeaders.expires'] = parameters['requestHeadersExpires'];
  queryParameters['requestHeaders.ifMatch'] = parameters['requestHeadersIfMatch'];
  queryParameters['requestHeaders.ifModifiedSince'] = parameters['requestHeadersIfModifiedSince'];
  queryParameters['requestHeaders.ifNoneMatch'] = parameters['requestHeadersIfNoneMatch'];
  queryParameters['requestHeaders.ifUnmodifiedSince'] = parameters['requestHeadersIfUnmodifiedSince'];
  queryParameters['requestHeaders.lastModified'] = parameters['requestHeadersLastModified'];
  queryParameters['requestHeaders.location.absolute'] = parameters['requestHeadersLocationAbsolute'];
  queryParameters['requestHeaders.location.authority'] = parameters['requestHeadersLocationAuthority'];
  queryParameters['requestHeaders.location.fragment'] = parameters['requestHeadersLocationFragment'];
  queryParameters['requestHeaders.location.host'] = parameters['requestHeadersLocationHost'];
  queryParameters['requestHeaders.location.opaque'] = parameters['requestHeadersLocationOpaque'];
  queryParameters['requestHeaders.location.path'] = parameters['requestHeadersLocationPath'];
  queryParameters['requestHeaders.location.port'] = parameters['requestHeadersLocationPort'];
  queryParameters['requestHeaders.location.query'] = parameters['requestHeadersLocationQuery'];
  queryParameters['requestHeaders.location.rawAuthority'] = parameters['requestHeadersLocationRawAuthority'];
  queryParameters['requestHeaders.location.rawFragment'] = parameters['requestHeadersLocationRawFragment'];
  queryParameters['requestHeaders.location.rawPath'] = parameters['requestHeadersLocationRawPath'];
  queryParameters['requestHeaders.location.rawQuery'] = parameters['requestHeadersLocationRawQuery'];
  queryParameters['requestHeaders.location.rawSchemeSpecificPart'] = parameters['requestHeadersLocationRawSchemeSpecificPart'];
  queryParameters['requestHeaders.location.rawUserInfo'] = parameters['requestHeadersLocationRawUserInfo'];
  queryParameters['requestHeaders.location.scheme'] = parameters['requestHeadersLocationScheme'];
  queryParameters['requestHeaders.location.schemeSpecificPart'] = parameters['requestHeadersLocationSchemeSpecificPart'];
  queryParameters['requestHeaders.location.userInfo'] = parameters['requestHeadersLocationUserInfo'];
  queryParameters['requestHeaders.origin'] = parameters['requestHeadersOrigin'];
  queryParameters['requestHeaders.pragma'] = parameters['requestHeadersPragma'];
  queryParameters['requestHeaders.upgrade'] = parameters['requestHeadersUpgrade'];
  queryParameters['requestHeaders.vary'] = parameters['requestHeadersVary'];
  queryParameters['requestMethod'] = parameters['requestMethod'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回全部备忘 - findAllUsingGET
 * /rest/companyAccount/merge/memo/memo/v0.1/list
 * @param accountsType - 账目类型
 * @param billStatus - 单据状态
 * @param businessBill - 单据编号,可根据销售单号、对账单号进行模糊查询
 * @param businessTimeEnd - 单据结束时间
 * @param businessTimeStart - 单据开始时间
 * @param customerName - 客户名称
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param supplierName - 供应商名称
 * @param take - 
 */
export const memoList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/memo/memo/v0.1/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountsType'] = parameters['accountsType'];
  queryParameters['billStatus'] = parameters['billStatus'];
  queryParameters['businessBill'] = parameters['businessBill'];
  queryParameters['businessTimeEnd'] = parameters['businessTimeEnd'];
  queryParameters['businessTimeStart'] = parameters['businessTimeStart'];
  queryParameters['customerName'] = parameters['customerName'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['supplierName'] = parameters['supplierName'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 定时核算 - taskMemoAccountUsingPUT_1
 * /rest/companyAccount/merge/memo/memo/v0.1/task/account
 */
export const memoTask = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/memo/memo/v0.1/task/account'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 添加收付款单 - insertPaymentUsingPOST_1
 * /rest/companyAccount/merge/payment/v1.0/add
 * @param vo - vo
 */
export const paymentAdd = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/payment/v1.0/add'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['vo']
  if (parameters['vo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 添加其它收付款单 - insertPaymentOtherUsingPOST
 * /rest/companyAccount/merge/payment/v1.0/addOther
 * @param vo - vo
 */
export const paymentAddother = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/payment/v1.0/addOther'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['vo']
  if (parameters['vo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 删除收付款单 - deletePaymentUsingDELETE
 * /rest/companyAccount/merge/payment/v1.0/delete
 * @param paymentIds - 收付款ID 批量删除有逗号隔开
 */
export const paymentDelete = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/payment/v1.0/delete'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['paymentIds'] = parameters['paymentIds'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 查询收付款列表 - findPaymentListUsingGET
 * /rest/companyAccount/merge/payment/v1.0/list
 * @param accountPeriod - 会计属期，YYYYMM
 * @param accountsType - 账目类型（字典）
 * @param billStatus - 单据状态（1：草稿，2：已对账）
 * @param businessTime - 业务时间
 * @param businessTimeFrom - 单据开始日期，YYYY-MM-DD
 * @param businessTimeTo - 单据结束日期，YYYY-MM-DD
 * @param categoryCode - 查询类型码：1.生产成本，2.管理费用，3.销售费用
 * @param categoryName - 查询类型名称
 * @param customerId - 销方-客户ID
 * @param customerName - 销方-客户名称
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param originalBill - 业务单编号
 * @param originalBillId - 业务单ID
 * @param page - 
 * @param pageSize - 
 * @param paymentBill - 收/付款单号
 * @param paymentId - 收/付款单ID, 更新时需要提供, 其他情况不需要
 * @param paymentType - 收/付款类型（字典 1：付款单、2：收款单）
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param supplierId - 销方-供应商ID
 * @param supplierName - 销方名称
 * @param take - 
 */
export const paymentList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/payment/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['accountsType'] = parameters['accountsType'];
  queryParameters['billStatus'] = parameters['billStatus'];
  queryParameters['businessTime'] = parameters['businessTime'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['categoryCode'] = parameters['categoryCode'];
  queryParameters['categoryName'] = parameters['categoryName'];
  queryParameters['customerId'] = parameters['customerId'];
  queryParameters['customerName'] = parameters['customerName'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['originalBill'] = parameters['originalBill'];
  queryParameters['originalBillId'] = parameters['originalBillId'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['paymentBill'] = parameters['paymentBill'];
  queryParameters['paymentId'] = parameters['paymentId'];
  queryParameters['paymentType'] = parameters['paymentType'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['supplierId'] = parameters['supplierId'];
  queryParameters['supplierName'] = parameters['supplierName'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询各种单据列表 - findOrdersListUsingGET
 * /rest/companyAccount/merge/payment/v1.0/orders
 * @param accountPeriod - 会计属期，YYYYMM
 * @param accountsType - 账目类型（字典）
 * @param billStatus - 单据状态（1：草稿，2：已对账）
 * @param businessTime - 业务时间
 * @param businessTimeFrom - 单据开始日期，YYYY-MM-DD
 * @param businessTimeTo - 单据结束日期，YYYY-MM-DD
 * @param categoryCode - 查询类型码：1.生产成本，2.管理费用，3.销售费用
 * @param categoryName - 查询类型名称
 * @param customerId - 销方-客户ID
 * @param customerName - 销方-客户名称
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param originalBill - 业务单编号
 * @param originalBillId - 业务单ID
 * @param page - 
 * @param pageSize - 
 * @param paymentBill - 收/付款单号
 * @param paymentId - 收/付款单ID, 更新时需要提供, 其他情况不需要
 * @param paymentType - 收/付款类型（字典 1：付款单、2：收款单）
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param supplierId - 销方-供应商ID
 * @param supplierName - 销方名称
 * @param take - 
 */
export const paymentOrders = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/payment/v1.0/orders'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['accountsType'] = parameters['accountsType'];
  queryParameters['billStatus'] = parameters['billStatus'];
  queryParameters['businessTime'] = parameters['businessTime'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['categoryCode'] = parameters['categoryCode'];
  queryParameters['categoryName'] = parameters['categoryName'];
  queryParameters['customerId'] = parameters['customerId'];
  queryParameters['customerName'] = parameters['customerName'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['originalBill'] = parameters['originalBill'];
  queryParameters['originalBillId'] = parameters['originalBillId'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['paymentBill'] = parameters['paymentBill'];
  queryParameters['paymentId'] = parameters['paymentId'];
  queryParameters['paymentType'] = parameters['paymentType'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['supplierId'] = parameters['supplierId'];
  queryParameters['supplierName'] = parameters['supplierName'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 删除其它收付款单 - deletePaymentOtherUsingDELETE
 * /rest/companyAccount/merge/payment/v1.0/otherDelete
 * @param paymentOtherIds - 收付款ID 批量删除有逗号隔开
 */
export const paymentOtherdelete = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/payment/v1.0/otherDelete'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['paymentOtherIds'] = parameters['paymentOtherIds'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 查询其它收付款列表 - findPaymentOtherListUsingGET
 * /rest/companyAccount/merge/payment/v1.0/otherList
 * @param accountPeriod - 会计属期，YYYYMM
 * @param accountsType - 账户类型
 * @param billStatus - 单据状态（1：草稿，2：已对账）
 * @param businessTimeFrom - 单据开始日期，YYYY-MM-DD
 * @param businessTimeTo - 单据结束日期，YYYY-MM-DD
 * @param categoryCode - 查询类型码：1.生产成本，2.管理费用，3.销售费用
 * @param categoryName - 查询类型名称
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param paymentCategory - 收/付款类别
 * @param paymentItem - 收/付款事项
 * @param paymentOtherBill - 业务单编码
 * @param paymentOtherId - 收/付款单ID, 更新时需要提供, 其他情况不需要
 * @param paymentType - 收/付款类型（字典 1：付款单、2：收款单）
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const paymentOtherlist = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/payment/v1.0/otherList'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['accountsType'] = parameters['accountsType'];
  queryParameters['billStatus'] = parameters['billStatus'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['categoryCode'] = parameters['categoryCode'];
  queryParameters['categoryName'] = parameters['categoryName'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['paymentCategory'] = parameters['paymentCategory'];
  queryParameters['paymentItem'] = parameters['paymentItem'];
  queryParameters['paymentOtherBill'] = parameters['paymentOtherBill'];
  queryParameters['paymentOtherId'] = parameters['paymentOtherId'];
  queryParameters['paymentType'] = parameters['paymentType'];
  if (parameters['paymentType'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: paymentType'))
  }
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获得单个收付款单信息 - getPaymentByIdUsingGET
 * /rest/companyAccount/merge/payment/v1.0/payment/{paymentId}
 * @param paymentId - 收付款ID
 */
export const paymentPayment = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/payment/v1.0/payment/{paymentId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{paymentId}', `${parameters['paymentId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获得单个其它收付款单信息 - getPaymentOtherByIdUsingGET
 * /rest/companyAccount/merge/payment/v1.0/paymentOther/{paymentOtherId}
 * @param paymentOtherId - 收付款ID
 */
export const paymentPaymentother = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/payment/v1.0/paymentOther/{paymentOtherId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{paymentOtherId}', `${parameters['paymentOtherId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改收付款单 - updatePaymentUsingPUT
 * /rest/companyAccount/merge/payment/v1.0/update
 * @param vo - vo
 */
export const paymentUpdate = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/payment/v1.0/update'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['vo']
  if (parameters['vo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 修改其它收付款单 - updatePaymentOtherUsingPUT
 * /rest/companyAccount/merge/payment/v1.0/updateOther
 * @param vo - vo
 */
export const paymentUpdateother = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/payment/v1.0/updateOther'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['vo']
  if (parameters['vo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 查询采购对账单列表，返回结果里没有商品列表和发票列表 - 
 * /rest/companyAccount/merge/purchase/v1.0
 * @param accountsType - 账目类型
 * @param businessTimeFrom - 单据开始日期
 * @param businessTimeTo - 单据结束日期
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param purchaseBill - 采购单号
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param supplierName - 供应商名称
 * @param take - 
 */
export const findPurchaseAccountsListUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/purchase/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountsType'] = parameters['accountsType'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['purchaseBill'] = parameters['purchaseBill'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['supplierName'] = parameters['supplierName'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 添加采购对账单 - 
 * /rest/companyAccount/merge/purchase/v1.0
 * @param vo - vo
 */
export const insertPurchaseAccountsUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/purchase/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['vo']
  if (parameters['vo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 修改采购对账单 - 
 * /rest/companyAccount/merge/purchase/v1.0
 * @param vo - vo
 */
export const updatePurchaseAccountsUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/purchase/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['vo']
  if (parameters['vo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 查询采购对账单详细信息 - 
 * /rest/companyAccount/merge/purchase/v1.0/{purchaseAccountsId}
 * @param purchaseAccountsId - 对账单ID
 */
export const getPurchaseAccountsDetailUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/purchase/v1.0/{purchaseAccountsId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{purchaseAccountsId}', `${parameters['purchaseAccountsId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 删除采购对账单 - 
 * /rest/companyAccount/merge/purchase/v1.0/{purchaseAccountsId}
 * @param purchaseAccountsId - 对账单ID
 */
export const deletePurchaseAccountsUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/purchase/v1.0/{purchaseAccountsId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{purchaseAccountsId}', `${parameters['purchaseAccountsId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 批量修改工资 - 
 * /rest/companyAccount/merge/salary/batch/v1.0
 * @param batchOpt - batchOpt
 */
export const batchUpdateSalaryUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/salary/batch/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['batchOpt']
  if (parameters['batchOpt'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: batchOpt'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 批量删除工资 - 
 * /rest/companyAccount/merge/salary/batch/v1.0
 * @param accountPeriod - 会计属期
 * @param departmentName - 部门名称
 * @param sequenceIds - 序号ID列表
 */
export const batchDeleteSalaryUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/salary/batch/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['departmentName'] = parameters['departmentName'];
  queryParameters['sequenceIds'] = parameters['sequenceIds'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 复制上月工资 - 
 * /rest/companyAccount/merge/salary/copy/{fromPeriod}/{toPeriod}/v1.0
 * @param fromPeriod - 开始属期
 * @param toPeriod - 结束属期
 */
export const insertCopySalaryUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/salary/copy/{fromPeriod}/{toPeriod}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{fromPeriod}', `${parameters['fromPeriod']}`)
  path = path.replace('{toPeriod}', `${parameters['toPeriod']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 导出工资 - 
 * /rest/companyAccount/merge/salary/excel/{accountPeriod}/v1.0
 * @param accountPeriod - 会计属期，YYYYMM
 * @param accountPeriod - 会计属期
 * @param isHavDepartment - 是否有部门
 * @param maxSequence - 公司所在分区的最大流水号
 * @param partitionId - 公司所在分区
 */
export const exportSalaryUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/salary/excel/{accountPeriod}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  queryParameters['isHavDepartment'] = parameters['isHavDepartment'];
  queryParameters['maxSequence'] = parameters['maxSequence'];
  queryParameters['partitionId'] = parameters['partitionId'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导入工资 - 
 * /rest/companyAccount/merge/salary/import/{accountPeriod}/v1.0
 * @param accountPeriod - 会计属期
 * @param requestHeadersETag - 
 * @param requestHeadersAcceptCharset0Registered - 
 * @param requestHeadersAccept0CharSetRegistered - 
 * @param requestHeadersAccept0CharsetRegistered - 
 * @param requestHeadersAccept0Concrete - 
 * @param requestHeadersAccept0QualityValue - 
 * @param requestHeadersAccept0Subtype - 
 * @param requestHeadersAccept0Type - 
 * @param requestHeadersAccept0WildcardSubtype - 
 * @param requestHeadersAccept0WildcardType - 
 * @param requestHeadersAccessControlAllowCredentials - 
 * @param requestHeadersAccessControlAllowHeaders - 
 * @param requestHeadersAccessControlAllowMethods - 
 * @param requestHeadersAccessControlAllowOrigin - 
 * @param requestHeadersAccessControlExposeHeaders - 
 * @param requestHeadersAccessControlMaxAge - 
 * @param requestHeadersAccessControlRequestHeaders - 
 * @param requestHeadersAccessControlRequestMethod - 
 * @param requestHeadersAllow - 
 * @param requestHeadersCacheControl - 
 * @param requestHeadersConnection - 
 * @param requestHeadersContentLength - 
 * @param requestHeadersContentTypeCharSetRegistered - 
 * @param requestHeadersContentTypeCharsetRegistered - 
 * @param requestHeadersContentTypeConcrete - 
 * @param requestHeadersContentTypeQualityValue - 
 * @param requestHeadersContentTypeSubtype - 
 * @param requestHeadersContentTypeType - 
 * @param requestHeadersContentTypeWildcardSubtype - 
 * @param requestHeadersContentTypeWildcardType - 
 * @param requestHeadersDate - 
 * @param requestHeadersExpires - 
 * @param requestHeadersIfMatch - 
 * @param requestHeadersIfModifiedSince - 
 * @param requestHeadersIfNoneMatch - 
 * @param requestHeadersIfUnmodifiedSince - 
 * @param requestHeadersLastModified - 
 * @param requestHeadersLocationAbsolute - 
 * @param requestHeadersLocationAuthority - 
 * @param requestHeadersLocationFragment - 
 * @param requestHeadersLocationHost - 
 * @param requestHeadersLocationOpaque - 
 * @param requestHeadersLocationPath - 
 * @param requestHeadersLocationPort - 
 * @param requestHeadersLocationQuery - 
 * @param requestHeadersLocationRawAuthority - 
 * @param requestHeadersLocationRawFragment - 
 * @param requestHeadersLocationRawPath - 
 * @param requestHeadersLocationRawQuery - 
 * @param requestHeadersLocationRawSchemeSpecificPart - 
 * @param requestHeadersLocationRawUserInfo - 
 * @param requestHeadersLocationScheme - 
 * @param requestHeadersLocationSchemeSpecificPart - 
 * @param requestHeadersLocationUserInfo - 
 * @param requestHeadersOrigin - 
 * @param requestHeadersPragma - 
 * @param requestHeadersUpgrade - 
 * @param requestHeadersVary - 
 * @param requestMethod - 
 */
export const importSalaryUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/salary/import/{accountPeriod}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  queryParameters['requestHeaders.ETag'] = parameters['requestHeadersETag'];
  queryParameters['requestHeaders.acceptCharset[0].registered'] = parameters['requestHeadersAcceptCharset0Registered'];
  queryParameters['requestHeaders.accept[0].charSet.registered'] = parameters['requestHeadersAccept0CharSetRegistered'];
  queryParameters['requestHeaders.accept[0].charset.registered'] = parameters['requestHeadersAccept0CharsetRegistered'];
  queryParameters['requestHeaders.accept[0].concrete'] = parameters['requestHeadersAccept0Concrete'];
  queryParameters['requestHeaders.accept[0].qualityValue'] = parameters['requestHeadersAccept0QualityValue'];
  queryParameters['requestHeaders.accept[0].subtype'] = parameters['requestHeadersAccept0Subtype'];
  queryParameters['requestHeaders.accept[0].type'] = parameters['requestHeadersAccept0Type'];
  queryParameters['requestHeaders.accept[0].wildcardSubtype'] = parameters['requestHeadersAccept0WildcardSubtype'];
  queryParameters['requestHeaders.accept[0].wildcardType'] = parameters['requestHeadersAccept0WildcardType'];
  queryParameters['requestHeaders.accessControlAllowCredentials'] = parameters['requestHeadersAccessControlAllowCredentials'];
  queryParameters['requestHeaders.accessControlAllowHeaders'] = parameters['requestHeadersAccessControlAllowHeaders'];
  queryParameters['requestHeaders.accessControlAllowMethods'] = parameters['requestHeadersAccessControlAllowMethods'];
  queryParameters['requestHeaders.accessControlAllowOrigin'] = parameters['requestHeadersAccessControlAllowOrigin'];
  queryParameters['requestHeaders.accessControlExposeHeaders'] = parameters['requestHeadersAccessControlExposeHeaders'];
  queryParameters['requestHeaders.accessControlMaxAge'] = parameters['requestHeadersAccessControlMaxAge'];
  queryParameters['requestHeaders.accessControlRequestHeaders'] = parameters['requestHeadersAccessControlRequestHeaders'];
  queryParameters['requestHeaders.accessControlRequestMethod'] = parameters['requestHeadersAccessControlRequestMethod'];
  queryParameters['requestHeaders.allow'] = parameters['requestHeadersAllow'];
  queryParameters['requestHeaders.cacheControl'] = parameters['requestHeadersCacheControl'];
  queryParameters['requestHeaders.connection'] = parameters['requestHeadersConnection'];
  queryParameters['requestHeaders.contentLength'] = parameters['requestHeadersContentLength'];
  queryParameters['requestHeaders.contentType.charSet.registered'] = parameters['requestHeadersContentTypeCharSetRegistered'];
  queryParameters['requestHeaders.contentType.charset.registered'] = parameters['requestHeadersContentTypeCharsetRegistered'];
  queryParameters['requestHeaders.contentType.concrete'] = parameters['requestHeadersContentTypeConcrete'];
  queryParameters['requestHeaders.contentType.qualityValue'] = parameters['requestHeadersContentTypeQualityValue'];
  queryParameters['requestHeaders.contentType.subtype'] = parameters['requestHeadersContentTypeSubtype'];
  queryParameters['requestHeaders.contentType.type'] = parameters['requestHeadersContentTypeType'];
  queryParameters['requestHeaders.contentType.wildcardSubtype'] = parameters['requestHeadersContentTypeWildcardSubtype'];
  queryParameters['requestHeaders.contentType.wildcardType'] = parameters['requestHeadersContentTypeWildcardType'];
  queryParameters['requestHeaders.date'] = parameters['requestHeadersDate'];
  queryParameters['requestHeaders.expires'] = parameters['requestHeadersExpires'];
  queryParameters['requestHeaders.ifMatch'] = parameters['requestHeadersIfMatch'];
  queryParameters['requestHeaders.ifModifiedSince'] = parameters['requestHeadersIfModifiedSince'];
  queryParameters['requestHeaders.ifNoneMatch'] = parameters['requestHeadersIfNoneMatch'];
  queryParameters['requestHeaders.ifUnmodifiedSince'] = parameters['requestHeadersIfUnmodifiedSince'];
  queryParameters['requestHeaders.lastModified'] = parameters['requestHeadersLastModified'];
  queryParameters['requestHeaders.location.absolute'] = parameters['requestHeadersLocationAbsolute'];
  queryParameters['requestHeaders.location.authority'] = parameters['requestHeadersLocationAuthority'];
  queryParameters['requestHeaders.location.fragment'] = parameters['requestHeadersLocationFragment'];
  queryParameters['requestHeaders.location.host'] = parameters['requestHeadersLocationHost'];
  queryParameters['requestHeaders.location.opaque'] = parameters['requestHeadersLocationOpaque'];
  queryParameters['requestHeaders.location.path'] = parameters['requestHeadersLocationPath'];
  queryParameters['requestHeaders.location.port'] = parameters['requestHeadersLocationPort'];
  queryParameters['requestHeaders.location.query'] = parameters['requestHeadersLocationQuery'];
  queryParameters['requestHeaders.location.rawAuthority'] = parameters['requestHeadersLocationRawAuthority'];
  queryParameters['requestHeaders.location.rawFragment'] = parameters['requestHeadersLocationRawFragment'];
  queryParameters['requestHeaders.location.rawPath'] = parameters['requestHeadersLocationRawPath'];
  queryParameters['requestHeaders.location.rawQuery'] = parameters['requestHeadersLocationRawQuery'];
  queryParameters['requestHeaders.location.rawSchemeSpecificPart'] = parameters['requestHeadersLocationRawSchemeSpecificPart'];
  queryParameters['requestHeaders.location.rawUserInfo'] = parameters['requestHeadersLocationRawUserInfo'];
  queryParameters['requestHeaders.location.scheme'] = parameters['requestHeadersLocationScheme'];
  queryParameters['requestHeaders.location.schemeSpecificPart'] = parameters['requestHeadersLocationSchemeSpecificPart'];
  queryParameters['requestHeaders.location.userInfo'] = parameters['requestHeadersLocationUserInfo'];
  queryParameters['requestHeaders.origin'] = parameters['requestHeadersOrigin'];
  queryParameters['requestHeaders.pragma'] = parameters['requestHeadersPragma'];
  queryParameters['requestHeaders.upgrade'] = parameters['requestHeadersUpgrade'];
  queryParameters['requestHeaders.vary'] = parameters['requestHeadersVary'];
  queryParameters['requestMethod'] = parameters['requestMethod'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 生成计提凭证 - 
 * /rest/companyAccount/merge/salary/memo/{accountPeriod}/v1.0
 * @param accountPeriod - 会计属期
 */
export const createMemoAndVoucherUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/salary/memo/{accountPeriod}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 获取个税数据 - 
 * /rest/companyAccount/merge/salary/personal/tax/{accountPeriod}/v1.0
 * @param accountPeriod - 会计属期
 */
export const importPersonalTaxDataUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/salary/personal/tax/{accountPeriod}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查询工资合计 - 
 * /rest/companyAccount/merge/salary/total/{accountPeriod}/v1.0
 * @param accountPeriod - 会计属期
 * @param accountPeriod - 会计属期，YYYYMM
 * @param isHavDepartment - 是否有部门
 * @param maxSequence - 公司所在分区的最大流水号
 * @param partitionId - 公司所在分区
 */
export const findSalarySummaryUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/salary/total/{accountPeriod}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['isHavDepartment'] = parameters['isHavDepartment'];
  queryParameters['maxSequence'] = parameters['maxSequence'];
  queryParameters['partitionId'] = parameters['partitionId'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询工资列表 - 
 * /rest/companyAccount/merge/salary/v1.0
 * @param accountPeriod - 会计属期，YYYYMM
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param isHavDepartment - 是否有部门
 * @param maxSequence - 公司所在分区的最大流水号
 * @param page - 
 * @param pageSize - 
 * @param partitionId - 公司所在分区
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const findSalaryListUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/salary/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['isHavDepartment'] = parameters['isHavDepartment'];
  queryParameters['maxSequence'] = parameters['maxSequence'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['partitionId'] = parameters['partitionId'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 添加工资 - 
 * /rest/companyAccount/merge/salary/v1.0
 * @param salary - salary
 */
export const insertSalaryUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/salary/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['salary']
  if (parameters['salary'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: salary'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 修改工资 - 
 * /rest/companyAccount/merge/salary/v1.0
 * @param salary - salary
 */
export const updateSalaryUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/salary/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['salary']
  if (parameters['salary'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: salary'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 删除工资 - 
 * /rest/companyAccount/merge/salary/{sequenceId}/v1.0
 * @param sequenceId - 工资ID
 */
export const deleteSalaryUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/salary/{sequenceId}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{sequenceId}', `${parameters['sequenceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 销售对账单对账 - 
 * /rest/companyAccount/merge/sale/v0.1
 * @param saleAccountsId - 销售对账单id
 */
export const saleCheckingUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/sale/v0.1'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['saleAccountsId']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 销售对账单收款 - saleCheckingGatheringUsingPOST
 * /rest/companyAccount/merge/sale/v0.1/gathering/{saleBill}
 * @param saleBill - 销售单业务单号
 * @param saleChecking - 销售对账单实体
 */
export const saleGathering = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/sale/v0.1/gathering/{saleBill}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{saleBill}', `${parameters['saleBill']}`)
  body = parameters['saleChecking']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回全部销售单 - findAllSaleCheckingUsingGET
 * /rest/companyAccount/merge/sale/v0.1/list
 * @param accountsType - 账目类型
 * @param bankAccount - 结算账户
 * @param billStatus - 单据状态
 * @param businessBill - 单据编号,可根据销售单号、对账单号进行模糊查询
 * @param businessTimeEnd - 单据结束时间
 * @param businessTimeStart - 单据开始时间
 * @param customerId - 客户Id
 * @param customerName - 客户名称
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param settlementMode - 结算类型
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const saleList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/sale/v0.1/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountsType'] = parameters['accountsType'];
  queryParameters['bankAccount'] = parameters['bankAccount'];
  queryParameters['billStatus'] = parameters['billStatus'];
  queryParameters['businessBill'] = parameters['businessBill'];
  queryParameters['businessTimeEnd'] = parameters['businessTimeEnd'];
  queryParameters['businessTimeStart'] = parameters['businessTimeStart'];
  queryParameters['customerId'] = parameters['customerId'];
  queryParameters['customerName'] = parameters['customerName'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['settlementMode'] = parameters['settlementMode'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 销售对账单付款 - saleCheckingPaymentUsingPOST
 * /rest/companyAccount/merge/sale/v0.1/payment/{saleBill}
 * @param saleBill - 销售单业务单号
 * @param saleChecking - 销售对账单实体
 */
export const salePayment = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/sale/v0.1/payment/{saleBill}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{saleBill}', `${parameters['saleBill']}`)
  body = parameters['saleChecking']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 找出某商品的库存数量 - 
 * /rest/companyAccount/merge/stock/v1.0/{goodsIdsStr}
 * @param goodsIdsStr - 商品ID（如有多个，用逗号分隔）
 */
export const findAvailableStockUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/stock/v1.0/{goodsIdsStr}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{goodsIdsStr}', `${parameters['goodsIdsStr']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询往来科目调整 - 
 * /rest/companyAccount/merge/voucher/202/{accountPeriod}/v1.0
 * @param accountPeriod - accountPeriod
 */
export const check202VoucherUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/202/{accountPeriod}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 往来科目调整 - 
 * /rest/companyAccount/merge/voucher/202/{accountsType}/{accountPeriod}/v1.0
 * @param accountPeriod - accountPeriod
 * @param accountsType - accountsType
 */
export const get202VoucherObjUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/202/{accountsType}/{accountPeriod}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  path = path.replace('{accountsType}', `${parameters['accountsType']}`)
  if (parameters['accountsType'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountsType'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 根据账目类型查看凭证 - 
 * /rest/companyAccount/merge/voucher/accountsType/{accountsType}/{accountPeriod}/v1.0
 * @param accountPeriod - 会计属期(YYYYMM)
 * @param accountsType - 账目类型
 */
export const findVouchersByAccountsTypeUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/accountsType/{accountsType}/{accountPeriod}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  path = path.replace('{accountsType}', `${parameters['accountsType']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 批量删除凭证 - 
 * /rest/companyAccount/merge/voucher/batch/v1.0
 * @param action - U: 更新, D: 删除
 * @param actionTime - 
 * @param billCode - 凭证号/备忘号
 * @param businessTimeFrom - 单据开始日期，YYYY-MM-DD
 * @param businessTimeTo - 单据结束日期，YYYY-MM-DD
 * @param categoryCode - 查询类型码：1.生产成本，2.管理费用，3.销售费用
 * @param categoryName - 查询类型名称
 * @param codeConditions0LikeCode - 
 * @param codeConditions0NotInCodes - 
 * @param codeNumber - 凭证号的号码
 * @param creationMode - 创建方式（1：自动、2：手动）
 * @param currencyCode - 币别编码
 * @param desc - 凭证号是否降序排序（true 降序,false 升序（默认升序））
 * @param export - 
 * @param fromNumber - 
 * @param memoBill - 备忘号
 * @param memoId - 备忘单ID
 * @param pageQueryList0AccountPeriod - 会计属期
 * @param pageQueryList0BalanceAmount - 余额
 * @param pageQueryList0BalanceAmountO - 余额(原币)
 * @param pageQueryList0CreateUserName - 财务
 * @param pageQueryList0CreationMode - 创建方式（1：自动、2：手动）
 * @param pageQueryList0CreditAmount - 贷方金额
 * @param pageQueryList0CreditAmountO - 贷方金额(原币)
 * @param pageQueryList0CurrencyCode - 币别编码
 * @param pageQueryList0DebitAmount - 借方金额
 * @param pageQueryList0DebitAmountO - 借方金额(原币)
 * @param pageQueryList0LendingDirection - 借贷方向（0：平、1：借、2：贷）
 * @param pageQueryList0LendingDirectionStr - 借贷方向（平、借、贷）
 * @param pageQueryList0OppositeSubject - 对方科目
 * @param pageQueryList0SubjectCode - 科目代码
 * @param pageQueryList0SubjectName - 科目
 * @param pageQueryList0VoucherAbstract - 摘要
 * @param pageQueryList0VoucherCode - 凭证号
 * @param pageQueryList0VoucherId - 凭证ID
 * @param pageQueryList0VoucherTime - 单据日期
 * @param periodFrom - 
 * @param periodTo - 
 * @param printInvoice - 是否打印关联发票
 * @param qty - 数量
 * @param subjectAmount - 金额
 * @param subjectBeginCode - 科目编码起
 * @param subjectCode - 科目代码
 * @param subjectCodes - 科目代码，如果有多个用逗号隔开
 * @param subjectEndCode - 科目编码止
 * @param toNumber - 
 * @param voucherAbstract - 凭证摘要
 * @param voucherBeginCode - 凭证号区间查询起始凭证号
 * @param voucherCode - 凭证号
 * @param voucherEndCode - 凭证号区间查询结束凭证号
 * @param voucherId - 凭证单ID
 * @param voucherType - 凭证类型（“main”：主凭证、“sub”：子凭证
 */
export const deleteVoucherByBatchUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/batch/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['action'] = parameters['action'];
  queryParameters['actionTime'] = parameters['actionTime'];
  queryParameters['billCode'] = parameters['billCode'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['categoryCode'] = parameters['categoryCode'];
  queryParameters['categoryName'] = parameters['categoryName'];
  queryParameters['codeConditions[0].likeCode'] = parameters['codeConditions0LikeCode'];
  queryParameters['codeConditions[0].notInCodes'] = parameters['codeConditions0NotInCodes'];
  queryParameters['codeNumber'] = parameters['codeNumber'];
  queryParameters['creationMode'] = parameters['creationMode'];
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['desc'] = parameters['desc'];
  queryParameters['export'] = parameters['export'];
  queryParameters['fromNumber'] = parameters['fromNumber'];
  queryParameters['memoBill'] = parameters['memoBill'];
  queryParameters['memoId'] = parameters['memoId'];
  queryParameters['pageQueryList[0].accountPeriod'] = parameters['pageQueryList0AccountPeriod'];
  queryParameters['pageQueryList[0].balanceAmount'] = parameters['pageQueryList0BalanceAmount'];
  queryParameters['pageQueryList[0].balanceAmountO'] = parameters['pageQueryList0BalanceAmountO'];
  queryParameters['pageQueryList[0].createUserName'] = parameters['pageQueryList0CreateUserName'];
  queryParameters['pageQueryList[0].creationMode'] = parameters['pageQueryList0CreationMode'];
  queryParameters['pageQueryList[0].creditAmount'] = parameters['pageQueryList0CreditAmount'];
  queryParameters['pageQueryList[0].creditAmountO'] = parameters['pageQueryList0CreditAmountO'];
  queryParameters['pageQueryList[0].currencyCode'] = parameters['pageQueryList0CurrencyCode'];
  queryParameters['pageQueryList[0].debitAmount'] = parameters['pageQueryList0DebitAmount'];
  queryParameters['pageQueryList[0].debitAmountO'] = parameters['pageQueryList0DebitAmountO'];
  queryParameters['pageQueryList[0].lendingDirection'] = parameters['pageQueryList0LendingDirection'];
  queryParameters['pageQueryList[0].lendingDirectionStr'] = parameters['pageQueryList0LendingDirectionStr'];
  queryParameters['pageQueryList[0].oppositeSubject'] = parameters['pageQueryList0OppositeSubject'];
  queryParameters['pageQueryList[0].subjectCode'] = parameters['pageQueryList0SubjectCode'];
  queryParameters['pageQueryList[0].subjectName'] = parameters['pageQueryList0SubjectName'];
  queryParameters['pageQueryList[0].voucherAbstract'] = parameters['pageQueryList0VoucherAbstract'];
  queryParameters['pageQueryList[0].voucherCode'] = parameters['pageQueryList0VoucherCode'];
  queryParameters['pageQueryList[0].voucherId'] = parameters['pageQueryList0VoucherId'];
  queryParameters['pageQueryList[0].voucherTime'] = parameters['pageQueryList0VoucherTime'];
  queryParameters['periodFrom'] = parameters['periodFrom'];
  queryParameters['periodTo'] = parameters['periodTo'];
  queryParameters['printInvoice'] = parameters['printInvoice'];
  queryParameters['qty'] = parameters['qty'];
  queryParameters['subjectAmount'] = parameters['subjectAmount'];
  queryParameters['subjectBeginCode'] = parameters['subjectBeginCode'];
  queryParameters['subjectCode'] = parameters['subjectCode'];
  queryParameters['subjectCodes'] = parameters['subjectCodes'];
  queryParameters['subjectEndCode'] = parameters['subjectEndCode'];
  queryParameters['toNumber'] = parameters['toNumber'];
  queryParameters['voucherAbstract'] = parameters['voucherAbstract'];
  queryParameters['voucherBeginCode'] = parameters['voucherBeginCode'];
  queryParameters['voucherCode'] = parameters['voucherCode'];
  queryParameters['voucherEndCode'] = parameters['voucherEndCode'];
  queryParameters['voucherId'] = parameters['voucherId'];
  queryParameters['voucherType'] = parameters['voucherType'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 获取最新凭证号 - 
 * /rest/companyAccount/merge/voucher/basic/manual/{voucherTime}/v1.0
 * @param voucherTime - voucherTime
 */
export const getLatestVoucherCodeUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/basic/manual/{voucherTime}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{voucherTime}', `${parameters['voucherTime']}`)
  if (parameters['voucherTime'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: voucherTime'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 复制上月凭证 - 
 * /rest/companyAccount/merge/voucher/copy/v1.0/{check}
 * @param check - 是否需要检查本月是否存在凭证，第一次调用必须检查，并且如果存在凭证，则提示是否继续复制
 * @param criteria - criteria
 */
export const insertCopyVouchersUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/copy/v1.0/{check}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{check}', `${parameters['check']}`)
  body = parameters['criteria']
  if (parameters['criteria'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: criteria'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 自定义拆分凭证 - 
 * /rest/companyAccount/merge/voucher/custom/split/{voucherId}/v1.0
 * @param subVoucherList - subVoucherList
 * @param voucherId - 母凭证ID
 */
export const customSplitVoucherUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/custom/split/{voucherId}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['subVoucherList']
  if (parameters['subVoucherList'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: subVoucherList'))
  }
  path = path.replace('{voucherId}', `${parameters['voucherId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查询某月凭证是否断号 - 
 * /rest/companyAccount/merge/voucher/hasSplit/{accountPeriod}/v1.0
 * @param accountPeriod - 会计属期
 */
export const isVoucherCodeSplitUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/hasSplit/{accountPeriod}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 在某凭证之后插入一个凭证 - 
 * /rest/companyAccount/merge/voucher/jump/{voucherCode}/v1.0
 * @param voucher - voucher
 * @param voucherCode - 凭证号
 */
export const insertJumpVoucherUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/jump/{voucherCode}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['voucher']
  if (parameters['voucher'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: voucher'))
  }
  path = path.replace('{voucherCode}', `${parameters['voucherCode']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 获取当月最新凭证 - 
 * /rest/companyAccount/merge/voucher/latest/{accountPeriod}/v1.0
 * @param accountPeriod - 会计属期
 */
export const getLatestVoucherDetailOfMonthUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/latest/{accountPeriod}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询凭证列表 - 
 * /rest/companyAccount/merge/voucher/list/v1.0
 * @param action - U: 更新, D: 删除
 * @param actionTime - 
 * @param billCode - 凭证号/备忘号
 * @param businessTimeFrom - 单据开始日期，YYYY-MM-DD
 * @param businessTimeTo - 单据结束日期，YYYY-MM-DD
 * @param categoryCode - 查询类型码：1.生产成本，2.管理费用，3.销售费用
 * @param categoryName - 查询类型名称
 * @param codeConditions0LikeCode - 
 * @param codeConditions0NotInCodes - 
 * @param codeNumber - 凭证号的号码
 * @param creationMode - 创建方式（1：自动、2：手动）
 * @param currencyCode - 币别编码
 * @param data - 
 * @param desc - 凭证号是否降序排序（true 降序,false 升序（默认升序））
 * @param export - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param fromNumber - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param memoBill - 备忘号
 * @param memoId - 备忘单ID
 * @param page - 
 * @param pageQueryList0AccountPeriod - 会计属期
 * @param pageQueryList0BalanceAmount - 余额
 * @param pageQueryList0BalanceAmountO - 余额(原币)
 * @param pageQueryList0CreateUserName - 财务
 * @param pageQueryList0CreationMode - 创建方式（1：自动、2：手动）
 * @param pageQueryList0CreditAmount - 贷方金额
 * @param pageQueryList0CreditAmountO - 贷方金额(原币)
 * @param pageQueryList0CurrencyCode - 币别编码
 * @param pageQueryList0DebitAmount - 借方金额
 * @param pageQueryList0DebitAmountO - 借方金额(原币)
 * @param pageQueryList0LendingDirection - 借贷方向（0：平、1：借、2：贷）
 * @param pageQueryList0LendingDirectionStr - 借贷方向（平、借、贷）
 * @param pageQueryList0OppositeSubject - 对方科目
 * @param pageQueryList0SubjectCode - 科目代码
 * @param pageQueryList0SubjectName - 科目
 * @param pageQueryList0VoucherAbstract - 摘要
 * @param pageQueryList0VoucherCode - 凭证号
 * @param pageQueryList0VoucherId - 凭证ID
 * @param pageQueryList0VoucherTime - 单据日期
 * @param pageSize - 
 * @param periodFrom - 
 * @param periodTo - 
 * @param printInvoice - 是否打印关联发票
 * @param qty - 数量
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param subjectAmount - 金额
 * @param subjectBeginCode - 科目编码起
 * @param subjectCode - 科目代码
 * @param subjectCodes - 科目代码，如果有多个用逗号隔开
 * @param subjectEndCode - 科目编码止
 * @param take - 
 * @param toNumber - 
 * @param voucherAbstract - 凭证摘要
 * @param voucherBeginCode - 凭证号区间查询起始凭证号
 * @param voucherCode - 凭证号
 * @param voucherEndCode - 凭证号区间查询结束凭证号
 * @param voucherId - 凭证单ID
 * @param voucherType - 凭证类型（“main”：主凭证、“sub”：子凭证
 */
export const findVouchersByPageUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/list/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['action'] = parameters['action'];
  queryParameters['actionTime'] = parameters['actionTime'];
  queryParameters['billCode'] = parameters['billCode'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['categoryCode'] = parameters['categoryCode'];
  queryParameters['categoryName'] = parameters['categoryName'];
  queryParameters['codeConditions[0].likeCode'] = parameters['codeConditions0LikeCode'];
  queryParameters['codeConditions[0].notInCodes'] = parameters['codeConditions0NotInCodes'];
  queryParameters['codeNumber'] = parameters['codeNumber'];
  queryParameters['creationMode'] = parameters['creationMode'];
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['data'] = parameters['data'];
  queryParameters['desc'] = parameters['desc'];
  queryParameters['export'] = parameters['export'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['fromNumber'] = parameters['fromNumber'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['memoBill'] = parameters['memoBill'];
  queryParameters['memoId'] = parameters['memoId'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageQueryList[0].accountPeriod'] = parameters['pageQueryList0AccountPeriod'];
  queryParameters['pageQueryList[0].balanceAmount'] = parameters['pageQueryList0BalanceAmount'];
  queryParameters['pageQueryList[0].balanceAmountO'] = parameters['pageQueryList0BalanceAmountO'];
  queryParameters['pageQueryList[0].createUserName'] = parameters['pageQueryList0CreateUserName'];
  queryParameters['pageQueryList[0].creationMode'] = parameters['pageQueryList0CreationMode'];
  queryParameters['pageQueryList[0].creditAmount'] = parameters['pageQueryList0CreditAmount'];
  queryParameters['pageQueryList[0].creditAmountO'] = parameters['pageQueryList0CreditAmountO'];
  queryParameters['pageQueryList[0].currencyCode'] = parameters['pageQueryList0CurrencyCode'];
  queryParameters['pageQueryList[0].debitAmount'] = parameters['pageQueryList0DebitAmount'];
  queryParameters['pageQueryList[0].debitAmountO'] = parameters['pageQueryList0DebitAmountO'];
  queryParameters['pageQueryList[0].lendingDirection'] = parameters['pageQueryList0LendingDirection'];
  queryParameters['pageQueryList[0].lendingDirectionStr'] = parameters['pageQueryList0LendingDirectionStr'];
  queryParameters['pageQueryList[0].oppositeSubject'] = parameters['pageQueryList0OppositeSubject'];
  queryParameters['pageQueryList[0].subjectCode'] = parameters['pageQueryList0SubjectCode'];
  queryParameters['pageQueryList[0].subjectName'] = parameters['pageQueryList0SubjectName'];
  queryParameters['pageQueryList[0].voucherAbstract'] = parameters['pageQueryList0VoucherAbstract'];
  queryParameters['pageQueryList[0].voucherCode'] = parameters['pageQueryList0VoucherCode'];
  queryParameters['pageQueryList[0].voucherId'] = parameters['pageQueryList0VoucherId'];
  queryParameters['pageQueryList[0].voucherTime'] = parameters['pageQueryList0VoucherTime'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['periodFrom'] = parameters['periodFrom'];
  queryParameters['periodTo'] = parameters['periodTo'];
  queryParameters['printInvoice'] = parameters['printInvoice'];
  queryParameters['qty'] = parameters['qty'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['subjectAmount'] = parameters['subjectAmount'];
  queryParameters['subjectBeginCode'] = parameters['subjectBeginCode'];
  queryParameters['subjectCode'] = parameters['subjectCode'];
  queryParameters['subjectCodes'] = parameters['subjectCodes'];
  queryParameters['subjectEndCode'] = parameters['subjectEndCode'];
  queryParameters['take'] = parameters['take'];
  queryParameters['toNumber'] = parameters['toNumber'];
  queryParameters['voucherAbstract'] = parameters['voucherAbstract'];
  queryParameters['voucherBeginCode'] = parameters['voucherBeginCode'];
  queryParameters['voucherCode'] = parameters['voucherCode'];
  queryParameters['voucherEndCode'] = parameters['voucherEndCode'];
  queryParameters['voucherId'] = parameters['voucherId'];
  queryParameters['voucherType'] = parameters['voucherType'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 移动凭证 - 
 * /rest/companyAccount/merge/voucher/move/{accountPeriod}/{fromCode}/{toCode}/v1.0
 * @param accountPeriod - 会计属期
 * @param fromCode - 移动自
 * @param toCode - 移动到
 */
export const moveVoucherUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/move/{accountPeriod}/{fromCode}/{toCode}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  path = path.replace('{fromCode}', `${parameters['fromCode']}`)
  path = path.replace('{toCode}', `${parameters['toCode']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 批量打印凭证 - printVouchersUsingGET
 * /rest/companyAccount/merge/voucher/pdf/{templateType}/batch/v1.0/export
 * @param printParam - printParam
 * @param templateType - 模板类型
 */
export const batchExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/pdf/{templateType}/batch/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['printParam'] = parameters['printParam'];
  if (parameters['printParam'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: printParam'))
  }
  path = path.replace('{templateType}', `${parameters['templateType']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 批量打印凭证(缓存参数) - beforePrintVouchersUsingPOST
 * /rest/companyAccount/merge/voucher/pdf/{templateType}/batch/v1.0/export
 * @param criteria - criteria
 * @param templateType - 模板类型
 */
export const beforePrintVouchers = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/pdf/{templateType}/batch/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['criteria']
  if (parameters['criteria'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: criteria'))
  }
  path = path.replace('{templateType}', `${parameters['templateType']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 打印某月所有凭证 - 
 * /rest/companyAccount/merge/voucher/pdf/{templateType}/v1.0/export
 * @param action - U: 更新, D: 删除
 * @param actionTime - 
 * @param billCode - 凭证号/备忘号
 * @param businessTimeFrom - 单据开始日期，YYYY-MM-DD
 * @param businessTimeTo - 单据结束日期，YYYY-MM-DD
 * @param categoryCode - 查询类型码：1.生产成本，2.管理费用，3.销售费用
 * @param categoryName - 查询类型名称
 * @param codeConditions0LikeCode - 
 * @param codeConditions0NotInCodes - 
 * @param codeNumber - 凭证号的号码
 * @param creationMode - 创建方式（1：自动、2：手动）
 * @param currencyCode - 币别编码
 * @param desc - 凭证号是否降序排序（true 降序,false 升序（默认升序））
 * @param export - 
 * @param fromNumber - 
 * @param memoBill - 备忘号
 * @param memoId - 备忘单ID
 * @param pageQueryList0AccountPeriod - 会计属期
 * @param pageQueryList0BalanceAmount - 余额
 * @param pageQueryList0BalanceAmountO - 余额(原币)
 * @param pageQueryList0CreateUserName - 财务
 * @param pageQueryList0CreationMode - 创建方式（1：自动、2：手动）
 * @param pageQueryList0CreditAmount - 贷方金额
 * @param pageQueryList0CreditAmountO - 贷方金额(原币)
 * @param pageQueryList0CurrencyCode - 币别编码
 * @param pageQueryList0DebitAmount - 借方金额
 * @param pageQueryList0DebitAmountO - 借方金额(原币)
 * @param pageQueryList0LendingDirection - 借贷方向（0：平、1：借、2：贷）
 * @param pageQueryList0LendingDirectionStr - 借贷方向（平、借、贷）
 * @param pageQueryList0OppositeSubject - 对方科目
 * @param pageQueryList0SubjectCode - 科目代码
 * @param pageQueryList0SubjectName - 科目
 * @param pageQueryList0VoucherAbstract - 摘要
 * @param pageQueryList0VoucherCode - 凭证号
 * @param pageQueryList0VoucherId - 凭证ID
 * @param pageQueryList0VoucherTime - 单据日期
 * @param periodFrom - 
 * @param periodTo - 
 * @param printInvoice - 是否打印关联发票
 * @param qty - 数量
 * @param subjectAmount - 金额
 * @param subjectBeginCode - 科目编码起
 * @param subjectCode - 科目代码
 * @param subjectCodes - 科目代码，如果有多个用逗号隔开
 * @param subjectEndCode - 科目编码止
 * @param templateType - 模板类型
 * @param toNumber - 
 * @param voucherAbstract - 凭证摘要
 * @param voucherBeginCode - 凭证号区间查询起始凭证号
 * @param voucherCode - 凭证号
 * @param voucherEndCode - 凭证号区间查询结束凭证号
 * @param voucherId - 凭证单ID
 * @param voucherType - 凭证类型（“main”：主凭证、“sub”：子凭证
 */
export const printVoucherByMonthUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/pdf/{templateType}/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['action'] = parameters['action'];
  queryParameters['actionTime'] = parameters['actionTime'];
  queryParameters['billCode'] = parameters['billCode'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['categoryCode'] = parameters['categoryCode'];
  queryParameters['categoryName'] = parameters['categoryName'];
  queryParameters['codeConditions[0].likeCode'] = parameters['codeConditions0LikeCode'];
  queryParameters['codeConditions[0].notInCodes'] = parameters['codeConditions0NotInCodes'];
  queryParameters['codeNumber'] = parameters['codeNumber'];
  queryParameters['creationMode'] = parameters['creationMode'];
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['desc'] = parameters['desc'];
  queryParameters['export'] = parameters['export'];
  queryParameters['fromNumber'] = parameters['fromNumber'];
  queryParameters['memoBill'] = parameters['memoBill'];
  queryParameters['memoId'] = parameters['memoId'];
  queryParameters['pageQueryList[0].accountPeriod'] = parameters['pageQueryList0AccountPeriod'];
  queryParameters['pageQueryList[0].balanceAmount'] = parameters['pageQueryList0BalanceAmount'];
  queryParameters['pageQueryList[0].balanceAmountO'] = parameters['pageQueryList0BalanceAmountO'];
  queryParameters['pageQueryList[0].createUserName'] = parameters['pageQueryList0CreateUserName'];
  queryParameters['pageQueryList[0].creationMode'] = parameters['pageQueryList0CreationMode'];
  queryParameters['pageQueryList[0].creditAmount'] = parameters['pageQueryList0CreditAmount'];
  queryParameters['pageQueryList[0].creditAmountO'] = parameters['pageQueryList0CreditAmountO'];
  queryParameters['pageQueryList[0].currencyCode'] = parameters['pageQueryList0CurrencyCode'];
  queryParameters['pageQueryList[0].debitAmount'] = parameters['pageQueryList0DebitAmount'];
  queryParameters['pageQueryList[0].debitAmountO'] = parameters['pageQueryList0DebitAmountO'];
  queryParameters['pageQueryList[0].lendingDirection'] = parameters['pageQueryList0LendingDirection'];
  queryParameters['pageQueryList[0].lendingDirectionStr'] = parameters['pageQueryList0LendingDirectionStr'];
  queryParameters['pageQueryList[0].oppositeSubject'] = parameters['pageQueryList0OppositeSubject'];
  queryParameters['pageQueryList[0].subjectCode'] = parameters['pageQueryList0SubjectCode'];
  queryParameters['pageQueryList[0].subjectName'] = parameters['pageQueryList0SubjectName'];
  queryParameters['pageQueryList[0].voucherAbstract'] = parameters['pageQueryList0VoucherAbstract'];
  queryParameters['pageQueryList[0].voucherCode'] = parameters['pageQueryList0VoucherCode'];
  queryParameters['pageQueryList[0].voucherId'] = parameters['pageQueryList0VoucherId'];
  queryParameters['pageQueryList[0].voucherTime'] = parameters['pageQueryList0VoucherTime'];
  queryParameters['periodFrom'] = parameters['periodFrom'];
  queryParameters['periodTo'] = parameters['periodTo'];
  queryParameters['printInvoice'] = parameters['printInvoice'];
  queryParameters['qty'] = parameters['qty'];
  queryParameters['subjectAmount'] = parameters['subjectAmount'];
  queryParameters['subjectBeginCode'] = parameters['subjectBeginCode'];
  queryParameters['subjectCode'] = parameters['subjectCode'];
  queryParameters['subjectCodes'] = parameters['subjectCodes'];
  queryParameters['subjectEndCode'] = parameters['subjectEndCode'];
  path = path.replace('{templateType}', `${parameters['templateType']}`)
  queryParameters['toNumber'] = parameters['toNumber'];
  queryParameters['voucherAbstract'] = parameters['voucherAbstract'];
  queryParameters['voucherBeginCode'] = parameters['voucherBeginCode'];
  queryParameters['voucherCode'] = parameters['voucherCode'];
  queryParameters['voucherEndCode'] = parameters['voucherEndCode'];
  queryParameters['voucherId'] = parameters['voucherId'];
  queryParameters['voucherType'] = parameters['voucherType'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 添加手工凭证 - 
 * /rest/companyAccount/merge/voucher/v1.0
 * @param vo - vo
 */
export const insertVoucherUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['vo']
  if (parameters['vo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 导出凭证 - exportExcelVoucherUsingGET
 * /rest/companyAccount/merge/voucher/v1.0/export
 * @param action - U: 更新, D: 删除
 * @param actionTime - 
 * @param billCode - 凭证号/备忘号
 * @param businessTimeFrom - 单据开始日期，YYYY-MM-DD
 * @param businessTimeTo - 单据结束日期，YYYY-MM-DD
 * @param categoryCode - 查询类型码：1.生产成本，2.管理费用，3.销售费用
 * @param categoryName - 查询类型名称
 * @param codeConditions0LikeCode - 
 * @param codeConditions0NotInCodes - 
 * @param codeNumber - 凭证号的号码
 * @param creationMode - 创建方式（1：自动、2：手动）
 * @param currencyCode - 币别编码
 * @param desc - 凭证号是否降序排序（true 降序,false 升序（默认升序））
 * @param export - 
 * @param fromNumber - 
 * @param memoBill - 备忘号
 * @param memoId - 备忘单ID
 * @param pageQueryList0AccountPeriod - 会计属期
 * @param pageQueryList0BalanceAmount - 余额
 * @param pageQueryList0BalanceAmountO - 余额(原币)
 * @param pageQueryList0CreateUserName - 财务
 * @param pageQueryList0CreationMode - 创建方式（1：自动、2：手动）
 * @param pageQueryList0CreditAmount - 贷方金额
 * @param pageQueryList0CreditAmountO - 贷方金额(原币)
 * @param pageQueryList0CurrencyCode - 币别编码
 * @param pageQueryList0DebitAmount - 借方金额
 * @param pageQueryList0DebitAmountO - 借方金额(原币)
 * @param pageQueryList0LendingDirection - 借贷方向（0：平、1：借、2：贷）
 * @param pageQueryList0LendingDirectionStr - 借贷方向（平、借、贷）
 * @param pageQueryList0OppositeSubject - 对方科目
 * @param pageQueryList0SubjectCode - 科目代码
 * @param pageQueryList0SubjectName - 科目
 * @param pageQueryList0VoucherAbstract - 摘要
 * @param pageQueryList0VoucherCode - 凭证号
 * @param pageQueryList0VoucherId - 凭证ID
 * @param pageQueryList0VoucherTime - 单据日期
 * @param periodFrom - 
 * @param periodTo - 
 * @param printInvoice - 是否打印关联发票
 * @param qty - 数量
 * @param subjectAmount - 金额
 * @param subjectBeginCode - 科目编码起
 * @param subjectCode - 科目代码
 * @param subjectCodes - 科目代码，如果有多个用逗号隔开
 * @param subjectEndCode - 科目编码止
 * @param toNumber - 
 * @param voucherAbstract - 凭证摘要
 * @param voucherBeginCode - 凭证号区间查询起始凭证号
 * @param voucherCode - 凭证号
 * @param voucherEndCode - 凭证号区间查询结束凭证号
 * @param voucherId - 凭证单ID
 * @param voucherType - 凭证类型（“main”：主凭证、“sub”：子凭证
 */
export const voucherExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['action'] = parameters['action'];
  queryParameters['actionTime'] = parameters['actionTime'];
  queryParameters['billCode'] = parameters['billCode'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['categoryCode'] = parameters['categoryCode'];
  queryParameters['categoryName'] = parameters['categoryName'];
  queryParameters['codeConditions[0].likeCode'] = parameters['codeConditions0LikeCode'];
  queryParameters['codeConditions[0].notInCodes'] = parameters['codeConditions0NotInCodes'];
  queryParameters['codeNumber'] = parameters['codeNumber'];
  queryParameters['creationMode'] = parameters['creationMode'];
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['desc'] = parameters['desc'];
  queryParameters['export'] = parameters['export'];
  queryParameters['fromNumber'] = parameters['fromNumber'];
  queryParameters['memoBill'] = parameters['memoBill'];
  queryParameters['memoId'] = parameters['memoId'];
  queryParameters['pageQueryList[0].accountPeriod'] = parameters['pageQueryList0AccountPeriod'];
  queryParameters['pageQueryList[0].balanceAmount'] = parameters['pageQueryList0BalanceAmount'];
  queryParameters['pageQueryList[0].balanceAmountO'] = parameters['pageQueryList0BalanceAmountO'];
  queryParameters['pageQueryList[0].createUserName'] = parameters['pageQueryList0CreateUserName'];
  queryParameters['pageQueryList[0].creationMode'] = parameters['pageQueryList0CreationMode'];
  queryParameters['pageQueryList[0].creditAmount'] = parameters['pageQueryList0CreditAmount'];
  queryParameters['pageQueryList[0].creditAmountO'] = parameters['pageQueryList0CreditAmountO'];
  queryParameters['pageQueryList[0].currencyCode'] = parameters['pageQueryList0CurrencyCode'];
  queryParameters['pageQueryList[0].debitAmount'] = parameters['pageQueryList0DebitAmount'];
  queryParameters['pageQueryList[0].debitAmountO'] = parameters['pageQueryList0DebitAmountO'];
  queryParameters['pageQueryList[0].lendingDirection'] = parameters['pageQueryList0LendingDirection'];
  queryParameters['pageQueryList[0].lendingDirectionStr'] = parameters['pageQueryList0LendingDirectionStr'];
  queryParameters['pageQueryList[0].oppositeSubject'] = parameters['pageQueryList0OppositeSubject'];
  queryParameters['pageQueryList[0].subjectCode'] = parameters['pageQueryList0SubjectCode'];
  queryParameters['pageQueryList[0].subjectName'] = parameters['pageQueryList0SubjectName'];
  queryParameters['pageQueryList[0].voucherAbstract'] = parameters['pageQueryList0VoucherAbstract'];
  queryParameters['pageQueryList[0].voucherCode'] = parameters['pageQueryList0VoucherCode'];
  queryParameters['pageQueryList[0].voucherId'] = parameters['pageQueryList0VoucherId'];
  queryParameters['pageQueryList[0].voucherTime'] = parameters['pageQueryList0VoucherTime'];
  queryParameters['periodFrom'] = parameters['periodFrom'];
  queryParameters['periodTo'] = parameters['periodTo'];
  queryParameters['printInvoice'] = parameters['printInvoice'];
  queryParameters['qty'] = parameters['qty'];
  queryParameters['subjectAmount'] = parameters['subjectAmount'];
  queryParameters['subjectBeginCode'] = parameters['subjectBeginCode'];
  queryParameters['subjectCode'] = parameters['subjectCode'];
  queryParameters['subjectCodes'] = parameters['subjectCodes'];
  queryParameters['subjectEndCode'] = parameters['subjectEndCode'];
  queryParameters['toNumber'] = parameters['toNumber'];
  queryParameters['voucherAbstract'] = parameters['voucherAbstract'];
  queryParameters['voucherBeginCode'] = parameters['voucherBeginCode'];
  queryParameters['voucherCode'] = parameters['voucherCode'];
  queryParameters['voucherEndCode'] = parameters['voucherEndCode'];
  queryParameters['voucherId'] = parameters['voucherId'];
  queryParameters['voucherType'] = parameters['voucherType'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导入凭证 - importExcelVoucherUsingPOST
 * /rest/companyAccount/merge/voucher/v1.0/import
 * @param requestHeadersETag - 
 * @param requestHeadersAcceptCharset0Registered - 
 * @param requestHeadersAccept0CharSetRegistered - 
 * @param requestHeadersAccept0CharsetRegistered - 
 * @param requestHeadersAccept0Concrete - 
 * @param requestHeadersAccept0QualityValue - 
 * @param requestHeadersAccept0Subtype - 
 * @param requestHeadersAccept0Type - 
 * @param requestHeadersAccept0WildcardSubtype - 
 * @param requestHeadersAccept0WildcardType - 
 * @param requestHeadersAccessControlAllowCredentials - 
 * @param requestHeadersAccessControlAllowHeaders - 
 * @param requestHeadersAccessControlAllowMethods - 
 * @param requestHeadersAccessControlAllowOrigin - 
 * @param requestHeadersAccessControlExposeHeaders - 
 * @param requestHeadersAccessControlMaxAge - 
 * @param requestHeadersAccessControlRequestHeaders - 
 * @param requestHeadersAccessControlRequestMethod - 
 * @param requestHeadersAllow - 
 * @param requestHeadersCacheControl - 
 * @param requestHeadersConnection - 
 * @param requestHeadersContentLength - 
 * @param requestHeadersContentTypeCharSetRegistered - 
 * @param requestHeadersContentTypeCharsetRegistered - 
 * @param requestHeadersContentTypeConcrete - 
 * @param requestHeadersContentTypeQualityValue - 
 * @param requestHeadersContentTypeSubtype - 
 * @param requestHeadersContentTypeType - 
 * @param requestHeadersContentTypeWildcardSubtype - 
 * @param requestHeadersContentTypeWildcardType - 
 * @param requestHeadersDate - 
 * @param requestHeadersExpires - 
 * @param requestHeadersIfMatch - 
 * @param requestHeadersIfModifiedSince - 
 * @param requestHeadersIfNoneMatch - 
 * @param requestHeadersIfUnmodifiedSince - 
 * @param requestHeadersLastModified - 
 * @param requestHeadersLocationAbsolute - 
 * @param requestHeadersLocationAuthority - 
 * @param requestHeadersLocationFragment - 
 * @param requestHeadersLocationHost - 
 * @param requestHeadersLocationOpaque - 
 * @param requestHeadersLocationPath - 
 * @param requestHeadersLocationPort - 
 * @param requestHeadersLocationQuery - 
 * @param requestHeadersLocationRawAuthority - 
 * @param requestHeadersLocationRawFragment - 
 * @param requestHeadersLocationRawPath - 
 * @param requestHeadersLocationRawQuery - 
 * @param requestHeadersLocationRawSchemeSpecificPart - 
 * @param requestHeadersLocationRawUserInfo - 
 * @param requestHeadersLocationScheme - 
 * @param requestHeadersLocationSchemeSpecificPart - 
 * @param requestHeadersLocationUserInfo - 
 * @param requestHeadersOrigin - 
 * @param requestHeadersPragma - 
 * @param requestHeadersUpgrade - 
 * @param requestHeadersVary - 
 * @param requestMethod - 
 */
export const voucherImport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/v1.0/import'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['requestHeaders.ETag'] = parameters['requestHeadersETag'];
  queryParameters['requestHeaders.acceptCharset[0].registered'] = parameters['requestHeadersAcceptCharset0Registered'];
  queryParameters['requestHeaders.accept[0].charSet.registered'] = parameters['requestHeadersAccept0CharSetRegistered'];
  queryParameters['requestHeaders.accept[0].charset.registered'] = parameters['requestHeadersAccept0CharsetRegistered'];
  queryParameters['requestHeaders.accept[0].concrete'] = parameters['requestHeadersAccept0Concrete'];
  queryParameters['requestHeaders.accept[0].qualityValue'] = parameters['requestHeadersAccept0QualityValue'];
  queryParameters['requestHeaders.accept[0].subtype'] = parameters['requestHeadersAccept0Subtype'];
  queryParameters['requestHeaders.accept[0].type'] = parameters['requestHeadersAccept0Type'];
  queryParameters['requestHeaders.accept[0].wildcardSubtype'] = parameters['requestHeadersAccept0WildcardSubtype'];
  queryParameters['requestHeaders.accept[0].wildcardType'] = parameters['requestHeadersAccept0WildcardType'];
  queryParameters['requestHeaders.accessControlAllowCredentials'] = parameters['requestHeadersAccessControlAllowCredentials'];
  queryParameters['requestHeaders.accessControlAllowHeaders'] = parameters['requestHeadersAccessControlAllowHeaders'];
  queryParameters['requestHeaders.accessControlAllowMethods'] = parameters['requestHeadersAccessControlAllowMethods'];
  queryParameters['requestHeaders.accessControlAllowOrigin'] = parameters['requestHeadersAccessControlAllowOrigin'];
  queryParameters['requestHeaders.accessControlExposeHeaders'] = parameters['requestHeadersAccessControlExposeHeaders'];
  queryParameters['requestHeaders.accessControlMaxAge'] = parameters['requestHeadersAccessControlMaxAge'];
  queryParameters['requestHeaders.accessControlRequestHeaders'] = parameters['requestHeadersAccessControlRequestHeaders'];
  queryParameters['requestHeaders.accessControlRequestMethod'] = parameters['requestHeadersAccessControlRequestMethod'];
  queryParameters['requestHeaders.allow'] = parameters['requestHeadersAllow'];
  queryParameters['requestHeaders.cacheControl'] = parameters['requestHeadersCacheControl'];
  queryParameters['requestHeaders.connection'] = parameters['requestHeadersConnection'];
  queryParameters['requestHeaders.contentLength'] = parameters['requestHeadersContentLength'];
  queryParameters['requestHeaders.contentType.charSet.registered'] = parameters['requestHeadersContentTypeCharSetRegistered'];
  queryParameters['requestHeaders.contentType.charset.registered'] = parameters['requestHeadersContentTypeCharsetRegistered'];
  queryParameters['requestHeaders.contentType.concrete'] = parameters['requestHeadersContentTypeConcrete'];
  queryParameters['requestHeaders.contentType.qualityValue'] = parameters['requestHeadersContentTypeQualityValue'];
  queryParameters['requestHeaders.contentType.subtype'] = parameters['requestHeadersContentTypeSubtype'];
  queryParameters['requestHeaders.contentType.type'] = parameters['requestHeadersContentTypeType'];
  queryParameters['requestHeaders.contentType.wildcardSubtype'] = parameters['requestHeadersContentTypeWildcardSubtype'];
  queryParameters['requestHeaders.contentType.wildcardType'] = parameters['requestHeadersContentTypeWildcardType'];
  queryParameters['requestHeaders.date'] = parameters['requestHeadersDate'];
  queryParameters['requestHeaders.expires'] = parameters['requestHeadersExpires'];
  queryParameters['requestHeaders.ifMatch'] = parameters['requestHeadersIfMatch'];
  queryParameters['requestHeaders.ifModifiedSince'] = parameters['requestHeadersIfModifiedSince'];
  queryParameters['requestHeaders.ifNoneMatch'] = parameters['requestHeadersIfNoneMatch'];
  queryParameters['requestHeaders.ifUnmodifiedSince'] = parameters['requestHeadersIfUnmodifiedSince'];
  queryParameters['requestHeaders.lastModified'] = parameters['requestHeadersLastModified'];
  queryParameters['requestHeaders.location.absolute'] = parameters['requestHeadersLocationAbsolute'];
  queryParameters['requestHeaders.location.authority'] = parameters['requestHeadersLocationAuthority'];
  queryParameters['requestHeaders.location.fragment'] = parameters['requestHeadersLocationFragment'];
  queryParameters['requestHeaders.location.host'] = parameters['requestHeadersLocationHost'];
  queryParameters['requestHeaders.location.opaque'] = parameters['requestHeadersLocationOpaque'];
  queryParameters['requestHeaders.location.path'] = parameters['requestHeadersLocationPath'];
  queryParameters['requestHeaders.location.port'] = parameters['requestHeadersLocationPort'];
  queryParameters['requestHeaders.location.query'] = parameters['requestHeadersLocationQuery'];
  queryParameters['requestHeaders.location.rawAuthority'] = parameters['requestHeadersLocationRawAuthority'];
  queryParameters['requestHeaders.location.rawFragment'] = parameters['requestHeadersLocationRawFragment'];
  queryParameters['requestHeaders.location.rawPath'] = parameters['requestHeadersLocationRawPath'];
  queryParameters['requestHeaders.location.rawQuery'] = parameters['requestHeadersLocationRawQuery'];
  queryParameters['requestHeaders.location.rawSchemeSpecificPart'] = parameters['requestHeadersLocationRawSchemeSpecificPart'];
  queryParameters['requestHeaders.location.rawUserInfo'] = parameters['requestHeadersLocationRawUserInfo'];
  queryParameters['requestHeaders.location.scheme'] = parameters['requestHeadersLocationScheme'];
  queryParameters['requestHeaders.location.schemeSpecificPart'] = parameters['requestHeadersLocationSchemeSpecificPart'];
  queryParameters['requestHeaders.location.userInfo'] = parameters['requestHeadersLocationUserInfo'];
  queryParameters['requestHeaders.origin'] = parameters['requestHeadersOrigin'];
  queryParameters['requestHeaders.pragma'] = parameters['requestHeadersPragma'];
  queryParameters['requestHeaders.upgrade'] = parameters['requestHeadersUpgrade'];
  queryParameters['requestHeaders.vary'] = parameters['requestHeadersVary'];
  queryParameters['requestMethod'] = parameters['requestMethod'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 重排某月份所有凭证的凭证号 - 
 * /rest/companyAccount/merge/voucher/voucherCode/{tip}/v1.0
 * @param action - U: 更新, D: 删除
 * @param actionTime - 
 * @param billCode - 凭证号/备忘号
 * @param businessTimeFrom - 单据开始日期，YYYY-MM-DD
 * @param businessTimeTo - 单据结束日期，YYYY-MM-DD
 * @param categoryCode - 查询类型码：1.生产成本，2.管理费用，3.销售费用
 * @param categoryName - 查询类型名称
 * @param codeConditions0LikeCode - 
 * @param codeConditions0NotInCodes - 
 * @param codeNumber - 凭证号的号码
 * @param creationMode - 创建方式（1：自动、2：手动）
 * @param currencyCode - 币别编码
 * @param desc - 凭证号是否降序排序（true 降序,false 升序（默认升序））
 * @param export - 
 * @param fromNumber - 
 * @param memoBill - 备忘号
 * @param memoId - 备忘单ID
 * @param pageQueryList0AccountPeriod - 会计属期
 * @param pageQueryList0BalanceAmount - 余额
 * @param pageQueryList0BalanceAmountO - 余额(原币)
 * @param pageQueryList0CreateUserName - 财务
 * @param pageQueryList0CreationMode - 创建方式（1：自动、2：手动）
 * @param pageQueryList0CreditAmount - 贷方金额
 * @param pageQueryList0CreditAmountO - 贷方金额(原币)
 * @param pageQueryList0CurrencyCode - 币别编码
 * @param pageQueryList0DebitAmount - 借方金额
 * @param pageQueryList0DebitAmountO - 借方金额(原币)
 * @param pageQueryList0LendingDirection - 借贷方向（0：平、1：借、2：贷）
 * @param pageQueryList0LendingDirectionStr - 借贷方向（平、借、贷）
 * @param pageQueryList0OppositeSubject - 对方科目
 * @param pageQueryList0SubjectCode - 科目代码
 * @param pageQueryList0SubjectName - 科目
 * @param pageQueryList0VoucherAbstract - 摘要
 * @param pageQueryList0VoucherCode - 凭证号
 * @param pageQueryList0VoucherId - 凭证ID
 * @param pageQueryList0VoucherTime - 单据日期
 * @param periodFrom - 
 * @param periodTo - 
 * @param printInvoice - 是否打印关联发票
 * @param qty - 数量
 * @param subjectAmount - 金额
 * @param subjectBeginCode - 科目编码起
 * @param subjectCode - 科目代码
 * @param subjectCodes - 科目代码，如果有多个用逗号隔开
 * @param subjectEndCode - 科目编码止
 * @param tip - 是否弹出提示
 * @param toNumber - 
 * @param voucherAbstract - 凭证摘要
 * @param voucherBeginCode - 凭证号区间查询起始凭证号
 * @param voucherCode - 凭证号
 * @param voucherEndCode - 凭证号区间查询结束凭证号
 * @param voucherId - 凭证单ID
 * @param voucherType - 凭证类型（“main”：主凭证、“sub”：子凭证
 */
export const reArrangeVoucherCodeUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/voucherCode/{tip}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['action'] = parameters['action'];
  queryParameters['actionTime'] = parameters['actionTime'];
  queryParameters['billCode'] = parameters['billCode'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['categoryCode'] = parameters['categoryCode'];
  queryParameters['categoryName'] = parameters['categoryName'];
  queryParameters['codeConditions[0].likeCode'] = parameters['codeConditions0LikeCode'];
  queryParameters['codeConditions[0].notInCodes'] = parameters['codeConditions0NotInCodes'];
  queryParameters['codeNumber'] = parameters['codeNumber'];
  queryParameters['creationMode'] = parameters['creationMode'];
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['desc'] = parameters['desc'];
  queryParameters['export'] = parameters['export'];
  queryParameters['fromNumber'] = parameters['fromNumber'];
  queryParameters['memoBill'] = parameters['memoBill'];
  queryParameters['memoId'] = parameters['memoId'];
  queryParameters['pageQueryList[0].accountPeriod'] = parameters['pageQueryList0AccountPeriod'];
  queryParameters['pageQueryList[0].balanceAmount'] = parameters['pageQueryList0BalanceAmount'];
  queryParameters['pageQueryList[0].balanceAmountO'] = parameters['pageQueryList0BalanceAmountO'];
  queryParameters['pageQueryList[0].createUserName'] = parameters['pageQueryList0CreateUserName'];
  queryParameters['pageQueryList[0].creationMode'] = parameters['pageQueryList0CreationMode'];
  queryParameters['pageQueryList[0].creditAmount'] = parameters['pageQueryList0CreditAmount'];
  queryParameters['pageQueryList[0].creditAmountO'] = parameters['pageQueryList0CreditAmountO'];
  queryParameters['pageQueryList[0].currencyCode'] = parameters['pageQueryList0CurrencyCode'];
  queryParameters['pageQueryList[0].debitAmount'] = parameters['pageQueryList0DebitAmount'];
  queryParameters['pageQueryList[0].debitAmountO'] = parameters['pageQueryList0DebitAmountO'];
  queryParameters['pageQueryList[0].lendingDirection'] = parameters['pageQueryList0LendingDirection'];
  queryParameters['pageQueryList[0].lendingDirectionStr'] = parameters['pageQueryList0LendingDirectionStr'];
  queryParameters['pageQueryList[0].oppositeSubject'] = parameters['pageQueryList0OppositeSubject'];
  queryParameters['pageQueryList[0].subjectCode'] = parameters['pageQueryList0SubjectCode'];
  queryParameters['pageQueryList[0].subjectName'] = parameters['pageQueryList0SubjectName'];
  queryParameters['pageQueryList[0].voucherAbstract'] = parameters['pageQueryList0VoucherAbstract'];
  queryParameters['pageQueryList[0].voucherCode'] = parameters['pageQueryList0VoucherCode'];
  queryParameters['pageQueryList[0].voucherId'] = parameters['pageQueryList0VoucherId'];
  queryParameters['pageQueryList[0].voucherTime'] = parameters['pageQueryList0VoucherTime'];
  queryParameters['periodFrom'] = parameters['periodFrom'];
  queryParameters['periodTo'] = parameters['periodTo'];
  queryParameters['printInvoice'] = parameters['printInvoice'];
  queryParameters['qty'] = parameters['qty'];
  queryParameters['subjectAmount'] = parameters['subjectAmount'];
  queryParameters['subjectBeginCode'] = parameters['subjectBeginCode'];
  queryParameters['subjectCode'] = parameters['subjectCode'];
  queryParameters['subjectCodes'] = parameters['subjectCodes'];
  queryParameters['subjectEndCode'] = parameters['subjectEndCode'];
  path = path.replace('{tip}', `${parameters['tip']}`)
  queryParameters['toNumber'] = parameters['toNumber'];
  queryParameters['voucherAbstract'] = parameters['voucherAbstract'];
  queryParameters['voucherBeginCode'] = parameters['voucherBeginCode'];
  queryParameters['voucherCode'] = parameters['voucherCode'];
  queryParameters['voucherEndCode'] = parameters['voucherEndCode'];
  queryParameters['voucherId'] = parameters['voucherId'];
  queryParameters['voucherType'] = parameters['voucherType'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 查询凭证详细信息 - 
 * /rest/companyAccount/merge/voucher/{voucherType}/{voucherId}/v1.0
 * @param voucherId - 凭证单ID
 * @param voucherType - 凭证类型(取值“main”表示是主凭证，“sub”表示是子凭证)
 */
export const getVoucherDetailUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/{voucherType}/{voucherId}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{voucherId}', `${parameters['voucherId']}`)
  path = path.replace('{voucherType}', `${parameters['voucherType']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 更新凭证 - 
 * /rest/companyAccount/merge/voucher/{voucherType}/{voucherId}/v1.0
 * @param voucherId - 凭证主单ID
 * @param voucherType - 凭证类型(取值“main”表示是主凭证，“sub”表示是子凭证)
 * @param voucherVo - voucherVo
 */
export const updateVoucherUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucher/{voucherType}/{voucherId}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{voucherId}', `${parameters['voucherId']}`)
  path = path.replace('{voucherType}', `${parameters['voucherType']}`)
  body = parameters['voucherVo']
  if (parameters['voucherVo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: voucherVo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 查询凭证摘要列表 - 
 * /rest/companyAccount/merge/voucherAbstract/v1.0
 * @param abstractId - 摘要ID
 * @param createTime - 创建时间
 * @param createUserName - 创建人员名字
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param source - 来源(0:系统, 1:用户)
 * @param take - 
 * @param voucherAbstract - 摘要内容
 */
export const findAllAbstractsUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucherAbstract/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['abstractId'] = parameters['abstractId'];
  queryParameters['createTime'] = parameters['createTime'];
  queryParameters['createUserName'] = parameters['createUserName'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['source'] = parameters['source'];
  queryParameters['take'] = parameters['take'];
  queryParameters['voucherAbstract'] = parameters['voucherAbstract'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 新增凭证摘要 - 
 * /rest/companyAccount/merge/voucherAbstract/v1.0
 * @param vAbstract - vAbstract
 */
export const addVoucherAbstractUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucherAbstract/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['vAbstract']
  if (parameters['vAbstract'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vAbstract'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 修改凭证摘要 - 
 * /rest/companyAccount/merge/voucherAbstract/v1.0
 * @param vAbstract - vAbstract
 */
export const updateVoucherAbstractUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucherAbstract/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['vAbstract']
  if (parameters['vAbstract'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vAbstract'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 删除凭证摘要 - 
 * /rest/companyAccount/merge/voucherAbstract/{abstractId}/v1.0
 * @param abstractId - 摘要ID
 */
export const deleteVoucherAbstractUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucherAbstract/{abstractId}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{abstractId}', `${parameters['abstractId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 新增凭证模板 - 
 * /rest/companyAccount/merge/voucherModel/v1.0
 * @param model - model
 */
export const insertVoucherModelUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucherModel/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['model']
  if (parameters['model'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: model'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查询凭证列表 - findAllModelUsingGET
 * /rest/companyAccount/merge/voucherModel/v1.0/list
 * @param accountPeriod - 会计属期，YYYYMM
 * @param businessTimeFrom - 单据开始日期，YYYY-MM-DD
 * @param businessTimeTo - 单据结束日期，YYYY-MM-DD
 * @param categoryCode - 查询类型码：1.生产成本，2.管理费用，3.销售费用
 * @param categoryName - 查询类型名称
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param modelId - 模板ID
 * @param numberOrName - 模板编号/名称
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param subjectFullName - 科目名称
 * @param take - 
 */
export const voucherModelList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucherModel/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['categoryCode'] = parameters['categoryCode'];
  queryParameters['categoryName'] = parameters['categoryName'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['modelId'] = parameters['modelId'];
  queryParameters['numberOrName'] = parameters['numberOrName'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['subjectFullName'] = parameters['subjectFullName'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询凭证模板 - 
 * /rest/companyAccount/merge/voucherModel/v1.0/{voucherModuleId}
 * @param voucherModuleId - 凭证模板ID
 */
export const getVoucherModelUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucherModel/v1.0/{voucherModuleId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{voucherModuleId}', `${parameters['voucherModuleId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改凭证模板 - 
 * /rest/companyAccount/merge/voucherModel/v1.0/{voucherModuleId}
 * @param model - model
 * @param voucherModuleId - 凭证模板ID
 */
export const updateVoucherModelUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucherModel/v1.0/{voucherModuleId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['model']
  if (parameters['model'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: model'))
  }
  path = path.replace('{voucherModuleId}', `${parameters['voucherModuleId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 删除凭证模板 - 
 * /rest/companyAccount/merge/voucherModel/v1.0/{voucherModuleId}
 * @param voucherModuleId - 凭证模板ID
 */
export const deleteVoucherModelUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyAccount/merge/voucherModel/v1.0/{voucherModuleId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{voucherModuleId}', `${parameters['voucherModuleId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 添加资产信息 - insertFixedAssetsCardUsingPOST_2
 * /restsafe/companyAccount/merge/fixedAssetsCard/v0.1/add
 * @param po - po
 * @param type - 0 保存草稿 1 确认新增
 */
export const insertFixedAssetsCard = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyAccount/merge/fixedAssetsCard/v0.1/add'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['po']
  if (parameters['po'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: po'))
  }
  body = parameters['type']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 生成资产折旧备忘并自动核算 - createAssetMemoAndVoucherUsingPOST_1
 * /restsafe/companyAccount/merge/fixedAssetsCard/v0.1/createMemoAndVoucher/{companyId}
 * @param companyId - 公司ID
 */
export const fixedAssetsCardCreatememoandvoucher = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyAccount/merge/fixedAssetsCard/v0.1/createMemoAndVoucher/{companyId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{companyId}', `${parameters['companyId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}