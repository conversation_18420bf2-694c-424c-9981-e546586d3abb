/*
 * @Description: 公司基础模块api
 * @version:
 * @Company: 海闻软件
 * @Author: 肖泽涛
 * @Date: 2019-01-15 19:55:14
 * @LastEditors: 肖泽涛
 * @LastEditTime: 2019-05-21 15:41:12
 */

import request from '@/utils/request';

/**
 *获取快速做账凭证生成规则
 *
 * @returns
 */
export function getVoucherRule() {
  return request.get('/rest/companyConfig/companyBasis/vrule/v1.0/list');
}

/**
 *修改快速做账凭证生成规则
 *
 * @export
 * @param {*} VoucherRule
 * @returns
 */
export function updateVoucherRule(VoucherRule) {
  return request.post('/rest/companyConfig/companyBasis/vrule/v1.0/insertBatch', VoucherRule);
}

export function editCompanyInfo(param) {
  return request.put('/rest/companyConfig/companyBasis/companys/v1.1/edit', param);
}

/**
 * 获取系统币别列表
 * /rest/global/dimensionality/currency/v1.0/currency/droplist
 */
export function getGlobalCurrencyList() {
  return request.get('/rest/global/dimensionality/currency/v1.0/currency/droplist');
}
/**
 *获取账套币别列表
 *
 * @export
 * @returns
 */
export function getCurrencyList() {
  return request.get('/rest/companyConfig/companyBasis/currency/v1.0/droplist');
}

/**
 *获取公司基本信息
 *
 * @export
 */
export function getCompanyData() {
  return request.get('/rest/companyConfig/companyBasis/companys/v1.1/company');
}

/** 单位换算新增
 * @description:
 * @param {list}
 * @return:
 */
export function postSaleunitCvt(list) {
  return request.post('/rest/companyConfig/goods/saleunitCvt/v1.0/insertBatch', JSON.stringify(list));
}

/** 单位换算修改
 * @description:
 * @param {type}  sequenceId 换算单位id singleData 单条数据
 * @return:
 */

export function putSaleunitCvt(sequenceId, singleData) {
  return request.put(`/rest/companyConfig/goods/saleunitCvt/v1.0/${sequenceId}`, JSON.stringify(singleData));
}

/** 单位换算删除
 * @description:
 * @param {type}  sequenceId 换算单位id singleData 单条数据
 * @return:
 */

export function deleteSaleunitCvt(sequenceId) {
  return request.delete(`/rest/companyConfig/goods/saleunitCvt/v1.0/${sequenceId}`);
}

/** 费用类型查询
 * @description:
 * @param {type}
 * @return:
 */

export function getCostType(params) {
  return request.get('/rest/companyConfig/companyBasis/expense/type/v1.0', { params });
}

/** 费用类型删除
 * @description:
 * @ids {type} 费用ID, 用逗号分隔
 * @return:
 */

export function deleteCostType(ids) {
  return request.delete(`/rest/proxy/account/companyAccount/merge/expenses/type/v1.0?ids=${ids}`);
}

/** 费用类型修改
 * @description:
 * @params {type} claimContentStatus expensesNameLv1 expensesId
 * @return:
 */

export function updateCostType(params) {
  return request.post('/rest/proxy/account/companyAccount/merge/expenses/type/v1.0/updateClaimContent', params);
}

/** 费用类型新增
 * @description:
 * @list {type}
 * @return:
 */
export function postCostType(list) {
  return request.post('/rest/proxy/account/companyAccount/merge/expenses/type/v1.0', JSON.stringify(list));
}

/** 费用类型 修改单位
 */
export function putCostType(row) {
  return request.put('/rest/companyConfig/companyBasis/expense/type/v1.0/update', row);
}

/**
 * 适用会计准则
*/
export function getStandard() {
  return request.get('/rest/global/dimensionality/accounting/v1.0/standard');
}

/**
 * 适用行业类型
*/
export function getBusiness() {
  return request.get('/rest/global/dimensionality/accounting/v1.0/business');
}

/**
 * 适用行业会计准则版本
*/
export function getVersion() {
  return request.get('/rest/global/dimensionality/accounting/v1.0/version');
}

/**
 * 准则ID
*/
export function getStandardIds() {
  return request.get('/rest/global/dimensionality/accounting/v1.0/ids');
}
/**
 * 会计准则三和一列表
 */
export function getStandardList() {
  return request.get('/rest/global/dimensionality/accounting/v1.0?pageSize=99999');
}

/**
 *  查询公司信息- 会计信息
*/
export function getAccountInfo() {
  return request.get('/rest/companyConfig/companyBasis/companyTaxInfo/v1.1/accountInfo');
}

/**
 *  公司信息- 会计信息-修改保存
*/
export function putAccountInfoEdit(params) {
  return request.put('/rest/companyConfig/companyBasis/companyTaxInfo/v1.1/accountinfoEdit', params);
}

/**
 * @typedef AccoutDateUpdateForm - 建账日期修改表单
 * @property {string} accountDate - 建账日期<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">不能为空</span>
* */

/**
 * @description 账套建账日期修改
 * @param {AccoutDateUpdateForm} params - form
* */
export const updateCompanyAccountDateUsingPUT = (params) => request.put('/rest/companyConfig/companyBasis/accoutdate/v1.0', params);

/** 
 * @description 公司资产负债表重分类选项修改 
 * @param {Object} params - vo
 * @param {string} params.bsReclassifyOptions - 资产负债表重分类选项(1：应收账款-预收账款；2：应付账款-预付账款；3.其他应收款-其他应付款；4：应交税费-其他流动资产。多选项参数如下：1,2,3,4)
  属性校验：资产负债表重分类选项参数错误，请检查：1：应收账款-预收账款；2：应付账款-预付账款；3.其他应收款-其他应付款；4：应交税费-其他流动资产。多选项参数如下：1,2,3,4
 
 * 
 * 
**/
export const updateBsReclassifyOptions_CompanyTaxinfo_UsingPUT = (params) =>
  request.put(`/rest/companyConfig/companyBasis/companyTaxInfo/v1.1/bsReOp`, params);
