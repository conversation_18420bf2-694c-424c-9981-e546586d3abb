/*
 * @Description:
 * @version:
 * @Company: 海闻软件
 * @Author: 马赛
 * @Date: 2019-01-15 10:51:23
 * @LastEditors: 启旭
 * @LastEditTime: 2019-05-27 16:11:25
 */

import request from '@/utils/request';

/**
 *获取联系人
 *
 * @export
 * @returns
 */
// export function getApproveList() {
//   return request.get('/rest/proxy/account/approval/v1.0/getApproveList');
// };

/**
 *获取审批单据
 *
 * @export
 * @param {*} approverStatus 审批状态(-1:驳回 0:没操作, 1:通过)
 * @param {*} claimType 单据类型：(1: 报销单, 2: 借款单, 3: 还款单)
 * @returns
 */
// export function getList({ approverStatus, claimType }) {
//   const params = objToUrl({ approverStatus, claimType });
//   return request.get(`/rest/proxy/account/approval/v1.0/list?${params}`);
// };

/**
 *获取审批单据审批人
 *
 * @export
 * @param {*} claimId 单据ID
 * @param {*} claimType 单据类型：(1: 报销单, 2: 借款单, 3: 还款单)
 * @returns
 */
export function getApprovalList(params) {
  return request.get('/rest/proxy/account/approval/v1.0/getApprovalList', { params });
}

/**
 *审批人单据审批
 *
 * @export
 * @param {*} approvalOpinion 审批意见
 * @param {*} approverStatus 审批状态(-1:驳回 0:没操作, 1:通过)
 * @param {*} claimId 单据ID
 * @param {*} claimType 单据类型：(1: 报销单, 2: 借款单, 3: 还款单)
 * @returns
 */
export function updateApproval({
  approvalOpinion, approverStatus, claimId, claimType,
}) {
  return request.post('/rest/proxy/account/approval/v1.0/updateApproval', [{
    approvalOpinion, approverStatus, claimId, claimType,
  }]);
}
