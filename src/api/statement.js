/*
 * @Description: 报表模块api
 * @version:
 * @Company: 海闻软件
 * @Author: 肖泽涛
 * @Date: 2019-01-15 19:19:17
 * @LastEditors: 肖泽涛
 * @LastEditTime: 2019-01-23 20:57:43
 */

import request from '@/utils/request';

/**
 *智能凭证检查概览统计
 *
 * @export
 * @param {string} accountPeriod -会计属期（yyyy-MM）
 * @returns
 *expenditureAmount	number支出金额 |

  expenditureCount	number统计支出数量  |

  incomeAmount	number收入金额 |

  incomeCount	number统计收入数量 |

  invoiceCounts	number数量、条数 |

  moduleId	number模块：（1.收入（销项）；2.采购（进项）；3.银行流水；4：现金流水；5.费用） |

  noTaxAmountTotal	number不含税金额合计 |

  taxAmountTotal	number税额合计
 */
export function fastAccCheck(accountPeriod, isVoucher) {
  return request.get('/rest/proxy/stmt/merge/report/fastAccCheck/v1.0/totals', { params: { accountPeriod, isVoucher } });
}

/**
 *查询智能凭证提醒
 *
 * @export
 * @param {*} accountPeriod -属期 （yyyy-MM）
 * @param {*} moduleIds -模块id（1.收入（销项）；2.采购（进项）；3.银行流水；4.现金流水；5.费用）例：1,2,3
 * @returns  [{
      "lineId": 0,
      "moduleId": 0,
      "tip": "string"
    }]
 */
export function checkTips(accountPeriod, moduleIds) {
  return request.get('/rest/proxy/stmt/merge/report/fastAccCheck/v1.0/tips', { params: { accountPeriod, moduleIds } });
}

/**
 *获取待办事项
 *
 * @export
 * @param {*} accountPeriod 属期（yyyyMM）
 * @returns
 */
export function getTodoList(accountPeriod) {
  return request.get(`/rest/proxy/stmt/merge/report/todolist/v1.0/get/${accountPeriod}`);
}

/**
 *获取财务报表
 *
 * @export
 * @param {*} { fromMonth='', toMonth='', accPeriods='' } -fromMonth 属期 格式201707| toMonth 属期 格式201707 | accPeriods 属期 格式201707,201703
 * @returns
 */
export function getFinancial({
  fromMonth = '', toMonth = '', accPeriods = '', yearFlag,
}) {
  return request.get('/rest/statements/merge/financial/mobile/v1.0/list', {
    params: {
      fromMonth, toMonth, accPeriods, yearFlag,
    },
  });
}

/**
 *检查账套是否已经初始化已经绑定税局信息
 *
 * @export
 * @returns
 *
 * totalData.initial	0 未完成
    totalData.registered	1 已完成
 */
export function checkInit() {
  return request.get('/rest/proxy/stmt/merge/report/fastAccCheck/v1.0/basis');
}
