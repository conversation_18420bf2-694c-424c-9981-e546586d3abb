/* eslint-disable */
import request from '@/utils/request';
/**
 * 查询所有环节完成情况 - allStatusUsingGET_1
 * /rest/proxy/stmt/merge/process/v1.0/all/list
 * @param accountPeriod - 会计属期（yyyyMM)
 * @param collectStatus - 收藏状态:true收藏，false未收藏
 * @param companyId - 公司ID
 * @param companyName - 公司名称
 * @param isCrrentUser - 是否用户本人:true是本人
 * @param processName - 状态名称('carried':结转 'input':进项 'output': 销项 'cashPayment': 现金收付 'bankAccount': 银行流水 'salary':工资 'costAccount': 成本核算 'vat':增值税申报 'cit': 所得税申报  'declaration' : 申报 'all':所有)
 * @param specialAccountPeriod - 所得税季报专用属期(yyyy字母)
 * @param updateNow - 是否立即更新:true立即更新，false从缓存中取
 * @param username - 用户姓名
 * @param 用户id列 - 
 */
export const processAll = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/process/v1.0/all/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['collectStatus'] = parameters['collectStatus'];
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['companyName'] = parameters['companyName'];
  queryParameters['isCrrentUser'] = parameters['isCrrentUser'];
  queryParameters['processName'] = parameters['processName'];
  queryParameters['specialAccountPeriod'] = parameters['specialAccountPeriod'];
  queryParameters['updateNow'] = parameters['updateNow'];
  queryParameters['username'] = parameters['username'];
  queryParameters['用户ID列'] = parameters['用户id列'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询所有环节完成情况 - 
 * /rest/proxy/stmt/merge/process/v1.0/{processName}/list
 * @param accountPeriod - 会计属期（yyyyMM)
 * @param collectStatus - 收藏状态:true收藏，false未收藏
 * @param companyId - 公司ID
 * @param companyName - 公司名称
 * @param isCrrentUser - 是否用户本人:true是本人
 * @param processName - 状态名称('carried':结转 'input':进项 'output': 销项 'cashPayment': 现金收付 'bankAccount': 银行流水 'salary':工资 'costAccount': 成本核算 'vat':增值税申报 'cit': 所得税申报  'declaration' : 申报 'all':所有)
 * @param processName - processName
 * @param specialAccountPeriod - 所得税季报专用属期(yyyy字母)
 * @param updateNow - 是否立即更新:true立即更新，false从缓存中取
 * @param username - 用户姓名
 * @param 用户id列 - 
 */
export const allStatusUsingGET_3 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/process/v1.0/{processName}/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['collectStatus'] = parameters['collectStatus'];
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['companyName'] = parameters['companyName'];
  queryParameters['isCrrentUser'] = parameters['isCrrentUser'];
  queryParameters['processName'] = parameters['processName'];
  path = path.replace('{processName}', `${parameters['processName']}`)
  if (parameters['processName'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: processName'))
  }
  queryParameters['specialAccountPeriod'] = parameters['specialAccountPeriod'];
  queryParameters['updateNow'] = parameters['updateNow'];
  queryParameters['username'] = parameters['username'];
  queryParameters['用户ID列'] = parameters['用户id列'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询所有环节完成情况 - allCompanyStatusUsingGET_1
 * /rest/proxy/stmt/merge/process/v1.2/all/cardList
 * @param accountPeriod - 会计属期（yyyyMM)
 * @param collectStatus - 收藏状态:true收藏，false未收藏
 * @param companyId - 公司ID
 * @param companyName - 公司名称
 * @param isCrrentUser - 是否用户本人:true是本人
 * @param processName - 状态名称('carried':结转 'input':进项 'output': 销项 'cashPayment': 现金收付 'bankAccount': 银行流水 'salary':工资 'costAccount': 成本核算 'vat':增值税申报 'cit': 所得税申报  'declaration' : 申报 'all':所有)
 * @param specialAccountPeriod - 所得税季报专用属期(yyyy字母)
 * @param updateNow - 是否立即更新:true立即更新，false从缓存中取
 * @param username - 用户姓名
 * @param 用户id列 - 
 */
export const allCompanyStatus= function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/process/v1.2/all/cardList'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['collectStatus'] = parameters['collectStatus'];
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['companyName'] = parameters['companyName'];
  queryParameters['isCrrentUser'] = parameters['isCrrentUser'];
  queryParameters['processName'] = parameters['processName'];
  queryParameters['specialAccountPeriod'] = parameters['specialAccountPeriod'];
  queryParameters['updateNow'] = parameters['updateNow'];
  queryParameters['username'] = parameters['username'];
  queryParameters['用户ID列'] = parameters['用户id列'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出账龄分析表 - exportAccountAgeUsingGET_1
 * /rest/proxy/stmt/merge/report/accountAge/v1.0/export
 * @param beginMonth - 开始月份（yyyy-MM）
 * @param currencyCode - 货币编码（不传为综合本位币）
 * @param endMonth - 结束月份（yyyy-MM）
 * @param reportType - 账龄分析表类型（1：应收账款，2：应付账款）
 */
export const accountAgeExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/accountAge/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginMonth'] = parameters['beginMonth'];
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['endMonth'] = parameters['endMonth'];
  queryParameters['reportType'] = parameters['reportType'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询账龄分析表 - findAccountAgeUsingGET
 * /rest/proxy/stmt/merge/report/accountAge/v1.0/list
 * @param accountPeriod - 截止月份（yyyy-MM）
 * @param currencyCode - 货币编码（不传查询综合本位币，默认查询RMB，传入RMB）
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param reportType - 账龄分析表类型（1：应收账款，2：应付账款）
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const accountAgeList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/accountAge/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['reportType'] = parameters['reportType'];
  if (parameters['reportType'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: reportType'))
  }
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出所有报表 - exportCashierAccountUsingGET_1
 * /rest/proxy/stmt/merge/report/allreport/v1.0/export
 * @param beginMonth - 开始月份（yyyy-MM）
 * @param endMonth - 结束月份（yyyy-MM）
 */
export const allreportExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/allreport/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginMonth'] = parameters['beginMonth'];
  queryParameters['endMonth'] = parameters['endMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出固定资产折旧明细总账 - exportAssetDepreDetailUsingGET
 * /rest/proxy/stmt/merge/report/asset/v1.0/export
 * @param accountPeriod - 会计属期，YYYYMM
 * @param businessTimeFrom - 单据开始日期，YYYY-MM-DD
 * @param businessTimeTo - 单据结束日期，YYYY-MM-DD
 * @param categoryCode - 查询类型码：1.生产成本，2.管理费用，3.销售费用
 * @param categoryName - 查询类型名称
 */
export const assetExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/asset/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['categoryCode'] = parameters['categoryCode'];
  queryParameters['categoryName'] = parameters['categoryName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询固定资产折旧明细总账 - findAssetDepreDetailUsingGET
 * /rest/proxy/stmt/merge/report/asset/v1.0/list
 * @param accountPeriod - 会计属期，YYYYMM
 * @param businessTimeFrom - 单据开始日期，YYYY-MM-DD
 * @param businessTimeTo - 单据结束日期，YYYY-MM-DD
 * @param categoryCode - 查询类型码：1.生产成本，2.管理费用，3.销售费用
 * @param categoryName - 查询类型名称
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const assetList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/asset/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['categoryCode'] = parameters['categoryCode'];
  queryParameters['categoryName'] = parameters['categoryName'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出出纳帐列表 - exportCashierAccountUsingGET
 * /rest/proxy/stmt/merge/report/cashier/v1.0/export
 * @param accountPeriodFrom - 属期起
 * @param accountPeriodTo - 属期止
 * @param cashierAccountList0AccountPeriod - 属期、日期
 * @param cashierAccountList0BalanceAmount - 支出
 * @param cashierAccountList0CreationMode - 创建方式（1：自动、2：手动）
 * @param cashierAccountList0CreditAmount - 支出
 * @param cashierAccountList0DebitAmount - 收入
 * @param cashierAccountList0LendingDirection - 借贷方向
 * @param cashierAccountList0SubjectCode - 科目代码
 * @param cashierAccountList0SubjectName - 科目名称
 * @param cashierAccountList0VoucherAbstract - 凭证摘要
 * @param cashierAccountList0VoucherCode - 凭证号
 * @param cashierAccountList0VoucherId - 凭证ID
 * @param cashierAccountList0VoucherTime - 凭证日期
 * @param subjectBeginCode - 科目编码起
 * @param subjectCodeList - 科目编码
 * @param subjectEndCode - 科目编码止
 * @param voucherTimeFrom - 凭证时间起
 * @param voucherTimeTo - 凭证时间止
 */
export const cashierExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/cashier/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriodFrom'] = parameters['accountPeriodFrom'];
  queryParameters['accountPeriodTo'] = parameters['accountPeriodTo'];
  queryParameters['cashierAccountList[0].accountPeriod'] = parameters['cashierAccountList0AccountPeriod'];
  queryParameters['cashierAccountList[0].balanceAmount'] = parameters['cashierAccountList0BalanceAmount'];
  queryParameters['cashierAccountList[0].creationMode'] = parameters['cashierAccountList0CreationMode'];
  queryParameters['cashierAccountList[0].creditAmount'] = parameters['cashierAccountList0CreditAmount'];
  queryParameters['cashierAccountList[0].debitAmount'] = parameters['cashierAccountList0DebitAmount'];
  queryParameters['cashierAccountList[0].lendingDirection'] = parameters['cashierAccountList0LendingDirection'];
  queryParameters['cashierAccountList[0].subjectCode'] = parameters['cashierAccountList0SubjectCode'];
  queryParameters['cashierAccountList[0].subjectName'] = parameters['cashierAccountList0SubjectName'];
  queryParameters['cashierAccountList[0].voucherAbstract'] = parameters['cashierAccountList0VoucherAbstract'];
  queryParameters['cashierAccountList[0].voucherCode'] = parameters['cashierAccountList0VoucherCode'];
  queryParameters['cashierAccountList[0].voucherId'] = parameters['cashierAccountList0VoucherId'];
  queryParameters['cashierAccountList[0].voucherTime'] = parameters['cashierAccountList0VoucherTime'];
  queryParameters['subjectBeginCode'] = parameters['subjectBeginCode'];
  queryParameters['subjectCodeList'] = parameters['subjectCodeList'];
  queryParameters['subjectEndCode'] = parameters['subjectEndCode'];
  queryParameters['voucherTimeFrom'] = parameters['voucherTimeFrom'];
  queryParameters['voucherTimeTo'] = parameters['voucherTimeTo'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询出纳帐 - findCashierAccountUsingGET
 * /rest/proxy/stmt/merge/report/cashier/v1.0/list
 * @param accountPeriodFrom - 属期起
 * @param accountPeriodTo - 属期止
 * @param cashierAccountList0AccountPeriod - 属期、日期
 * @param cashierAccountList0BalanceAmount - 支出
 * @param cashierAccountList0CreationMode - 创建方式（1：自动、2：手动）
 * @param cashierAccountList0CreditAmount - 支出
 * @param cashierAccountList0DebitAmount - 收入
 * @param cashierAccountList0LendingDirection - 借贷方向
 * @param cashierAccountList0SubjectCode - 科目代码
 * @param cashierAccountList0SubjectName - 科目名称
 * @param cashierAccountList0VoucherAbstract - 凭证摘要
 * @param cashierAccountList0VoucherCode - 凭证号
 * @param cashierAccountList0VoucherId - 凭证ID
 * @param cashierAccountList0VoucherTime - 凭证日期
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param subjectBeginCode - 科目编码起
 * @param subjectCodeList - 科目编码
 * @param subjectEndCode - 科目编码止
 * @param take - 
 * @param voucherTimeFrom - 凭证时间起
 * @param voucherTimeTo - 凭证时间止
 */
export const cashierList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/cashier/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriodFrom'] = parameters['accountPeriodFrom'];
  queryParameters['accountPeriodTo'] = parameters['accountPeriodTo'];
  queryParameters['cashierAccountList[0].accountPeriod'] = parameters['cashierAccountList0AccountPeriod'];
  queryParameters['cashierAccountList[0].balanceAmount'] = parameters['cashierAccountList0BalanceAmount'];
  queryParameters['cashierAccountList[0].creationMode'] = parameters['cashierAccountList0CreationMode'];
  queryParameters['cashierAccountList[0].creditAmount'] = parameters['cashierAccountList0CreditAmount'];
  queryParameters['cashierAccountList[0].debitAmount'] = parameters['cashierAccountList0DebitAmount'];
  queryParameters['cashierAccountList[0].lendingDirection'] = parameters['cashierAccountList0LendingDirection'];
  queryParameters['cashierAccountList[0].subjectCode'] = parameters['cashierAccountList0SubjectCode'];
  queryParameters['cashierAccountList[0].subjectName'] = parameters['cashierAccountList0SubjectName'];
  queryParameters['cashierAccountList[0].voucherAbstract'] = parameters['cashierAccountList0VoucherAbstract'];
  queryParameters['cashierAccountList[0].voucherCode'] = parameters['cashierAccountList0VoucherCode'];
  queryParameters['cashierAccountList[0].voucherId'] = parameters['cashierAccountList0VoucherId'];
  queryParameters['cashierAccountList[0].voucherTime'] = parameters['cashierAccountList0VoucherTime'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['subjectBeginCode'] = parameters['subjectBeginCode'];
  queryParameters['subjectCodeList'] = parameters['subjectCodeList'];
  queryParameters['subjectEndCode'] = parameters['subjectEndCode'];
  queryParameters['take'] = parameters['take'];
  queryParameters['voucherTimeFrom'] = parameters['voucherTimeFrom'];
  queryParameters['voucherTimeTo'] = parameters['voucherTimeTo'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 智能凭证基础数据检测 - findBasisValidTipsUsingGET_1
 * /rest/proxy/stmt/merge/report/fastAccCheck/v1.0/basis
 */
export const fastAccCheckBasis = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/fastAccCheck/v1.0/basis'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询智能凭证检查提醒 - findFastTipsUsingGET_1
 * /rest/proxy/stmt/merge/report/fastAccCheck/v1.0/tips
 * @param accountPeriod - 会计属期（yyyy-MM）
 * @param moduleIds - 模块id（1.收入（销项）；2.采购（进项）；3.银行流水；4.现金流水；5.费用）例：1,2,3
 */
export const fastAccCheckTips = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/fastAccCheck/v1.0/tips'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  queryParameters['moduleIds'] = parameters['moduleIds'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 智能凭证检查概览统计 - findTotalsUsingGET_1
 * /rest/proxy/stmt/merge/report/fastAccCheck/v1.0/totals
 * @param accountPeriod - 会计属期（yyyy-MM）
 * @param moduleIds - 模块id组（1.收入（销项）；2.采购（进项）；3.银行流水；4.现金流水；5.费用），不传则查全部；传入示例：moduleIds=1,2,3
 */
export const fastAccCheckTotals = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/fastAccCheck/v1.0/totals'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  queryParameters['moduleIds'] = parameters['moduleIds'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出财务费用明细总账 - exportNumberAmountUsingGET
 * /rest/proxy/stmt/merge/report/financial/v1.0/export
 * @param accountPeriod - 会计属期，YYYYMM
 * @param businessTimeFrom - 单据开始日期，YYYY-MM-DD
 * @param businessTimeTo - 单据结束日期，YYYY-MM-DD
 * @param categoryCode - 查询类型码：1.生产成本，2.管理费用，3.销售费用
 * @param categoryName - 查询类型名称
 */
export const financialExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/financial/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['categoryCode'] = parameters['categoryCode'];
  queryParameters['categoryName'] = parameters['categoryName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询财务费用明细总账 - findFinancialChargeUsingGET
 * /rest/proxy/stmt/merge/report/financial/v1.0/list
 * @param accountPeriod - 会计属期，YYYYMM
 * @param businessTimeFrom - 单据开始日期，YYYY-MM-DD
 * @param businessTimeTo - 单据结束日期，YYYY-MM-DD
 * @param categoryCode - 查询类型码：1.生产成本，2.管理费用，3.销售费用
 * @param categoryName - 查询类型名称
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const financialList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/financial/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['categoryCode'] = parameters['categoryCode'];
  queryParameters['categoryName'] = parameters['categoryName'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出商品流水报表 - exportUsingGET
 * /rest/proxy/stmt/merge/report/goods/v1.0/export
 * @param beginPeriod - 起始查询日期（yyyyMM）
 * @param endPeriod - 截止查询日期（yyyyMM）
 * @param goodsId - 商品ID
 * @param goodsType - 商品类型（1：库存商品、2：半成品、3：原材料、4：包装物、5：低值易耗品）
 */
export const goodsExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/goods/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginPeriod'] = parameters['beginPeriod'];
  if (parameters['beginPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: beginPeriod'))
  }
  queryParameters['endPeriod'] = parameters['endPeriod'];
  if (parameters['endPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: endPeriod'))
  }
  queryParameters['goodsId'] = parameters['goodsId'];
  queryParameters['goodsType'] = parameters['goodsType'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获取有单据数据的商品 - getAvailableGoodsUsingGET
 * /rest/proxy/stmt/merge/report/goods/v1.0/getAvailableGoods
 * @param beginPeriod - 起始查询日期（yyyyMM）
 * @param endPeriod - 截止查询日期（yyyyMM）
 * @param goodsType - 商品类型（1：库存商品、2：半成品、3：原材料、4：包装物、5：低值易耗品）
 */
export const goodsGetavailablegoods = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/goods/v1.0/getAvailableGoods'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginPeriod'] = parameters['beginPeriod'];
  if (parameters['beginPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: beginPeriod'))
  }
  queryParameters['endPeriod'] = parameters['endPeriod'];
  if (parameters['endPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: endPeriod'))
  }
  queryParameters['goodsType'] = parameters['goodsType'];
  if (parameters['goodsType'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: goodsType'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 商品流水查询 - findUsingGET
 * /rest/proxy/stmt/merge/report/goods/v1.0/list
 * @param beginPeriod - 起始查询日期（yyyyMM）
 * @param endPeriod - 截止查询日期（yyyyMM）
 * @param goodsId - 商品ID
 * @param goodsType - 商品类型（1：库存商品、2：半成品、3：原材料、4：包装物、5：低值易耗品）
 */
export const goodsList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/goods/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginPeriod'] = parameters['beginPeriod'];
  if (parameters['beginPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: beginPeriod'))
  }
  queryParameters['endPeriod'] = parameters['endPeriod'];
  if (parameters['endPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: endPeriod'))
  }
  queryParameters['goodsId'] = parameters['goodsId'];
  if (parameters['goodsId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: goodsId'))
  }
  queryParameters['goodsType'] = parameters['goodsType'];
  if (parameters['goodsType'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: goodsType'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询进项发票 - findInvoiceInputDetailsUsingGET_1
 * /rest/proxy/stmt/merge/report/invoiceInput/v1.0/list
 * @param accountPeriod - 会计属期（yyyy-MM）
 * @param isVoucher - 是否已生成凭证（0.未生成，1.已生成）
 * @param page - 页数
 * @param pageSize - 页码
 */
export const invoiceInputList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/invoiceInput/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  queryParameters['isVoucher'] = parameters['isVoucher'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 统计进项发票 - findInvoiceInputTotalsUsingGET_1
 * /rest/proxy/stmt/merge/report/invoiceInput/v1.0/totals
 * @param accountPeriod - 会计属期（yyyy-MM）
 * @param isVoucher - 是否已生成凭证（0.未生成，1.已生成）
 */
export const invoiceInputTotals = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/invoiceInput/v1.0/totals'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  queryParameters['isVoucher'] = parameters['isVoucher'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询销项发票 - findInvoiceOutputDetailsUsingGET_1
 * /rest/proxy/stmt/merge/report/invoiceOutput/v1.0/list
 * @param accountPeriod - 会计属期（yyyy-MM）
 * @param isVoucher - 是否已生成凭证（0.未生成，1.已生成）
 * @param page - 页数
 * @param pageSize - 页码
 */
export const invoiceOutputList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/invoiceOutput/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  queryParameters['isVoucher'] = parameters['isVoucher'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 统计销项发票 - findInvoiceOutputTotalsUsingGET_1
 * /rest/proxy/stmt/merge/report/invoiceOutput/v1.0/totals
 * @param accountPeriod - 会计属期（yyyy-MM）
 * @param isVoucher - 是否已生成凭证（0.未生成，1.已生成）
 */
export const invoiceOutputTotals = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/invoiceOutput/v1.0/totals'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  queryParameters['isVoucher'] = parameters['isVoucher'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出总分类账（单外币）列表 - exportGeneralLedgerCurrencyUsingGET_1
 * /rest/proxy/stmt/merge/report/ledger/v1.0/currency/export
 * @param beginMonth - 开始月份（yyyy-MM）
 * @param currencyCode - 货币编码（除RMB外，不传为综合本位币）
 * @param endMonth - 结束月份（yyyy-MM）
 */
export const ledgerCurrency = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/ledger/v1.0/currency/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginMonth'] = parameters['beginMonth'];
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['endMonth'] = parameters['endMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出出纳帐列表 - exportGeneralLedgerUsingGET
 * /rest/proxy/stmt/merge/report/ledger/v1.0/export
 * @param beginMonth - 开始月份（yyyy-MM）
 * @param currencyCode - 货币编码（限RMB或不传（综合本位币））
 * @param endMonth - 结束月份（yyyy-MM）
 */
export const ledgerExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/ledger/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginMonth'] = parameters['beginMonth'];
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['endMonth'] = parameters['endMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 总分类帐列表查询 - findGeneralLedgerUsingGET
 * /rest/proxy/stmt/merge/report/ledger/v1.0/list
 * @param beginMonth - 开始月份（yyyy-MM）
 * @param currencyCode - 货币编码（限RMB或不传（综合本位币））
 * @param data - 
 * @param endMonth - 结束月份（yyyy-MM）
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const ledgerList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/ledger/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginMonth'] = parameters['beginMonth'];
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['data'] = parameters['data'];
  queryParameters['endMonth'] = parameters['endMonth'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询总分类账（外币）列表 - findGeneralLedgerCurrencyUsingGET
 * /rest/proxy/stmt/merge/report/ledger/v1.0/listc
 * @param beginMonth - 开始月份（yyyy-MM）
 * @param currencyCode - 货币编码（除RMB外，不传为综合本位币）
 * @param data - 
 * @param endMonth - 结束月份（yyyy-MM）
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const ledgerListc = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/ledger/v1.0/listc'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginMonth'] = parameters['beginMonth'];
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['data'] = parameters['data'];
  queryParameters['endMonth'] = parameters['endMonth'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出数量金额明细账列表 - exportNumberAmountDetailsUsingGET
 * /rest/proxy/stmt/merge/report/numDetails/v1.0/export
 * @param beginMonth - 开始月份（yyyy-MM）
 * @param endMonth - 结束月份（yyyy-MM）
 * @param subjectCodes - 科目编码集
 */
export const numDetailsExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/numDetails/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginMonth'] = parameters['beginMonth'];
  queryParameters['endMonth'] = parameters['endMonth'];
  queryParameters['subjectCodes'] = parameters['subjectCodes'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询数量金额明细账 - findNumberAmountDetailsUsingGET
 * /rest/proxy/stmt/merge/report/numDetails/v1.0/list
 * @param beginMonth - 开始月份（yyyy-MM）
 * @param endMonth - 结束月份（yyyy-MM）
 * @param page - 当前页
 * @param pageSize - 页码
 * @param subjectCodes - 科目编码集
 */
export const numDetailsList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/numDetails/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginMonth'] = parameters['beginMonth'];
  queryParameters['endMonth'] = parameters['endMonth'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['subjectCodes'] = parameters['subjectCodes'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出数量金额总账列表 - exportNumberAmountUsingGET_1
 * /rest/proxy/stmt/merge/report/number/v1.0/export
 * @param accountPeriod - 会计属期（yyyy-MM）
 * @param subjectCodes - 科目编码集
 */
export const numberExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/number/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['subjectCodes'] = parameters['subjectCodes'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询数量金额总账 - findNumberAmountUsingPOST
 * /rest/proxy/stmt/merge/report/number/v1.0/list
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 * @param vo - vo
 */
export const numberList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/number/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  body = parameters['vo']
  if (parameters['vo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 导出数量金额总账列表2.0 - exportNumberAmountUsingGET_2
 * /rest/proxy/stmt/merge/report/number/v2.0/export
 * @param accountPeriod - 会计属期（yyyy-MM）
 * @param subjectCodes - 科目编码集
 */
export const exportNumberAmount = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/number/v2.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['subjectCodes'] = parameters['subjectCodes'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询数量金额总账2.0 - findNumberAmountUsingGET
 * /rest/proxy/stmt/merge/report/number/v2.0/list
 * @param accountPeriod - 会计属期（yyyy-MM）
 * @param beginMonth - 开始月份（yyyy-MM）
 * @param beginPeriod - 开始月份（yyyyMM）
 * @param companyId - 公司id
 * @param data - 
 * @param endMonth - 结束月份（yyyy-MM）
 * @param endPeriod - 结束月份（yyyyMM）
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param subjectBeginCode - 科目编码起
 * @param subjectCode - 科目编码
 * @param subjectCodes - 科目编码集合
 * @param subjectCodesLike - 父科目编码集合
 * @param subjectEndCode - 科目编码止
 * @param subjectFullName - 科目全称
 * @param take - 
 * @param yearFlag - 显示年累计列
 */
export const findNumberAmount= function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/number/v2.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['beginMonth'] = parameters['beginMonth'];
  queryParameters['beginPeriod'] = parameters['beginPeriod'];
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['data'] = parameters['data'];
  queryParameters['endMonth'] = parameters['endMonth'];
  queryParameters['endPeriod'] = parameters['endPeriod'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['subjectBeginCode'] = parameters['subjectBeginCode'];
  queryParameters['subjectCode'] = parameters['subjectCode'];
  queryParameters['subjectCodes'] = parameters['subjectCodes'];
  queryParameters['subjectCodesLike'] = parameters['subjectCodesLike'];
  queryParameters['subjectEndCode'] = parameters['subjectEndCode'];
  queryParameters['subjectFullName'] = parameters['subjectFullName'];
  queryParameters['take'] = parameters['take'];
  queryParameters['yearFlag'] = parameters['yearFlag'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出生产成本明细总账 - exportProductionCostUsingGET
 * /rest/proxy/stmt/merge/report/production/v1.0/export
 * @param accountPeriod - 会计属期，YYYYMM
 * @param businessTimeFrom - 单据开始日期，YYYY-MM-DD
 * @param businessTimeTo - 单据结束日期，YYYY-MM-DD
 * @param categoryCode - 查询类型码：1.生产成本，2.管理费用，3.销售费用
 * @param categoryName - 查询类型名称
 */
export const productionExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/production/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['categoryCode'] = parameters['categoryCode'];
  if (parameters['categoryCode'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: categoryCode'))
  }
  queryParameters['categoryName'] = parameters['categoryName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询生产成本明细总账 - findProductionCostUsingGET
 * /rest/proxy/stmt/merge/report/production/v1.0/list
 * @param accountPeriod - 会计属期，YYYYMM
 * @param businessTimeFrom - 单据开始日期，YYYY-MM-DD
 * @param businessTimeTo - 单据结束日期，YYYY-MM-DD
 * @param categoryCode - 查询类型码：1.生产成本，2.管理费用，3.销售费用
 * @param categoryName - 查询类型名称
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const productionList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/production/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['businessTimeFrom'] = parameters['businessTimeFrom'];
  queryParameters['businessTimeTo'] = parameters['businessTimeTo'];
  queryParameters['categoryCode'] = parameters['categoryCode'];
  if (parameters['categoryCode'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: categoryCode'))
  }
  queryParameters['categoryName'] = parameters['categoryName'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询所有待办事项 - findUsingGET_1
 * /rest/proxy/stmt/merge/report/todolist/v1.0/get/{accountPeriod}
 * @param accountPeriod - 会计周期（yyyyMM）
 */
export const todolistGet = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/todolist/v1.0/get/{accountPeriod}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出应交增值税明细账列表 - exportVATPayableDetailsUsingGET
 * /rest/proxy/stmt/merge/report/vatDetails/v1.0/export
 * @param beginMonth - 开始月份（yyyy-MM）
 * @param endMonth - 结束月份（yyyy-MM）
 */
export const vatDetailsExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/vatDetails/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginMonth'] = parameters['beginMonth'];
  queryParameters['endMonth'] = parameters['endMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询应交增值税明细账 - findVATPayableDetailsUsingGET
 * /rest/proxy/stmt/merge/report/vatDetails/v1.0/list
 * @param beginMonth - 开始月份（yyyy-MM）
 * @param endMonth - 结束月份（yyyy-MM）
 */
export const vatDetailsList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/stmt/merge/report/vatDetails/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginMonth'] = parameters['beginMonth'];
  queryParameters['endMonth'] = parameters['endMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询银行流水，现金流水明细 - findPaymentDetailsUsingGET_1
 * /rest/statements/merge/financial/payment/v1.0/list
 * @param accountPeriod - 会计属期（yyyy-MM）
 * @param isVoucher - 是否生成凭证（0.未生成，1.已生成），默认为未生成
 * @param page - 页数
 * @param pageSize - 页码
 */
export const paymentList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/payment/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  queryParameters['isVoucher'] = parameters['isVoucher'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 统计银行流水，现金流水 - findPaymentTotalsUsingGET_1
 * /rest/statements/merge/financial/payment/v1.0/totals
 * @param accountPeriod - 会计属期（yyyy-MM）
 * @param businessType - 业务类型（1.银行流水,  2.现金收付）
 * @param isVoucher - 是否生成凭证（0.未生成，1.已生成），默认为未生成
 */
export const paymentTotals = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/payment/v1.0/totals'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  queryParameters['businessType'] = parameters['businessType'];
  if (parameters['businessType'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: businessType'))
  }
  queryParameters['isVoucher'] = parameters['isVoucher'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}