/*
 * @Description:
 * @version:
 * @Company: 海闻软件
 * @Author: 马赛
 * @Date: 2019-01-14 15:13:41
 * @LastEditors: 郑启旭
 * @LastEditTime: 2019-01-23 16:43:33
 */
import request from '@/utils/request';

/**
 *删除还款单
 *
 * @export
 * @param {*} repayId 还款单id
 * @returns
 */
// export function deleteRepay(repayId) {
//   return request.delete(`/rest/proxy/account/repayAccount/v1.0/delete/${repayId}`);
// };

/**
 *获取记录(单条)
 *
 * @export
 * @param {*} repayId 还款id
 * @returns
 */
export function getRepay(repayId) {
  return request.get(`/rest/proxy/account/repayAccount/v1.0/get/${repayId}`);
}

/**
* 新增还款单
*/
// export function postRepay(params) {
//   return request.post('/rest/proxy/account/repayAccount/v1.0/insert', params);
// };

/**
 *还款列表
 *
 */
export function getRepayList(params) {
  return request.get('/rest/proxy/account/repayAccount/v1.0/list', { params });
}

/**
* 修改还款单
*/
export function putRepay(params) {
  return request.put(`/rest/proxy/account/repayAccount/v1.0/update/${params.repayId}`, params);
}

/**
* 还款单付款
*/
export function updatePay(params) {
  return request.put(`/rest/proxy/account/repayAccount/v1.0/updatePay/${params.repayId}`, params);
}
