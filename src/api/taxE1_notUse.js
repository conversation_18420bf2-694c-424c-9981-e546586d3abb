/* eslint-disable */
import request from '@/utils/request';
/**
 * 根据票面名称记忆匹配或推荐商品 - findGoodRelUsingGET_1
 * /rest/proxy/tax/merge/invoice/goodRel/v1.0/list
 * @param invoiceGoodsName - 票面名称
 */
export const goodRelList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/goodRel/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['invoiceGoodsName']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 手工新增进项发票 - 
 * /rest/proxy/tax/merge/invoice/input/v1.0
 * @param invoice - invoice
 */
export const insertInvoiceUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['invoice']
  if (parameters['invoice'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: invoice'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 采集发票 - downLoadInvoiceUsingPOST_1
 * /rest/proxy/tax/merge/invoice/input/v1.0/downloadInvoice
 * @param vo - vo
 */
export const inputDownloadinvoice = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/downloadInvoice'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['vo']
  if (parameters['vo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 手工新增进项发票草稿 - insertInvoiceDraftUsingPOST
 * /rest/proxy/tax/merge/invoice/input/v1.0/draft
 * @param invoice - invoice
 */
export const inputDraft = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/draft'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['invoice']
  if (parameters['invoice'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: invoice'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 修改进项发票草稿 - updateInvoiceDraftUsingPUT
 * /rest/proxy/tax/merge/invoice/input/v1.0/draft/{invoiceId}
 * @param invoice - invoice
 * @param invoiceId - 进项发票ID
 */
export const updateInvoiceDraft = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/draft/{invoiceId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['invoice']
  if (parameters['invoice'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: invoice'))
  }
  path = path.replace('{invoiceId}', `${parameters['invoiceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 查找上一张或下一张凭证 - findLastOrNextVoucherUsingGET_1
 * /rest/proxy/tax/merge/invoice/input/v1.0/findLastOrNextVoucher/{lastOrNext}
 * @param accountPeriod - 会计周期
 * @param certifyPeriodFrom - 属期开始
 * @param certifyPeriodTo - 属期结束
 * @param createType - 合并方式: {4: 一张发票一张凭证, 5: ...商品不合并, 1: ...商品名称相同合并, 2: ...商品名称及单价相同合并, 3: 合并成一张凭证}
 * @param dateType - 凭证日期: {0: 发票日期, 1: 本月最后一天}
 * @param detailSearchFlag - 明细抓取标志1、待处理、2成功、3失败、4手动补充
 * @param dirSort - 
 * @param fieldSort - 
 * @param goodsType - 商品类别（1.库存商品、2.半成品、3.原材料）
 * @param groupModelSize - 以规格组合
 * @param groupSaleunitName - 以单位组合
 * @param invoiceCode - 发票代码
 * @param invoiceDateFrom - 开票开始日期
 * @param invoiceDateTo - 开票结束日期
 * @param invoiceGoodsName - 票面货物名称
 * @param invoiceItemsId - 
 * @param invoiceNumber - 发票号码
 * @param invoiceType - 发票类型（字典 1、专票；2、可抵扣普票；3、不可抵扣普票）
 * @param isAccount - 是否做账
 * @param isDefaultSort - 
 * @param isQtyGtZero - 是否数量大于0
 * @param isVoucher - 是否已生成凭证
 * @param lastOrNext - 凭证号
 * @param notaxCostAmountFrom - 不含税金额起
 * @param notaxCostAmountTo - 不含税金额止
 * @param sellerName - 销方名称
 * @param subjectType - 借方科目: {0: 往来科目, 1: 库存现金}
 * @param tickStatus - 勾选状态，1：勾选、0：未勾选
 * @param usedStatus - 使用状态，1：使用、0：未使用
 * @param voucherCode - 凭证号
 * @param voucherId - 凭证ID
 */
export const inputFindlastornextvoucher = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/findLastOrNextVoucher/{lastOrNext}'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['certifyPeriodFrom'] = parameters['certifyPeriodFrom'];
  queryParameters['certifyPeriodTo'] = parameters['certifyPeriodTo'];
  queryParameters['createType'] = parameters['createType'];
  queryParameters['dateType'] = parameters['dateType'];
  queryParameters['detailSearchFlag'] = parameters['detailSearchFlag'];
  queryParameters['dirSort'] = parameters['dirSort'];
  queryParameters['fieldSort'] = parameters['fieldSort'];
  queryParameters['goodsType'] = parameters['goodsType'];
  queryParameters['groupModelSize'] = parameters['groupModelSize'];
  queryParameters['groupSaleunitName'] = parameters['groupSaleunitName'];
  queryParameters['invoiceCode'] = parameters['invoiceCode'];
  queryParameters['invoiceDateFrom'] = parameters['invoiceDateFrom'];
  queryParameters['invoiceDateTo'] = parameters['invoiceDateTo'];
  queryParameters['invoiceGoodsName'] = parameters['invoiceGoodsName'];
  queryParameters['invoiceItemsId'] = parameters['invoiceItemsId'];
  queryParameters['invoiceNumber'] = parameters['invoiceNumber'];
  queryParameters['invoiceType'] = parameters['invoiceType'];
  queryParameters['isAccount'] = parameters['isAccount'];
  queryParameters['isDefaultSort'] = parameters['isDefaultSort'];
  queryParameters['isQtyGtZero'] = parameters['isQtyGtZero'];
  queryParameters['isVoucher'] = parameters['isVoucher'];
  path = path.replace('{lastOrNext}', `${parameters['lastOrNext']}`)
  queryParameters['notaxCostAmountFrom'] = parameters['notaxCostAmountFrom'];
  queryParameters['notaxCostAmountTo'] = parameters['notaxCostAmountTo'];
  queryParameters['sellerName'] = parameters['sellerName'];
  queryParameters['subjectType'] = parameters['subjectType'];
  queryParameters['tickStatus'] = parameters['tickStatus'];
  queryParameters['usedStatus'] = parameters['usedStatus'];
  queryParameters['voucherCode'] = parameters['voucherCode'];
  queryParameters['voucherId'] = parameters['voucherId'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询账套税局关联登录信息 - getTaxLoginInfoUsingGET_1
 * /rest/proxy/tax/merge/invoice/input/v1.0/getTaxLoginInfo
 */
export const inputGettaxlogininfo = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/getTaxLoginInfo'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获取进项额度 - getInputAmountUsingGET_1
 * /rest/proxy/tax/merge/invoice/input/v1.0/inputAmount/{accountPeriod}
 * @param accountPeriod - 会计周期
 */
export const inputInputamount = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/inputAmount/{accountPeriod}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 按条件查找进项发票列表 - findInvoiceListUsingGET
 * /rest/proxy/tax/merge/invoice/input/v1.0/list
 * @param accountPeriod - 会计周期
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param invoiceCode - 发票代码
 * @param invoiceDateFrom - 开票开始日期
 * @param invoiceDateTo - 开票结束日期
 * @param invoiceNumber - 发票号码
 * @param invoiceType - 发票类型（字典 1、专票；2、可抵扣普票；3、不可抵扣普票）
 * @param isVoucher - 是否已生成凭证（当true时只查询生成凭证的数据）
 * @param notaxCostAmountFrom - 不含税金额起
 * @param notaxCostAmountTo - 不含税金额止
 * @param page - 
 * @param pageSize - 
 * @param sellerName - 销方名称
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const inputList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['invoiceCode'] = parameters['invoiceCode'];
  queryParameters['invoiceDateFrom'] = parameters['invoiceDateFrom'];
  queryParameters['invoiceDateTo'] = parameters['invoiceDateTo'];
  queryParameters['invoiceNumber'] = parameters['invoiceNumber'];
  queryParameters['invoiceType'] = parameters['invoiceType'];
  queryParameters['isVoucher'] = parameters['isVoucher'];
  queryParameters['notaxCostAmountFrom'] = parameters['notaxCostAmountFrom'];
  queryParameters['notaxCostAmountTo'] = parameters['notaxCostAmountTo'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['sellerName'] = parameters['sellerName'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询所有入账货物为空的细单 - selectAllGoodsIsNullUsingGET_1
 * /rest/proxy/tax/merge/invoice/input/v1.0/list/allGoodsIsNull
 * @param accountPeriod - 会计周期
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const selectAllGoodsIsNull = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/list/allGoodsIsNull'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 批量删除进项发票 - deleteBatchUsingDELETE
 * /rest/proxy/tax/merge/invoice/input/v1.0/list/batchDel
 * @param invoiceIds - 发票ID 批量删除 用逗号隔开
 */
export const deleteBatch= function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/list/batchDel'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['invoiceIds']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 检查是否能生成凭证 - checkCreatVoucherUsingGET
 * /rest/proxy/tax/merge/invoice/input/v1.0/list/checkCreatVoucher
 */
export const checkCreatVoucherInput = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/list/checkCreatVoucher'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 生成凭证 - createVoucherUsingPUT
 * /rest/proxy/tax/merge/invoice/input/v1.0/list/createVoucher/{createType}/{dateType}/{subjectType}
 * @param createType - 合并方式: {4: 一张发票一张凭证, 5: ...商品不合并, 1: ...商品名称相同合并, 2: ...商品名称及单价相同合并, 3: 合并成一张凭证}
 * @param data - 
 * @param dateType - 凭证日期: {0: 发票日期, 1: 本月最后一天}
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param subjectType - 贷方科目: {0: 往来科目, 1: 库存现金}
 * @param take - 
 */
export const createVoucher= function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/list/createVoucher/{createType}/{dateType}/{subjectType}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{createType}', `${parameters['createType']}`)
  queryParameters['data'] = parameters['data'];
  path = path.replace('{dateType}', `${parameters['dateType']}`)
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  path = path.replace('{subjectType}', `${parameters['subjectType']}`)
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 查找没有属期的进项发票列表 - findPeridIsNullUsingGET
 * /rest/proxy/tax/merge/invoice/input/v1.0/list/findPeridIsNull
 * @param chargeAmountStatus - false为只查询未交费
 * @param companyName - 客户名称
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 * @param yearId - 年份
 */
export const findPeridIsNull = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/list/findPeridIsNull'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['chargeAmountStatus'] = parameters['chargeAmountStatus'];
  queryParameters['companyName'] = parameters['companyName'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  queryParameters['yearId'] = parameters['yearId'];
  if (parameters['yearId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: yearId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 重新获取发票明细 - getDetailAgainUsingPUT
 * /rest/proxy/tax/merge/invoice/input/v1.0/list/getDetailAgain
 * @param criteria - 批量操作的ID集合
 */
export const getDetailAgain = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/list/getDetailAgain'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['criteria']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 根据凭证ID集合查询进项发票 - getListByVoucherIdUsingGET
 * /rest/proxy/tax/merge/invoice/input/v1.0/list/getListByVoucherId
 * @param accountPeriod - 会计周期
 * @param certifyPeriodFrom - 属期开始
 * @param certifyPeriodTo - 属期结束
 * @param createType - 合并方式: {4: 一张发票一张凭证, 5: ...商品不合并, 1: ...商品名称相同合并, 2: ...商品名称及单价相同合并, 3: 合并成一张凭证}
 * @param dateType - 凭证日期: {0: 发票日期, 1: 本月最后一天}
 * @param detailSearchFlag - 明细抓取标志1、待处理、2成功、3失败、4手动补充
 * @param dirSort - 
 * @param fieldSort - 
 * @param goodsType - 商品类别（1.库存商品、2.半成品、3.原材料）
 * @param groupModelSize - 以规格组合
 * @param groupSaleunitName - 以单位组合
 * @param invoiceCode - 发票代码
 * @param invoiceDateFrom - 开票开始日期
 * @param invoiceDateTo - 开票结束日期
 * @param invoiceGoodsName - 票面货物名称
 * @param invoiceItemsId - 
 * @param invoiceNumber - 发票号码
 * @param invoiceType - 发票类型（字典 1、专票；2、可抵扣普票；3、不可抵扣普票）
 * @param isAccount - 是否做账
 * @param isDefaultSort - 
 * @param isQtyGtZero - 是否数量大于0
 * @param isVoucher - 是否已生成凭证
 * @param notaxCostAmountFrom - 不含税金额起
 * @param notaxCostAmountTo - 不含税金额止
 * @param sellerName - 销方名称
 * @param subjectType - 借方科目: {0: 往来科目, 1: 库存现金}
 * @param tickStatus - 勾选状态，1：勾选、0：未勾选
 * @param usedStatus - 使用状态，1：使用、0：未使用
 * @param voucherCode - 凭证号
 * @param voucherId - 凭证ID
 */
export const getListByVoucherId = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/list/getListByVoucherId'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['certifyPeriodFrom'] = parameters['certifyPeriodFrom'];
  queryParameters['certifyPeriodTo'] = parameters['certifyPeriodTo'];
  queryParameters['createType'] = parameters['createType'];
  queryParameters['dateType'] = parameters['dateType'];
  queryParameters['detailSearchFlag'] = parameters['detailSearchFlag'];
  queryParameters['dirSort'] = parameters['dirSort'];
  queryParameters['fieldSort'] = parameters['fieldSort'];
  queryParameters['goodsType'] = parameters['goodsType'];
  queryParameters['groupModelSize'] = parameters['groupModelSize'];
  queryParameters['groupSaleunitName'] = parameters['groupSaleunitName'];
  queryParameters['invoiceCode'] = parameters['invoiceCode'];
  queryParameters['invoiceDateFrom'] = parameters['invoiceDateFrom'];
  queryParameters['invoiceDateTo'] = parameters['invoiceDateTo'];
  queryParameters['invoiceGoodsName'] = parameters['invoiceGoodsName'];
  queryParameters['invoiceItemsId'] = parameters['invoiceItemsId'];
  queryParameters['invoiceNumber'] = parameters['invoiceNumber'];
  queryParameters['invoiceType'] = parameters['invoiceType'];
  queryParameters['isAccount'] = parameters['isAccount'];
  queryParameters['isDefaultSort'] = parameters['isDefaultSort'];
  queryParameters['isQtyGtZero'] = parameters['isQtyGtZero'];
  queryParameters['isVoucher'] = parameters['isVoucher'];
  queryParameters['notaxCostAmountFrom'] = parameters['notaxCostAmountFrom'];
  queryParameters['notaxCostAmountTo'] = parameters['notaxCostAmountTo'];
  queryParameters['sellerName'] = parameters['sellerName'];
  queryParameters['subjectType'] = parameters['subjectType'];
  queryParameters['tickStatus'] = parameters['tickStatus'];
  queryParameters['usedStatus'] = parameters['usedStatus'];
  queryParameters['voucherCode'] = parameters['voucherCode'];
  queryParameters['voucherId'] = parameters['voucherId'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 确认做账/取消做账 - updateTickStatusUsingPUT
 * /rest/proxy/tax/merge/invoice/input/v1.0/list/invoiceTick/{tickStatus}
 * @param accountPeriod - 属期
 * @param invoiceList - invoiceList
 * @param tickStatus - 勾选状态
 */
export const updateTickStatus = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/list/invoiceTick/{tickStatus}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['accountPeriod']
  body = parameters['invoiceList']
  if (parameters['invoiceList'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: invoiceList'))
  }
  path = path.replace('{tickStatus}', `${parameters['tickStatus']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 预览生成凭证 - viewVoucherUsingGET
 * /rest/proxy/tax/merge/invoice/input/v1.0/list/previewVoucher/{createType}/{dateType}/{subjectType}
 * @param createType - 合并方式: {4: 一张发票一张凭证, 5: ...商品不合并, 1: ...商品名称相同合并, 2: ...商品名称及单价相同合并, 3: 合并成一张凭证}
 * @param data - 
 * @param dateType - 凭证日期: {0: 发票日期, 1: 本月最后一天}
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param subjectType - 借方科目: {0: 往来科目, 1: 库存现金}
 * @param take - 
 */
export const viewVoucher = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/list/previewVoucher/{createType}/{dateType}/{subjectType}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{createType}', `${parameters['createType']}`)
  queryParameters['data'] = parameters['data'];
  path = path.replace('{dateType}', `${parameters['dateType']}`)
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  path = path.replace('{subjectType}', `${parameters['subjectType']}`)
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询单位不一致的细单 - selectSaleunitIsNeqtUsingGET_1
 * /rest/proxy/tax/merge/invoice/input/v1.0/list/selectSaleunitIsNeqt
 * @param accountPeriod - 会计周期
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const selectSaleunitIsNeqt = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/list/selectSaleunitIsNeqt'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 登录获取企业选择列表 - loginTaxUsingPOST_1
 * /rest/proxy/tax/merge/invoice/input/v1.0/loginTax
 * @param provinceCode - 省份code
 * @param provinceName - 省份名字
 * @param userName - 用户名
 * @param userPasswd - 密码
 * @param vo - vo
 */
export const inputLogintax = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/loginTax'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['provinceCode'] = parameters['provinceCode'];
  if (parameters['provinceCode'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: provinceCode'))
  }
  queryParameters['provinceName'] = parameters['provinceName'];
  if (parameters['provinceName'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: provinceName'))
  }
  queryParameters['userName'] = parameters['userName'];
  if (parameters['userName'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: userName'))
  }
  queryParameters['userPasswd'] = parameters['userPasswd'];
  if (parameters['userPasswd'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: userPasswd'))
  }
  body = parameters['vo']
  if (parameters['vo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 保存账套税局关联登录信息 - saveTaxLoginInfoUsingPOST_1
 * /rest/proxy/tax/merge/invoice/input/v1.0/saveTaxLoginInfo
 * @param vo - vo
 */
export const inputSavetaxlogininfo = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/saveTaxLoginInfo'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['vo']
  if (parameters['vo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 批量更新入账货物 - updateAllGoodsIsNullUsingPUT_1
 * /rest/proxy/tax/merge/invoice/input/v1.0/updateGoods
 * @param proxyInvoiceInputItems - proxyInvoiceInputItems
 */
export const inputUpdategoods = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/updateGoods'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['proxyInvoiceInputItems']
  if (parameters['proxyInvoiceInputItems'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: proxyInvoiceInputItems'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 数量换算 - updateQtySaleunitIsNeqtUsingPUT_1
 * /rest/proxy/tax/merge/invoice/input/v1.0/updateQtySaleunitIsNeqt
 * @param proxyInvoiceInputItems - proxyInvoiceInputItems
 */
export const inputUpdateqtysaleunitisneqt = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/updateQtySaleunitIsNeqt'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['proxyInvoiceInputItems']
  if (parameters['proxyInvoiceInputItems'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: proxyInvoiceInputItems'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 根据id查询进项发票 - 
 * /rest/proxy/tax/merge/invoice/input/v1.0/{invoiceId}
 * @param invoiceId - 进项发票ID
 */
export const getInvoiceUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/{invoiceId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{invoiceId}', `${parameters['invoiceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改进项发票 - 
 * /rest/proxy/tax/merge/invoice/input/v1.0/{invoiceId}
 * @param invoice - invoice
 * @param invoiceId - 进项发票ID
 */
export const updateInvoiceUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/{invoiceId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['invoice']
  if (parameters['invoice'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: invoice'))
  }
  path = path.replace('{invoiceId}', `${parameters['invoiceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 删除进项发票 - 
 * /rest/proxy/tax/merge/invoice/input/v1.0/{invoiceId}
 * @param invoiceId - 进项发票ID
 */
export const deleteInvoiceUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v1.0/{invoiceId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{invoiceId}', `${parameters['invoiceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 待勾选清单导入V2 - importDeductionListExcelV2UsingPOST
 * /rest/proxy/tax/merge/invoice/input/v2.0/importDeductionExcel
 */
export const inputImportdeductionexcel = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v2.0/importDeductionExcel'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 确认结果导入V2 - importVerifyExcelV2UsingPOST
 * /rest/proxy/tax/merge/invoice/input/v2.0/importVerifyExcel
 */
export const inputImportverifyexcel = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v2.0/importVerifyExcel'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 认证结果Xml导入V2 - importXmlV2UsingPOST
 * /rest/proxy/tax/merge/invoice/input/v2.0/importXml/{accountPeriod}
 * @param accountPeriod - 会计周期
 */
export const inputImportxml = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/input/v2.0/importXml/{accountPeriod}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 识别发票（上传文件方式） - autoRecognizeInvoiceUsingPOST_1
 * /rest/proxy/tax/merge/invoice/ocr/v1.0/glority/frm
 * @param imageFile - 图片文件
 */
export const ocrGlority = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/ocr/v1.0/glority/frm'
  let body
  let queryParameters = {}
  let form = {}
  form['imageFile'] = parameters['imageFile']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 识别发票（图片base64字符方式） - autoRecognizeInvoiceUsingPOST_3
 * /rest/proxy/tax/merge/invoice/ocr/v1.0/glority/get
 * @param imageData - 发票
 */
export const autoRecognizeInvoice = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/ocr/v1.0/glority/get'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['imageData']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 新增手工销项发票 - 
 * /rest/proxy/tax/merge/invoice/output/v1.0
 * @param proxyInvoiceOutput - proxyInvoiceOutput
 */
export const insertInvoiceOutputUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['proxyInvoiceOutput']
  if (parameters['proxyInvoiceOutput'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: proxyInvoiceOutput'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 采集发票 - downLoadInvoiceUsingPOST_3
 * /rest/proxy/tax/merge/invoice/output/v1.0/downloadInvoice
 * @param vo - vo
 */
export const outputDownloadinvoice = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/downloadInvoice'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['vo']
  if (parameters['vo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: vo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 新增手工销项发票草稿 - insertInvoiceOutputDraftUsingPOST
 * /rest/proxy/tax/merge/invoice/output/v1.0/draft
 * @param proxyInvoiceOutput - proxyInvoiceOutput
 */
export const outputDraft = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/draft'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['proxyInvoiceOutput']
  if (parameters['proxyInvoiceOutput'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: proxyInvoiceOutput'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 修改销项发票草稿 - updateInvoiceOutputDraftUsingPUT
 * /rest/proxy/tax/merge/invoice/output/v1.0/draft/{invoiceId}
 * @param invoiceId - 销项发票ID
 * @param proxyInvoiceOutput - proxyInvoiceOutput
 */
export const updateInvoiceOutputDraft = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/draft/{invoiceId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{invoiceId}', `${parameters['invoiceId']}`)
  body = parameters['proxyInvoiceOutput']
  if (parameters['proxyInvoiceOutput'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: proxyInvoiceOutput'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 查找上一张或下一张凭证 - findLastOrNextVoucherUsingGET_3
 * /rest/proxy/tax/merge/invoice/output/v1.0/findLastOrNextVoucher/{lastOrNext}
 * @param accountPeriod - 会计周期
 * @param checkCode - 明细抓取校验码
 * @param detailSearchFlag - 明细抓取标志1、待处理、2成功、3失败、4手动补充
 * @param dirSort - 
 * @param fieldSort - 
 * @param goodsType - 商品类别（1.库存商品、2.半成品、3.原材料）
 * @param groupModelSize - 以规格组合
 * @param groupSaleunitName - 以单位组合
 * @param invoiceCode - 发票代码
 * @param invoiceDateFrom - 开票开始日期
 * @param invoiceDateTo - 开票结束日期
 * @param invoiceGoodsName - 票面货物名称
 * @param invoiceItemsId - 
 * @param invoiceNumber - 发票号码
 * @param invoiceType - 发票类型（字典 1、专票；2、普票）
 * @param isDefaultSort - 
 * @param isQtyGtZero - 是否数量大于0
 * @param isVoucher - 是否已生成凭证
 * @param lastOrNext - 凭证号
 * @param purchaserName - 购方企业名称
 * @param saleAmountFrom - 查询金额起
 * @param saleAmountTo - 查询金额止
 * @param taxRate - 税率
 * @param voucherCode - 凭证号
 * @param voucherId - 凭证ID
 */
export const outputFindlastornextvoucher = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/findLastOrNextVoucher/{lastOrNext}'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['checkCode'] = parameters['checkCode'];
  queryParameters['detailSearchFlag'] = parameters['detailSearchFlag'];
  queryParameters['dirSort'] = parameters['dirSort'];
  queryParameters['fieldSort'] = parameters['fieldSort'];
  queryParameters['goodsType'] = parameters['goodsType'];
  queryParameters['groupModelSize'] = parameters['groupModelSize'];
  queryParameters['groupSaleunitName'] = parameters['groupSaleunitName'];
  queryParameters['invoiceCode'] = parameters['invoiceCode'];
  queryParameters['invoiceDateFrom'] = parameters['invoiceDateFrom'];
  queryParameters['invoiceDateTo'] = parameters['invoiceDateTo'];
  queryParameters['invoiceGoodsName'] = parameters['invoiceGoodsName'];
  queryParameters['invoiceItemsId'] = parameters['invoiceItemsId'];
  queryParameters['invoiceNumber'] = parameters['invoiceNumber'];
  queryParameters['invoiceType'] = parameters['invoiceType'];
  queryParameters['isDefaultSort'] = parameters['isDefaultSort'];
  queryParameters['isQtyGtZero'] = parameters['isQtyGtZero'];
  queryParameters['isVoucher'] = parameters['isVoucher'];
  path = path.replace('{lastOrNext}', `${parameters['lastOrNext']}`)
  queryParameters['purchaserName'] = parameters['purchaserName'];
  queryParameters['saleAmountFrom'] = parameters['saleAmountFrom'];
  queryParameters['saleAmountTo'] = parameters['saleAmountTo'];
  queryParameters['taxRate'] = parameters['taxRate'];
  queryParameters['voucherCode'] = parameters['voucherCode'];
  queryParameters['voucherId'] = parameters['voucherId'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导入销项发票 - importOutputExcelUsingPOST
 * /rest/proxy/tax/merge/invoice/output/v1.0/importOutputExcel/{type}
 * @param type - 导入类型:(1:航信,2:百旺)
 */
export const outputImportoutputexcel = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/importOutputExcel/{type}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{type}', `${parameters['type']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查找销项发票列表 - findInvoiceOutputListUsingGET
 * /rest/proxy/tax/merge/invoice/output/v1.0/list
 * @param accountPeriod - 会计周期
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param invoiceCode - 发票代码
 * @param invoiceDateFrom - 开票开始日期
 * @param invoiceDateTo - 开票结束日期
 * @param invoiceNumber - 发票号码
 * @param invoiceType - 发票类型（字典 1、专票；2、普票）
 * @param isVoucher - 是否已生成凭证（当true时只查询生成凭证的数据）
 * @param page - 
 * @param pageSize - 
 * @param purchaserName - 购方企业名称
 * @param saleAmountFrom - 金额起
 * @param saleAmountTo - 金额止
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const outputList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['invoiceCode'] = parameters['invoiceCode'];
  queryParameters['invoiceDateFrom'] = parameters['invoiceDateFrom'];
  queryParameters['invoiceDateTo'] = parameters['invoiceDateTo'];
  queryParameters['invoiceNumber'] = parameters['invoiceNumber'];
  queryParameters['invoiceType'] = parameters['invoiceType'];
  queryParameters['isVoucher'] = parameters['isVoucher'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['purchaserName'] = parameters['purchaserName'];
  queryParameters['saleAmountFrom'] = parameters['saleAmountFrom'];
  queryParameters['saleAmountTo'] = parameters['saleAmountTo'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询所有入账货物为空的细单 - selectAllGoodsIsNullUsingGET_3
 * /rest/proxy/tax/merge/invoice/output/v1.0/list/allGoodsIsNull
 * @param accountPeriod - 会计周期
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const allGoodsIsNull = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/list/allGoodsIsNull'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 批量删除销项发票 - deleteBatchUsingDELETE_1
 * /rest/proxy/tax/merge/invoice/output/v1.0/list/batchDel
 * @param invoiceIds - 发票ID 批量删除 用逗号隔开
 */
export const batchDel = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/list/batchDel'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['invoiceIds']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 检查是否能生成凭证 - checkCreatVoucherUsingGET_1
 * /rest/proxy/tax/merge/invoice/output/v1.0/list/checkCreatVoucher
 */
export const checkCreatVoucherOutput = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/list/checkCreatVoucher'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 生成凭证 - createVoucherUsingPUT_1
 * /rest/proxy/tax/merge/invoice/output/v1.0/list/createVoucher/{createType}/{dateType}/{subjectType}
 * @param createType - 合并方式: {4: 一张发票一张凭证, 5: ...商品不合并, 1: ...商品名称相同合并, 2: ...商品名称及单价相同合并, 3: 合并成一张凭证}
 * @param data - 
 * @param dateType - 凭证日期: {0: 发票日期, 1: 本月最后一天}
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param subjectType - 借方科目: {0: 往来科目, 1: 库存现金}
 * @param take - 
 */
export const createVoucherOutput = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/list/createVoucher/{createType}/{dateType}/{subjectType}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{createType}', `${parameters['createType']}`)
  queryParameters['data'] = parameters['data'];
  path = path.replace('{dateType}', `${parameters['dateType']}`)
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  path = path.replace('{subjectType}', `${parameters['subjectType']}`)
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 获取需重新采集明细的发票 - getAgainInvoiceByIdsUsingGET
 * /rest/proxy/tax/merge/invoice/output/v1.0/list/getAgainInvoiceByIds
 * @param accountPeriod - 会计周期
 * @param checkCode - 明细抓取校验码
 * @param detailSearchFlag - 明细抓取标志1、待处理、2成功、3失败、4手动补充
 * @param dirSort - 
 * @param fieldSort - 
 * @param goodsType - 商品类别（1.库存商品、2.半成品、3.原材料）
 * @param groupModelSize - 以规格组合
 * @param groupSaleunitName - 以单位组合
 * @param invoiceCode - 发票代码
 * @param invoiceDateFrom - 开票开始日期
 * @param invoiceDateTo - 开票结束日期
 * @param invoiceGoodsName - 票面货物名称
 * @param invoiceItemsId - 
 * @param invoiceNumber - 发票号码
 * @param invoiceType - 发票类型（字典 1、专票；2、普票）
 * @param isDefaultSort - 
 * @param isQtyGtZero - 是否数量大于0
 * @param isVoucher - 是否已生成凭证
 * @param purchaserName - 购方企业名称
 * @param saleAmountFrom - 查询金额起
 * @param saleAmountTo - 查询金额止
 * @param taxRate - 税率
 * @param voucherCode - 凭证号
 * @param voucherId - 凭证ID
 */
export const outpgetAgainInvoiceByIds = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/list/getAgainInvoiceByIds'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['checkCode'] = parameters['checkCode'];
  queryParameters['detailSearchFlag'] = parameters['detailSearchFlag'];
  queryParameters['dirSort'] = parameters['dirSort'];
  queryParameters['fieldSort'] = parameters['fieldSort'];
  queryParameters['goodsType'] = parameters['goodsType'];
  queryParameters['groupModelSize'] = parameters['groupModelSize'];
  queryParameters['groupSaleunitName'] = parameters['groupSaleunitName'];
  queryParameters['invoiceCode'] = parameters['invoiceCode'];
  queryParameters['invoiceDateFrom'] = parameters['invoiceDateFrom'];
  queryParameters['invoiceDateTo'] = parameters['invoiceDateTo'];
  queryParameters['invoiceGoodsName'] = parameters['invoiceGoodsName'];
  queryParameters['invoiceItemsId'] = parameters['invoiceItemsId'];
  queryParameters['invoiceNumber'] = parameters['invoiceNumber'];
  queryParameters['invoiceType'] = parameters['invoiceType'];
  queryParameters['isDefaultSort'] = parameters['isDefaultSort'];
  queryParameters['isQtyGtZero'] = parameters['isQtyGtZero'];
  queryParameters['isVoucher'] = parameters['isVoucher'];
  queryParameters['purchaserName'] = parameters['purchaserName'];
  queryParameters['saleAmountFrom'] = parameters['saleAmountFrom'];
  queryParameters['saleAmountTo'] = parameters['saleAmountTo'];
  queryParameters['taxRate'] = parameters['taxRate'];
  queryParameters['voucherCode'] = parameters['voucherCode'];
  queryParameters['voucherId'] = parameters['voucherId'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 重新获取发票明细 - getDetailAgainUsingPUT_1
 * /rest/proxy/tax/merge/invoice/output/v1.0/list/getDetailAgain
 * @param criteria - 批量操作的ID集合
 */
export const getDetailAgainOutput = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/list/getDetailAgain'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['criteria']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 根据凭证id集合查询销项发票 - getListByVoucherIdUsingGET_1
 * /rest/proxy/tax/merge/invoice/output/v1.0/list/getListByVoucherId
 * @param accountPeriod - 会计周期
 * @param checkCode - 明细抓取校验码
 * @param detailSearchFlag - 明细抓取标志1、待处理、2成功、3失败、4手动补充
 * @param dirSort - 
 * @param fieldSort - 
 * @param goodsType - 商品类别（1.库存商品、2.半成品、3.原材料）
 * @param groupModelSize - 以规格组合
 * @param groupSaleunitName - 以单位组合
 * @param invoiceCode - 发票代码
 * @param invoiceDateFrom - 开票开始日期
 * @param invoiceDateTo - 开票结束日期
 * @param invoiceGoodsName - 票面货物名称
 * @param invoiceItemsId - 
 * @param invoiceNumber - 发票号码
 * @param invoiceType - 发票类型（字典 1、专票；2、普票）
 * @param isDefaultSort - 
 * @param isQtyGtZero - 是否数量大于0
 * @param isVoucher - 是否已生成凭证
 * @param purchaserName - 购方企业名称
 * @param saleAmountFrom - 查询金额起
 * @param saleAmountTo - 查询金额止
 * @param taxRate - 税率
 * @param voucherCode - 凭证号
 * @param voucherId - 凭证ID
 */
export const getListByVoucherIdOutput= function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/list/getListByVoucherId'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['checkCode'] = parameters['checkCode'];
  queryParameters['detailSearchFlag'] = parameters['detailSearchFlag'];
  queryParameters['dirSort'] = parameters['dirSort'];
  queryParameters['fieldSort'] = parameters['fieldSort'];
  queryParameters['goodsType'] = parameters['goodsType'];
  queryParameters['groupModelSize'] = parameters['groupModelSize'];
  queryParameters['groupSaleunitName'] = parameters['groupSaleunitName'];
  queryParameters['invoiceCode'] = parameters['invoiceCode'];
  queryParameters['invoiceDateFrom'] = parameters['invoiceDateFrom'];
  queryParameters['invoiceDateTo'] = parameters['invoiceDateTo'];
  queryParameters['invoiceGoodsName'] = parameters['invoiceGoodsName'];
  queryParameters['invoiceItemsId'] = parameters['invoiceItemsId'];
  queryParameters['invoiceNumber'] = parameters['invoiceNumber'];
  queryParameters['invoiceType'] = parameters['invoiceType'];
  queryParameters['isDefaultSort'] = parameters['isDefaultSort'];
  queryParameters['isQtyGtZero'] = parameters['isQtyGtZero'];
  queryParameters['isVoucher'] = parameters['isVoucher'];
  queryParameters['purchaserName'] = parameters['purchaserName'];
  queryParameters['saleAmountFrom'] = parameters['saleAmountFrom'];
  queryParameters['saleAmountTo'] = parameters['saleAmountTo'];
  queryParameters['taxRate'] = parameters['taxRate'];
  queryParameters['voucherCode'] = parameters['voucherCode'];
  queryParameters['voucherId'] = parameters['voucherId'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 预览生成凭证 - previewVoucherUsingGET
 * /rest/proxy/tax/merge/invoice/output/v1.0/list/previewVoucher/{createType}/{dateType}/{subjectType}
 * @param createType - 合并方式: {4: 一张发票一张凭证, 5: ...商品不合并, 1: ...商品名称相同合并, 2: ...商品名称及单价相同合并, 3: 合并成一张凭证}
 * @param data - 
 * @param dateType - 凭证日期: {0: 发票日期, 1: 本月最后一天}
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param subjectType - 借方科目: {0: 往来科目, 1: 库存现金}
 * @param take - 
 */
export const previewVoucher = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/list/previewVoucher/{createType}/{dateType}/{subjectType}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{createType}', `${parameters['createType']}`)
  queryParameters['data'] = parameters['data'];
  path = path.replace('{dateType}', `${parameters['dateType']}`)
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  path = path.replace('{subjectType}', `${parameters['subjectType']}`)
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询单位不一致的细单 - selectSaleunitIsNeqtUsingGET_3
 * /rest/proxy/tax/merge/invoice/output/v1.0/list/selectSaleunitIsNeqt
 * @param accountPeriod - 会计周期
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const selectSaleunitIsNeqtOutput = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/list/selectSaleunitIsNeqt'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获取销项额度 - getOutputAmountUsingGET_1
 * /rest/proxy/tax/merge/invoice/output/v1.0/outputAmount/{accountPeriod}
 * @param accountPeriod - 会计周期
 */
export const outputOutputamount = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/outputAmount/{accountPeriod}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获取销项商品信息 - queryGoodsByAccountPeriodUsingGET_1
 * /rest/proxy/tax/merge/invoice/output/v1.0/outputGoods/{accountPeriod}
 * @param accountPeriod - 会计周期
 */
export const outputOutputgoods = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/outputGoods/{accountPeriod}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改外销相关信息 - updateForeignCurrencyUsingPUT
 * /rest/proxy/tax/merge/invoice/output/v1.0/updateForeignCurrency
 * @param proxyInvoiceOutputs - proxyInvoiceOutputs
 */
export const outputUpdateforeigncurrency = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/updateForeignCurrency'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['proxyInvoiceOutputs']
  if (parameters['proxyInvoiceOutputs'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: proxyInvoiceOutputs'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 批量更新入账货物 - updateAllGoodsIsNullUsingPUT_3
 * /rest/proxy/tax/merge/invoice/output/v1.0/updateGoods
 * @param proxyInvoiceInputItems - proxyInvoiceInputItems
 */
export const outputUpdategoods = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/updateGoods'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['proxyInvoiceInputItems']
  if (parameters['proxyInvoiceInputItems'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: proxyInvoiceInputItems'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 数量换算 - updateQtySaleunitIsNeqtUsingPUT_3
 * /rest/proxy/tax/merge/invoice/output/v1.0/updateQtySaleunitIsNeqt
 * @param proxyInvoiceInputItems - proxyInvoiceInputItems
 */
export const outputUpdateqtysaleunitisneqt = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/updateQtySaleunitIsNeqt'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['proxyInvoiceInputItems']
  if (parameters['proxyInvoiceInputItems'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: proxyInvoiceInputItems'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 根据id查询销项发票 - 
 * /rest/proxy/tax/merge/invoice/output/v1.0/{invoiceId}
 * @param invoiceId - 销项发票ID
 */
export const getInvoiceUsingGET_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/{invoiceId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{invoiceId}', `${parameters['invoiceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改销项发票 - 
 * /rest/proxy/tax/merge/invoice/output/v1.0/{invoiceId}
 * @param invoiceId - 销项发票ID
 * @param proxyInvoiceOutput - proxyInvoiceOutput
 */
export const updateInvoiceOutputUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/{invoiceId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{invoiceId}', `${parameters['invoiceId']}`)
  body = parameters['proxyInvoiceOutput']
  if (parameters['proxyInvoiceOutput'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: proxyInvoiceOutput'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 删除销项发票 - 
 * /rest/proxy/tax/merge/invoice/output/v1.0/{invoiceId}
 * @param invoiceId - 销项发票ID
 */
export const deleteInvoiceOutputUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/tax/merge/invoice/output/v1.0/{invoiceId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{invoiceId}', `${parameters['invoiceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}