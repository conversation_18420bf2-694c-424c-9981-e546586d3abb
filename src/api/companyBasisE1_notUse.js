/* eslint-disable */
import request from '@/utils/request';
/**
 * 新增委外加工 - insertUsingPOST_3
 * /rest/proxy/config/goods/process/v1.0/insert
 * @param prcess - 商品实体
 */
export const processInsert = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/process/v1.0/insert'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['prcess']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回全部或部分 - findUsingGET_3
 * /rest/proxy/config/goods/process/v1.0/list
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param processName - 加工名称
 * @param processStatus - 加工状态
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const processList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/process/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['processName'] = parameters['processName'];
  queryParameters['processStatus'] = parameters['processStatus'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改委外加工 - 
 * /rest/proxy/config/goods/process/v1.0/{processId}
 * @param prcess - 商品实体
 * @param processId - processId
 */
export const updateUsingPUT_3 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/process/v1.0/{processId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['prcess']
  path = path.replace('{processId}', `${parameters['processId']}`)
  if (parameters['processId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: processId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 获取商品代码 - getGoodsCodeUsingGET_1
 * /rest/proxy/config/goods/v1.0/generate/goodsCode
 */
export const goodsGenerate = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/generate/goodsCode'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询成本核算正算的商品数据源 - findGoodsSourceUsingGET_1
 * /rest/proxy/config/goods/v1.0/goodsSource
 */
export const goodsGoodssource = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/goodsSource'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导入 - importGoodsUsingPOST_1
 * /rest/proxy/config/goods/v1.0/import
 * @param params - params
 */
export const goodsImport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/import'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['params']
  if (parameters['params'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: params'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 新增商品 - insertUsingPOST_1
 * /rest/proxy/config/goods/v1.0/insert
 * @param cdGoods - 商品实体
 */
export const goodsInsert = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/insert'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['cdGoods']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 批量新增商品 - insertBatchUsingPOST_1
 * /rest/proxy/config/goods/v1.0/insertBatch
 * @param cdGoods - 商品实体
 */
export const goodsInsertbatch = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/insertBatch'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['cdGoods']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回全部或部分商品 - findUsingGET_1
 * /rest/proxy/config/goods/v1.0/list
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param goodsClassId - 商品分类id
 * @param goodsName - 商品名称
 * @param goodsStatus - 商品状态
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const goodsList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['goodsClassId'] = parameters['goodsClassId'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['goodsStatus'] = parameters['goodsStatus'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询构成材料 - findMaterialUsingGET_1
 * /rest/proxy/config/goods/v1.0/material/list/{accountPeriod}
 * @param accountPeriod - accountPeriod
 */
export const goodsMaterial = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/material/list/{accountPeriod}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询计算了权重的构成材料 - findMaterialWithPercentUsingGET_1
 * /rest/proxy/config/goods/v1.0/material/percent/{accountPeriod}
 * @param accountPeriod - accountPeriod
 */
export const findMaterialWithPercent = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/material/percent/{accountPeriod}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改原材料的权重 - updateMaterialPercentUsingPUT_1
 * /rest/proxy/config/goods/v1.0/material/percent/{accountPeriod}
 * @param accountPeriod - accountPeriod
 */
export const updateMaterialPercent = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/material/percent/{accountPeriod}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{accountPeriod}', `${parameters['accountPeriod']}`)
  if (parameters['accountPeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: accountPeriod'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 单记录查询 - 
 * /rest/proxy/config/goods/v1.0/{goodsId}
 * @param goodsId - 商品ID
 */
export const getUsingGET_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/{goodsId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{goodsId}', `${parameters['goodsId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改商品 - 
 * /rest/proxy/config/goods/v1.0/{goodsId}
 * @param cdGoods - 商品实体
 * @param goodsId - goodsId
 */
export const updateUsingPUT_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/goods/v1.0/{goodsId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['cdGoods']
  path = path.replace('{goodsId}', `${parameters['goodsId']}`)
  if (parameters['goodsId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: goodsId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 新增项目 - insertUsingPOST_5
 * /rest/proxy/config/project/v1.0/insert
 * @param info - 项目实体
 */
export const projectInsert = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/project/v1.0/insert'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['info']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查询项目信息 - getGoodsCodeUsingGET_3
 * /rest/proxy/config/project/v1.0/list
 * @param companyId - 公司ID
 * @param projectName - 项目名称
 * @param projectNameLike - 项目名称模糊
 * @param 项目状态（字典0、停用；1、启用；） - 
 */
export const projectList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/project/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['projectName'] = parameters['projectName'];
  queryParameters['projectNameLike'] = parameters['projectNameLike'];
  queryParameters['项目状态（字典  0、停用；1、启用；）'] = parameters['项目状态（字典0、停用；1、启用；）'];
  if (parameters['项目状态（字典0、停用；1、启用；）'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: 项目状态（字典0、停用；1、启用；）'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 单记录查询 - 
 * /rest/proxy/config/project/v1.0/{projectId}
 * @param projectId - 项目ID
 */
export const getUsingGET_3 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/project/v1.0/{projectId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{projectId}', `${parameters['projectId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改项目 - 
 * /rest/proxy/config/project/v1.0/{projectId}
 * @param info - 项目实体
 * @param projectId - projectId
 */
export const updateUsingPUT_5 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/project/v1.0/{projectId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['info']
  path = path.replace('{projectId}', `${parameters['projectId']}`)
  if (parameters['projectId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: projectId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 删除项目 - 
 * /rest/proxy/config/project/v1.0/{projectId}
 * @param projectId - projectId
 */
export const updateUsingDELETE_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/config/project/v1.0/{projectId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{projectId}', `${parameters['projectId']}`)
  if (parameters['projectId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: projectId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}