/* eslint-disable */
import request from '@/utils/request';
/**
 * 语音识别 - getSpeechUsingPOST_1
 * /rest/companyConfig/companyBasis/baiduSpeech/v1.0/getSpeech
 * @param requestHeadersETag - 
 * @param requestHeadersAcceptCharset0Registered - 
 * @param requestHeadersAccept0CharSetRegistered - 
 * @param requestHeadersAccept0CharsetRegistered - 
 * @param requestHeadersAccept0Concrete - 
 * @param requestHeadersAccept0QualityValue - 
 * @param requestHeadersAccept0Subtype - 
 * @param requestHeadersAccept0Type - 
 * @param requestHeadersAccept0WildcardSubtype - 
 * @param requestHeadersAccept0WildcardType - 
 * @param requestHeadersAccessControlAllowCredentials - 
 * @param requestHeadersAccessControlAllowHeaders - 
 * @param requestHeadersAccessControlAllowMethods - 
 * @param requestHeadersAccessControlAllowOrigin - 
 * @param requestHeadersAccessControlExposeHeaders - 
 * @param requestHeadersAccessControlMaxAge - 
 * @param requestHeadersAccessControlRequestHeaders - 
 * @param requestHeadersAccessControlRequestMethod - 
 * @param requestHeadersAllow - 
 * @param requestHeadersCacheControl - 
 * @param requestHeadersConnection - 
 * @param requestHeadersContentLength - 
 * @param requestHeadersContentTypeCharSetRegistered - 
 * @param requestHeadersContentTypeCharsetRegistered - 
 * @param requestHeadersContentTypeConcrete - 
 * @param requestHeadersContentTypeQualityValue - 
 * @param requestHeadersContentTypeSubtype - 
 * @param requestHeadersContentTypeType - 
 * @param requestHeadersContentTypeWildcardSubtype - 
 * @param requestHeadersContentTypeWildcardType - 
 * @param requestHeadersDate - 
 * @param requestHeadersExpires - 
 * @param requestHeadersIfMatch - 
 * @param requestHeadersIfModifiedSince - 
 * @param requestHeadersIfNoneMatch - 
 * @param requestHeadersIfUnmodifiedSince - 
 * @param requestHeadersLastModified - 
 * @param requestHeadersLocationAbsolute - 
 * @param requestHeadersLocationAuthority - 
 * @param requestHeadersLocationFragment - 
 * @param requestHeadersLocationHost - 
 * @param requestHeadersLocationOpaque - 
 * @param requestHeadersLocationPath - 
 * @param requestHeadersLocationPort - 
 * @param requestHeadersLocationQuery - 
 * @param requestHeadersLocationRawAuthority - 
 * @param requestHeadersLocationRawFragment - 
 * @param requestHeadersLocationRawPath - 
 * @param requestHeadersLocationRawQuery - 
 * @param requestHeadersLocationRawSchemeSpecificPart - 
 * @param requestHeadersLocationRawUserInfo - 
 * @param requestHeadersLocationScheme - 
 * @param requestHeadersLocationSchemeSpecificPart - 
 * @param requestHeadersLocationUserInfo - 
 * @param requestHeadersOrigin - 
 * @param requestHeadersPragma - 
 * @param requestHeadersUpgrade - 
 * @param requestHeadersVary - 
 * @param requestMethod - 
 */
export const baiduSpeechGetspeech = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/baiduSpeech/v1.0/getSpeech'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['requestHeaders.ETag'] = parameters['requestHeadersETag'];
  queryParameters['requestHeaders.acceptCharset[0].registered'] = parameters['requestHeadersAcceptCharset0Registered'];
  queryParameters['requestHeaders.accept[0].charSet.registered'] = parameters['requestHeadersAccept0CharSetRegistered'];
  queryParameters['requestHeaders.accept[0].charset.registered'] = parameters['requestHeadersAccept0CharsetRegistered'];
  queryParameters['requestHeaders.accept[0].concrete'] = parameters['requestHeadersAccept0Concrete'];
  queryParameters['requestHeaders.accept[0].qualityValue'] = parameters['requestHeadersAccept0QualityValue'];
  queryParameters['requestHeaders.accept[0].subtype'] = parameters['requestHeadersAccept0Subtype'];
  queryParameters['requestHeaders.accept[0].type'] = parameters['requestHeadersAccept0Type'];
  queryParameters['requestHeaders.accept[0].wildcardSubtype'] = parameters['requestHeadersAccept0WildcardSubtype'];
  queryParameters['requestHeaders.accept[0].wildcardType'] = parameters['requestHeadersAccept0WildcardType'];
  queryParameters['requestHeaders.accessControlAllowCredentials'] = parameters['requestHeadersAccessControlAllowCredentials'];
  queryParameters['requestHeaders.accessControlAllowHeaders'] = parameters['requestHeadersAccessControlAllowHeaders'];
  queryParameters['requestHeaders.accessControlAllowMethods'] = parameters['requestHeadersAccessControlAllowMethods'];
  queryParameters['requestHeaders.accessControlAllowOrigin'] = parameters['requestHeadersAccessControlAllowOrigin'];
  queryParameters['requestHeaders.accessControlExposeHeaders'] = parameters['requestHeadersAccessControlExposeHeaders'];
  queryParameters['requestHeaders.accessControlMaxAge'] = parameters['requestHeadersAccessControlMaxAge'];
  queryParameters['requestHeaders.accessControlRequestHeaders'] = parameters['requestHeadersAccessControlRequestHeaders'];
  queryParameters['requestHeaders.accessControlRequestMethod'] = parameters['requestHeadersAccessControlRequestMethod'];
  queryParameters['requestHeaders.allow'] = parameters['requestHeadersAllow'];
  queryParameters['requestHeaders.cacheControl'] = parameters['requestHeadersCacheControl'];
  queryParameters['requestHeaders.connection'] = parameters['requestHeadersConnection'];
  queryParameters['requestHeaders.contentLength'] = parameters['requestHeadersContentLength'];
  queryParameters['requestHeaders.contentType.charSet.registered'] = parameters['requestHeadersContentTypeCharSetRegistered'];
  queryParameters['requestHeaders.contentType.charset.registered'] = parameters['requestHeadersContentTypeCharsetRegistered'];
  queryParameters['requestHeaders.contentType.concrete'] = parameters['requestHeadersContentTypeConcrete'];
  queryParameters['requestHeaders.contentType.qualityValue'] = parameters['requestHeadersContentTypeQualityValue'];
  queryParameters['requestHeaders.contentType.subtype'] = parameters['requestHeadersContentTypeSubtype'];
  queryParameters['requestHeaders.contentType.type'] = parameters['requestHeadersContentTypeType'];
  queryParameters['requestHeaders.contentType.wildcardSubtype'] = parameters['requestHeadersContentTypeWildcardSubtype'];
  queryParameters['requestHeaders.contentType.wildcardType'] = parameters['requestHeadersContentTypeWildcardType'];
  queryParameters['requestHeaders.date'] = parameters['requestHeadersDate'];
  queryParameters['requestHeaders.expires'] = parameters['requestHeadersExpires'];
  queryParameters['requestHeaders.ifMatch'] = parameters['requestHeadersIfMatch'];
  queryParameters['requestHeaders.ifModifiedSince'] = parameters['requestHeadersIfModifiedSince'];
  queryParameters['requestHeaders.ifNoneMatch'] = parameters['requestHeadersIfNoneMatch'];
  queryParameters['requestHeaders.ifUnmodifiedSince'] = parameters['requestHeadersIfUnmodifiedSince'];
  queryParameters['requestHeaders.lastModified'] = parameters['requestHeadersLastModified'];
  queryParameters['requestHeaders.location.absolute'] = parameters['requestHeadersLocationAbsolute'];
  queryParameters['requestHeaders.location.authority'] = parameters['requestHeadersLocationAuthority'];
  queryParameters['requestHeaders.location.fragment'] = parameters['requestHeadersLocationFragment'];
  queryParameters['requestHeaders.location.host'] = parameters['requestHeadersLocationHost'];
  queryParameters['requestHeaders.location.opaque'] = parameters['requestHeadersLocationOpaque'];
  queryParameters['requestHeaders.location.path'] = parameters['requestHeadersLocationPath'];
  queryParameters['requestHeaders.location.port'] = parameters['requestHeadersLocationPort'];
  queryParameters['requestHeaders.location.query'] = parameters['requestHeadersLocationQuery'];
  queryParameters['requestHeaders.location.rawAuthority'] = parameters['requestHeadersLocationRawAuthority'];
  queryParameters['requestHeaders.location.rawFragment'] = parameters['requestHeadersLocationRawFragment'];
  queryParameters['requestHeaders.location.rawPath'] = parameters['requestHeadersLocationRawPath'];
  queryParameters['requestHeaders.location.rawQuery'] = parameters['requestHeadersLocationRawQuery'];
  queryParameters['requestHeaders.location.rawSchemeSpecificPart'] = parameters['requestHeadersLocationRawSchemeSpecificPart'];
  queryParameters['requestHeaders.location.rawUserInfo'] = parameters['requestHeadersLocationRawUserInfo'];
  queryParameters['requestHeaders.location.scheme'] = parameters['requestHeadersLocationScheme'];
  queryParameters['requestHeaders.location.schemeSpecificPart'] = parameters['requestHeadersLocationSchemeSpecificPart'];
  queryParameters['requestHeaders.location.userInfo'] = parameters['requestHeadersLocationUserInfo'];
  queryParameters['requestHeaders.origin'] = parameters['requestHeadersOrigin'];
  queryParameters['requestHeaders.pragma'] = parameters['requestHeadersPragma'];
  queryParameters['requestHeaders.upgrade'] = parameters['requestHeadersUpgrade'];
  queryParameters['requestHeaders.vary'] = parameters['requestHeadersVary'];
  queryParameters['requestMethod'] = parameters['requestMethod'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回银行账号信息(简单) - getAllUsingGET
 * /rest/companyConfig/companyBasis/bankAccounts/v1.1/all
 */
export const bankAccountsAll = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/bankAccounts/v1.1/all'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 银行账号缓存清理 - clearBatchUsingGET_1
 * /rest/companyConfig/companyBasis/bankAccounts/v1.1/cache/clear
 */
export const bankAccountsCache = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/bankAccounts/v1.1/cache/clear'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回银行账号信息 - findAllUsingGET
 * /rest/companyConfig/companyBasis/bankAccounts/v1.1/list
 */
export const bankAccountsList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/bankAccounts/v1.1/list'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 银行账号信息新增(修改) - saveBankaccountsUsingPOST
 * /rest/companyConfig/companyBasis/bankAccounts/v1.1/save
 * @param accounts - 银行账号实体数组
 */
export const bankAccountsSave = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/bankAccounts/v1.1/save'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['accounts']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 公司会计信息查询 - findCompanyAccountInfoUsingGET
 * /rest/companyConfig/companyBasis/companyTaxInfo/v1.1/accountInfo
 */
export const companyTaxInfoAccountinfo = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companyTaxInfo/v1.1/accountInfo'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 公司会计信息修改 - editAccountInfoUsingPUT
 * /rest/companyConfig/companyBasis/companyTaxInfo/v1.1/accountinfoEdit
 * @param taxInfoAccount - 公司会计信息实体
 */
export const companyTaxInfoAccountinfoedit = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companyTaxInfo/v1.1/accountinfoEdit'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['taxInfoAccount']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 查询税务信息 - getTaxInfoUsingGET_1
 * /rest/companyConfig/companyBasis/companyTaxInfo/v1.1/get
 */
export const companyTaxInfoGet = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companyTaxInfo/v1.1/get'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 公司税务信息查询 - findCompanyTaxinfoUsingGET
 * /rest/companyConfig/companyBasis/companyTaxInfo/v1.1/taxInfo
 */
export const companyTaxInfoTaxinfo = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companyTaxInfo/v1.1/taxInfo'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 公司税务信息修改 - editTaxInfoUsingPOST
 * /rest/companyConfig/companyBasis/companyTaxInfo/v1.1/taxinfoEdit
 * @param requestHeadersETag - 
 * @param requestHeadersAcceptCharset0Registered - 
 * @param requestHeadersAccept0CharSetRegistered - 
 * @param requestHeadersAccept0CharsetRegistered - 
 * @param requestHeadersAccept0Concrete - 
 * @param requestHeadersAccept0QualityValue - 
 * @param requestHeadersAccept0Subtype - 
 * @param requestHeadersAccept0Type - 
 * @param requestHeadersAccept0WildcardSubtype - 
 * @param requestHeadersAccept0WildcardType - 
 * @param requestHeadersAccessControlAllowCredentials - 
 * @param requestHeadersAccessControlAllowHeaders - 
 * @param requestHeadersAccessControlAllowMethods - 
 * @param requestHeadersAccessControlAllowOrigin - 
 * @param requestHeadersAccessControlExposeHeaders - 
 * @param requestHeadersAccessControlMaxAge - 
 * @param requestHeadersAccessControlRequestHeaders - 
 * @param requestHeadersAccessControlRequestMethod - 
 * @param requestHeadersAllow - 
 * @param requestHeadersCacheControl - 
 * @param requestHeadersConnection - 
 * @param requestHeadersContentLength - 
 * @param requestHeadersContentTypeCharSetRegistered - 
 * @param requestHeadersContentTypeCharsetRegistered - 
 * @param requestHeadersContentTypeConcrete - 
 * @param requestHeadersContentTypeQualityValue - 
 * @param requestHeadersContentTypeSubtype - 
 * @param requestHeadersContentTypeType - 
 * @param requestHeadersContentTypeWildcardSubtype - 
 * @param requestHeadersContentTypeWildcardType - 
 * @param requestHeadersDate - 
 * @param requestHeadersExpires - 
 * @param requestHeadersIfMatch - 
 * @param requestHeadersIfModifiedSince - 
 * @param requestHeadersIfNoneMatch - 
 * @param requestHeadersIfUnmodifiedSince - 
 * @param requestHeadersLastModified - 
 * @param requestHeadersLocationAbsolute - 
 * @param requestHeadersLocationAuthority - 
 * @param requestHeadersLocationFragment - 
 * @param requestHeadersLocationHost - 
 * @param requestHeadersLocationOpaque - 
 * @param requestHeadersLocationPath - 
 * @param requestHeadersLocationPort - 
 * @param requestHeadersLocationQuery - 
 * @param requestHeadersLocationRawAuthority - 
 * @param requestHeadersLocationRawFragment - 
 * @param requestHeadersLocationRawPath - 
 * @param requestHeadersLocationRawQuery - 
 * @param requestHeadersLocationRawSchemeSpecificPart - 
 * @param requestHeadersLocationRawUserInfo - 
 * @param requestHeadersLocationScheme - 
 * @param requestHeadersLocationSchemeSpecificPart - 
 * @param requestHeadersLocationUserInfo - 
 * @param requestHeadersOrigin - 
 * @param requestHeadersPragma - 
 * @param requestHeadersUpgrade - 
 * @param requestHeadersVary - 
 * @param requestMethod - 
 * @param taxInfoCustom - 公司税务信息
 */
export const companyTaxInfoTaxinfoedit = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companyTaxInfo/v1.1/taxinfoEdit'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['requestHeaders.ETag'] = parameters['requestHeadersETag'];
  queryParameters['requestHeaders.acceptCharset[0].registered'] = parameters['requestHeadersAcceptCharset0Registered'];
  queryParameters['requestHeaders.accept[0].charSet.registered'] = parameters['requestHeadersAccept0CharSetRegistered'];
  queryParameters['requestHeaders.accept[0].charset.registered'] = parameters['requestHeadersAccept0CharsetRegistered'];
  queryParameters['requestHeaders.accept[0].concrete'] = parameters['requestHeadersAccept0Concrete'];
  queryParameters['requestHeaders.accept[0].qualityValue'] = parameters['requestHeadersAccept0QualityValue'];
  queryParameters['requestHeaders.accept[0].subtype'] = parameters['requestHeadersAccept0Subtype'];
  queryParameters['requestHeaders.accept[0].type'] = parameters['requestHeadersAccept0Type'];
  queryParameters['requestHeaders.accept[0].wildcardSubtype'] = parameters['requestHeadersAccept0WildcardSubtype'];
  queryParameters['requestHeaders.accept[0].wildcardType'] = parameters['requestHeadersAccept0WildcardType'];
  queryParameters['requestHeaders.accessControlAllowCredentials'] = parameters['requestHeadersAccessControlAllowCredentials'];
  queryParameters['requestHeaders.accessControlAllowHeaders'] = parameters['requestHeadersAccessControlAllowHeaders'];
  queryParameters['requestHeaders.accessControlAllowMethods'] = parameters['requestHeadersAccessControlAllowMethods'];
  queryParameters['requestHeaders.accessControlAllowOrigin'] = parameters['requestHeadersAccessControlAllowOrigin'];
  queryParameters['requestHeaders.accessControlExposeHeaders'] = parameters['requestHeadersAccessControlExposeHeaders'];
  queryParameters['requestHeaders.accessControlMaxAge'] = parameters['requestHeadersAccessControlMaxAge'];
  queryParameters['requestHeaders.accessControlRequestHeaders'] = parameters['requestHeadersAccessControlRequestHeaders'];
  queryParameters['requestHeaders.accessControlRequestMethod'] = parameters['requestHeadersAccessControlRequestMethod'];
  queryParameters['requestHeaders.allow'] = parameters['requestHeadersAllow'];
  queryParameters['requestHeaders.cacheControl'] = parameters['requestHeadersCacheControl'];
  queryParameters['requestHeaders.connection'] = parameters['requestHeadersConnection'];
  queryParameters['requestHeaders.contentLength'] = parameters['requestHeadersContentLength'];
  queryParameters['requestHeaders.contentType.charSet.registered'] = parameters['requestHeadersContentTypeCharSetRegistered'];
  queryParameters['requestHeaders.contentType.charset.registered'] = parameters['requestHeadersContentTypeCharsetRegistered'];
  queryParameters['requestHeaders.contentType.concrete'] = parameters['requestHeadersContentTypeConcrete'];
  queryParameters['requestHeaders.contentType.qualityValue'] = parameters['requestHeadersContentTypeQualityValue'];
  queryParameters['requestHeaders.contentType.subtype'] = parameters['requestHeadersContentTypeSubtype'];
  queryParameters['requestHeaders.contentType.type'] = parameters['requestHeadersContentTypeType'];
  queryParameters['requestHeaders.contentType.wildcardSubtype'] = parameters['requestHeadersContentTypeWildcardSubtype'];
  queryParameters['requestHeaders.contentType.wildcardType'] = parameters['requestHeadersContentTypeWildcardType'];
  queryParameters['requestHeaders.date'] = parameters['requestHeadersDate'];
  queryParameters['requestHeaders.expires'] = parameters['requestHeadersExpires'];
  queryParameters['requestHeaders.ifMatch'] = parameters['requestHeadersIfMatch'];
  queryParameters['requestHeaders.ifModifiedSince'] = parameters['requestHeadersIfModifiedSince'];
  queryParameters['requestHeaders.ifNoneMatch'] = parameters['requestHeadersIfNoneMatch'];
  queryParameters['requestHeaders.ifUnmodifiedSince'] = parameters['requestHeadersIfUnmodifiedSince'];
  queryParameters['requestHeaders.lastModified'] = parameters['requestHeadersLastModified'];
  queryParameters['requestHeaders.location.absolute'] = parameters['requestHeadersLocationAbsolute'];
  queryParameters['requestHeaders.location.authority'] = parameters['requestHeadersLocationAuthority'];
  queryParameters['requestHeaders.location.fragment'] = parameters['requestHeadersLocationFragment'];
  queryParameters['requestHeaders.location.host'] = parameters['requestHeadersLocationHost'];
  queryParameters['requestHeaders.location.opaque'] = parameters['requestHeadersLocationOpaque'];
  queryParameters['requestHeaders.location.path'] = parameters['requestHeadersLocationPath'];
  queryParameters['requestHeaders.location.port'] = parameters['requestHeadersLocationPort'];
  queryParameters['requestHeaders.location.query'] = parameters['requestHeadersLocationQuery'];
  queryParameters['requestHeaders.location.rawAuthority'] = parameters['requestHeadersLocationRawAuthority'];
  queryParameters['requestHeaders.location.rawFragment'] = parameters['requestHeadersLocationRawFragment'];
  queryParameters['requestHeaders.location.rawPath'] = parameters['requestHeadersLocationRawPath'];
  queryParameters['requestHeaders.location.rawQuery'] = parameters['requestHeadersLocationRawQuery'];
  queryParameters['requestHeaders.location.rawSchemeSpecificPart'] = parameters['requestHeadersLocationRawSchemeSpecificPart'];
  queryParameters['requestHeaders.location.rawUserInfo'] = parameters['requestHeadersLocationRawUserInfo'];
  queryParameters['requestHeaders.location.scheme'] = parameters['requestHeadersLocationScheme'];
  queryParameters['requestHeaders.location.schemeSpecificPart'] = parameters['requestHeadersLocationSchemeSpecificPart'];
  queryParameters['requestHeaders.location.userInfo'] = parameters['requestHeadersLocationUserInfo'];
  queryParameters['requestHeaders.origin'] = parameters['requestHeadersOrigin'];
  queryParameters['requestHeaders.pragma'] = parameters['requestHeadersPragma'];
  queryParameters['requestHeaders.upgrade'] = parameters['requestHeadersUpgrade'];
  queryParameters['requestHeaders.vary'] = parameters['requestHeadersVary'];
  queryParameters['requestMethod'] = parameters['requestMethod'];
  body = parameters['taxInfoCustom']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 公司税务信息修改 - updateTaxInfoUsingPUT
 * /rest/companyConfig/companyBasis/companyTaxInfo/v1.1/taxinfoUpdate
 * @param taxinfoCustom - 税务信息实体
 */
export const companyTaxInfoTaxinfoupdate = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companyTaxInfo/v1.1/taxinfoUpdate'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['taxinfoCustom']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 查询增值税类型(1：一般纳税人、 2：小规模纳税人) - findVatTypeUsingGET_1
 * /rest/companyConfig/companyBasis/companyTaxInfo/v1.1/vattype
 */
export const companyTaxInfoVattype = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companyTaxInfo/v1.1/vattype'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 优惠信息附件下载 - downloadFileUsingGET
 * /rest/companyConfig/companyBasis/companyTaxInfodiscount/v1.1/download/{attachmentId}
 * @param attachmentId - 附件id
 */
export const companyTaxInfodiscountDownload = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companyTaxInfodiscount/v1.1/download/{attachmentId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{attachmentId}', `${parameters['attachmentId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 公司税务优惠信息删除 - 
 * /rest/companyConfig/companyBasis/companyTaxInfodiscount/v1.1/{taxdisId}/{attachmentId}
 * @param attachmentId - 优惠信息附件id
 * @param taxdisId - 公司税务优惠信息id
 */
export const editTaxInfoUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companyTaxInfodiscount/v1.1/{taxdisId}/{attachmentId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{attachmentId}', `${parameters['attachmentId']}`)
  path = path.replace('{taxdisId}', `${parameters['taxdisId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回公司用户信息(简单) - findAllCustomUsingGET
 * /rest/companyConfig/companyBasis/companyUsers/v1.1/all
 */
export const companyUsersAll = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companyUsers/v1.1/all'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 用户取消收藏公司 - disabledCollectStatusUsingPUT
 * /rest/companyConfig/companyBasis/companyUsers/v1.1/cancelcollect/{companyId}/{userId}
 * @param companyId - 公司id
 * @param userId - 用户id
 */
export const companyUsersCancelcollect = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companyUsers/v1.1/cancelcollect/{companyId}/{userId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{companyId}', `${parameters['companyId']}`)
  path = path.replace('{userId}', `${parameters['userId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 用户收藏公司 - enableCollectStatusUsingPUT
 * /rest/companyConfig/companyBasis/companyUsers/v1.1/collect/{companyId}/{userId}
 * @param companyId - 公司id
 * @param userId - 用户id
 */
export const companyUsersCollect = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companyUsers/v1.1/collect/{companyId}/{userId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{companyId}', `${parameters['companyId']}`)
  path = path.replace('{userId}', `${parameters['userId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回公司用户信息 - findAllUsingGET_2
 * /rest/companyConfig/companyBasis/companyUsers/v1.1/list
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 * @param userName - 用户姓名
 */
export const companyUsersList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companyUsers/v1.1/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  queryParameters['userName'] = parameters['userName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 公司信息查询 - findCompanyUsingGET
 * /rest/companyConfig/companyBasis/companys/v1.1/company
 */
export const companysCompany = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companys/v1.1/company'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 公司信息修改（企业所得税模块） - editCorporateCompanyUsingPUT
 * /rest/companyConfig/companyBasis/companys/v1.1/corporate/edit
 * @param company - 公司税务申报信息实体<div></div><span class="hljs-literal">所属校验组:CorporateIncomeTax</span>
 */
export const companysCorporate = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companys/v1.1/corporate/edit'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['company']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 根据公司id查找建账月份 - findCompanyAccountPeriodUsingPOST
 * /rest/companyConfig/companyBasis/companys/v1.1/corporate/findCompanyAccountPeriod
 */
export const findCompanyAccountPeriod = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companys/v1.1/corporate/findCompanyAccountPeriod'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 公司信息与税务信息修改（申报税模块） - editDeclareCompanyUsingPUT
 * /rest/companyConfig/companyBasis/companys/v1.1/declare/edit
 * @param declare - 公司税务申报信息实体<div></div><span class="hljs-literal">所属校验组:UpdateGroup</span>
 */
export const companysDeclare = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companys/v1.1/declare/edit'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['declare']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 公司信息修改 - editCompanyUsingPUT
 * /rest/companyConfig/companyBasis/companys/v1.1/edit
 * @param company - 公司实体
 */
export const companysEdit = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companys/v1.1/edit'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['company']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 资格信息 - getQualificationsInfoUsingGET
 * /rest/companyConfig/companyBasis/companysOthers/v1.0/get/qualificationsInfo
 * @param accountNonExpired - 
 * @param accountNonLocked - 
 * @param authorities0Authority - 
 * @param bkCompanyCode - 
 * @param bkCompanyName - 
 * @param bkUserStatus - 
 * @param captcha - 
 * @param clientIp - 
 * @param companyId - 
 * @param companyName - 
 * @param confirmPwd - 
 * @param createTime - 
 * @param credentialsNonExpired - 
 * @param email - 
 * @param enabled - 
 * @param groupId - 
 * @param loginErrorTimes - 
 * @param mobilePhone - 
 * @param modifyTime - 
 * @param orgId - 
 * @param password - 
 * @param roleNames - 
 * @param selectCompanyId - 
 * @param sessionId - 
 * @param ucId - 
 * @param userAccount - 
 * @param userId - 
 * @param userMask - 
 * @param userSource - 
 * @param userStatus - 
 * @param userType - 
 * @param username - 
 * @param wechatCode - 
 */
export const companysOthersGet = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companysOthers/v1.0/get/qualificationsInfo'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountNonExpired'] = parameters['accountNonExpired'];
  queryParameters['accountNonLocked'] = parameters['accountNonLocked'];
  queryParameters['authorities[0].authority'] = parameters['authorities0Authority'];
  queryParameters['bkCompanyCode'] = parameters['bkCompanyCode'];
  queryParameters['bkCompanyName'] = parameters['bkCompanyName'];
  queryParameters['bkUserStatus'] = parameters['bkUserStatus'];
  queryParameters['captcha'] = parameters['captcha'];
  queryParameters['clientIp'] = parameters['clientIp'];
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['companyName'] = parameters['companyName'];
  queryParameters['confirmPwd'] = parameters['confirmPwd'];
  queryParameters['createTime'] = parameters['createTime'];
  queryParameters['credentialsNonExpired'] = parameters['credentialsNonExpired'];
  queryParameters['email'] = parameters['email'];
  queryParameters['enabled'] = parameters['enabled'];
  queryParameters['groupId'] = parameters['groupId'];
  queryParameters['loginErrorTimes'] = parameters['loginErrorTimes'];
  queryParameters['mobilePhone'] = parameters['mobilePhone'];
  queryParameters['modifyTime'] = parameters['modifyTime'];
  queryParameters['orgId'] = parameters['orgId'];
  queryParameters['password'] = parameters['password'];
  queryParameters['roleNames'] = parameters['roleNames'];
  queryParameters['selectCompanyId'] = parameters['selectCompanyId'];
  queryParameters['sessionId'] = parameters['sessionId'];
  queryParameters['ucId'] = parameters['ucId'];
  queryParameters['userAccount'] = parameters['userAccount'];
  queryParameters['userId'] = parameters['userId'];
  queryParameters['userMask'] = parameters['userMask'];
  queryParameters['userSource'] = parameters['userSource'];
  queryParameters['userStatus'] = parameters['userStatus'];
  queryParameters['userType'] = parameters['userType'];
  queryParameters['username'] = parameters['username'];
  queryParameters['wechatCode'] = parameters['wechatCode'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 注册信息 - getRegisterInfoUsingGET
 * /rest/companyConfig/companyBasis/companysOthers/v1.0/get/registerInfo
 * @param accountNonExpired - 
 * @param accountNonLocked - 
 * @param authorities0Authority - 
 * @param bkCompanyCode - 
 * @param bkCompanyName - 
 * @param bkUserStatus - 
 * @param captcha - 
 * @param clientIp - 
 * @param companyId - 
 * @param companyName - 
 * @param confirmPwd - 
 * @param createTime - 
 * @param credentialsNonExpired - 
 * @param email - 
 * @param enabled - 
 * @param groupId - 
 * @param loginErrorTimes - 
 * @param mobilePhone - 
 * @param modifyTime - 
 * @param orgId - 
 * @param password - 
 * @param roleNames - 
 * @param selectCompanyId - 
 * @param sessionId - 
 * @param ucId - 
 * @param userAccount - 
 * @param userId - 
 * @param userMask - 
 * @param userSource - 
 * @param userStatus - 
 * @param userType - 
 * @param username - 
 * @param wechatCode - 
 */
export const getRegisterInfo = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companysOthers/v1.0/get/registerInfo'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountNonExpired'] = parameters['accountNonExpired'];
  queryParameters['accountNonLocked'] = parameters['accountNonLocked'];
  queryParameters['authorities[0].authority'] = parameters['authorities0Authority'];
  queryParameters['bkCompanyCode'] = parameters['bkCompanyCode'];
  queryParameters['bkCompanyName'] = parameters['bkCompanyName'];
  queryParameters['bkUserStatus'] = parameters['bkUserStatus'];
  queryParameters['captcha'] = parameters['captcha'];
  queryParameters['clientIp'] = parameters['clientIp'];
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['companyName'] = parameters['companyName'];
  queryParameters['confirmPwd'] = parameters['confirmPwd'];
  queryParameters['createTime'] = parameters['createTime'];
  queryParameters['credentialsNonExpired'] = parameters['credentialsNonExpired'];
  queryParameters['email'] = parameters['email'];
  queryParameters['enabled'] = parameters['enabled'];
  queryParameters['groupId'] = parameters['groupId'];
  queryParameters['loginErrorTimes'] = parameters['loginErrorTimes'];
  queryParameters['mobilePhone'] = parameters['mobilePhone'];
  queryParameters['modifyTime'] = parameters['modifyTime'];
  queryParameters['orgId'] = parameters['orgId'];
  queryParameters['password'] = parameters['password'];
  queryParameters['roleNames'] = parameters['roleNames'];
  queryParameters['selectCompanyId'] = parameters['selectCompanyId'];
  queryParameters['sessionId'] = parameters['sessionId'];
  queryParameters['ucId'] = parameters['ucId'];
  queryParameters['userAccount'] = parameters['userAccount'];
  queryParameters['userId'] = parameters['userId'];
  queryParameters['userMask'] = parameters['userMask'];
  queryParameters['userSource'] = parameters['userSource'];
  queryParameters['userStatus'] = parameters['userStatus'];
  queryParameters['userType'] = parameters['userType'];
  queryParameters['username'] = parameters['username'];
  queryParameters['wechatCode'] = parameters['wechatCode'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 国地税税费种认定信息 - getTaxationInfoUsingGET
 * /rest/companyConfig/companyBasis/companysOthers/v1.0/get/taxationInfo
 * @param accountNonExpired - 
 * @param accountNonLocked - 
 * @param authorities0Authority - 
 * @param bkCompanyCode - 
 * @param bkCompanyName - 
 * @param bkUserStatus - 
 * @param captcha - 
 * @param clientIp - 
 * @param companyId - 
 * @param companyName - 
 * @param confirmPwd - 
 * @param createTime - 
 * @param credentialsNonExpired - 
 * @param email - 
 * @param enabled - 
 * @param groupId - 
 * @param loginErrorTimes - 
 * @param mobilePhone - 
 * @param modifyTime - 
 * @param orgId - 
 * @param password - 
 * @param roleNames - 
 * @param selectCompanyId - 
 * @param sessionId - 
 * @param ucId - 
 * @param userAccount - 
 * @param userId - 
 * @param userMask - 
 * @param userSource - 
 * @param userStatus - 
 * @param userType - 
 * @param username - 
 * @param wechatCode - 
 */
export const getTaxationInfo = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companysOthers/v1.0/get/taxationInfo'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountNonExpired'] = parameters['accountNonExpired'];
  queryParameters['accountNonLocked'] = parameters['accountNonLocked'];
  queryParameters['authorities[0].authority'] = parameters['authorities0Authority'];
  queryParameters['bkCompanyCode'] = parameters['bkCompanyCode'];
  queryParameters['bkCompanyName'] = parameters['bkCompanyName'];
  queryParameters['bkUserStatus'] = parameters['bkUserStatus'];
  queryParameters['captcha'] = parameters['captcha'];
  queryParameters['clientIp'] = parameters['clientIp'];
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['companyName'] = parameters['companyName'];
  queryParameters['confirmPwd'] = parameters['confirmPwd'];
  queryParameters['createTime'] = parameters['createTime'];
  queryParameters['credentialsNonExpired'] = parameters['credentialsNonExpired'];
  queryParameters['email'] = parameters['email'];
  queryParameters['enabled'] = parameters['enabled'];
  queryParameters['groupId'] = parameters['groupId'];
  queryParameters['loginErrorTimes'] = parameters['loginErrorTimes'];
  queryParameters['mobilePhone'] = parameters['mobilePhone'];
  queryParameters['modifyTime'] = parameters['modifyTime'];
  queryParameters['orgId'] = parameters['orgId'];
  queryParameters['password'] = parameters['password'];
  queryParameters['roleNames'] = parameters['roleNames'];
  queryParameters['selectCompanyId'] = parameters['selectCompanyId'];
  queryParameters['sessionId'] = parameters['sessionId'];
  queryParameters['ucId'] = parameters['ucId'];
  queryParameters['userAccount'] = parameters['userAccount'];
  queryParameters['userId'] = parameters['userId'];
  queryParameters['userMask'] = parameters['userMask'];
  queryParameters['userSource'] = parameters['userSource'];
  queryParameters['userStatus'] = parameters['userStatus'];
  queryParameters['userType'] = parameters['userType'];
  queryParameters['username'] = parameters['username'];
  queryParameters['wechatCode'] = parameters['wechatCode'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 发起采集 - startCollectCompanyInfoUsingPOST
 * /rest/companyConfig/companyBasis/companysOthers/v1.0/startCollect
 */
export const companysOthersStartcollect = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/companysOthers/v1.0/startCollect'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 公司货币缓存清除 - clearCacheUsingGET_3
 * /rest/companyConfig/companyBasis/currency/v1.0/cc
 */
export const currencyCc = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/currency/v1.0/cc'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 公司货币批量删除 - deleteUsingDELETE
 * /rest/companyConfig/companyBasis/currency/v1.0/deletes/{currencyIds}
 * @param currencyIds - 货币id，相邻id请用,号隔开
 */
export const currencyDeletes = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/currency/v1.0/deletes/{currencyIds}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{currencyIds}', `${parameters['currencyIds']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 货币下拉列表查询 - findDropListUsingGET_1
 * /rest/companyConfig/companyBasis/currency/v1.0/droplist
 * @param currencyCode - 币别编码
 * @param currencyName - 币别名称
 * @param usedStatus - 币别状态
 */
export const currencyDroplist = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/currency/v1.0/droplist'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['currencyName'] = parameters['currencyName'];
  queryParameters['usedStatus'] = parameters['usedStatus'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 公司货币新增 - addUsingPOST
 * /rest/companyConfig/companyBasis/currency/v1.0/inserts
 * @param currencies - 货币数组
 */
export const currencyInserts = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/currency/v1.0/inserts'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['currencies']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 货币列表查询 - findUsingGET
 * /rest/companyConfig/companyBasis/currency/v1.0/list
 * @param currencyCode - 币别编码
 * @param currencyName - 币别名称
 * @param usedStatus - 币别状态
 */
export const currencyList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/currency/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['currencyName'] = parameters['currencyName'];
  queryParameters['usedStatus'] = parameters['usedStatus'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 公司货币修改 - updateUsingPUT
 * /rest/companyConfig/companyBasis/currency/v1.0/update/{currencyId}
 * @param currency - currency
 * @param currencyId - currencyId
 */
export const currencyUpdate = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/currency/v1.0/update/{currencyId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['currency']
  if (parameters['currency'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: currency'))
  }
  path = path.replace('{currencyId}', `${parameters['currencyId']}`)
  if (parameters['currencyId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: currencyId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 导出客户信息列表 - exportcustomersUsingGET
 * /rest/companyConfig/companyBasis/customers/v1.0/export
 * @param createTimeBegin - 创建时间范围_起
 * @param createTimeEnd - 创建时间范围_止
 * @param customerCustomerName - 客户名称
 * @param customerCustomerStatus - 客户状态
 * @param customerCustomerType - 客户类型
 */
export const customersExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/customers/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['createTimeBegin'] = parameters['createTimeBegin'];
  queryParameters['createTimeEnd'] = parameters['createTimeEnd'];
  queryParameters['customer.customerName'] = parameters['customerCustomerName'];
  queryParameters['customer.customerStatus'] = parameters['customerCustomerStatus'];
  queryParameters['customer.customerType'] = parameters['customerCustomerType'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导入客户信息列表 - importcustomersUsingPOST
 * /rest/companyConfig/companyBasis/customers/v1.0/import
 * @param requestHeadersETag - 
 * @param requestHeadersAcceptCharset0Registered - 
 * @param requestHeadersAccept0CharSetRegistered - 
 * @param requestHeadersAccept0CharsetRegistered - 
 * @param requestHeadersAccept0Concrete - 
 * @param requestHeadersAccept0QualityValue - 
 * @param requestHeadersAccept0Subtype - 
 * @param requestHeadersAccept0Type - 
 * @param requestHeadersAccept0WildcardSubtype - 
 * @param requestHeadersAccept0WildcardType - 
 * @param requestHeadersAccessControlAllowCredentials - 
 * @param requestHeadersAccessControlAllowHeaders - 
 * @param requestHeadersAccessControlAllowMethods - 
 * @param requestHeadersAccessControlAllowOrigin - 
 * @param requestHeadersAccessControlExposeHeaders - 
 * @param requestHeadersAccessControlMaxAge - 
 * @param requestHeadersAccessControlRequestHeaders - 
 * @param requestHeadersAccessControlRequestMethod - 
 * @param requestHeadersAllow - 
 * @param requestHeadersCacheControl - 
 * @param requestHeadersConnection - 
 * @param requestHeadersContentLength - 
 * @param requestHeadersContentTypeCharSetRegistered - 
 * @param requestHeadersContentTypeCharsetRegistered - 
 * @param requestHeadersContentTypeConcrete - 
 * @param requestHeadersContentTypeQualityValue - 
 * @param requestHeadersContentTypeSubtype - 
 * @param requestHeadersContentTypeType - 
 * @param requestHeadersContentTypeWildcardSubtype - 
 * @param requestHeadersContentTypeWildcardType - 
 * @param requestHeadersDate - 
 * @param requestHeadersExpires - 
 * @param requestHeadersIfMatch - 
 * @param requestHeadersIfModifiedSince - 
 * @param requestHeadersIfNoneMatch - 
 * @param requestHeadersIfUnmodifiedSince - 
 * @param requestHeadersLastModified - 
 * @param requestHeadersLocationAbsolute - 
 * @param requestHeadersLocationAuthority - 
 * @param requestHeadersLocationFragment - 
 * @param requestHeadersLocationHost - 
 * @param requestHeadersLocationOpaque - 
 * @param requestHeadersLocationPath - 
 * @param requestHeadersLocationPort - 
 * @param requestHeadersLocationQuery - 
 * @param requestHeadersLocationRawAuthority - 
 * @param requestHeadersLocationRawFragment - 
 * @param requestHeadersLocationRawPath - 
 * @param requestHeadersLocationRawQuery - 
 * @param requestHeadersLocationRawSchemeSpecificPart - 
 * @param requestHeadersLocationRawUserInfo - 
 * @param requestHeadersLocationScheme - 
 * @param requestHeadersLocationSchemeSpecificPart - 
 * @param requestHeadersLocationUserInfo - 
 * @param requestHeadersOrigin - 
 * @param requestHeadersPragma - 
 * @param requestHeadersUpgrade - 
 * @param requestHeadersVary - 
 * @param requestMethod - 
 */
export const customersImport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/customers/v1.0/import'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['requestHeaders.ETag'] = parameters['requestHeadersETag'];
  queryParameters['requestHeaders.acceptCharset[0].registered'] = parameters['requestHeadersAcceptCharset0Registered'];
  queryParameters['requestHeaders.accept[0].charSet.registered'] = parameters['requestHeadersAccept0CharSetRegistered'];
  queryParameters['requestHeaders.accept[0].charset.registered'] = parameters['requestHeadersAccept0CharsetRegistered'];
  queryParameters['requestHeaders.accept[0].concrete'] = parameters['requestHeadersAccept0Concrete'];
  queryParameters['requestHeaders.accept[0].qualityValue'] = parameters['requestHeadersAccept0QualityValue'];
  queryParameters['requestHeaders.accept[0].subtype'] = parameters['requestHeadersAccept0Subtype'];
  queryParameters['requestHeaders.accept[0].type'] = parameters['requestHeadersAccept0Type'];
  queryParameters['requestHeaders.accept[0].wildcardSubtype'] = parameters['requestHeadersAccept0WildcardSubtype'];
  queryParameters['requestHeaders.accept[0].wildcardType'] = parameters['requestHeadersAccept0WildcardType'];
  queryParameters['requestHeaders.accessControlAllowCredentials'] = parameters['requestHeadersAccessControlAllowCredentials'];
  queryParameters['requestHeaders.accessControlAllowHeaders'] = parameters['requestHeadersAccessControlAllowHeaders'];
  queryParameters['requestHeaders.accessControlAllowMethods'] = parameters['requestHeadersAccessControlAllowMethods'];
  queryParameters['requestHeaders.accessControlAllowOrigin'] = parameters['requestHeadersAccessControlAllowOrigin'];
  queryParameters['requestHeaders.accessControlExposeHeaders'] = parameters['requestHeadersAccessControlExposeHeaders'];
  queryParameters['requestHeaders.accessControlMaxAge'] = parameters['requestHeadersAccessControlMaxAge'];
  queryParameters['requestHeaders.accessControlRequestHeaders'] = parameters['requestHeadersAccessControlRequestHeaders'];
  queryParameters['requestHeaders.accessControlRequestMethod'] = parameters['requestHeadersAccessControlRequestMethod'];
  queryParameters['requestHeaders.allow'] = parameters['requestHeadersAllow'];
  queryParameters['requestHeaders.cacheControl'] = parameters['requestHeadersCacheControl'];
  queryParameters['requestHeaders.connection'] = parameters['requestHeadersConnection'];
  queryParameters['requestHeaders.contentLength'] = parameters['requestHeadersContentLength'];
  queryParameters['requestHeaders.contentType.charSet.registered'] = parameters['requestHeadersContentTypeCharSetRegistered'];
  queryParameters['requestHeaders.contentType.charset.registered'] = parameters['requestHeadersContentTypeCharsetRegistered'];
  queryParameters['requestHeaders.contentType.concrete'] = parameters['requestHeadersContentTypeConcrete'];
  queryParameters['requestHeaders.contentType.qualityValue'] = parameters['requestHeadersContentTypeQualityValue'];
  queryParameters['requestHeaders.contentType.subtype'] = parameters['requestHeadersContentTypeSubtype'];
  queryParameters['requestHeaders.contentType.type'] = parameters['requestHeadersContentTypeType'];
  queryParameters['requestHeaders.contentType.wildcardSubtype'] = parameters['requestHeadersContentTypeWildcardSubtype'];
  queryParameters['requestHeaders.contentType.wildcardType'] = parameters['requestHeadersContentTypeWildcardType'];
  queryParameters['requestHeaders.date'] = parameters['requestHeadersDate'];
  queryParameters['requestHeaders.expires'] = parameters['requestHeadersExpires'];
  queryParameters['requestHeaders.ifMatch'] = parameters['requestHeadersIfMatch'];
  queryParameters['requestHeaders.ifModifiedSince'] = parameters['requestHeadersIfModifiedSince'];
  queryParameters['requestHeaders.ifNoneMatch'] = parameters['requestHeadersIfNoneMatch'];
  queryParameters['requestHeaders.ifUnmodifiedSince'] = parameters['requestHeadersIfUnmodifiedSince'];
  queryParameters['requestHeaders.lastModified'] = parameters['requestHeadersLastModified'];
  queryParameters['requestHeaders.location.absolute'] = parameters['requestHeadersLocationAbsolute'];
  queryParameters['requestHeaders.location.authority'] = parameters['requestHeadersLocationAuthority'];
  queryParameters['requestHeaders.location.fragment'] = parameters['requestHeadersLocationFragment'];
  queryParameters['requestHeaders.location.host'] = parameters['requestHeadersLocationHost'];
  queryParameters['requestHeaders.location.opaque'] = parameters['requestHeadersLocationOpaque'];
  queryParameters['requestHeaders.location.path'] = parameters['requestHeadersLocationPath'];
  queryParameters['requestHeaders.location.port'] = parameters['requestHeadersLocationPort'];
  queryParameters['requestHeaders.location.query'] = parameters['requestHeadersLocationQuery'];
  queryParameters['requestHeaders.location.rawAuthority'] = parameters['requestHeadersLocationRawAuthority'];
  queryParameters['requestHeaders.location.rawFragment'] = parameters['requestHeadersLocationRawFragment'];
  queryParameters['requestHeaders.location.rawPath'] = parameters['requestHeadersLocationRawPath'];
  queryParameters['requestHeaders.location.rawQuery'] = parameters['requestHeadersLocationRawQuery'];
  queryParameters['requestHeaders.location.rawSchemeSpecificPart'] = parameters['requestHeadersLocationRawSchemeSpecificPart'];
  queryParameters['requestHeaders.location.rawUserInfo'] = parameters['requestHeadersLocationRawUserInfo'];
  queryParameters['requestHeaders.location.scheme'] = parameters['requestHeadersLocationScheme'];
  queryParameters['requestHeaders.location.schemeSpecificPart'] = parameters['requestHeadersLocationSchemeSpecificPart'];
  queryParameters['requestHeaders.location.userInfo'] = parameters['requestHeadersLocationUserInfo'];
  queryParameters['requestHeaders.origin'] = parameters['requestHeadersOrigin'];
  queryParameters['requestHeaders.pragma'] = parameters['requestHeadersPragma'];
  queryParameters['requestHeaders.upgrade'] = parameters['requestHeadersUpgrade'];
  queryParameters['requestHeaders.vary'] = parameters['requestHeadersVary'];
  queryParameters['requestMethod'] = parameters['requestMethod'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 以自定义列表的方式返回公司客户信息 - getAllUsingGET_1
 * /rest/companyConfig/companyBasis/customers/v1.1/all
 */
export const customersAll = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/customers/v1.1/all'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 清除客户缓存 - cacheClearUsingGET_1
 * /rest/companyConfig/companyBasis/customers/v1.1/cache/clear
 */
export const customersCache = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/customers/v1.1/cache/clear'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 公司客户信息新增 - addCustomerUsingPOST
 * /rest/companyConfig/companyBasis/customers/v1.1/insert
 * @param customer - 公司客户实体<div></div><span class="hljs-literal">所属校验组:FrontendGroup</span>
 */
export const customersInsert = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/customers/v1.1/insert'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['customer']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回公司客户信息 - findAllUsingGET_3
 * /rest/companyConfig/companyBasis/customers/v1.1/list
 * @param createTimeBegin - 创建时间范围_起
 * @param createTimeEnd - 创建时间范围_止
 * @param customerCustomerName - 客户名称
 * @param customerCustomerStatus - 客户状态
 * @param customerCustomerType - 客户类型
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const customersList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/customers/v1.1/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['createTimeBegin'] = parameters['createTimeBegin'];
  queryParameters['createTimeEnd'] = parameters['createTimeEnd'];
  queryParameters['customer.customerName'] = parameters['customerCustomerName'];
  queryParameters['customer.customerStatus'] = parameters['customerCustomerStatus'];
  queryParameters['customer.customerType'] = parameters['customerCustomerType'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 批量新增修改客户信息 - mergeCustomersUsingPUT_1
 * /rest/companyConfig/companyBasis/customers/v1.1/save
 * @param list - list
 */
export const customersSave = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/customers/v1.1/save'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['list']
  if (parameters['list'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: list'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 批量新增修改客户信息(预览，不进行写操作) - mergeViewCustomersUsingPUT_1
 * /rest/companyConfig/companyBasis/customers/v1.1/viewOfSave
 * @param list - list
 */
export const customersViewofsave = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/customers/v1.1/viewOfSave'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['list']
  if (parameters['list'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: list'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 公司客户信息修改 - 
 * /rest/companyConfig/companyBasis/customers/v1.1/{customerId}
 * @param customer - 公司客户实体<div></div><span class="hljs-literal">所属校验组:FrontendGroup</span>
 * @param customerId - 公司客户id
 */
export const editCustomerUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/customers/v1.1/{customerId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['customer']
  path = path.replace('{customerId}', `${parameters['customerId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 外币科目批量新增 - insertBatchUsingPOST
 * /rest/companyConfig/companyBasis/foreign/v1.1/inserts
 * @param list - 外币科目集合
 */
export const foreignInserts = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/foreign/v1.1/inserts'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['list']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 最后修改时间查询 - findUsingGET_2
 * /rest/companyConfig/companyBasis/lasttime/v1.0/list
 */
export const lasttimeList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/lasttime/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 公司财务标准查询 - findCompanyUsingGET_1
 * /rest/companyConfig/companyBasis/rate/v1.1/get
 */
export const rateGet = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/rate/v1.1/get'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 公司财务标准修改 - editCompanyUsingPUT_1
 * /rest/companyConfig/companyBasis/rate/v1.1/update
 * @param rate - 公司实体
 */
export const rateUpdate = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/rate/v1.1/update'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['rate']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 公司风险测评标准率查询 - findCompanyUsingGET_2
 * /rest/companyConfig/companyBasis/risk/v1.1/get
 */
export const riskGet = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/risk/v1.1/get'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 公司风险测评标准率修改 - editCompanyUsingPUT_2
 * /rest/companyConfig/companyBasis/risk/v1.1/update
 * @param rate - 风险测评实体
 */
export const riskUpdate = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/risk/v1.1/update'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['rate']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 科目缓存清理 - 
 * /rest/companyConfig/companyBasis/subjectinfo/cacheclear
 */
export const clearCacheUsingGET_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/subjectinfo/cacheclear'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 根据id删除科目 - 
 * /rest/companyConfig/companyBasis/subjectinfo/v1.0/{subjectId}
 * @param subjectId - subjectId
 */
export const removeSaleUnitByIdUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/subjectinfo/v1.0/{subjectId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{subjectId}', `${parameters['subjectId']}`)
  if (parameters['subjectId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: subjectId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回下一级科目列表信息(简单) - findVoByNodeUsingGET_1
 * /rest/companyConfig/companyBasis/subjectinfo/v1.1/all/{subjectParentCode}
 * @param subjectParentCode - 上级科目编码
 */
export const subjectinfoAll = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/subjectinfo/v1.1/all/{subjectParentCode}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{subjectParentCode}', `${parameters['subjectParentCode']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 根据上级科目编码查询所有子级（包括自己）科目 - findAllSubNodesUsingGET
 * /rest/companyConfig/companyBasis/subjectinfo/v1.1/allSubs/{subjectParentCode}
 * @param subjectParentCode - 上级科目编码
 */
export const subjectinfoAllsubs = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/subjectinfo/v1.1/allSubs/{subjectParentCode}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{subjectParentCode}', `${parameters['subjectParentCode']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 科目信息查询（末级至顶级） - findSubjectsFromLastToTopUsingGET_1
 * /rest/companyConfig/companyBasis/subjectinfo/v1.1/chains
 * @param subjectFullName - 科目全称
 */
export const subjectinfoChains = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/subjectinfo/v1.1/chains'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['subjectFullName'] = parameters['subjectFullName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出科目列表信息 - exportSubjectsUsingGET
 * /rest/companyConfig/companyBasis/subjectinfo/v1.1/export
 * @param codeAndName - 科目编码或全称
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param subjectCategories - 科目类型
 * @param subjectCode - 科目编码
 * @param subjectFullName - 科目全称
 * @param subjectName - 科目名称
 * @param subjectStatus - 科目状态
 * @param take - 
 */
export const subjectinfoExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/subjectinfo/v1.1/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['codeAndName'] = parameters['codeAndName'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['subjectCategories'] = parameters['subjectCategories'];
  queryParameters['subjectCode'] = parameters['subjectCode'];
  queryParameters['subjectFullName'] = parameters['subjectFullName'];
  queryParameters['subjectName'] = parameters['subjectName'];
  queryParameters['subjectStatus'] = parameters['subjectStatus'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 科目信息查询 - getUsingGET
 * /rest/companyConfig/companyBasis/subjectinfo/v1.1/gets
 * @param currencySwitch - 是否开启货币关联查询
 * @param subjectCodes - 科目编码集合，科目编码之间用,号隔开
 */
export const subjectinfoGets = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/subjectinfo/v1.1/gets'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['currencySwitch'] = parameters['currencySwitch'];
  queryParameters['subjectCodes'] = parameters['subjectCodes'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 商品类别（1.库存商品、2.半成品、3.原材料、4.包装物、5.低值易耗品） - getUsingGET_2
 * /rest/companyConfig/companyBasis/subjectinfo/v1.1/goodssubject
 * @param goodsName - 商品名称
 * @param goodsStatus - 商品状态（1.启用，2.停用）
 * @param goodsTypeList - 商品类别（1.库存商品、2.半成品、3.原材料、4.包装物、5.低值易耗品）
 */
export const subjectinfoGoodssubject = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/subjectinfo/v1.1/goodssubject'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['goodsStatus'] = parameters['goodsStatus'];
  queryParameters['goodsTypeList'] = parameters['goodsTypeList'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询去重科目名称列表 - findImagesUsingGET_1
 * /rest/companyConfig/companyBasis/subjectinfo/v1.1/images
 * @param subjectName - 科目名称
 */
export const subjectinfoImages = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/subjectinfo/v1.1/images'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['subjectName'] = parameters['subjectName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 科目信息新增 - addSubjectsUsingPOST
 * /rest/companyConfig/companyBasis/subjectinfo/v1.1/insert
 * @param subject - 科目实体<div></div><span class="hljs-literal">所属校验组:InsertGroup</span>
 */
export const insertSubject = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/subjectinfo/v1.1/insert'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['subject']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查询科目最新的修改时间 - findLastModifyTimeUsingGET_1
 * /rest/companyConfig/companyBasis/subjectinfo/v1.1/lastTime
 */
export const subjectinfoLasttime = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/subjectinfo/v1.1/lastTime'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回科目信息 - findAllUsingGET_1
 * /rest/companyConfig/companyBasis/subjectinfo/v1.1/list
 * @param codeAndName - 科目编码或全称
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param subjectCategories - 科目类型
 * @param subjectCode - 科目编码
 * @param subjectFullName - 科目全称
 * @param subjectName - 科目名称
 * @param subjectStatus - 科目状态
 * @param take - 
 */
export const subjectinfoList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/subjectinfo/v1.1/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['codeAndName'] = parameters['codeAndName'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['subjectCategories'] = parameters['subjectCategories'];
  queryParameters['subjectCode'] = parameters['subjectCode'];
  queryParameters['subjectFullName'] = parameters['subjectFullName'];
  queryParameters['subjectName'] = parameters['subjectName'];
  queryParameters['subjectStatus'] = parameters['subjectStatus'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 根据科目全称模糊查询科目，并返回其上级到顶级的科目 - findMatchesWithAllParentsUsingGET_1
 * /rest/companyConfig/companyBasis/subjectinfo/v1.1/matches
 * @param beginCode - 开始的科目编码
 * @param codeAndName - 科目编码或全称
 * @param endCode - 结束的科目编码
 * @param subjectCategories - 科目类型
 * @param subjectCode - 科目编码
 * @param subjectFullName - 科目全称
 * @param subjectName - 科目名称
 * @param subjectStatus - 科目状态
 */
export const subjectinfoMatches = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/subjectinfo/v1.1/matches'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginCode'] = parameters['beginCode'];
  queryParameters['codeAndName'] = parameters['codeAndName'];
  queryParameters['endCode'] = parameters['endCode'];
  queryParameters['subjectCategories'] = parameters['subjectCategories'];
  queryParameters['subjectCode'] = parameters['subjectCode'];
  queryParameters['subjectFullName'] = parameters['subjectFullName'];
  queryParameters['subjectName'] = parameters['subjectName'];
  queryParameters['subjectStatus'] = parameters['subjectStatus'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 科目关联业务信息查询 - findSubjectsRelatedUsingGET_1
 * /rest/companyConfig/companyBasis/subjectinfo/v1.1/related
 * @param relatedTable - 关联表
 * @param relatedTableId - 关联表ID
 * @param subjectCode - 科目编码
 * @param subjectName - 科目名称
 */
export const subjectinfoRelated = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/subjectinfo/v1.1/related'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['relatedTable'] = parameters['relatedTable'];
  queryParameters['relatedTableId'] = parameters['relatedTableId'];
  queryParameters['subjectCode'] = parameters['subjectCode'];
  queryParameters['subjectName'] = parameters['subjectName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 批量添加科目，同时反写商品，供应商等配置 - insertBatchAndReverseUsingPOST_1
 * /rest/companyConfig/companyBasis/subjectinfo/v1.1/reverse/inserts
 * @param subjects - subjects
 */
export const subjectinfoReverse = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/subjectinfo/v1.1/reverse/inserts'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['subjects']
  if (parameters['subjects'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: subjects'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 科目树信息查询 - findTreeUsingGET_1
 * /rest/companyConfig/companyBasis/subjectinfo/v1.1/trees/{subjectCode}
 * @param subjectCode - 科目编码
 */
export const subjectinfoTrees = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/subjectinfo/v1.1/trees/{subjectCode}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{subjectCode}', `${parameters['subjectCode']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 科目信息修改 - updateSubjectsUsingPUT
 * /rest/companyConfig/companyBasis/subjectinfo/v1.1/update/{subjectId}
 * @param subject - 科目实体<div></div><span class="hljs-literal">所属校验组:UpdateGroup</span>
 * @param subjectId - 科目id
 */
export const subjectinfoUpdate = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/subjectinfo/v1.1/update/{subjectId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['subject']
  path = path.replace('{subjectId}', `${parameters['subjectId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 导出供应商信息列表 - exportSuppliersUsingGET
 * /rest/companyConfig/companyBasis/suppliers/v1.0/export
 * @param contactName - 联系人
 * @param supplierName - 供应商名称
 */
export const suppliersExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/suppliers/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['contactName'] = parameters['contactName'];
  queryParameters['supplierName'] = parameters['supplierName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导入供应商信息列表 - importSuppliersUsingPOST
 * /rest/companyConfig/companyBasis/suppliers/v1.0/import
 * @param requestHeadersETag - 
 * @param requestHeadersAcceptCharset0Registered - 
 * @param requestHeadersAccept0CharSetRegistered - 
 * @param requestHeadersAccept0CharsetRegistered - 
 * @param requestHeadersAccept0Concrete - 
 * @param requestHeadersAccept0QualityValue - 
 * @param requestHeadersAccept0Subtype - 
 * @param requestHeadersAccept0Type - 
 * @param requestHeadersAccept0WildcardSubtype - 
 * @param requestHeadersAccept0WildcardType - 
 * @param requestHeadersAccessControlAllowCredentials - 
 * @param requestHeadersAccessControlAllowHeaders - 
 * @param requestHeadersAccessControlAllowMethods - 
 * @param requestHeadersAccessControlAllowOrigin - 
 * @param requestHeadersAccessControlExposeHeaders - 
 * @param requestHeadersAccessControlMaxAge - 
 * @param requestHeadersAccessControlRequestHeaders - 
 * @param requestHeadersAccessControlRequestMethod - 
 * @param requestHeadersAllow - 
 * @param requestHeadersCacheControl - 
 * @param requestHeadersConnection - 
 * @param requestHeadersContentLength - 
 * @param requestHeadersContentTypeCharSetRegistered - 
 * @param requestHeadersContentTypeCharsetRegistered - 
 * @param requestHeadersContentTypeConcrete - 
 * @param requestHeadersContentTypeQualityValue - 
 * @param requestHeadersContentTypeSubtype - 
 * @param requestHeadersContentTypeType - 
 * @param requestHeadersContentTypeWildcardSubtype - 
 * @param requestHeadersContentTypeWildcardType - 
 * @param requestHeadersDate - 
 * @param requestHeadersExpires - 
 * @param requestHeadersIfMatch - 
 * @param requestHeadersIfModifiedSince - 
 * @param requestHeadersIfNoneMatch - 
 * @param requestHeadersIfUnmodifiedSince - 
 * @param requestHeadersLastModified - 
 * @param requestHeadersLocationAbsolute - 
 * @param requestHeadersLocationAuthority - 
 * @param requestHeadersLocationFragment - 
 * @param requestHeadersLocationHost - 
 * @param requestHeadersLocationOpaque - 
 * @param requestHeadersLocationPath - 
 * @param requestHeadersLocationPort - 
 * @param requestHeadersLocationQuery - 
 * @param requestHeadersLocationRawAuthority - 
 * @param requestHeadersLocationRawFragment - 
 * @param requestHeadersLocationRawPath - 
 * @param requestHeadersLocationRawQuery - 
 * @param requestHeadersLocationRawSchemeSpecificPart - 
 * @param requestHeadersLocationRawUserInfo - 
 * @param requestHeadersLocationScheme - 
 * @param requestHeadersLocationSchemeSpecificPart - 
 * @param requestHeadersLocationUserInfo - 
 * @param requestHeadersOrigin - 
 * @param requestHeadersPragma - 
 * @param requestHeadersUpgrade - 
 * @param requestHeadersVary - 
 * @param requestMethod - 
 */
export const suppliersImport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/suppliers/v1.0/import'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['requestHeaders.ETag'] = parameters['requestHeadersETag'];
  queryParameters['requestHeaders.acceptCharset[0].registered'] = parameters['requestHeadersAcceptCharset0Registered'];
  queryParameters['requestHeaders.accept[0].charSet.registered'] = parameters['requestHeadersAccept0CharSetRegistered'];
  queryParameters['requestHeaders.accept[0].charset.registered'] = parameters['requestHeadersAccept0CharsetRegistered'];
  queryParameters['requestHeaders.accept[0].concrete'] = parameters['requestHeadersAccept0Concrete'];
  queryParameters['requestHeaders.accept[0].qualityValue'] = parameters['requestHeadersAccept0QualityValue'];
  queryParameters['requestHeaders.accept[0].subtype'] = parameters['requestHeadersAccept0Subtype'];
  queryParameters['requestHeaders.accept[0].type'] = parameters['requestHeadersAccept0Type'];
  queryParameters['requestHeaders.accept[0].wildcardSubtype'] = parameters['requestHeadersAccept0WildcardSubtype'];
  queryParameters['requestHeaders.accept[0].wildcardType'] = parameters['requestHeadersAccept0WildcardType'];
  queryParameters['requestHeaders.accessControlAllowCredentials'] = parameters['requestHeadersAccessControlAllowCredentials'];
  queryParameters['requestHeaders.accessControlAllowHeaders'] = parameters['requestHeadersAccessControlAllowHeaders'];
  queryParameters['requestHeaders.accessControlAllowMethods'] = parameters['requestHeadersAccessControlAllowMethods'];
  queryParameters['requestHeaders.accessControlAllowOrigin'] = parameters['requestHeadersAccessControlAllowOrigin'];
  queryParameters['requestHeaders.accessControlExposeHeaders'] = parameters['requestHeadersAccessControlExposeHeaders'];
  queryParameters['requestHeaders.accessControlMaxAge'] = parameters['requestHeadersAccessControlMaxAge'];
  queryParameters['requestHeaders.accessControlRequestHeaders'] = parameters['requestHeadersAccessControlRequestHeaders'];
  queryParameters['requestHeaders.accessControlRequestMethod'] = parameters['requestHeadersAccessControlRequestMethod'];
  queryParameters['requestHeaders.allow'] = parameters['requestHeadersAllow'];
  queryParameters['requestHeaders.cacheControl'] = parameters['requestHeadersCacheControl'];
  queryParameters['requestHeaders.connection'] = parameters['requestHeadersConnection'];
  queryParameters['requestHeaders.contentLength'] = parameters['requestHeadersContentLength'];
  queryParameters['requestHeaders.contentType.charSet.registered'] = parameters['requestHeadersContentTypeCharSetRegistered'];
  queryParameters['requestHeaders.contentType.charset.registered'] = parameters['requestHeadersContentTypeCharsetRegistered'];
  queryParameters['requestHeaders.contentType.concrete'] = parameters['requestHeadersContentTypeConcrete'];
  queryParameters['requestHeaders.contentType.qualityValue'] = parameters['requestHeadersContentTypeQualityValue'];
  queryParameters['requestHeaders.contentType.subtype'] = parameters['requestHeadersContentTypeSubtype'];
  queryParameters['requestHeaders.contentType.type'] = parameters['requestHeadersContentTypeType'];
  queryParameters['requestHeaders.contentType.wildcardSubtype'] = parameters['requestHeadersContentTypeWildcardSubtype'];
  queryParameters['requestHeaders.contentType.wildcardType'] = parameters['requestHeadersContentTypeWildcardType'];
  queryParameters['requestHeaders.date'] = parameters['requestHeadersDate'];
  queryParameters['requestHeaders.expires'] = parameters['requestHeadersExpires'];
  queryParameters['requestHeaders.ifMatch'] = parameters['requestHeadersIfMatch'];
  queryParameters['requestHeaders.ifModifiedSince'] = parameters['requestHeadersIfModifiedSince'];
  queryParameters['requestHeaders.ifNoneMatch'] = parameters['requestHeadersIfNoneMatch'];
  queryParameters['requestHeaders.ifUnmodifiedSince'] = parameters['requestHeadersIfUnmodifiedSince'];
  queryParameters['requestHeaders.lastModified'] = parameters['requestHeadersLastModified'];
  queryParameters['requestHeaders.location.absolute'] = parameters['requestHeadersLocationAbsolute'];
  queryParameters['requestHeaders.location.authority'] = parameters['requestHeadersLocationAuthority'];
  queryParameters['requestHeaders.location.fragment'] = parameters['requestHeadersLocationFragment'];
  queryParameters['requestHeaders.location.host'] = parameters['requestHeadersLocationHost'];
  queryParameters['requestHeaders.location.opaque'] = parameters['requestHeadersLocationOpaque'];
  queryParameters['requestHeaders.location.path'] = parameters['requestHeadersLocationPath'];
  queryParameters['requestHeaders.location.port'] = parameters['requestHeadersLocationPort'];
  queryParameters['requestHeaders.location.query'] = parameters['requestHeadersLocationQuery'];
  queryParameters['requestHeaders.location.rawAuthority'] = parameters['requestHeadersLocationRawAuthority'];
  queryParameters['requestHeaders.location.rawFragment'] = parameters['requestHeadersLocationRawFragment'];
  queryParameters['requestHeaders.location.rawPath'] = parameters['requestHeadersLocationRawPath'];
  queryParameters['requestHeaders.location.rawQuery'] = parameters['requestHeadersLocationRawQuery'];
  queryParameters['requestHeaders.location.rawSchemeSpecificPart'] = parameters['requestHeadersLocationRawSchemeSpecificPart'];
  queryParameters['requestHeaders.location.rawUserInfo'] = parameters['requestHeadersLocationRawUserInfo'];
  queryParameters['requestHeaders.location.scheme'] = parameters['requestHeadersLocationScheme'];
  queryParameters['requestHeaders.location.schemeSpecificPart'] = parameters['requestHeadersLocationSchemeSpecificPart'];
  queryParameters['requestHeaders.location.userInfo'] = parameters['requestHeadersLocationUserInfo'];
  queryParameters['requestHeaders.origin'] = parameters['requestHeadersOrigin'];
  queryParameters['requestHeaders.pragma'] = parameters['requestHeadersPragma'];
  queryParameters['requestHeaders.upgrade'] = parameters['requestHeadersUpgrade'];
  queryParameters['requestHeaders.vary'] = parameters['requestHeadersVary'];
  queryParameters['requestMethod'] = parameters['requestMethod'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回供应商信息(简单) - getAllUsingGET_2
 * /rest/companyConfig/companyBasis/suppliers/v1.1/all
 */
export const suppliersAll = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/suppliers/v1.1/all'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 清除供应商缓存 - cacheClearUsingGET_3
 * /rest/companyConfig/companyBasis/suppliers/v1.1/cache/clear
 */
export const suppliersCache = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/suppliers/v1.1/cache/clear'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 供应商信息新增 - addSupplierUsingPOST
 * /rest/companyConfig/companyBasis/suppliers/v1.1/insert
 * @param supplier - 供应商实体<div></div><span class="hljs-literal">所属校验组:FrontendGroup</span>
 */
export const suppliersInsert = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/suppliers/v1.1/insert'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['supplier']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回供应商信息 - findAllUsingGET_4
 * /rest/companyConfig/companyBasis/suppliers/v1.1/list
 * @param contactName - 联系人
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param supplierName - 供应商名称
 * @param take - 
 */
export const supplierList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/suppliers/v1.1/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['contactName'] = parameters['contactName'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['supplierName'] = parameters['supplierName'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 批量新增修改供应商信息 - mergeSuppliersUsingPUT_1
 * /rest/companyConfig/companyBasis/suppliers/v1.1/save
 * @param list - list
 */
export const suppliersSave = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/suppliers/v1.1/save'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['list']
  if (parameters['list'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: list'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 批量新增修改供应商信息(用于预览，不会进行写操作) - mergeViewSuppliersUsingPUT_1
 * /rest/companyConfig/companyBasis/suppliers/v1.1/viewOfSave
 * @param list - list
 */
export const suppliersViewofsave = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/suppliers/v1.1/viewOfSave'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['list']
  if (parameters['list'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: list'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 单个供应商信息查询 - 
 * /rest/companyConfig/companyBasis/suppliers/v1.1/{supplierId}
 * @param supplierId - 供应商id
 */
export const getSupplierUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/suppliers/v1.1/{supplierId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{supplierId}', `${parameters['supplierId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 供应商信息修改 - 
 * /rest/companyConfig/companyBasis/suppliers/v1.1/{supplierId}
 * @param supplier - 供应商实体<div></div><span class="hljs-literal">所属校验组:FrontendGroup</span>
 * @param supplierId - 供应商id
 */
export const editSupplierUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/suppliers/v1.1/{supplierId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['supplier']
  path = path.replace('{supplierId}', `${parameters['supplierId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 凭证生成规则新增 - insertBatchUsingPOST_4
 * /rest/companyConfig/companyBasis/vrule/v1.0/insertBatch
 * @param rules - 凭证规则集合
 */
export const vruleInsertbatch = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/vrule/v1.0/insertBatch'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['rules']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 凭证生成规则查询 - findByUsingGET_1
 * /rest/companyConfig/companyBasis/vrule/v1.0/list
 */
export const vruleList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/companyBasis/vrule/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 代账公司用户名称批量修改 - 
 * /restsafe/companyConfig/companyBasis/companys/v1.0/{userId}/{bkCompanyCode}
 * @param bkCompanyCode - 代帐公司编号
 * @param companyUser - 公司用户实体
 * @param userId - 用户id
 */
export const editBkUserNameUsingPUT_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/companyBasis/companys/v1.0/{userId}/{bkCompanyCode}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{bkCompanyCode}', `${parameters['bkCompanyCode']}`)
  body = parameters['companyUser']
  path = path.replace('{userId}', `${parameters['userId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回公司用户信息(简单) - findAllCustomUsingGET_2
 * /restsafe/companyConfig/companyBasis/companys/v1.1/all
 * @param collectStatus - 收藏状态
 * @param companyId - 公司id
 * @param companyRelId - 公司-用户简表id
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param modifyTime - 修改时间
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 * @param userId - 用户id
 * @param userName - 用户姓名
 * @param userStatus - 用户状态
 */
export const companysAll = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/companyBasis/companys/v1.1/all'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['collectStatus'] = parameters['collectStatus'];
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['companyRelId'] = parameters['companyRelId'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['modifyTime'] = parameters['modifyTime'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  queryParameters['userId'] = parameters['userId'];
  queryParameters['userName'] = parameters['userName'];
  queryParameters['userStatus'] = parameters['userStatus'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 内部压测用户清除 - clearBatchUsingGET_3
 * /restsafe/companyConfig/companyBasis/companys/v1.1/clearBatch
 */
export const companysClearbatch = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/companyBasis/companys/v1.1/clearBatch'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 公司用户删除 - deleteCompanyUserUsingDELETE_3
 * /restsafe/companyConfig/companyBasis/companys/v1.1/delete/{companyId}
 * @param companyId - 公司id
 */
export const companysDelete = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/companyBasis/companys/v1.1/delete/{companyId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{companyId}', `${parameters['companyId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 公司用户删除 - deleteCompanyUserUsingDELETE_1
 * /restsafe/companyConfig/companyBasis/companys/v1.1/delete/{companyId}/{userId}
 * @param companyId - 公司id
 * @param userId - 用户id
 */
export const deleteCompanyUser = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/companyBasis/companys/v1.1/delete/{companyId}/{userId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{companyId}', `${parameters['companyId']}`)
  path = path.replace('{userId}', `${parameters['userId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 公司用户删除 - deleteCompanyUserByIdsUsingDELETE_1
 * /restsafe/companyConfig/companyBasis/companys/v1.1/deleteByIds/{companyId}
 * @param companyId - 公司id
 * @param userIdList - userIdList
 */
export const companysDeletebyids = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/companyBasis/companys/v1.1/deleteByIds/{companyId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{companyId}', `${parameters['companyId']}`)
  body = parameters['userIdList']
  if (parameters['userIdList'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: userIdList'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 公司用户删除 - deleteCompanysByUserIdUsingDELETE_1
 * /restsafe/companyConfig/companyBasis/companys/v1.1/deleteByUserId/{userId}
 * @param userId - 用户id
 */
export const companysDeletebyuserid = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/companyBasis/companys/v1.1/deleteByUserId/{userId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{userId}', `${parameters['userId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 公司用户状态停用 - disabledUserStatusUsingPUT_1
 * /restsafe/companyConfig/companyBasis/companys/v1.1/disable/{companyId}/{userId}
 * @param companyId - 公司id
 * @param userId - 用户id
 */
export const companysDisable = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/companyBasis/companys/v1.1/disable/{companyId}/{userId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{companyId}', `${parameters['companyId']}`)
  path = path.replace('{userId}', `${parameters['userId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 公司用户状态启用 - enableUserStatusUsingPUT_1
 * /restsafe/companyConfig/companyBasis/companys/v1.1/enable/{companyId}/{userId}
 * @param companyId - 公司id
 * @param userId - 用户id
 */
export const companysEnable = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/companyBasis/companys/v1.1/enable/{companyId}/{userId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{companyId}', `${parameters['companyId']}`)
  path = path.replace('{userId}', `${parameters['userId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 公司信息-获取公司名称 - getCompanyNameUsingGET_1
 * /restsafe/companyConfig/companyBasis/companys/v1.1/get/companyname/{companyId}
 * @param companyId - companyId
 */
export const companysGet = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/companyBasis/companys/v1.1/get/companyname/{companyId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{companyId}', `${parameters['companyId']}`)
  if (parameters['companyId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: companyId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 公司用户新增 - addCompanyUserUsingPOST_1
 * /restsafe/companyConfig/companyBasis/companys/v1.1/insert
 * @param companyUser - 公司用户实体
 */
export const companysInsert = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/companyBasis/companys/v1.1/insert'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['companyUser']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 公司信息(注册)新增-内部调用不做安全校验 - addRegisterUsingPOST_1
 * /restsafe/companyConfig/companyBasis/companys/v1.1/insertRegister
 * @param register - 注册公司实体(用于新增)
 */
export const companysInsertregister = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/companyBasis/companys/v1.1/insertRegister'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['register']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 公司信息-修改公司名称、建账日期 - updateCompanyNameUsingPUT_1
 * /restsafe/companyConfig/companyBasis/companys/v1.1/update/companyname
 * @param entity - entity
 */
export const companysUpdate = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/companyBasis/companys/v1.1/update/companyname'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['entity']
  if (parameters['entity'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: entity'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 批量添加科目，不反写商品，供应商等配置 - insertBatchUsingPOST_2
 * /restsafe/companyConfig/companyBasis/subjectinfo/v1.1/inserts
 * @param subjects - 科目信息集合的List
 */
export const insertSubjects = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/companyBasis/subjectinfo/v1.1/inserts'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['subjects']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 添加某些项目时新增科目信息（已存在科目不作提示） - insertBatchOfSubjectsUsingPOST_1
 * /restsafe/companyConfig/companyBasis/subjectinfo/v1.1/inserts/subjects
 * @param subjects - 科目信息集合map，键是二、三级，值是对应的List
 */
export const insertBatchOfSubjects = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/companyBasis/subjectinfo/v1.1/inserts/subjects'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['subjects']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 添加某些项目时新增科目信息（已存在科目提示） - insertBatchOfSubjectsWithRepeatTipsUsingPOST_1
 * /restsafe/companyConfig/companyBasis/subjectinfo/v1.1/inserts/tips
 * @param subjects - 科目信息集合map，键是二、三级，值是对应的List
 */
export const insertBatchOfSubjectsWithRepeatTips = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/companyBasis/subjectinfo/v1.1/inserts/tips'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['subjects']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 批量添加科目，同时反写商品，供应商等配置 - insertBatchAndReverseUsingPOST_3
 * /restsafe/companyConfig/companyBasis/subjectinfo/v1.1/reverse/inserts
 * @param subjects - 科目信息集合map，键是二、三级，值是对应的List
 */
export const insertBatchAndReverse = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/companyBasis/subjectinfo/v1.1/reverse/inserts'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['subjects']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}