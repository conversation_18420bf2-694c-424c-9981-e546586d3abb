/*
 * @Description:
 * @version:
 * @Company: 海闻软件
 * @Author: 马赛
 * @Date: 2019-01-14 15:13:41
 * @LastEditors: 郑启旭
 * @LastEditTime: 2019-01-23 17:26:12
 */
import request from '@/utils/request';

/**
 *删除借款单
 *
 * @export
 * @param {*} borrowId 借款单id
 * @returns
 */
// export function deleteBorrow(borrowId) {
//   return request.delete(`/rest/proxy/account/borrowAccount/v1.0/delete/${borrowId}`);
// };

/**
 *获取记录(单条)
 *
 * @export
 * @param {*} borrowId 借款id
 * @returns
 */
export function getBorrow(borrowId) {
  return request.get(`/rest/proxy/account/borrowAccount/v1.0/get/${borrowId}`);
}

/**
* 新增借款单
*/
// export function postBorrow(params) {
//   return request.post('/rest/proxy/account/borrowAccount/v1.0/insert', params);
// };

/**
 *借款列表
 *
 */
export function getBorrowList(params) {
  return request.get('/rest/proxy/account/borrowAccount/v1.0/list', { params });
}

/**
* 修改借款单
*/
export function putBorrow(params) {
  return request.put(`/rest/proxy/account/borrowAccount/v1.0/update/${params.borrowId}`, params);
}

/**
* 借款单付款
*/
export function updatePay(params) {
  return request.put(`/rest/proxy/account/borrowAccount/v1.0/updatePay/${params.borrowId}`, params);
}
