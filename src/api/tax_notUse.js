/* eslint-disable */
import request from '@/utils/request';
/**
 * 勾选结果导出 - exportCheckUsingGET
 * /rest/companytax/merge/invoice/v1.0/exportCheck
 */
export const invoiceExportcheck = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/invoice/v1.0/exportCheck'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 待勾选清单导入 - importDeductionListExcelUsingPOST
 * /rest/companytax/merge/invoice/v1.0/importDeductionExcel
 * @param accountNonExpired - 
 * @param accountNonLocked - 
 * @param authorities0Authority - 
 * @param bkCompanyCode - 
 * @param bkCompanyName - 
 * @param bkUserStatus - 
 * @param captcha - 
 * @param clientIp - 
 * @param companyId - 
 * @param companyName - 
 * @param confirmPwd - 
 * @param createTime - 
 * @param credentialsNonExpired - 
 * @param email - 
 * @param enabled - 
 * @param groupId - 
 * @param loginErrorTimes - 
 * @param mobilePhone - 
 * @param modifyTime - 
 * @param orgId - 
 * @param password - 
 * @param requestHeadersETag - 
 * @param requestHeadersAcceptCharset0Registered - 
 * @param requestHeadersAccept0CharSetRegistered - 
 * @param requestHeadersAccept0CharsetRegistered - 
 * @param requestHeadersAccept0Concrete - 
 * @param requestHeadersAccept0QualityValue - 
 * @param requestHeadersAccept0Subtype - 
 * @param requestHeadersAccept0Type - 
 * @param requestHeadersAccept0WildcardSubtype - 
 * @param requestHeadersAccept0WildcardType - 
 * @param requestHeadersAccessControlAllowCredentials - 
 * @param requestHeadersAccessControlAllowHeaders - 
 * @param requestHeadersAccessControlAllowMethods - 
 * @param requestHeadersAccessControlAllowOrigin - 
 * @param requestHeadersAccessControlExposeHeaders - 
 * @param requestHeadersAccessControlMaxAge - 
 * @param requestHeadersAccessControlRequestHeaders - 
 * @param requestHeadersAccessControlRequestMethod - 
 * @param requestHeadersAllow - 
 * @param requestHeadersCacheControl - 
 * @param requestHeadersConnection - 
 * @param requestHeadersContentLength - 
 * @param requestHeadersContentTypeCharSetRegistered - 
 * @param requestHeadersContentTypeCharsetRegistered - 
 * @param requestHeadersContentTypeConcrete - 
 * @param requestHeadersContentTypeQualityValue - 
 * @param requestHeadersContentTypeSubtype - 
 * @param requestHeadersContentTypeType - 
 * @param requestHeadersContentTypeWildcardSubtype - 
 * @param requestHeadersContentTypeWildcardType - 
 * @param requestHeadersDate - 
 * @param requestHeadersExpires - 
 * @param requestHeadersIfMatch - 
 * @param requestHeadersIfModifiedSince - 
 * @param requestHeadersIfNoneMatch - 
 * @param requestHeadersIfUnmodifiedSince - 
 * @param requestHeadersLastModified - 
 * @param requestHeadersLocationAbsolute - 
 * @param requestHeadersLocationAuthority - 
 * @param requestHeadersLocationFragment - 
 * @param requestHeadersLocationHost - 
 * @param requestHeadersLocationOpaque - 
 * @param requestHeadersLocationPath - 
 * @param requestHeadersLocationPort - 
 * @param requestHeadersLocationQuery - 
 * @param requestHeadersLocationRawAuthority - 
 * @param requestHeadersLocationRawFragment - 
 * @param requestHeadersLocationRawPath - 
 * @param requestHeadersLocationRawQuery - 
 * @param requestHeadersLocationRawSchemeSpecificPart - 
 * @param requestHeadersLocationRawUserInfo - 
 * @param requestHeadersLocationScheme - 
 * @param requestHeadersLocationSchemeSpecificPart - 
 * @param requestHeadersLocationUserInfo - 
 * @param requestHeadersOrigin - 
 * @param requestHeadersPragma - 
 * @param requestHeadersUpgrade - 
 * @param requestHeadersVary - 
 * @param requestMethod - 
 * @param roleNames - 
 * @param selectCompanyId - 
 * @param sessionId - 
 * @param ucId - 
 * @param userAccount - 
 * @param userId - 
 * @param userMask - 
 * @param userSource - 
 * @param userStatus - 
 * @param userType - 
 * @param username - 
 * @param wechatCode - 
 */
export const invoiceImportdeductionexcel = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/invoice/v1.0/importDeductionExcel'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountNonExpired'] = parameters['accountNonExpired'];
  queryParameters['accountNonLocked'] = parameters['accountNonLocked'];
  queryParameters['authorities[0].authority'] = parameters['authorities0Authority'];
  queryParameters['bkCompanyCode'] = parameters['bkCompanyCode'];
  queryParameters['bkCompanyName'] = parameters['bkCompanyName'];
  queryParameters['bkUserStatus'] = parameters['bkUserStatus'];
  queryParameters['captcha'] = parameters['captcha'];
  queryParameters['clientIp'] = parameters['clientIp'];
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['companyName'] = parameters['companyName'];
  queryParameters['confirmPwd'] = parameters['confirmPwd'];
  queryParameters['createTime'] = parameters['createTime'];
  queryParameters['credentialsNonExpired'] = parameters['credentialsNonExpired'];
  queryParameters['email'] = parameters['email'];
  queryParameters['enabled'] = parameters['enabled'];
  queryParameters['groupId'] = parameters['groupId'];
  queryParameters['loginErrorTimes'] = parameters['loginErrorTimes'];
  queryParameters['mobilePhone'] = parameters['mobilePhone'];
  queryParameters['modifyTime'] = parameters['modifyTime'];
  queryParameters['orgId'] = parameters['orgId'];
  queryParameters['password'] = parameters['password'];
  queryParameters['requestHeaders.ETag'] = parameters['requestHeadersETag'];
  queryParameters['requestHeaders.acceptCharset[0].registered'] = parameters['requestHeadersAcceptCharset0Registered'];
  queryParameters['requestHeaders.accept[0].charSet.registered'] = parameters['requestHeadersAccept0CharSetRegistered'];
  queryParameters['requestHeaders.accept[0].charset.registered'] = parameters['requestHeadersAccept0CharsetRegistered'];
  queryParameters['requestHeaders.accept[0].concrete'] = parameters['requestHeadersAccept0Concrete'];
  queryParameters['requestHeaders.accept[0].qualityValue'] = parameters['requestHeadersAccept0QualityValue'];
  queryParameters['requestHeaders.accept[0].subtype'] = parameters['requestHeadersAccept0Subtype'];
  queryParameters['requestHeaders.accept[0].type'] = parameters['requestHeadersAccept0Type'];
  queryParameters['requestHeaders.accept[0].wildcardSubtype'] = parameters['requestHeadersAccept0WildcardSubtype'];
  queryParameters['requestHeaders.accept[0].wildcardType'] = parameters['requestHeadersAccept0WildcardType'];
  queryParameters['requestHeaders.accessControlAllowCredentials'] = parameters['requestHeadersAccessControlAllowCredentials'];
  queryParameters['requestHeaders.accessControlAllowHeaders'] = parameters['requestHeadersAccessControlAllowHeaders'];
  queryParameters['requestHeaders.accessControlAllowMethods'] = parameters['requestHeadersAccessControlAllowMethods'];
  queryParameters['requestHeaders.accessControlAllowOrigin'] = parameters['requestHeadersAccessControlAllowOrigin'];
  queryParameters['requestHeaders.accessControlExposeHeaders'] = parameters['requestHeadersAccessControlExposeHeaders'];
  queryParameters['requestHeaders.accessControlMaxAge'] = parameters['requestHeadersAccessControlMaxAge'];
  queryParameters['requestHeaders.accessControlRequestHeaders'] = parameters['requestHeadersAccessControlRequestHeaders'];
  queryParameters['requestHeaders.accessControlRequestMethod'] = parameters['requestHeadersAccessControlRequestMethod'];
  queryParameters['requestHeaders.allow'] = parameters['requestHeadersAllow'];
  queryParameters['requestHeaders.cacheControl'] = parameters['requestHeadersCacheControl'];
  queryParameters['requestHeaders.connection'] = parameters['requestHeadersConnection'];
  queryParameters['requestHeaders.contentLength'] = parameters['requestHeadersContentLength'];
  queryParameters['requestHeaders.contentType.charSet.registered'] = parameters['requestHeadersContentTypeCharSetRegistered'];
  queryParameters['requestHeaders.contentType.charset.registered'] = parameters['requestHeadersContentTypeCharsetRegistered'];
  queryParameters['requestHeaders.contentType.concrete'] = parameters['requestHeadersContentTypeConcrete'];
  queryParameters['requestHeaders.contentType.qualityValue'] = parameters['requestHeadersContentTypeQualityValue'];
  queryParameters['requestHeaders.contentType.subtype'] = parameters['requestHeadersContentTypeSubtype'];
  queryParameters['requestHeaders.contentType.type'] = parameters['requestHeadersContentTypeType'];
  queryParameters['requestHeaders.contentType.wildcardSubtype'] = parameters['requestHeadersContentTypeWildcardSubtype'];
  queryParameters['requestHeaders.contentType.wildcardType'] = parameters['requestHeadersContentTypeWildcardType'];
  queryParameters['requestHeaders.date'] = parameters['requestHeadersDate'];
  queryParameters['requestHeaders.expires'] = parameters['requestHeadersExpires'];
  queryParameters['requestHeaders.ifMatch'] = parameters['requestHeadersIfMatch'];
  queryParameters['requestHeaders.ifModifiedSince'] = parameters['requestHeadersIfModifiedSince'];
  queryParameters['requestHeaders.ifNoneMatch'] = parameters['requestHeadersIfNoneMatch'];
  queryParameters['requestHeaders.ifUnmodifiedSince'] = parameters['requestHeadersIfUnmodifiedSince'];
  queryParameters['requestHeaders.lastModified'] = parameters['requestHeadersLastModified'];
  queryParameters['requestHeaders.location.absolute'] = parameters['requestHeadersLocationAbsolute'];
  queryParameters['requestHeaders.location.authority'] = parameters['requestHeadersLocationAuthority'];
  queryParameters['requestHeaders.location.fragment'] = parameters['requestHeadersLocationFragment'];
  queryParameters['requestHeaders.location.host'] = parameters['requestHeadersLocationHost'];
  queryParameters['requestHeaders.location.opaque'] = parameters['requestHeadersLocationOpaque'];
  queryParameters['requestHeaders.location.path'] = parameters['requestHeadersLocationPath'];
  queryParameters['requestHeaders.location.port'] = parameters['requestHeadersLocationPort'];
  queryParameters['requestHeaders.location.query'] = parameters['requestHeadersLocationQuery'];
  queryParameters['requestHeaders.location.rawAuthority'] = parameters['requestHeadersLocationRawAuthority'];
  queryParameters['requestHeaders.location.rawFragment'] = parameters['requestHeadersLocationRawFragment'];
  queryParameters['requestHeaders.location.rawPath'] = parameters['requestHeadersLocationRawPath'];
  queryParameters['requestHeaders.location.rawQuery'] = parameters['requestHeadersLocationRawQuery'];
  queryParameters['requestHeaders.location.rawSchemeSpecificPart'] = parameters['requestHeadersLocationRawSchemeSpecificPart'];
  queryParameters['requestHeaders.location.rawUserInfo'] = parameters['requestHeadersLocationRawUserInfo'];
  queryParameters['requestHeaders.location.scheme'] = parameters['requestHeadersLocationScheme'];
  queryParameters['requestHeaders.location.schemeSpecificPart'] = parameters['requestHeadersLocationSchemeSpecificPart'];
  queryParameters['requestHeaders.location.userInfo'] = parameters['requestHeadersLocationUserInfo'];
  queryParameters['requestHeaders.origin'] = parameters['requestHeadersOrigin'];
  queryParameters['requestHeaders.pragma'] = parameters['requestHeadersPragma'];
  queryParameters['requestHeaders.upgrade'] = parameters['requestHeadersUpgrade'];
  queryParameters['requestHeaders.vary'] = parameters['requestHeadersVary'];
  queryParameters['requestMethod'] = parameters['requestMethod'];
  queryParameters['roleNames'] = parameters['roleNames'];
  queryParameters['selectCompanyId'] = parameters['selectCompanyId'];
  queryParameters['sessionId'] = parameters['sessionId'];
  queryParameters['ucId'] = parameters['ucId'];
  queryParameters['userAccount'] = parameters['userAccount'];
  queryParameters['userId'] = parameters['userId'];
  queryParameters['userMask'] = parameters['userMask'];
  queryParameters['userSource'] = parameters['userSource'];
  queryParameters['userStatus'] = parameters['userStatus'];
  queryParameters['userType'] = parameters['userType'];
  queryParameters['username'] = parameters['username'];
  queryParameters['wechatCode'] = parameters['wechatCode'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 认证结果Xml导入 - importXmlUsingPOST
 * /rest/companytax/merge/invoice/v1.0/importXml
 * @param accountNonExpired - 
 * @param accountNonLocked - 
 * @param authorities0Authority - 
 * @param bkCompanyCode - 
 * @param bkCompanyName - 
 * @param bkUserStatus - 
 * @param captcha - 
 * @param clientIp - 
 * @param companyId - 
 * @param companyName - 
 * @param confirmPwd - 
 * @param createTime - 
 * @param credentialsNonExpired - 
 * @param email - 
 * @param enabled - 
 * @param groupId - 
 * @param loginErrorTimes - 
 * @param mobilePhone - 
 * @param modifyTime - 
 * @param orgId - 
 * @param password - 
 * @param requestHeadersETag - 
 * @param requestHeadersAcceptCharset0Registered - 
 * @param requestHeadersAccept0CharSetRegistered - 
 * @param requestHeadersAccept0CharsetRegistered - 
 * @param requestHeadersAccept0Concrete - 
 * @param requestHeadersAccept0QualityValue - 
 * @param requestHeadersAccept0Subtype - 
 * @param requestHeadersAccept0Type - 
 * @param requestHeadersAccept0WildcardSubtype - 
 * @param requestHeadersAccept0WildcardType - 
 * @param requestHeadersAccessControlAllowCredentials - 
 * @param requestHeadersAccessControlAllowHeaders - 
 * @param requestHeadersAccessControlAllowMethods - 
 * @param requestHeadersAccessControlAllowOrigin - 
 * @param requestHeadersAccessControlExposeHeaders - 
 * @param requestHeadersAccessControlMaxAge - 
 * @param requestHeadersAccessControlRequestHeaders - 
 * @param requestHeadersAccessControlRequestMethod - 
 * @param requestHeadersAllow - 
 * @param requestHeadersCacheControl - 
 * @param requestHeadersConnection - 
 * @param requestHeadersContentLength - 
 * @param requestHeadersContentTypeCharSetRegistered - 
 * @param requestHeadersContentTypeCharsetRegistered - 
 * @param requestHeadersContentTypeConcrete - 
 * @param requestHeadersContentTypeQualityValue - 
 * @param requestHeadersContentTypeSubtype - 
 * @param requestHeadersContentTypeType - 
 * @param requestHeadersContentTypeWildcardSubtype - 
 * @param requestHeadersContentTypeWildcardType - 
 * @param requestHeadersDate - 
 * @param requestHeadersExpires - 
 * @param requestHeadersIfMatch - 
 * @param requestHeadersIfModifiedSince - 
 * @param requestHeadersIfNoneMatch - 
 * @param requestHeadersIfUnmodifiedSince - 
 * @param requestHeadersLastModified - 
 * @param requestHeadersLocationAbsolute - 
 * @param requestHeadersLocationAuthority - 
 * @param requestHeadersLocationFragment - 
 * @param requestHeadersLocationHost - 
 * @param requestHeadersLocationOpaque - 
 * @param requestHeadersLocationPath - 
 * @param requestHeadersLocationPort - 
 * @param requestHeadersLocationQuery - 
 * @param requestHeadersLocationRawAuthority - 
 * @param requestHeadersLocationRawFragment - 
 * @param requestHeadersLocationRawPath - 
 * @param requestHeadersLocationRawQuery - 
 * @param requestHeadersLocationRawSchemeSpecificPart - 
 * @param requestHeadersLocationRawUserInfo - 
 * @param requestHeadersLocationScheme - 
 * @param requestHeadersLocationSchemeSpecificPart - 
 * @param requestHeadersLocationUserInfo - 
 * @param requestHeadersOrigin - 
 * @param requestHeadersPragma - 
 * @param requestHeadersUpgrade - 
 * @param requestHeadersVary - 
 * @param requestMethod - 
 * @param roleNames - 
 * @param selectCompanyId - 
 * @param sessionId - 
 * @param ucId - 
 * @param userAccount - 
 * @param userId - 
 * @param userMask - 
 * @param userSource - 
 * @param userStatus - 
 * @param userType - 
 * @param username - 
 * @param wechatCode - 
 */
export const invoiceImportxml = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/invoice/v1.0/importXml'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountNonExpired'] = parameters['accountNonExpired'];
  queryParameters['accountNonLocked'] = parameters['accountNonLocked'];
  queryParameters['authorities[0].authority'] = parameters['authorities0Authority'];
  queryParameters['bkCompanyCode'] = parameters['bkCompanyCode'];
  queryParameters['bkCompanyName'] = parameters['bkCompanyName'];
  queryParameters['bkUserStatus'] = parameters['bkUserStatus'];
  queryParameters['captcha'] = parameters['captcha'];
  queryParameters['clientIp'] = parameters['clientIp'];
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['companyName'] = parameters['companyName'];
  queryParameters['confirmPwd'] = parameters['confirmPwd'];
  queryParameters['createTime'] = parameters['createTime'];
  queryParameters['credentialsNonExpired'] = parameters['credentialsNonExpired'];
  queryParameters['email'] = parameters['email'];
  queryParameters['enabled'] = parameters['enabled'];
  queryParameters['groupId'] = parameters['groupId'];
  queryParameters['loginErrorTimes'] = parameters['loginErrorTimes'];
  queryParameters['mobilePhone'] = parameters['mobilePhone'];
  queryParameters['modifyTime'] = parameters['modifyTime'];
  queryParameters['orgId'] = parameters['orgId'];
  queryParameters['password'] = parameters['password'];
  queryParameters['requestHeaders.ETag'] = parameters['requestHeadersETag'];
  queryParameters['requestHeaders.acceptCharset[0].registered'] = parameters['requestHeadersAcceptCharset0Registered'];
  queryParameters['requestHeaders.accept[0].charSet.registered'] = parameters['requestHeadersAccept0CharSetRegistered'];
  queryParameters['requestHeaders.accept[0].charset.registered'] = parameters['requestHeadersAccept0CharsetRegistered'];
  queryParameters['requestHeaders.accept[0].concrete'] = parameters['requestHeadersAccept0Concrete'];
  queryParameters['requestHeaders.accept[0].qualityValue'] = parameters['requestHeadersAccept0QualityValue'];
  queryParameters['requestHeaders.accept[0].subtype'] = parameters['requestHeadersAccept0Subtype'];
  queryParameters['requestHeaders.accept[0].type'] = parameters['requestHeadersAccept0Type'];
  queryParameters['requestHeaders.accept[0].wildcardSubtype'] = parameters['requestHeadersAccept0WildcardSubtype'];
  queryParameters['requestHeaders.accept[0].wildcardType'] = parameters['requestHeadersAccept0WildcardType'];
  queryParameters['requestHeaders.accessControlAllowCredentials'] = parameters['requestHeadersAccessControlAllowCredentials'];
  queryParameters['requestHeaders.accessControlAllowHeaders'] = parameters['requestHeadersAccessControlAllowHeaders'];
  queryParameters['requestHeaders.accessControlAllowMethods'] = parameters['requestHeadersAccessControlAllowMethods'];
  queryParameters['requestHeaders.accessControlAllowOrigin'] = parameters['requestHeadersAccessControlAllowOrigin'];
  queryParameters['requestHeaders.accessControlExposeHeaders'] = parameters['requestHeadersAccessControlExposeHeaders'];
  queryParameters['requestHeaders.accessControlMaxAge'] = parameters['requestHeadersAccessControlMaxAge'];
  queryParameters['requestHeaders.accessControlRequestHeaders'] = parameters['requestHeadersAccessControlRequestHeaders'];
  queryParameters['requestHeaders.accessControlRequestMethod'] = parameters['requestHeadersAccessControlRequestMethod'];
  queryParameters['requestHeaders.allow'] = parameters['requestHeadersAllow'];
  queryParameters['requestHeaders.cacheControl'] = parameters['requestHeadersCacheControl'];
  queryParameters['requestHeaders.connection'] = parameters['requestHeadersConnection'];
  queryParameters['requestHeaders.contentLength'] = parameters['requestHeadersContentLength'];
  queryParameters['requestHeaders.contentType.charSet.registered'] = parameters['requestHeadersContentTypeCharSetRegistered'];
  queryParameters['requestHeaders.contentType.charset.registered'] = parameters['requestHeadersContentTypeCharsetRegistered'];
  queryParameters['requestHeaders.contentType.concrete'] = parameters['requestHeadersContentTypeConcrete'];
  queryParameters['requestHeaders.contentType.qualityValue'] = parameters['requestHeadersContentTypeQualityValue'];
  queryParameters['requestHeaders.contentType.subtype'] = parameters['requestHeadersContentTypeSubtype'];
  queryParameters['requestHeaders.contentType.type'] = parameters['requestHeadersContentTypeType'];
  queryParameters['requestHeaders.contentType.wildcardSubtype'] = parameters['requestHeadersContentTypeWildcardSubtype'];
  queryParameters['requestHeaders.contentType.wildcardType'] = parameters['requestHeadersContentTypeWildcardType'];
  queryParameters['requestHeaders.date'] = parameters['requestHeadersDate'];
  queryParameters['requestHeaders.expires'] = parameters['requestHeadersExpires'];
  queryParameters['requestHeaders.ifMatch'] = parameters['requestHeadersIfMatch'];
  queryParameters['requestHeaders.ifModifiedSince'] = parameters['requestHeadersIfModifiedSince'];
  queryParameters['requestHeaders.ifNoneMatch'] = parameters['requestHeadersIfNoneMatch'];
  queryParameters['requestHeaders.ifUnmodifiedSince'] = parameters['requestHeadersIfUnmodifiedSince'];
  queryParameters['requestHeaders.lastModified'] = parameters['requestHeadersLastModified'];
  queryParameters['requestHeaders.location.absolute'] = parameters['requestHeadersLocationAbsolute'];
  queryParameters['requestHeaders.location.authority'] = parameters['requestHeadersLocationAuthority'];
  queryParameters['requestHeaders.location.fragment'] = parameters['requestHeadersLocationFragment'];
  queryParameters['requestHeaders.location.host'] = parameters['requestHeadersLocationHost'];
  queryParameters['requestHeaders.location.opaque'] = parameters['requestHeadersLocationOpaque'];
  queryParameters['requestHeaders.location.path'] = parameters['requestHeadersLocationPath'];
  queryParameters['requestHeaders.location.port'] = parameters['requestHeadersLocationPort'];
  queryParameters['requestHeaders.location.query'] = parameters['requestHeadersLocationQuery'];
  queryParameters['requestHeaders.location.rawAuthority'] = parameters['requestHeadersLocationRawAuthority'];
  queryParameters['requestHeaders.location.rawFragment'] = parameters['requestHeadersLocationRawFragment'];
  queryParameters['requestHeaders.location.rawPath'] = parameters['requestHeadersLocationRawPath'];
  queryParameters['requestHeaders.location.rawQuery'] = parameters['requestHeadersLocationRawQuery'];
  queryParameters['requestHeaders.location.rawSchemeSpecificPart'] = parameters['requestHeadersLocationRawSchemeSpecificPart'];
  queryParameters['requestHeaders.location.rawUserInfo'] = parameters['requestHeadersLocationRawUserInfo'];
  queryParameters['requestHeaders.location.scheme'] = parameters['requestHeadersLocationScheme'];
  queryParameters['requestHeaders.location.schemeSpecificPart'] = parameters['requestHeadersLocationSchemeSpecificPart'];
  queryParameters['requestHeaders.location.userInfo'] = parameters['requestHeadersLocationUserInfo'];
  queryParameters['requestHeaders.origin'] = parameters['requestHeadersOrigin'];
  queryParameters['requestHeaders.pragma'] = parameters['requestHeadersPragma'];
  queryParameters['requestHeaders.upgrade'] = parameters['requestHeadersUpgrade'];
  queryParameters['requestHeaders.vary'] = parameters['requestHeadersVary'];
  queryParameters['requestMethod'] = parameters['requestMethod'];
  queryParameters['roleNames'] = parameters['roleNames'];
  queryParameters['selectCompanyId'] = parameters['selectCompanyId'];
  queryParameters['sessionId'] = parameters['sessionId'];
  queryParameters['ucId'] = parameters['ucId'];
  queryParameters['userAccount'] = parameters['userAccount'];
  queryParameters['userId'] = parameters['userId'];
  queryParameters['userMask'] = parameters['userMask'];
  queryParameters['userSource'] = parameters['userSource'];
  queryParameters['userStatus'] = parameters['userStatus'];
  queryParameters['userType'] = parameters['userType'];
  queryParameters['username'] = parameters['username'];
  queryParameters['wechatCode'] = parameters['wechatCode'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 勾选/取消勾选 - updateTickStatusUsingPUT
 * /rest/companytax/merge/invoice/v1.0/invoiceTick
 * @param invoices - invoices
 * @param tickStatus - 勾选状态
 */
export const invoiceInvoicetick = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/invoice/v1.0/invoiceTick'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['invoices']
  if (parameters['invoices'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: invoices'))
  }
  body = parameters['tickStatus']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 按条件查找进项发票列表 - findAllUsingGET
 * /rest/companytax/merge/invoice/v1.0/list
 * @param certifyPeriodFrom - 属期开始
 * @param certifyPeriodTo - 属期结束
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param invoiceCode - 发票代码
 * @param invoiceNumber - 发票号码
 * @param page - 
 * @param pageSize - 
 * @param purchaseAccountsId - 对账单ID
 * @param sellerName - 销方名称
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 * @param tickStatus - 勾选状态
 */
export const invoiceList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/invoice/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['certifyPeriodFrom'] = parameters['certifyPeriodFrom'];
  queryParameters['certifyPeriodTo'] = parameters['certifyPeriodTo'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['invoiceCode'] = parameters['invoiceCode'];
  queryParameters['invoiceNumber'] = parameters['invoiceNumber'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['purchaseAccountsId'] = parameters['purchaseAccountsId'];
  queryParameters['sellerName'] = parameters['sellerName'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  queryParameters['tickStatus'] = parameters['tickStatus'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 新增其他进项发票 - 
 * /rest/companytax/merge/invoiceOther/v1.0
 * @param invoiceOther - invoiceOther
 */
export const insertInvoiceInputOtherUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/invoiceOther/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['invoiceOther']
  if (parameters['invoiceOther'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: invoiceOther'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查找其他进项发票列表 - findAllUsingGET_1
 * /rest/companytax/merge/invoiceOther/v1.0/list
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param invoicePeriodFrom - 发票属期开始
 * @param invoicePeriodTo - 发票属期结束
 * @param invoiceType - 发票类型
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const invoiceOtherList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/invoiceOther/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['invoicePeriodFrom'] = parameters['invoicePeriodFrom'];
  queryParameters['invoicePeriodTo'] = parameters['invoicePeriodTo'];
  queryParameters['invoiceType'] = parameters['invoiceType'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改其他进项发票 - 
 * /rest/companytax/merge/invoiceOther/v1.0/{invoiceId}
 * @param invoiceId - invoiceId
 * @param invoiceOther - invoiceOther
 */
export const updateInvoiceStatusByBatchUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/invoiceOther/v1.0/{invoiceId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{invoiceId}', `${parameters['invoiceId']}`)
  if (parameters['invoiceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: invoiceId'))
  }
  body = parameters['invoiceOther']
  if (parameters['invoiceOther'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: invoiceOther'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 根据ID删除其他发票 - 
 * /rest/companytax/merge/invoiceOther/v1.0/{invoiceId}
 * @param invoiceId - invoiceId
 */
export const deleteByIdUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/invoiceOther/v1.0/{invoiceId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{invoiceId}', `${parameters['invoiceId']}`)
  if (parameters['invoiceId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: invoiceId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 新增销项发票 - 
 * /rest/companytax/merge/invoiceOutput/v1.0
 * @param invoiceOutput - invoiceOutput
 */
export const insertInvoiceInputUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/invoiceOutput/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['invoiceOutput']
  if (parameters['invoiceOutput'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: invoiceOutput'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查找销项发票列表 - findInvoiceOutputListUsingGET
 * /rest/companytax/merge/invoiceOutput/v1.0/list
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param invoiceCode - 发票代码
 * @param invoiceDateFrom - 开票开始日期
 * @param invoiceDateTo - 开票结束日期
 * @param invoiceNumber - 发票号码
 * @param invoiceType - 发票类型（字典 1、专票；2、普票）
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const invoiceOutputList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/invoiceOutput/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['invoiceCode'] = parameters['invoiceCode'];
  queryParameters['invoiceDateFrom'] = parameters['invoiceDateFrom'];
  queryParameters['invoiceDateTo'] = parameters['invoiceDateTo'];
  queryParameters['invoiceNumber'] = parameters['invoiceNumber'];
  queryParameters['invoiceType'] = parameters['invoiceType'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改销项发票 - 
 * /rest/companytax/merge/invoiceOutput/v1.0/{invoiceId}
 * @param invoiceId - 发票id
 * @param invoiceOutput - invoiceOutput
 */
export const updateInvoiceOutputUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/invoiceOutput/v1.0/{invoiceId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{invoiceId}', `${parameters['invoiceId']}`)
  body = parameters['invoiceOutput']
  if (parameters['invoiceOutput'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: invoiceOutput'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 删除销项发票 - 
 * /rest/companytax/merge/invoiceOutput/v1.0/{invoiceId}
 * @param invoiceId - 发票id
 */
export const deleteInvoiceOutputUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/invoiceOutput/v1.0/{invoiceId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{invoiceId}', `${parameters['invoiceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 新增申报数据表 - 
 * /rest/companytax/merge/vatdeclare/declaration/v1.0
 * @param declaration - 申报实体<div></div><span class="hljs-literal">所属校验组:InsertGroup</span>
 */
export const insertDeclarationUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/declaration/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['declaration']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 根据属期税种查询是否存在数据 - checkDeclarationUsingGET
 * /rest/companytax/merge/vatdeclare/declaration/v1.0/check/{taxName}/{declarationPeriod}
 * @param declarationPeriod - 属期
 * @param taxName - 税种
 */
export const declarationCheck = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/declaration/v1.0/check/{taxName}/{declarationPeriod}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{declarationPeriod}', `${parameters['declarationPeriod']}`)
  path = path.replace('{taxName}', `${parameters['taxName']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 申报表申报 - updateValidStatusUsingPUT
 * /rest/companytax/merge/vatdeclare/declaration/v1.0/declare/{declarationId}
 * @param declarationId - 申报表id
 */
export const declarationDeclare = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/declaration/v1.0/declare/{declarationId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{declarationId}', `${parameters['declarationId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 查询申报数据表 - findDeclarationUsingGET
 * /rest/companytax/merge/vatdeclare/declaration/v1.0/list
 * @param data - 
 * @param declarationPeriod - 申报属期
 * @param declarationType - 申报属期类型
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param periodForYear - 按照年度查询属期
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 * @param taxName - 税种
 */
export const declarationList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/declaration/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['declarationPeriod'] = parameters['declarationPeriod'];
  queryParameters['declarationType'] = parameters['declarationType'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['periodForYear'] = parameters['periodForYear'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  queryParameters['taxName'] = parameters['taxName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 生成申报表PDF - getDeclarationPdfUsingGET
 * /rest/companytax/merge/vatdeclare/declaration/v1.0/pdf/{declarationId}
 * @param declarationId - 申报表id
 */
export const declarationPdf = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/declaration/v1.0/pdf/{declarationId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{declarationId}', `${parameters['declarationId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 根据属期查询申报list - getByTaxPeriodListUsingGET
 * /rest/companytax/merge/vatdeclare/declaration/v1.0/peroidList/{taxName}/{declarationPeriod}
 * @param declarationPeriod - 申报属期
 * @param taxName - 税种
 */
export const declarationPeroidlist = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/declaration/v1.0/peroidList/{taxName}/{declarationPeriod}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{declarationPeriod}', `${parameters['declarationPeriod']}`)
  path = path.replace('{taxName}', `${parameters['taxName']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 生成申报表xml - getDeclarationXmlUsingGET
 * /rest/companytax/merge/vatdeclare/declaration/v1.0/xml/{declarationId}
 * @param declarationId - 申报表id
 */
export const declarationXml = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/declaration/v1.0/xml/{declarationId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{declarationId}', `${parameters['declarationId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询申报数据表 - 
 * /rest/companytax/merge/vatdeclare/declaration/v1.0/{declarationId}
 * @param declarationId - 申报表id
 */
export const getDeclarationUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/declaration/v1.0/{declarationId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{declarationId}', `${parameters['declarationId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改申报表 - 
 * /rest/companytax/merge/vatdeclare/declaration/v1.0/{declarationId}
 * @param declaration - 申报实体<div></div><span class="hljs-literal">所属校验组:UpdateGroup</span>
 * @param declarationId - 申报表id
 */
export const updateDeclarationUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/declaration/v1.0/{declarationId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['declaration']
  path = path.replace('{declarationId}', `${parameters['declarationId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 删除申报表 - 
 * /rest/companytax/merge/vatdeclare/declaration/v1.0/{declarationId}
 * @param declarationId - 申报表id
 */
export const deleteDeclarationUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/declaration/v1.0/{declarationId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{declarationId}', `${parameters['declarationId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 根据属期税种查询上期数据 - 
 * /rest/companytax/merge/vatdeclare/declaration/v1.0/{taxName}/{declarationPeriod}
 * @param declarationPeriod - 属期
 * @param taxName - 税种
 */
export const getDeclarationHistNoDataUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/declaration/v1.0/{taxName}/{declarationPeriod}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{declarationPeriod}', `${parameters['declarationPeriod']}`)
  path = path.replace('{taxName}', `${parameters['taxName']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询申报表历史取数，返回格式为{key:value,key:value} - findDeclarationForHistrptUsingGET
 * /rest/companytax/merge/vatdeclare/declarationHistrpt/v1.0/list/{taxName}/{declarationPeriod}
 * @param declarationPeriod - 属期
 * @param taxName - 税种
 */
export const declarationHistrptList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/declarationHistrpt/v1.0/list/{taxName}/{declarationPeriod}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{declarationPeriod}', `${parameters['declarationPeriod']}`)
  path = path.replace('{taxName}', `${parameters['taxName']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}

/**
 * 根据申报表属期、税种、key取数，返回格式为{key:value,key:value} - findDeclarationForRptByKeyUsingGET
 * /rest/companytax/merge/vatdeclare/declarationRpt/v1.0/findDeclarationForRptByKey
 * @param declarationPeriod - 
 * @param keyList - 
 * @param periodList - 
 * @param taxName - 
 */
export const declarationRptFinddeclarationforrptbykey = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/declarationRpt/v1.0/findDeclarationForRptByKey'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['declarationPeriod'] = parameters['declarationPeriod'];
  queryParameters['keyList'] = parameters['keyList'];
  queryParameters['periodList'] = parameters['periodList'];
  queryParameters['taxName'] = parameters['taxName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 根据申报表id查询申报表报表取数，返回格式为{key:value,key:value} - findDeclarationForHistrptUsingGET_1
 * /rest/companytax/merge/vatdeclare/declarationRpt/v1.0/list/{declarationId}
 * @param declarationId - 申报表Id
 */
export const declarationRptList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/declarationRpt/v1.0/list/{declarationId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{declarationId}', `${parameters['declarationId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 申报查询系统取数留档，返回格式为{key:{sys:value,user:value}} - findDeclarationForRptheadUsingGET
 * /rest/companytax/merge/vatdeclare/declarationRpthead/v1.0/list/{declarationId}
 * @param declarationId - 申报表Id
 */
export const declarationRptheadList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/declarationRpthead/v1.0/list/{declarationId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{declarationId}', `${parameters['declarationId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 新增申报数据表模板 - 
 * /rest/companytax/merge/vatdeclare/module/v1.0
 * @param module - 申报模板实体<div></div><span class="hljs-literal">所属校验组:InsertGroup</span>
 */
export const insertDeclarationUsingPOST_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/module/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['module']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 根据税种返回符合的模板 - getDecModuleByTaxUsingGET_1
 * /rest/companytax/merge/vatdeclare/module/v1.0/getDecModuleByTax/{taxName}
 * @param taxName - 税种
 */
export const moduleGetdecmodulebytax = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/module/v1.0/getDecModuleByTax/{taxName}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{taxName}', `${parameters['taxName']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询申报数据表模板 - findDeclarationModuleUsingGET_1
 * /rest/companytax/merge/vatdeclare/module/v1.0/list
 * @param data - 
 * @param endValiddate - 有效期结束时间
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param startValiddate - 有效期开始时间
 * @param take - 
 * @param taxName - 税种
 * @param taxType - 纳税人类型
 */
export const moduleList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/module/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['endValiddate'] = parameters['endValiddate'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['startValiddate'] = parameters['startValiddate'];
  queryParameters['take'] = parameters['take'];
  queryParameters['taxName'] = parameters['taxName'];
  queryParameters['taxType'] = parameters['taxType'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询申报数据表模板 - 
 * /rest/companytax/merge/vatdeclare/module/v1.0/{moduleId}
 * @param moduleId - 申报表id
 */
export const getDeclarationModuleUsingGET_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/module/v1.0/{moduleId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{moduleId}', `${parameters['moduleId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改申报表模板 - 
 * /rest/companytax/merge/vatdeclare/module/v1.0/{moduleId}
 * @param module - 申报模板实体<div></div><span class="hljs-literal">所属校验组:UpdateGroup</span>
 * @param moduleId - 申报表id
 */
export const updateDeclarationModuleUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/module/v1.0/{moduleId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['module']
  path = path.replace('{moduleId}', `${parameters['moduleId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 删除申报表模板 - 
 * /rest/companytax/merge/vatdeclare/module/v1.0/{moduleId}
 * @param moduleId - 申报表id
 */
export const deleteDeclarationModuleUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/module/v1.0/{moduleId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{moduleId}', `${parameters['moduleId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 新增申报申报表适合区域 - 
 * /rest/companytax/merge/vatdeclare/moduleArea/v1.0
 * @param area - 申报模板实体
 */
export const insertDeclarationModuleAreaUsingPOST_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/moduleArea/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['area']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查询申报表适合区域 - findDeclarationModuleAreaUsingGET_1
 * /rest/companytax/merge/vatdeclare/moduleArea/v1.0/list
 * @param data - 
 * @param declarationModuleId - 模板编号
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param provinceCode - 省份代码 (null表示全国适用)
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const moduleAreaList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/moduleArea/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['declarationModuleId'] = parameters['declarationModuleId'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['provinceCode'] = parameters['provinceCode'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 根据ID申报表适合区域 - 
 * /rest/companytax/merge/vatdeclare/moduleArea/v1.0/{sequenceId}
 * @param sequenceId - 申报表id
 */
export const getDeclarationModuleAreaUsingGET_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/moduleArea/v1.0/{sequenceId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{sequenceId}', `${parameters['sequenceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改申报表适合区域 - 
 * /rest/companytax/merge/vatdeclare/moduleArea/v1.0/{sequenceId}
 * @param area - 申报模板实体
 * @param sequenceId - 申报表id
 */
export const updateDeclarationModuleAreaUsingPUT_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/moduleArea/v1.0/{sequenceId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['area']
  path = path.replace('{sequenceId}', `${parameters['sequenceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 删除申报表适合区域 - 
 * /rest/companytax/merge/vatdeclare/moduleArea/v1.0/{sequenceId}
 * @param sequenceId - 申报表id
 */
export const deleteDeclarationModuleAreaUsingDELETE_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/moduleArea/v1.0/{sequenceId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{sequenceId}', `${parameters['sequenceId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 新增申报模板规则 - 
 * /rest/companytax/merge/vatdeclare/moduleRule/v1.0
 * @param rule - 模板规则实体
 */
export const insertDeclarationModuleRuleUsingPOST_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/moduleRule/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['rule']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查询模板规则表 - findDeclarationModuleRuleUsingGET_1
 * /rest/companytax/merge/vatdeclare/moduleRule/v1.0/list
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param moduleId - 模板编号
 * @param operateType - 操作类型（增删改）
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 * @param taxName - 税种
 */
export const moduleRuleList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/moduleRule/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['moduleId'] = parameters['moduleId'];
  queryParameters['operateType'] = parameters['operateType'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  queryParameters['taxName'] = parameters['taxName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 根据ID模板规则 - 
 * /rest/companytax/merge/vatdeclare/moduleRule/v1.0/{ruleId}
 * @param ruleId - 模板规则id
 */
export const getDeclarationModuleRuleUsingGET_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/moduleRule/v1.0/{ruleId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{ruleId}', `${parameters['ruleId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 删除模板规则 - 
 * /rest/companytax/merge/vatdeclare/moduleRule/v1.0/{ruleId}
 * @param rule - rule
 * @param ruleId - 申报规则id
 */
export const updateDeclarationModuleRuleUsingPUT_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/moduleRule/v1.0/{ruleId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['rule']
  if (parameters['rule'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: rule'))
  }
  path = path.replace('{ruleId}', `${parameters['ruleId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 删除模板规则 - 
 * /rest/companytax/merge/vatdeclare/moduleRule/v1.0/{ruleId}
 * @param ruleId - 申报规则id
 */
export const deleteDeclarationModuleRuleUsingDELETE_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/moduleRule/v1.0/{ruleId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{ruleId}', `${parameters['ruleId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 查看取数规则 - getCellRuleUsingGET
 * /rest/companytax/merge/vatdeclare/settlement/v1.0/cell/rule/{flowNo}
 * @param flowNo - 流水号
 * @param position - 坐标-填报表单名:单元格
 */
export const settlementCell = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/settlement/v1.0/cell/rule/{flowNo}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{flowNo}', `${parameters['flowNo']}`)
  queryParameters['position'] = parameters['position'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 根据申报表ID获取简易申报数据历史 - getDeclarationDraftUsingGET
 * /rest/companytax/merge/vatdeclare/settlement/v1.0/declare/getRuleDraft/{declarationId}
 * @param declarationId - 申报表ID
 */
export const settlementDeclare = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/settlement/v1.0/declare/getRuleDraft/{declarationId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{declarationId}', `${parameters['declarationId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 保存申报表草稿：关联申报表和取数明细历史 - saveDeclarationDraftUsingPOST
 * /rest/companytax/merge/vatdeclare/settlement/v1.0/declare/rule/draft/{flowNo}/{declarationId}
 * @param data - 简易申报数据
 * @param declarationId - 申报表ID
 * @param flowNo - 流水号
 */
export const saveDeclarationDraft = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/settlement/v1.0/declare/rule/draft/{flowNo}/{declarationId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['data']
  path = path.replace('{declarationId}', `${parameters['declarationId']}`)
  path = path.replace('{flowNo}', `${parameters['flowNo']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 显示汇算清缴规则配置信息表单 - infoUsingGET
 * /rest/companytax/merge/vatdeclare/settlement/v1.0/info/{ruleId}
 * @param ruleId - ruleId
 */
export const settlementInfo = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/settlement/v1.0/info/{ruleId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{ruleId}', `${parameters['ruleId']}`)
  if (parameters['ruleId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: ruleId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 列出汇算清缴规则配置列表 - listUsingGET
 * /rest/companytax/merge/vatdeclare/settlement/v1.0/list
 */
export const settlementList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/settlement/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 从全局配置创建：用于新建申报时创建公司规则 - initFromGlobalUsingPOST_1
 * /rest/companytax/merge/vatdeclare/settlement/v1.0/rule/init
 */
export const settlementRule = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/settlement/v1.0/rule/init'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 从全局配置创建：重置规则 - createFromGlobalUsingGET
 * /rest/companytax/merge/vatdeclare/settlement/v1.0/rule/refresh
 */
export const createFromGlobal= function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/settlement/v1.0/rule/refresh'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 新增或更新规则表单信息 - saveUsingPOST
 * /rest/companytax/merge/vatdeclare/settlement/v1.0/save
 * @param settlement - settlement
 */
export const settlementSave = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/settlement/v1.0/save'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['settlement']
  if (parameters['settlement'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: settlement'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 保存一个单元格规则并刷新单元格数据 - saveCellUsingPOST
 * /rest/companytax/merge/vatdeclare/settlement/v1.0/save/cell
 * @param queryVo - queryVo
 */
export const saveCell= function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/settlement/v1.0/save/cell'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['queryVo']
  if (parameters['queryVo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: queryVo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 申报查询进项明细 - findDeclareInvoiceInputDetailUsingGET
 * /rest/companytax/merge/vatdeclare/v1.0/list/findDeclareInvoiceDetail
 * @param invoicePeriod - 发票属期
 */
export const vatdeclareList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/v1.0/list/findDeclareInvoiceDetail'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['invoicePeriod'] = parameters['invoicePeriod'];
  if (parameters['invoicePeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: invoicePeriod'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 申报查询进项税额合计 - findDeclareInvoiceInputTaxTotalUsingGET
 * /rest/companytax/merge/vatdeclare/v1.0/list/findDeclareInvoiceTaxTotal
 * @param invoicePeriod - 发票属期
 */
export const findDeclareInvoiceInputTaxTotal= function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/v1.0/list/findDeclareInvoiceTaxTotal'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['invoicePeriod'] = parameters['invoicePeriod'];
  if (parameters['invoicePeriod'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: invoicePeriod'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询账载应税收入明细 - findVatDeclareIncomeDetailUsingGET
 * /rest/companytax/merge/vatdeclare/v1.0/list/findIncomeDetail
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 * @param voucherTimeFrom - 凭证日期开始
 * @param voucherTimeTo - 凭证日期结束
 */
export const findVatDeclareIncomeDetail= function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/v1.0/list/findIncomeDetail'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  queryParameters['voucherTimeFrom'] = parameters['voucherTimeFrom'];
  if (parameters['voucherTimeFrom'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: voucherTimeFrom'))
  }
  queryParameters['voucherTimeTo'] = parameters['voucherTimeTo'];
  if (parameters['voucherTimeTo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: voucherTimeTo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 分组查询基本项目数据 - findVatDeclareGroupByUsingGET
 * /rest/companytax/merge/vatdeclare/v1.0/list/findVatDeclareGroupBy
 * @param voucherTimeFrom - 凭证日期开始
 * @param voucherTimeTo - 凭证日期结束
 */
export const findVatDeclareGroup= function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/v1.0/list/findVatDeclareGroupBy'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['voucherTimeFrom'] = parameters['voucherTimeFrom'];
  if (parameters['voucherTimeFrom'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: voucherTimeFrom'))
  }
  queryParameters['voucherTimeTo'] = parameters['voucherTimeTo'];
  if (parameters['voucherTimeTo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: voucherTimeTo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询提醒注意项目 - findVatDeclareReminderItemsUsingGET
 * /rest/companytax/merge/vatdeclare/v1.0/list/findVatDeclareReminderItems
 * @param voucherTimeFrom - 凭证日期开始
 * @param voucherTimeTo - 凭证日期结束
 */
export const findVatDeclareReminderItems = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/v1.0/list/findVatDeclareReminderItems'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['voucherTimeFrom'] = parameters['voucherTimeFrom'];
  if (parameters['voucherTimeFrom'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: voucherTimeFrom'))
  }
  queryParameters['voucherTimeTo'] = parameters['voucherTimeTo'];
  if (parameters['voucherTimeTo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: voucherTimeTo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询提醒注意项目明细 - findVatDeclareReminderItemsDeatilUsingGET
 * /rest/companytax/merge/vatdeclare/v1.0/list/findVatDeclareReminderItemsDeatil
 * @param content - 查询哪种注意项目
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 * @param voucherTimeFrom - 凭证日期开始
 * @param voucherTimeTo - 凭证日期结束
 */
export const findVatDeclareReminderItemsDeatil= function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companytax/merge/vatdeclare/v1.0/list/findVatDeclareReminderItemsDeatil'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['content'] = parameters['content'];
  if (parameters['content'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: content'))
  }
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  queryParameters['voucherTimeFrom'] = parameters['voucherTimeFrom'];
  if (parameters['voucherTimeFrom'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: voucherTimeFrom'))
  }
  queryParameters['voucherTimeTo'] = parameters['voucherTimeTo'];
  if (parameters['voucherTimeTo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: voucherTimeTo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 从全局配置创建：用于新建账套时创建公司规则 - createFromGlobalUsingPOST_1
 * /restsafe/companytax/merge/vatdeclare/settlement/v1.0/init/{companyId}
 * @param companyId - companyId
 */
export const settlementInit = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companytax/merge/vatdeclare/settlement/v1.0/init/{companyId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{companyId}', `${parameters['companyId']}`)
  if (parameters['companyId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: companyId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}