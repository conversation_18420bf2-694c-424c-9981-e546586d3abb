import request from '@/utils/request';

/**
 * 白莲银行流水预览凭证
 * @export
 * @param {Object} params 请求参数
 * @param {string} params.paymentIds 收付款ID组
 * @param {number} params.paymentType 收付类型（1：采购，2：收入）
 */
export function bailianBankAccountPreviewVoucher(params = {}) {
  const API = '/rest/proxy/account/merge/payment/v1.0/previewVoucherBatch';
  const config = { params };
  return request.post(API, null, config);
}
/**
 * 银行流水预览凭证
 * @export
 * @param {Object} params 请求参数
 * @param {number} params.abstractType 凭证摘要: {0: 默认凭证摘要, 1: 流水上的摘要}
 * @param {string} params.accountPeriod 会计周期
 * @param {number} params.dateType 凭证日期: {0: 流水交易日期, 1: 本月最后一天}
 * @param {string} params.paymentIds 生成凭证单据Id数组
 * @param {number} params.voucherType 生成凭证类型(1、2、3、4)
 */
export function bankAccountPreviewVoucher(params = {}) {
  const API = `/rest/proxy/account/bankAccount/v1.0/preViewVouchers/${params.dateType}/${params.abstractType}`;
  const config = { params };
  return request.post(API, null, config);
}
/**
 * 现金流水预览凭证
 * @export
 * @param {Object} params 请求参数
 * @param {number} params.abstractType 凭证摘要: {0: 默认凭证摘要, 1: 流水上的摘要}
 * @param {string} params.accountPeriod 会计周期
 * @param {number} params.dateType 凭证日期: {0: 流水交易日期, 1: 本月最后一天}
 * @param {string} params.paymentIds 生成凭证单据Id数组
 * @param {number} params.voucherType 生成凭证类型(1、2、3、4)
 */
export function cashPaymentPreviewVoucher(params = {}) {
  const API = `/rest/proxy/account/cashPayment/v1.0/preViewVouchers/${params.dateType}/${params.abstractType}`;
  const config = { params };
  return request.post(API, null, config);
}
/**
 * 白莲现金流水预览凭证
 * @export
 * @param {Object} params 请求参数
 * @param {string} params.accountPeriod 会计周期
 * @param {string} params.paymentIds 生成凭证单据Id数组
 * @param {number} params.voucherType 生成凭证类型(1、2、3、4)
 */
export function bailianCashPaymentPreviewVoucher(params = {}) {
  const API = '/rest/proxy/account/cashPayment/v2.0/preViewVouchers';
  const config = { params };
  return request.post(API, null, config);
}

/**
 * 预览折旧资产凭证
 * @export
 * @param {Object} params 请求参数
 * @param {string} params.accountPeriod 会计周期
 */
export function depreciationPreviewVoucher(params = {}) {
  const API = `/rest/companyAccount/merge/depreciation/v1.0/preView/${params.accountPeriod}`;
  return request.post(API);
}
