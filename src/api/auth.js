/*
 * @Description: auth模块接口
 * @version:
 * @Company: 海闻软件
 * @Author: 肖泽涛
 * @Date: 2019-01-19 14:43:43
 * @LastEditors: 肖泽涛
 * @LastEditTime: 2019-03-13 10:50:14
 */
import request from '@/utils/request';

/**
 *  账套-分享弹窗  查询/新增   指定账套分享码
 *
 * @export
 * @returns
 */
export function getCompanyShare(type, companyId, params) {
  return request[type](`/auth/rest/companyShare/token/v1.0/owner/company/${companyId}`, params);
}

/**
 *  账套-分享弹窗  删除   指定账套分享码
 *
 * @export
 * @returns
 */
export function deleteCompanyShare(companyId) {
  return request.delete(`/auth/rest/companyShare/token/v1.0/owner/company/${companyId}`);
}

/**
 *  账套-分享弹窗  查询指定账套分享关系
 *
 * @export
 * @returns
 */
export function getShareList(companyId, params = { page: 1, pageSize: 9999 }) {
  return request.get(`/auth/rest/companyShare/rel/v1.0/owner/company/${companyId}`, { params });
}

/**
 *  账套-分享弹窗  更新分享关系的分享状态
 *
 * @export
 * @returns
 */
export function updataShareStatus(relId, enabled) {
  return request.put(`/auth/rest/companyShare/rel/v1.0/owner/${relId}/${enabled}`);
}

/**
 *  账套-分享弹窗  删除分享关系
 *
 * @export
 * @returns
 */
export function deleteShare(relId) {
  return request.delete(`/auth/rest/companyShare/rel/v1.0/owner/${relId}`);
}

/**
 *  "任意用户"查询分享码信息
 *
 * @export
 * @returns
 */
export function getShareMessage(token) {
  return request.get(`/auth/rest/companyShare/token/v1.0/${token}`);
}

/**
 * 受邀者"加入分享关系
 *
 * @export
 * @returns
 */
export function agreeShare(token, params) {
  return request.post(`/auth/rest/companyShare/rel/v1.0/invitee/${token}`, params);
}

/**
 * 查询"受邀者"是当前用户已有的分享关系
 *
 * @export
 * @returns
 */
export function getInviteeList(params = { page: 1, pageSize: 9999 }) {
  return request.get('/auth/rest/companyShare/rel/v1.0/invitee/list', { params });
}

// 更新代账公司描述信息
export function updateDescribe(params) {
  return request.post('/auth/bk/company/describe', params);
}

// 获取代账公司描述信息
export function getDescribeDate(bkCompanyCode) {
  return request.get(`/auth/bk/company/describe/${bkCompanyCode}`);
}

// 帐套管理-移交帐套-获取转接链接
export function getCompanyTransfer(companyId) {
  return request.get(`/auth/rest/handOver/v1.0/owner/company/${companyId}`);
}
// 帐套管理-移交帐套-重新生成
export function postCompanyTransfer(companyId) {
  return request.post(`/auth/rest/handOver/v1.0/owner/company/${companyId}`);
}

// 帐套管理-转接帐套-查询信息
export function getCompanyTransferToken(token) {
  return request.get(`/auth/rest/handOver/v1.0/${token}`);
}

// 帐套管理-转接帐套-接受账套
export function postCompanyTransferToken(params) {
  return request.post('/auth/rest/handOver/v1.0/accept', params);
}

// 账号管理-用户外部信息表-更新
/**
 * 账号管理-用户外部信息表-更新
 * @param {*} params
 * @param {string} params.externalAddress 外部联系地址
 * @param {string} params.externalLinkage 外部联系方式
 * @param {string} params.jobNumber 工号
 * @param {number} params.userId 用户id
 * @returns
 */
export function updateExternalInfo(params) {
  const API = '/auth/usersExternalInfo/v1.1/addOrUpdate';
  return request.post(API, params);
}

/*
* CRM  自定义角色
* @param {*} params.scope crm，jz
*/
export function getCustomRuleList(params) {
  return request.get('/auth/roles/custom/v1.0/list', { params });
}
/**
 * @typedef BkComSubAccountForm - 记账公司子账号编辑表单
 * @property {string} confirmPwd - 二次确认密码,必须和登录时一样的方式加密<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">二次确认密码不能为空</span>
 * @property {number[]} customRoleIds - 自定义角色ID
 * @property {string} externalLinkage - 对外联系方式<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">不能超出{max}位</span>
 * @property {number} groupId - 用户组ID<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">不能为空</span>
 * @property {number} groupScope - 数据权限<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">值范围不在支持枚举内</span><br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">值范围不在支持枚举内</span><br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">不能为空</span>
 * @property {string} newPwd - 密码,必须和登录时一样的方式加密<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">密码不能为空</span>
 * @property {number[]} sysRoleIds - 系统角色ID
 * @property {string} userAccount - 用户帐号<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">不能为空</span><br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">账号名需要{min}个字符以上且不能超出{max}个字符</span>
 * @property {string} username - 用户名称（显示名）<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">用户名称不能为空</span><br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">用户名称需要{min}个字符以上且不能超出{max}个字符</span>
* */

/**
 * @description 员工管理-添加子账号
 * @param {BkComSubAccountForm} params - subAccountForm
* */
export const addBkComSubAccount = (params) => request.post('/auth/staff/management/v1.0/subAccount/insert', params);
/**
 * @typedef StaffManagementUpdateForm - 编辑用户表单
 * @property {number[]} customRoleIds - 自定义角色ID
 * @property {string} externalLinkage - 对外联系方式<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">不能超出{max}位</span>
 * @property {number} groupId - 用户组ID<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">不能为空</span>
 * @property {number} groupScope - 数据权限(1本人2本组织3全公司)<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">数据权限值范围不在支持枚举内</span><br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">数据权限值范围不在支持枚举内</span><br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">数据权限不能为空</span>
 * @property {number[]} sysRoleIds - 系统角色ID
 * @property {number} userId - 用户ID<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">不能为空</span>
 * @property {number} userStatus - 用户状态(0停用1启用)<br/>&nbsp;&nbsp;属性校验：<span class="hljs-string">用户状态不能为空</span>
* */

/**
 * @description 员工管理-修改员工
 * @param {StaffManagementUpdateForm} params - f
* */
export const updateStaff = (params) => request.put('/auth/staff/management/v1.0/update', params);

/*
* 员工管理-邀请员工-生成邀请链接v2.0
* @param {*} params.companyId 账套ID(空时不自动加入账套)
* @param {*} params.customRoleIds 自定义角色ID
* @param {*} params.groupId 用户组ID
* @param {*} params.groupScope 数据权限
* @param {*} params.系统角色ID 邀请人ID
*/
export function postInviteInfo(params) {
  const API = '/auth/staff/management/createInviteInfo/v2.0';
  return request.post(API, params);
}

/*
* 员工管理-邀请员工-获取邀请信息V2.0
* @param {*} token邀请链接的token
*/
export function getInviteInfo(token) {
  const API = `/auth/staff/management/getInviterInfo/v2.0/${token}`;
  return request.get(API);
}
/**
 * 获取公司列表
 */
export const getCompanyList = () => request.get('/auth/user/companys/list?pageSize=99999');
/**
 * 获取公司列表, 按已加入 和 未加入 分组返回
 * {data: [{join: [], }]}
 */
export const getCompanyListByGroup = () => request.get('/auth/user/companys/list/group?pageSize=99999');

export const selectCompany = (companyId) => request.get(`/auth/select/company/${companyId}`);
/**
 * 取消选中账套
 */
export const cancelSelectCompany = () => request.put('/auth/select/company/cancel');
