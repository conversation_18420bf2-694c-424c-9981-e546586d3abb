/*
 * @Description:  基础设置相关api
 * @version: 过验证eslint
 * @Company: 海闻软件
 * @Author: 晓荣
 * @LastEditors: 晓荣
 * @Date: 2019-04-25 16:55:14
 * @LastEditTime: 2019-04-25 17:36:07
 */

import request from '@/utils/request';

/** 单位换算新增
 * @description:
 * @param {list}
 * @return:
 */
export function postSaleunitCvt(list) {
  return request.post('/rest/companyConfig/goods/saleunitCvt/v1.0/insertBatch', JSON.stringify(list));
}

/** 单位换算修改
 * @description:
 * @param {type}  sequenceId 换算单位id singleData 单条数据
 * @return:
 */

export function putSaleunitCvt(sequenceId, singleData) {
  return request.put(`/rest/companyConfig/goods/saleunitCvt/v1.0/${sequenceId}`, JSON.stringify(singleData));
}

/** 打印封面： 查询进项发票的不含税金额、税额、张数各种条件下的合计
 *
 * @certifyPeriodFrom
 * @certifyPeriodTo
 * @invoiceType
 *
 */
 export function getCriteriaTotal(params) {
  return request.get('/rest/proxy/tax/merge/invoice/input/v1.0/list/criteriaTotal', { params });
}

/**
 * 账套业务设置修改
 * @param {number} moduleId 模块id:1.销售 2.采购 3.工资 4.资金
 * @param {string} settingJson 配置的json字符串
 */
export function putBusinessSetting(moduleId, settingJson) {
  return request.put(`/rest/companyConfig/companyBasis/moduleSetting/v1.0/${moduleId}`, {
    settingDetail: settingJson,
  });
}
/**
 * 获取账套业务设置
 * @param {number} moduleId 模块id:1.销售 2.采购 3.工资 4.资金
 */
export function getBusinessSetting(moduleId) {
  return request.get(`/rest/companyConfig/companyBasis/moduleSetting/v1.0/${moduleId}`);
}
