/*
 * @Description: 发票模块api
 * @version:
 * @Company: 海闻软件
 * @Author: 肖泽涛
 * @Date: 2019-01-08 10:39:20
 * @LastEditors: 启旭
 * @LastEditTime: 2019-05-31 12:09:20
 */

import request from '@/utils/request';

/**
  *统计进项发票
  *
  * @export
  * @param {*} {
  *   accountPeriod,
  *   isVoucher
  *  } -accountPeriod 会计属期（yyyy-MM） isVoucher 是否已生成凭证（0.未生成，1.已生成）
  * @returns Promise
  */
export function getInvoiceInputTotals({
  accountPeriod,
  isVoucher,
}) {
  return request.get('/rest/proxy/stmt/merge/report/invoiceInput/v1.0/totals', {
    params: {
      accountPeriod,
      isVoucher,
    },
  });
}

/**
  *统计销项发票
  *
  * @export
  * @param {*} {
  *   accountPeriod,
  *   isVoucher
  *  } -accountPeriod 会计属期（yyyy-MM） isVoucher 是否已生成凭证（0.未生成，1.已生成）
  * @returns Promise
  * 返回字段：
  * saleAmountTotal 金额合计（含税）
  * outputTaxAmountTotal 税额合计
  * invoiceCounts 发票数量
  * accountPeriod 开票属期
  */
export function getInvoiceOutputTotals({
  accountPeriod,
  isVoucher,
}) {
  return request.get('/rest/proxy/stmt/merge/report/invoiceOutput/v1.0/totals', {
    params: {
      accountPeriod,
      isVoucher,
    },
  });
}

/**
  *获取销项发票列表
  *
  * @export
  * @param {*} {
  *    accountPeriod = '',
  *   invoiceType = '',
  *   invoiceNumber =' ',
  *   isVoucher = '',
  *   purchaserName = '',
  *   saleAmountFrom = '',
  *   invoiceDateFrom = '',
  *   invoiceDateTo = '',
  *   pageSize = '',
  *   page = '',
  *   taxRate = ''
  * }
  * @returns
  */
export function getOutputInvoices({
  accountPeriod = '',
  invoiceType = '',
  invoiceNumber = ' ',
  isVoucher = '',
  purchaserName = '',
  saleAmountFrom = '',
  invoiceDateFrom = '',
  invoiceDateTo = '',
  pageSize = '',
  page = '',
  taxRate = '',
}) {
  return request.get('/rest/proxy/tax/merge/invoice/output/v1.0/list', {
    params: {
      accountPeriod,
      isVoucher,
      taxRate,
      invoiceType,
      invoiceNumber,
      purchaserName,
      saleAmountFrom,
      invoiceDateFrom,
      invoiceDateTo,
      pageSize,
      page,
    },
  });
}

/**
  *批量销项币别及汇率
  *
  * @export
  * @param {*} data [{"invoiceId":"2192522034089310978","currencyCode":1,"enabledInvoiceDest":true,"exchangeRate":2,"saleAmountO":200}]
  * @returns
  */
export function updateForeignCurrency(data) {
  return request.put('/rest/proxy/tax/merge/invoice/output/v1.0/updateForeignCurrency', data);
}
