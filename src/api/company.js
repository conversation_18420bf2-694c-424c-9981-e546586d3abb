/*
 * @Description:
 * @version: 过验证eslint
 * @Company: 海闻软件
 * @Author: 晓荣
 * @Date: 2019-07-17 10:38:36
 * @LastEditors: 晓荣
 * @LastEditTime: 2019-08-20 10:03:57
 */
import request from '@/utils/request';

/**
 *获取公司信息
 *
 * @export
 * @returns
 */
export function getCompanyInfo() {
  return request.get('/rest/companyConfig/companyBasis/companys/v1.1/company');
}

/**
 *修改公司纳税编号
 *
 * @export
 * @param {*} creditCode 公司编号
 * @returns
 */
export function editCompanyCode({ creditCode }) {
  return request.put('/rest/companyConfig/companyBasis/companys/v1.1/edit', JSON.stringify({ creditCode }));
}

/**
 *修改公司名称
 *
 * @export
 * @param {*} {companyName, companyId} : {公司名称，公司id}
 * @returns
 */
export function editCompanyName({ companyName, companyId }) {
  return request.put('/auth/usercenter/create/company', JSON.stringify({ companyName, companyId }));
}

/**
 *获取公司银行账号信息
 *
 * @export
 * @returns
 */
export function getBankAccount() {
  return request.get('/rest/companyConfig/companyBasis/bankAccounts/v1.1/list?invAccountFlag=true');
}

/**
 *获取公司所有的银行账号信息
 *
 * @export
 * @returns
 */
export function getAllBankAccount() {
  return request.get('/rest/companyConfig/companyBasis/bankAccounts/v1.1/list');
}

/**
 *修改公司银行账号信息
 *
 * @export
 * @param {*} bankName 银行名称
 * @param {*} bankAccount 银行账号
 * @returns
 */
export function postCompanyInfo({ bankName, bankAccount }) {
  return request.post('/rest/companyConfig/companyBasis/bankAccounts/v1.1/save', JSON.stringify([{
    bankName, bankAccount, accountStatus: true, accountType: 1, invAccountFlag: true,
  }]));
}

/**
 *获取公司一级部门信息
 *
 * @export
 * @returns
 */
export function getLv1GroupList() {
  return request.get('/rest/global/dimensionality/gdconfigs/v0.1/TAXCLOUD_APRATMENT');
}

/**
 *获取公司部门员工信息
 *
 * @export
 * @param {*} params
 * @returns
 */
export function getManagement(params) {
  // return request.get('/rest/companyConfig/companyBasis/companyUsers/v1.1/all');
  return request.get('/auth/staff/management/list', { params });
}

/**
 *获取用户组列表
 *
 * @param {string} params.groupId 用户组id, 过滤出其下级的部门
 * @returns
 */
 export function getGroupList(params) {
  return request.get('/auth/user/group/list', { params });
}

/**
 *获取公司部门树
 *
 * @export
 * @returns
 */
export async function getDepartmentTree() {
  const dp = await getGroupList();
  const resdata = dp.data.data;
  const tree = [];
  resdata.forEach((item) => {
    if (item.ifDefault) {
      tree.push({
        value: item.groupId,
        label: item.groupName,
        firstLevelDepartment: item.firstLevelDepartment,
        isShow: false,
        isDefault: true,
      });
    }
  });
  resdata.forEach((item) => {
    if (!item.ifDefault) {
      tree.forEach((tItem) => {
        if (item.firstLevelDepartment === tItem.firstLevelDepartment) {
          (tItem.children || (tItem.children = []))
            .push({
              value: item.groupId,
              label: item.groupName,
              firstLevelDepartment: item.firstLevelDepartment,
              isShow: false,
              isDefault: false,
            });
        }
      });
    }
  });
  return tree;
}

/**
 *新增公司部门
 *
 * @export
 * @param {*} groupName 部门名称
 * @param {*} firstLevelDepartment 上级部门标识
 * @returns
 */
export function insertGroup({ groupName, firstLevelDepartment, parentGroupId }) {
  return request.post('/auth/user/group/insert', { groupName, firstLevelDepartment, parentGroupId });
}

/**
 *修改公司部门
 *
 * @export
 * @param {*} groupId 部门id
 * @param {*} groupName 部门名称
 * @returns
 */
export function updateGroup({
 groupId, groupName, firstLevelDepartment, parentGroupId,
}) {
  return request.put('/auth/user/group/update', JSON.stringify({
 groupId, firstLevelDepartment, groupName, parentGroupId,
}));
}

/**
 *删除公司二级部门
 *
 * @export
 * @param {*} groupId 部门id
 * @returns
 */
export function deleteGroup(groupId) {
  return request.delete(`/auth/user/group/delete?groupId=${groupId}`);
}

/**
 *
 *
 * @export
 * @param {*} {userId- 用户id,firstLevelDepartment- 上级 ,
 * @param {*} bkCompanyCode- 代账公司code,
 * @param {*} groupId- 组id,companyId
 * @param {*} 公司id （可选）}
 * @returns
 */
export function editDepartment({
  userId,
  firstLevelDepartment,
  bkCompanyCode,
  groupId,
  companyId,
}) {
  return request.put(`/auth/staff/management/editMyApartment/${userId}`, JSON.stringify({
    firstLevelDepartment, bkCompanyCode, groupId, companyId,
  }));
}

/**
 * 员工借支余额
 *
 * @export
 * @returns
 */
export function debitbalance() {
  return request.get('/rest/proxy/account/staffbalance/v1.0/debitbalance');
}

/**
 * 获取邀请进入公司的token
 *
 * @export
 * @param {*} companyId -公司id
 * @param {*} {groupId, groupComName, roleNames}
 * @returns
 */
export function createShareCompanyInfo(companyId, {
  groupId, groupComName, roleNames, roleIds,
}) {
  const params = JSON.stringify({
    groupId,
    groupComName,
    roleNames,
    roleIds,
  });
  return request.post(`/auth/staff/management/createShareCompanyInfo/${companyId}?isRebuild=true`, params);
}

/**
 * 根据分享的token加入公司
 *
 * @export
 * @param {*} companyToken
 * @returns
 */
export function addCompany(companyToken) {
  return request.post(`/auth/staff/management/shareCompany?token=${companyToken}`);
}

/**
 *获取分享二维码
 *
 * @export
 * @param {*} {scene, page, width} -scene 分享时携带的参数 | page 分享的二维码打开的页面 | width 二维码的大小 默认值300px
 * @returns
 */
export function getQrCode({ scene, page, width }) {
  width = width || '300px';
  return request.post('/auth/wechatMini/getQrcode', { scene, page, width });
}

/**
 *通过token获取邀请信息
 *
 * @export
 * @param {*} token
 * @returns
 */
export function getTokenInfo(token) {
  return request.get(`/auth/staff/management/getCompanyInfo?token=${token}`, { header: { sessiontoken: '' } });
}

/**
 * 查询注册信息
 *
 * @export
 * @returns
 */
export function getRegisterInfo() {
  return request.get('/rest/companyConfig/companyBasis/companysOthers/v1.0/get/registerInfo');
}

/**
 * 查询税费种认定
 *
 * @export
 * @returns
 */
export function getTaxationInfo() {
  return request.get('/rest/companyConfig/companyBasis/companysOthers/v1.0/get/taxationInfo');
}

/**
 * 查询资格信
 *
 * @export
 * @returns
 */
export function getQualificationsInfo() {
  return request.get('/rest/companyConfig/companyBasis/companysOthers/v1.0/get/qualificationsInfo');
}

/**
 * 根据公司查询 公司标签
 */
export function getCompanyLabel(companyId) {
  return request.get(`/rest/companyConfig/companyBasis/companyTag/v1.0/findGdCompanyTagListByCompany/${companyId}`);
}
/**
 * 根据公司修改 公司标签
 */
export function editCompanyLabel(companyId, list) {
  return request.put(`/rest/companyConfig/companyBasis/companyTag/v1.0/updateGdCompanyTagForCompany/${companyId}`, list);
}

/**
 * 根据登录人查询 公司标签
 */
export function getLabelList() {
  return request.get('/rest/companyConfig/companyBasis/companyTag/v1.0/findGdCompanyTagListByUser');
}

/**
 * 新增 公司标签
 */
export function insertLabel(list) {
  return request.post('/rest/companyConfig/companyBasis/companyTag/v1.0/insert', list);
}

/**
 * 修改 公司标签
 */
export function editLabel(list) {
  return request.put('/rest/companyConfig/companyBasis/companyTag/v1.0/edit', list);
}

/**
 * 删除 公司标签
 */
export function deleteLabel(ids) {
  return request.delete(`/rest/companyConfig/companyBasis/companyTag/v1.0/delete?ids=${ids}`);
}

/**
 * 批量修改账套人员
 * controllType : 1 新增
 * controllType : 2 删除
 */
export function batchEditUser(userId, controllType, list) {
  return request.post(`/auth/user/batchCompany/role/${userId}/${controllType}`, list);
}

/**
 *
 *
 * @export
 * @param {*} params
 *  params.subjectCode  科目编码开头的科目
 *  params.subjectName  科目名称模糊匹配
 *  params.subjectCategories - 科目类型
    params.subjectFullName - 科目全称
    params.subjectStatus - 科目状态
 */
export function getSubjectinfoList(params) {
  return request.get('/rest/companyConfig/companyBasis/subjectinfo/v1.1/list', { params });
}

/**
 * 批量删除科目数据列表
 *
 * @export
 * @returns
 */
export function getRemoveSubjectList(params) {
  return request.get('/rest/companyConfig/companyBasis/subjectinfo/v1.1/removable', { params });
}

/**
 * 批量删除科目
 * 最多一次删除50个
 * @export
 * @param {*} "subjectCodes":["1001","1002"]
 * @returns
 */
export function deleteSubjectList(list) {
  return request.put('/rest/companyConfig/companyBasis/subjectinfo/v1.0/remove', list);
}

/**
 * 用户管理查询接口
 *
 * @export
 * @param {*}
 * @returns
 */
export function getCompanyUsersManage(list) {
  return request.post('/rest/companyConfig/companyBasis/companyUsersManage/v1.22/findUserManage', list);
}

/**
 *  修改用户管理接口
 *
 * @export
 * @param {*} list
 * @returns
 */
export function editCompanyUsersManage(list) {
  return request.post('/rest/companyConfig/companyBasis/companyUsersManage/v1.22/edit', list);
}

/**
 *科目标签新增
 *
 * @export
 * @param {*} tagOfSubject 参数要为 1
 * @returns
 */
export function insertSubjectTag(tagOfSubject) {
  return request.post('/rest/companyConfig/companyBasis/relSubjectTag/v1.0/insert', tagOfSubject);
}

/**
 *科目标签修改 (去除 没用了)
 *
 * @export
 * @param {*} list
 * @returns
 */
export function editSubjectTag(list) {
  return request.put('/rest/companyConfig/companyBasis/relSubjectTag/v1.0/update', list);
}

/**
 * 科目标签删除  (去除 没用了)
 *
 * @export
 * @param {*} relId 科目-标签关系id
 * @returns
 */
export function deleteSubjectTag(relId) {
  return request.delete(`/rest/companyConfig/companyBasis/relSubjectTag/v1.0/${relId}`);
}

/**
 * 科目标签期初余额列表查询
 *
 * @export
 * @param {*} params  subjectCategorie 1：资产、2：负债、3：权益、4：损益、5：成本
 *                    withOriginal 是否查询原期初余额和数量（0：否，1：是）
 *                    withTagNames 是否查询标签内容及标签类别的名称（0：否，1：是）
 * @returns
 */
export function subjectTagList(params) {
  return request.get('/rest/companyConfig/companyBasis/stInit/v1.0/list', { params });
}

/**
 * 科目标签期初余额删除
 *
 * @export
 * @returns
 */
export function subjectTagdelete(initialId) {
  return request.delete(`/rest/companyConfig/companyBasis/stInit/v1.0/${initialId}`);
}

/**
 * 科目标签期初余额修改
 *
 * @export
 * @returns
 */
export function subjectTagEdit(initialId, params) {
  return request.put(`/rest/companyConfig/companyBasis/stInit/v1.0/${initialId}`, params);
}

/**
 * 科目标签期初余额新增
 *
 * @export
 * @param {*} params
 * @returns
 */
export function subjectTagInsert(currencyCode, params) {
  return request.post(`/rest/companyConfig/companyBasis/stInit/v1.0/${currencyCode}/insert`, params);
}

// 新增标签链
export function insertTagTypeAndTagName(list) {
  return request.post('/rest/companyConfig/companyBasis/tag/v1.0/plus', list);
}


/**
 * /rest/statements/merge/company/v1.0/commonTip
 * 查询用户指定账套列表的公共提示信息
 * @param {*} comIds - 账套id 字符串数组， 结构为 1001,1002,1003
 * @returns
 */
export function getCommonTipByCompanyIds(comIds) {
  return request.get('/rest/statements/merge/company/v1.0/commonTip', { params: { comIds } });
}
