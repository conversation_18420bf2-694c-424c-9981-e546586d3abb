/* eslint-disable */
import request from '@/utils/request';
/**
 * 财务分析报表查询 - 
 * /rest/statements/merge/boss/analysis/v1.0/{queryMonth}
 * @param queryMonth - 查询属期（月:yyyyMM）
 */
export const findFinancialAnalysisUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/boss/analysis/v1.0/{queryMonth}'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['queryMonth'] = parameters['queryMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询纳税汇总 - findTaxSummaryByMonthUsingGET
 * /rest/statements/merge/boss/taxSummary/v1.0/taxSummary/list
 * @param queryMonth - 查询月份
 */
export const taxSummaryTaxsummary = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/boss/taxSummary/v1.0/taxSummary/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['queryMonth'] = parameters['queryMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询纳税汇总 - findTaxSummaryByMonth2UsingGET
 * /rest/statements/merge/boss/taxSummary/v2.0/taxSummary/list
 * @param accPeriods - 会计属期集合（yyyy-MM，yyyy-MM，yyyy-MM）
 * @param declareFlag - 申报税（增值税，企业所得税）查询开关：0：不开启；1：开启
 * @param fromMonth - 开始会计属期（yyyy-MM）
 * @param toMonth - 结束会计属期（yyyy-MM）
 * @param yearFlag - 本年累计(无结转)规则科目金额开关：0：不开启；1：开启
 */
export const findTaxSummaryByMonth2 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/boss/taxSummary/v2.0/taxSummary/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accPeriods'] = parameters['accPeriods'];
  queryParameters['declareFlag'] = parameters['declareFlag'];
  queryParameters['fromMonth'] = parameters['fromMonth'];
  queryParameters['toMonth'] = parameters['toMonth'];
  queryParameters['yearFlag'] = parameters['yearFlag'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 复制账套组装层 - 
 * /rest/statements/merge/company/copy/v1.0
 */
export const copyCompanyUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/company/copy/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 按月查询资产负债列表信息 - findALByMonthUsingGET
 * /rest/statements/merge/financial/assetLiabilities/v1.0/alByMonth
 * @param accPeriod - 会计属期（yyyy-MM）
 * @param switchFlag - 切换标记(1：切换前，2：切换后)
 */
export const assetLiabilitiesAlbymonth = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/assetLiabilities/v1.0/alByMonth'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accPeriod'] = parameters['accPeriod'];
  queryParameters['switchFlag'] = parameters['switchFlag'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 按月查询资产负债列表的导出 - exportALByMonthUsingGET
 * /rest/statements/merge/financial/assetLiabilities/v1.0/alByMonth/export
 * @param accPeriod - 会计属期（yyyy-MM）
 * @param switchFlag - 切换标记(1：切换前，2：切换后)
 */
export const exportALByMonth = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/assetLiabilities/v1.0/alByMonth/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accPeriod'] = parameters['accPeriod'];
  queryParameters['switchFlag'] = parameters['switchFlag'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 按季度查询资产负债列表信息 - findALBySeasonUsingGET
 * /rest/statements/merge/financial/assetLiabilities/v1.0/alBySeason
 * @param season - 季度（yyyyQ1：第一季度，yyyyQ2：第二季度，yyyyQ3：第三季度，yyyyQ4：第四季度）
 * @param switchFlag - 切换标记(1：切换前，2：切换后)
 */
export const assetLiabilitiesAlbyseason = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/assetLiabilities/v1.0/alBySeason'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['season'] = parameters['season'];
  queryParameters['switchFlag'] = parameters['switchFlag'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 按季度查询资产负债列表的导出 - exportALBySeasonUsingGET
 * /rest/statements/merge/financial/assetLiabilities/v1.0/alBySeason/export
 * @param season - 季度（yyyyQ1：第一季度，yyyyQ2：第二季度，yyyyQ3：第三季度，yyyyQ4：第四季度）
 * @param switchFlag - 切换标记(1：切换前，2：切换后)
 */
export const exportALBySeason = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/assetLiabilities/v1.0/alBySeason/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['season'] = parameters['season'];
  queryParameters['switchFlag'] = parameters['switchFlag'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 按月查询利润列表信息 - findProfitByMonthUsingGET
 * /rest/statements/merge/financial/assetLiabilities/v1.0/profitByMonth
 * @param accPeriod - 会计属期（yyyy-MM）
 */
export const assetLiabilitiesProfitbymonth = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/assetLiabilities/v1.0/profitByMonth'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accPeriod'] = parameters['accPeriod'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 按月查询利润列表的导出 - exportProfitByMonthUsingGET
 * /rest/statements/merge/financial/assetLiabilities/v1.0/profitByMonth/export
 * @param accPeriod - 会计属期（yyyy-MM）
 */
export const exportProfitByMonth = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/assetLiabilities/v1.0/profitByMonth/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accPeriod'] = parameters['accPeriod'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 按季度查询利润列表信息 - findProfitBySeasonUsingGET
 * /rest/statements/merge/financial/assetLiabilities/v1.0/profitBySeason
 * @param season - 季度（yyyyQ1：第一季度，yyyyQ2：第二季度，yyyyQ3：第三季度，yyyyQ4：第四季度）
 */
export const assetLiabilitiesProfitbyseason = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/assetLiabilities/v1.0/profitBySeason'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['season'] = parameters['season'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 按季查询利润列表的导出 - exportProfitBySeasonUsingGET
 * /rest/statements/merge/financial/assetLiabilities/v1.0/profitBySeason/export
 * @param season - 季度（yyyyQ1：第一季度，yyyyQ2：第二季度，yyyyQ3：第三季度，yyyyQ4：第四季度）
 */
export const exportProfitBySeason = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/assetLiabilities/v1.0/profitBySeason/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['season'] = parameters['season'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询特殊规则的应收或应付账款末级科目期末余额 - findAccountUsingGET_1
 * /rest/statements/merge/financial/balance/v1.0/account
 * @param accountFlag - 特殊规则的应付或应收账款末级科目查询开关（1.应付账款明细；2.应收账款明细）
 * @param beginMonth - 开始月份（yyyy-MM）
 * @param data - 
 * @param endMonth - 结束月份（yyyy-MM）
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const balanceAccount = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/balance/v1.0/account'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountFlag'] = parameters['accountFlag'];
  queryParameters['beginMonth'] = parameters['beginMonth'];
  queryParameters['data'] = parameters['data'];
  queryParameters['endMonth'] = parameters['endMonth'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出科目余额列表 - exportUsingGET_8
 * /rest/statements/merge/financial/balance/v1.0/export
 * @param accountPeriod - 会计属期（yyyy-MM）
 * @param currencyCode - 币别编码
 * @param qtySwitch - 是否显示数量列（1.是 0.否）
 * @param subjectCodes - 科目编码集合（如：1001,1002,...）
 * @param subjectFullName - 科目全称
 * @param yearSwitch - 是否显示年累计列（1.是 0.否）
 */
export const balanceExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/balance/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['qtySwitch'] = parameters['qtySwitch'];
  queryParameters['subjectCodes'] = parameters['subjectCodes'];
  queryParameters['subjectFullName'] = parameters['subjectFullName'];
  queryParameters['yearSwitch'] = parameters['yearSwitch'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表方式返回科目余额 - findAllUsingGET_2
 * /rest/statements/merge/financial/balance/v1.0/list
 * @param accountPeriod - 会计属期（yyyy-MM）
 * @param currencyCode - 币别编码
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param qtySwitch - 是否显示数量列（1.是 0.否）
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param subjectCodes - 科目编码集合（如：1001,1002,...）
 * @param subjectFullName - 科目全称
 * @param take - 
 * @param yearSwitch - 是否显示年累计列（1.是 0.否）
 */
export const balanceList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/balance/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['qtySwitch'] = parameters['qtySwitch'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['subjectCodes'] = parameters['subjectCodes'];
  queryParameters['subjectFullName'] = parameters['subjectFullName'];
  queryParameters['take'] = parameters['take'];
  queryParameters['yearSwitch'] = parameters['yearSwitch'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 按月（季）导出资产负债列表信息 - exportBalanceSheetUsingGET
 * /rest/statements/merge/financial/bs/v1.0/export
 * @param fromMonth - 开始会计属期（yyyy-MM）
 * @param season - 季度（yyyyQ1：第一季度，yyyyQ2：第二季度，yyyyQ3：第三季度，yyyyQ4：第四季度）
 * @param seasonFlag - 按季查询开关
 * @param switchFlag - 切换标记(1：切换前，2：切换后)
 * @param toMonth - 结束会计属期（yyyy-MM）
 */
export const bsExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/bs/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['fromMonth'] = parameters['fromMonth'];
  queryParameters['season'] = parameters['season'];
  queryParameters['seasonFlag'] = parameters['seasonFlag'];
  queryParameters['switchFlag'] = parameters['switchFlag'];
  queryParameters['toMonth'] = parameters['toMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 按月（季）查询资产负债列表信息 - findBalanceSheetUsingGET
 * /rest/statements/merge/financial/bs/v1.0/list
 * @param fromMonth - 开始会计属期（yyyy-MM）
 * @param lineNames - 行名称集合，如：YingShou,YingFu
 * @param season - 季度（yyyyQ1：第一季度，yyyyQ2：第二季度，yyyyQ3：第三季度，yyyyQ4：第四季度）
 * @param seasonFlag - 按季查询开关
 * @param switchFlag - 切换标记(1：切换前，2：切换后)
 * @param toMonth - 结束会计属期（yyyy-MM）
 */
export const bsList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/bs/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['fromMonth'] = parameters['fromMonth'];
  queryParameters['lineNames'] = parameters['lineNames'];
  queryParameters['season'] = parameters['season'];
  queryParameters['seasonFlag'] = parameters['seasonFlag'];
  queryParameters['switchFlag'] = parameters['switchFlag'];
  queryParameters['toMonth'] = parameters['toMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询现金流量列表详细信息(含小计) - findCFByMonthUsingGET_1
 * /rest/statements/merge/financial/cashflow/v1.0/list
 * @param accountPeriod - 会计属期（yyyyMM）
 */
export const cashflowList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/cashflow/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 现金流量导出 - exportUsingGET_1
 * /rest/statements/merge/financial/cashflowex/v1.0/export
 * @param accountPeriod - 会计属期(yyyy-MM)
 */
export const cashflowexExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/cashflowex/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询现金流量列表详细信息v2.0 - findCFByMonthUsingGET
 * /rest/statements/merge/financial/cashflowex/v1.0/list
 * @param accountPeriod - 会计属期（yyyy-MM）
 */
export const cashflowexList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/cashflowex/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 现金流量导出 - exportUsingGET
 * /rest/statements/merge/financial/cashflowex2/v3.0/export
 * @param accountPeriod - 会计属期(yyyy-MM)
 */
export const cashflowex2Export = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/cashflowex2/v3.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询现金流量信息列表v3.0 - findCashFlowsUsingGET
 * /rest/statements/merge/financial/cashflowex2/v3.0/list
 * @param accountPeriod - 会计属期（yyyy-MM）
 */
export const cashflowex2List = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/cashflowex2/v3.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出客户往来明细 - exportCustRDUsingGET
 * /rest/statements/merge/financial/custRD/v1.0/export
 * @param businessTime - 业务发生时间（yyyy-MM）
 * @param customerName - 客户名称
 */
export const custRDExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/custRD/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['businessTime'] = parameters['businessTime'];
  queryParameters['customerName'] = parameters['customerName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 根据业务发生时间查询客户往来明细 - findCustRDUsingGET_1
 * /rest/statements/merge/financial/custRD/v1.0/list/{businessTime}
 * @param businessTime - 业务发生时间（yyyy-MM）
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const custRDList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/custRD/v1.0/list/{businessTime}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{businessTime}', `${parameters['businessTime']}`)
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 根据客户名称、业务发生时间查询客户往来明细 - findCustRDUsingGET
 * /rest/statements/merge/financial/custRD/v1.0/list/{businessTime}/{customerName}
 * @param businessTime - 业务发生时间（yyyy-MM）
 * @param customerName - 客户名称
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const findCustRD = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/custRD/v1.0/list/{businessTime}/{customerName}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{businessTime}', `${parameters['businessTime']}`)
  path = path.replace('{customerName}', `${parameters['customerName']}`)
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询有效的科目编码索引 - getEffectiveCodesUsingGET
 * /rest/statements/merge/financial/detailAccount/v1.0/codes
 * @param beginMonth - 开始时间（yyyy-MM）
 * @param currencyCode - 货币编码
 * @param endMonth - 结束时间（yyyy-MM）
 */
export const detailAccountCodes = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/detailAccount/v1.0/codes'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginMonth'] = parameters['beginMonth'];
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['endMonth'] = parameters['endMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出明细账 - exportExcelDetailAccountUsingGET
 * /rest/statements/merge/financial/detailAccount/v1.0/export
 * @param beginMonth - 开始时间（yyyy-MM）
 * @param endMonth - 结束时间（yyyy-MM）
 * @param reportType - 报表导出类型 (1: 明细账, 2: 银行存款日记账)
 */
export const detailAccountExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/detailAccount/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginMonth'] = parameters['beginMonth'];
  queryParameters['endMonth'] = parameters['endMonth'];
  queryParameters['reportType'] = parameters['reportType'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询明细账 - getDetailAccountUsingGET
 * /rest/statements/merge/financial/detailAccount/v1.0/list
 * @param beginMonth - 开始时间（yyyy-MM）
 * @param data - 
 * @param endMonth - 结束时间（yyyy-MM）
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const detailAccountList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/detailAccount/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginMonth'] = parameters['beginMonth'];
  queryParameters['data'] = parameters['data'];
  queryParameters['endMonth'] = parameters['endMonth'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询费用汇总列表 - findExpensesSummaryUsingGET
 * /rest/statements/merge/financial/expensessummary/v1.0/list
 * @param data - 
 * @param expensesType - 费用类型（1：财务费用、2：管理费用、3：销售费用、4：采购运输费）
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param queryMonth - 查询属期（月:yyyyMM）
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const expensessummaryList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/expensessummary/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['expensesType'] = parameters['expensesType'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['queryMonth'] = parameters['queryMonth'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询费用汇总列表合计项 - findExpensesSummaryTotalsUsingGET
 * /rest/statements/merge/financial/expensessummary/v1.0/totals
 * @param expensesType - 费用类型（1：财务费用、2：管理费用、3：销售费用、4：采购运输费）
 * @param queryMonth - 查询属期（月:yyyyMM）
 */
export const expensessummaryTotals = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/expensessummary/v1.0/totals'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['expensesType'] = parameters['expensesType'];
  queryParameters['queryMonth'] = parameters['queryMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 进项发票价税金额及合计 - findInputInvoiceUsingGET_1
 * /rest/statements/merge/financial/invoice/v1.0/input/list
 * @param beginMonth - 开始月份（yyyy-MM）
 * @param endMonth - 结束月份（yyyy-MM）
 * @param invoiceTypes - 发票类型（ 1、专票；2、可抵扣普票；3、不可抵扣普票）
 */
export const invoiceInput = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/invoice/v1.0/input/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginMonth'] = parameters['beginMonth'];
  queryParameters['endMonth'] = parameters['endMonth'];
  queryParameters['invoiceTypes'] = parameters['invoiceTypes'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 销项发票价税金额及合计 - findOutputInvoiceUsingGET_1
 * /rest/statements/merge/financial/invoice/v1.0/output/list
 * @param beginMonth - 开始月份（yyyy-MM）
 * @param endMonth - 结束月份（yyyy-MM）
 * @param invoiceTypes - 发票类型（字典 1、专票；2、普票）
 */
export const invoiceOutput = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/invoice/v1.0/output/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginMonth'] = parameters['beginMonth'];
  queryParameters['endMonth'] = parameters['endMonth'];
  queryParameters['invoiceTypes'] = parameters['invoiceTypes'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询移动端小程序取数报表 - findCashFlowsUsingGET_1
 * /rest/statements/merge/financial/mobile/v1.0/list
 * @param accPeriods - 会计属期集合（yyyy-MM）
 * @param fromMonth - 开始会计属期（yyyy-MM）
 * @param toMonth - 结束会计属期（yyyy-MM）
 * @param yearFlag - 本年累计(无结转)规则科目金额开关
 */
export const findCashFlows = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/mobile/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accPeriods'] = parameters['accPeriods'];
  queryParameters['fromMonth'] = parameters['fromMonth'];
  queryParameters['toMonth'] = parameters['toMonth'];
  queryParameters['yearFlag'] = parameters['yearFlag'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询资产负债表的取数规则 - findRulesUsingGET_1
 * /rest/statements/merge/financial/mobile/v1.0/rules
 * @param lineId - 行号
 * @param standard - 会计准则：1.小企业会计准则；2.企业会计准则
 * @param switchFlag - 是否开启重分类（1：不开启，2：开启）
 */
export const mobileRules = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/mobile/v1.0/rules'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['lineId'] = parameters['lineId'];
  queryParameters['standard'] = parameters['standard'];
  queryParameters['switchFlag'] = parameters['switchFlag'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询期初余额的试算平衡 - findDebitCreditBalanceUsingGET
 * /rest/statements/merge/financial/openingbalance/v1.0/balance
 * @param childCount - 子级数量
 * @param currencyCode - 币别编码
 * @param currencyName - 货币名称
 * @param displayFlag - 期初余额和数量都为0时是否显示：1：显示，0：不显示
 * @param enabledModify - 是否可修改科目信息
 * @param initialAmount - 初始余额
 * @param initialAmountO - 初始余额（原币）
 * @param initialQty - 初始数量
 * @param lendingDirection - 余额方向——1：借，2：贷
 * @param modifiable - 是否可以修改期初余额标识
 * @param subjectBeginCode - 科目编码起
 * @param subjectCategory - 科目分类
 * @param subjectCode - 科目编码
 * @param subjectCodes - 科目编码集合
 * @param subjectEndCode - 科目编码止
 * @param subjectFullName - 科目全称
 * @param subjectId - 科目id
 * @param subjectParentCode - 上级科目编码
 * @param subjectStatus - 科目状态
 * @param yearInitialAmount - 年初始余额（本位币）
 * @param yearInitialAmountO - 年初始余额（原币）
 * @param yearInitialCreditAmount - 年初始累计贷方金额（本位币）
 * @param yearInitialCreditAmountO - 年初始累计贷方金额（原币）
 * @param yearInitialCreditQty - 年初始累计贷方数量
 * @param yearInitialDebitAmount - 年初始累计借方金额（本位币）
 * @param yearInitialDebitAmountO - 年初始累计借方金额（原币）
 * @param yearInitialDebitQty - 年初始累计借方数量
 * @param yearInitialQty - 年初始数量
 */
export const openingbalanceBalance = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/openingbalance/v1.0/balance'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['childCount'] = parameters['childCount'];
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['currencyName'] = parameters['currencyName'];
  queryParameters['displayFlag'] = parameters['displayFlag'];
  queryParameters['enabledModify'] = parameters['enabledModify'];
  queryParameters['initialAmount'] = parameters['initialAmount'];
  queryParameters['initialAmountO'] = parameters['initialAmountO'];
  queryParameters['initialQty'] = parameters['initialQty'];
  queryParameters['lendingDirection'] = parameters['lendingDirection'];
  queryParameters['modifiable'] = parameters['modifiable'];
  queryParameters['subjectBeginCode'] = parameters['subjectBeginCode'];
  queryParameters['subjectCategory'] = parameters['subjectCategory'];
  queryParameters['subjectCode'] = parameters['subjectCode'];
  queryParameters['subjectCodes'] = parameters['subjectCodes'];
  queryParameters['subjectEndCode'] = parameters['subjectEndCode'];
  queryParameters['subjectFullName'] = parameters['subjectFullName'];
  queryParameters['subjectId'] = parameters['subjectId'];
  queryParameters['subjectParentCode'] = parameters['subjectParentCode'];
  queryParameters['subjectStatus'] = parameters['subjectStatus'];
  queryParameters['yearInitialAmount'] = parameters['yearInitialAmount'];
  queryParameters['yearInitialAmountO'] = parameters['yearInitialAmountO'];
  queryParameters['yearInitialCreditAmount'] = parameters['yearInitialCreditAmount'];
  queryParameters['yearInitialCreditAmountO'] = parameters['yearInitialCreditAmountO'];
  queryParameters['yearInitialCreditQty'] = parameters['yearInitialCreditQty'];
  queryParameters['yearInitialDebitAmount'] = parameters['yearInitialDebitAmount'];
  queryParameters['yearInitialDebitAmountO'] = parameters['yearInitialDebitAmountO'];
  queryParameters['yearInitialDebitQty'] = parameters['yearInitialDebitQty'];
  queryParameters['yearInitialQty'] = parameters['yearInitialQty'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改期初余额 - balanceUpdateUsingPUT
 * /rest/statements/merge/financial/openingbalance/v1.0/balanceUpdate/{subjectId}
 * @param balance - 期初余额实体
 * @param subjectId - subjectId
 */
export const openingbalanceBalanceupdate = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/openingbalance/v1.0/balanceUpdate/{subjectId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['balance']
  path = path.replace('{subjectId}', `${parameters['subjectId']}`)
  if (parameters['subjectId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: subjectId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 导出最末级期初余额 - exportUsingGET_2
 * /rest/statements/merge/financial/openingbalance/v1.0/export
 */
export const openingbalanceExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/openingbalance/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表方式返回科目期初余额 - getByUsingGET_1
 * /rest/statements/merge/financial/openingbalance/v1.0/get
 * @param isInitial - 开启初始化状态查询：0.不开启，1.开启
 */
export const openingbalanceGet = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/openingbalance/v1.0/get'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['isInitial'] = parameters['isInitial'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导入期初余额 - importGoodsUsingPOST
 * /rest/statements/merge/financial/openingbalance/v1.0/import
 */
export const openingbalanceImport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/openingbalance/v1.0/import'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表方式返回科目期初余额 - findAllUsingGET
 * /rest/statements/merge/financial/openingbalance/v1.0/list
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param subjectCategory - 科目类型(1:资产，2：负债，3：权益，5：成本)
 * @param subjectCodes - 科目编码集合（如：1001,1002,...）
 * @param take - 
 */
export const openingbalanceList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/openingbalance/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['subjectCategory'] = parameters['subjectCategory'];
  queryParameters['subjectCodes'] = parameters['subjectCodes'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询期初余额的分类合计信息 - findTotalsUsingGET
 * /rest/statements/merge/financial/openingbalance/v1.0/totals
 * @param childCount - 子级数量
 * @param currencyCode - 币别编码
 * @param currencyName - 货币名称
 * @param displayFlag - 期初余额和数量都为0时是否显示：1：显示，0：不显示
 * @param enabledModify - 是否可修改科目信息
 * @param initialAmount - 初始余额
 * @param initialAmountO - 初始余额（原币）
 * @param initialQty - 初始数量
 * @param lendingDirection - 余额方向——1：借，2：贷
 * @param modifiable - 是否可以修改期初余额标识
 * @param subjectBeginCode - 科目编码起
 * @param subjectCategory - 科目分类
 * @param subjectCode - 科目编码
 * @param subjectCodes - 科目编码集合
 * @param subjectEndCode - 科目编码止
 * @param subjectFullName - 科目全称
 * @param subjectId - 科目id
 * @param subjectParentCode - 上级科目编码
 * @param subjectStatus - 科目状态
 * @param yearInitialAmount - 年初始余额（本位币）
 * @param yearInitialAmountO - 年初始余额（原币）
 * @param yearInitialCreditAmount - 年初始累计贷方金额（本位币）
 * @param yearInitialCreditAmountO - 年初始累计贷方金额（原币）
 * @param yearInitialCreditQty - 年初始累计贷方数量
 * @param yearInitialDebitAmount - 年初始累计借方金额（本位币）
 * @param yearInitialDebitAmountO - 年初始累计借方金额（原币）
 * @param yearInitialDebitQty - 年初始累计借方数量
 * @param yearInitialQty - 年初始数量
 */
export const openingbalanceTotals = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/openingbalance/v1.0/totals'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['childCount'] = parameters['childCount'];
  queryParameters['currencyCode'] = parameters['currencyCode'];
  queryParameters['currencyName'] = parameters['currencyName'];
  queryParameters['displayFlag'] = parameters['displayFlag'];
  queryParameters['enabledModify'] = parameters['enabledModify'];
  queryParameters['initialAmount'] = parameters['initialAmount'];
  queryParameters['initialAmountO'] = parameters['initialAmountO'];
  queryParameters['initialQty'] = parameters['initialQty'];
  queryParameters['lendingDirection'] = parameters['lendingDirection'];
  queryParameters['modifiable'] = parameters['modifiable'];
  queryParameters['subjectBeginCode'] = parameters['subjectBeginCode'];
  queryParameters['subjectCategory'] = parameters['subjectCategory'];
  queryParameters['subjectCode'] = parameters['subjectCode'];
  queryParameters['subjectCodes'] = parameters['subjectCodes'];
  queryParameters['subjectEndCode'] = parameters['subjectEndCode'];
  queryParameters['subjectFullName'] = parameters['subjectFullName'];
  queryParameters['subjectId'] = parameters['subjectId'];
  queryParameters['subjectParentCode'] = parameters['subjectParentCode'];
  queryParameters['subjectStatus'] = parameters['subjectStatus'];
  queryParameters['yearInitialAmount'] = parameters['yearInitialAmount'];
  queryParameters['yearInitialAmountO'] = parameters['yearInitialAmountO'];
  queryParameters['yearInitialCreditAmount'] = parameters['yearInitialCreditAmount'];
  queryParameters['yearInitialCreditAmountO'] = parameters['yearInitialCreditAmountO'];
  queryParameters['yearInitialCreditQty'] = parameters['yearInitialCreditQty'];
  queryParameters['yearInitialDebitAmount'] = parameters['yearInitialDebitAmount'];
  queryParameters['yearInitialDebitAmountO'] = parameters['yearInitialDebitAmountO'];
  queryParameters['yearInitialDebitQty'] = parameters['yearInitialDebitQty'];
  queryParameters['yearInitialQty'] = parameters['yearInitialQty'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询附加税 - findAdditionalTaxUsingGET
 * /rest/statements/merge/financial/paytaxDetail/v1.0/additionalTax/list
 * @param queryMonth - 查询月份（yyyy-MM）
 */
export const paytaxDetailAdditionaltax = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/paytaxDetail/v1.0/additionalTax/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['queryMonth'] = parameters['queryMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询所得税 - findIncomeTaxUsingGET
 * /rest/statements/merge/financial/paytaxDetail/v1.0/incomeTax/list
 * @param queryMonth - 查询月份（yyyy-MM）
 */
export const paytaxDetailIncometax = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/paytaxDetail/v1.0/incomeTax/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['queryMonth'] = parameters['queryMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 新增或修改纳税明细信息 - insertTaxDetailssUsingPOST
 * /rest/statements/merge/financial/paytaxDetail/v1.0/save
 * @param taxDetails - 纳税明细信息实体
 */
export const paytaxDetailSave = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/paytaxDetail/v1.0/save'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['taxDetails']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查询印花税 - findStampTaxUsingGET
 * /rest/statements/merge/financial/paytaxDetail/v1.0/stampTax/list
 * @param queryMonth - 查询月份（yyyy-MM）
 */
export const paytaxDetailStamptax = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/paytaxDetail/v1.0/stampTax/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['queryMonth'] = parameters['queryMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询税收优惠 - findTaxDiscountUsingGET
 * /rest/statements/merge/financial/paytaxDetail/v1.0/taxDiscount/list
 * @param queryMonth - 查询月份（yyyy-MM）
 */
export const paytaxDetailTaxdiscount = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/paytaxDetail/v1.0/taxDiscount/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['queryMonth'] = parameters['queryMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询增值税 - findVatUsingGET
 * /rest/statements/merge/financial/paytaxDetail/v1.0/vat/list
 * @param queryMonth - 查询月份（yyyy-MM）
 */
export const paytaxDetailVat = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/paytaxDetail/v1.0/vat/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['queryMonth'] = parameters['queryMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出库存汇总列表 - exportUsingGET_4
 * /rest/statements/merge/financial/procurementStatistic/v1.0/export/{startDate}/{endDate}
 * @param endDate - 结束时间:格式yyyy-MM-dd
 * @param startDate - 开始时间:格式yyyy-MM-dd
 */
export const procurementStatisticExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/procurementStatistic/v1.0/export/{startDate}/{endDate}'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['endDate'] = parameters['endDate'];
  queryParameters['startDate'] = parameters['startDate'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 采购统计表 - findBuyAccountUsingGET_1
 * /rest/statements/merge/financial/procurementStatistic/v1.0/list/{startDate}/{endDate}
 * @param endDate - 结束时间:格式yyyy-MM-dd
 * @param startDate - 开始时间:格式yyyy-MM-dd
 */
export const procurementStatisticList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/procurementStatistic/v1.0/list/{startDate}/{endDate}'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['endDate'] = parameters['endDate'];
  queryParameters['startDate'] = parameters['startDate'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 按月（季、年）导出利润列表信息 - exportProfitSheetUsingGET_1
 * /rest/statements/merge/financial/ps/v1.0/export
 * @param fromMonth - 开始会计属期（yyyy-MM）
 * @param previousFlag - 是否开启上期显示（0：不开启，1：开启）
 * @param season - 季度（yyyyQ1：第一季度，yyyyQ2：第二季度，yyyyQ3：第三季度，yyyyQ4：第四季度）；年：yyyy；月：yyyy-MM
 * @param toMonth - 结束会计属期（yyyy-MM）
 * @param yearFlag - 是否开启年查询（0：不开启，1：开启）注意：利润表使用月查询时，此字面必须为开启年查询
 */
export const psExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/ps/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['fromMonth'] = parameters['fromMonth'];
  queryParameters['previousFlag'] = parameters['previousFlag'];
  queryParameters['season'] = parameters['season'];
  queryParameters['toMonth'] = parameters['toMonth'];
  queryParameters['yearFlag'] = parameters['yearFlag'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 按月（季、年）查询利润列表信息 - findProfitSheetUsingGET_1
 * /rest/statements/merge/financial/ps/v1.0/list
 * @param fromMonth - 开始会计属期（yyyy-MM）
 * @param lineNames - 行名称集合，如：YingShou,YingFu
 * @param season - 季度（yyyyQ1：第一季度，yyyyQ2：第二季度，yyyyQ3：第三季度，yyyyQ4：第四季度）；年：yyyy；月：yyyy-MM
 * @param toMonth - 结束会计属期（yyyy-MM）
 * @param yearFlag - 是否开启年查询（0：不开启，1：开启）注意：利润表使用月查询时，此字面必须为开启年查询
 */
export const psList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/ps/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['fromMonth'] = parameters['fromMonth'];
  queryParameters['lineNames'] = parameters['lineNames'];
  queryParameters['season'] = parameters['season'];
  queryParameters['toMonth'] = parameters['toMonth'];
  queryParameters['yearFlag'] = parameters['yearFlag'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询财务均值全数据 - findIndustryRateAllUsingGET
 * /rest/statements/merge/financial/rate/v1.0/list
 * @param accountPeriod - 查询月份（yyyy-MM）
 */
export const rateList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/rate/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询行业风险测评数据 - findIndustryRiskUsingGET
 * /rest/statements/merge/financial/risk/v1.0/industry
 * @param year - 年份（yyyy）
 */
export const riskIndustry = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/risk/v1.0/industry'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['year'] = parameters['year'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询单账套风险测评数据 - findCompanyRiskUsingGET
 * /rest/statements/merge/financial/risk/v1.0/list
 * @param year - 年份（yyyy）
 */
export const riskList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/risk/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['year'] = parameters['year'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出库存现金日记账列表 - exportExcelDetailAccountUsingGET_1
 * /rest/statements/merge/financial/stock/v1.0/export
 * @param beginDate - 开始时间
 * @param endDate - 结束时间
 */
export const stockExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/stock/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginDate'] = parameters['beginDate'];
  queryParameters['endDate'] = parameters['endDate'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表方式返回库存现金日记账 - findAllUsingGET_1
 * /rest/statements/merge/financial/stock/v1.0/list
 * @param beginDate - 开始时间
 * @param data - 
 * @param endDate - 结束时间
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const stockList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/stock/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['beginDate'] = parameters['beginDate'];
  queryParameters['data'] = parameters['data'];
  queryParameters['endDate'] = parameters['endDate'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出库存明细列表 - exportUsingGET_6
 * /rest/statements/merge/financial/stockdetail/v1.0/export
 * @param accountType - 业务类型
 * @param beginDate - 开始时间
 * @param billNum - 单据编号
 * @param colourName - 颜色
 * @param dimentionName - 规格
 * @param endDate - 结束时间
 * @param goodsClassName - 商品分类
 * @param goodsName - 商品名称
 * @param saleunitName - 单位
 * @param storeName - 仓库名称
 */
export const stockdetailExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/stockdetail/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountType'] = parameters['accountType'];
  queryParameters['beginDate'] = parameters['beginDate'];
  queryParameters['billNum'] = parameters['billNum'];
  queryParameters['colourName'] = parameters['colourName'];
  queryParameters['dimentionName'] = parameters['dimentionName'];
  queryParameters['endDate'] = parameters['endDate'];
  queryParameters['goodsClassName'] = parameters['goodsClassName'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['saleunitName'] = parameters['saleunitName'];
  queryParameters['storeName'] = parameters['storeName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询库存明细列表 - findStockDetailsUsingGET
 * /rest/statements/merge/financial/stockdetail/v1.0/list
 * @param accountType - 业务类型
 * @param beginDate - 开始时间
 * @param billNum - 单据编号
 * @param colourName - 颜色
 * @param data - 
 * @param dimentionName - 规格
 * @param endDate - 结束时间
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param goodsClassName - 商品分类
 * @param goodsName - 商品名称
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param saleunitName - 单位
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param storeName - 仓库名称
 * @param take - 
 */
export const stockdetailList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/stockdetail/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountType'] = parameters['accountType'];
  queryParameters['beginDate'] = parameters['beginDate'];
  queryParameters['billNum'] = parameters['billNum'];
  queryParameters['colourName'] = parameters['colourName'];
  queryParameters['data'] = parameters['data'];
  queryParameters['dimentionName'] = parameters['dimentionName'];
  queryParameters['endDate'] = parameters['endDate'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['goodsClassName'] = parameters['goodsClassName'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['saleunitName'] = parameters['saleunitName'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['storeName'] = parameters['storeName'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询库存明细入、出库数量合计 - findStockInOutQtyTotalUsingGET
 * /rest/statements/merge/financial/stockdetail/v1.0/total
 * @param accountType - 业务类型
 * @param beginDate - 开始时间
 * @param billNum - 单据编号
 * @param colourName - 颜色
 * @param dimentionName - 规格
 * @param endDate - 结束时间
 * @param goodsClassName - 商品分类
 * @param goodsName - 商品名称
 * @param saleunitName - 单位
 * @param storeName - 仓库名称
 */
export const stockdetailTotal = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/stockdetail/v1.0/total'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountType'] = parameters['accountType'];
  queryParameters['beginDate'] = parameters['beginDate'];
  queryParameters['billNum'] = parameters['billNum'];
  queryParameters['colourName'] = parameters['colourName'];
  queryParameters['dimentionName'] = parameters['dimentionName'];
  queryParameters['endDate'] = parameters['endDate'];
  queryParameters['goodsClassName'] = parameters['goodsClassName'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['saleunitName'] = parameters['saleunitName'];
  queryParameters['storeName'] = parameters['storeName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出库存汇总列表 - exportUsingGET_7
 * /rest/statements/merge/financial/stocksummary/v1.0/export
 * @param businessPeriod - 业务属期（yyyy-MM）
 * @param colourName - 颜色
 * @param differentReason - 差异原因
 * @param dimentionName - 规格
 * @param goodsClassName - 商品分类
 * @param goodsName - 商品名称
 * @param saleunitName - 单位
 */
export const stocksummaryExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/stocksummary/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['businessPeriod'] = parameters['businessPeriod'];
  queryParameters['colourName'] = parameters['colourName'];
  queryParameters['differentReason'] = parameters['differentReason'];
  queryParameters['dimentionName'] = parameters['dimentionName'];
  queryParameters['goodsClassName'] = parameters['goodsClassName'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['saleunitName'] = parameters['saleunitName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询库存汇总列表 - findStockSummaryUsingGET
 * /rest/statements/merge/financial/stocksummary/v1.0/list
 * @param businessPeriod - 业务属期（yyyy-MM）
 * @param colourName - 颜色
 * @param data - 
 * @param differentReason - 差异原因
 * @param dimentionName - 规格
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param goodsClassName - 商品分类
 * @param goodsName - 商品名称
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param saleunitName - 单位
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const stocksummaryList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/stocksummary/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['businessPeriod'] = parameters['businessPeriod'];
  queryParameters['colourName'] = parameters['colourName'];
  queryParameters['data'] = parameters['data'];
  queryParameters['differentReason'] = parameters['differentReason'];
  queryParameters['dimentionName'] = parameters['dimentionName'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['goodsClassName'] = parameters['goodsClassName'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['saleunitName'] = parameters['saleunitName'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询库存汇总列表各项合计 - findStockSummaryTotalsUsingGET
 * /rest/statements/merge/financial/stocksummary/v1.0/totals
 * @param businessPeriod - 业务属期（yyyy-MM）
 * @param colourName - 颜色
 * @param differentReason - 差异原因
 * @param dimentionName - 规格
 * @param goodsClassName - 商品分类
 * @param goodsName - 商品名称
 * @param saleunitName - 单位
 */
export const stocksummaryTotals = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/stocksummary/v1.0/totals'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['businessPeriod'] = parameters['businessPeriod'];
  queryParameters['colourName'] = parameters['colourName'];
  queryParameters['differentReason'] = parameters['differentReason'];
  queryParameters['dimentionName'] = parameters['dimentionName'];
  queryParameters['goodsClassName'] = parameters['goodsClassName'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['saleunitName'] = parameters['saleunitName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出供应商往来明细 - exportUsingGET_9
 * /rest/statements/merge/financial/supplierRD/v1.0/export
 * @param businessTime - 业务发生时间（yyyy-MM）
 * @param customerName - 供应商名称
 */
export const supplierRDExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/supplierRD/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['businessTime'] = parameters['businessTime'];
  queryParameters['customerName'] = parameters['customerName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 根据业务发生时间查询供用商往来明细 - findsupplierRDUsingGET_1
 * /rest/statements/merge/financial/supplierRD/v1.0/list/{businessTime}
 * @param businessTime - 业务发生时间（yyyy-MM）
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const supplierRDList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/supplierRD/v1.0/list/{businessTime}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{businessTime}', `${parameters['businessTime']}`)
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 根据供用商名称、业务发生时间查询供用商往来明细 - findsupplierRDUsingGET
 * /rest/statements/merge/financial/supplierRD/v1.0/list/{businessTime}/{supplierName}
 * @param businessTime - 业务发生时间（yyyy-MM）
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param supplierName - 供用商名称
 * @param take - 
 */
export const findsupplier = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/supplierRD/v1.0/list/{businessTime}/{supplierName}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{businessTime}', `${parameters['businessTime']}`)
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  path = path.replace('{supplierName}', `${parameters['supplierName']}`)
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询行为提醒 - findBehaviourTipsUsingGET
 * /rest/statements/merge/financial/tips/v1.0/list
 * @param year - 年（yyyy）
 */
export const tipsList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/tips/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['year'] = parameters['year'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询收入总额 - 
 * /rest/statements/merge/financial/voucherReport/incomeTotals/v1.0
 * @param accountPeriod - 会计属期(yyyy-MM)
 */
export const getIncomeTotalsUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/financial/voucherReport/incomeTotals/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountPeriod'] = parameters['accountPeriod'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 销售明细导出-老板、财务入口 - exportForAdminUsingGET
 * /rest/statements/merge/sale/details/v1.0/admin/export
 * @param accountsType - 业务类型
 * @param companyId - 公司ID
 * @param createTimeEnd - 查询结束时间
 * @param createTimeStart - 查询开始时间
 * @param createUserid - 业务员ID
 * @param customerName - 客户名称
 * @param goodsName - 商品名称
 * @param saleBill - 销售单号
 * @param username - 业务员名称
 */
export const detailsAdmin = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/sale/details/v1.0/admin/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountsType'] = parameters['accountsType'];
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['createTimeEnd'] = parameters['createTimeEnd'];
  queryParameters['createTimeStart'] = parameters['createTimeStart'];
  queryParameters['createUserid'] = parameters['createUserid'];
  queryParameters['customerName'] = parameters['customerName'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['saleBill'] = parameters['saleBill'];
  queryParameters['username'] = parameters['username'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 销售明细列表-老板、财务入口 - listForAdminUsingGET
 * /rest/statements/merge/sale/details/v1.0/admin/list
 * @param accountsType - 业务类型
 * @param companyId - 公司ID
 * @param createTimeEnd - 查询结束时间
 * @param createTimeStart - 查询开始时间
 * @param createUserid - 业务员ID
 * @param customerName - 客户名称
 * @param goodsName - 商品名称
 * @param saleBill - 销售单号
 * @param username - 业务员名称
 */
export const listForAdmin = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/sale/details/v1.0/admin/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountsType'] = parameters['accountsType'];
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['createTimeEnd'] = parameters['createTimeEnd'];
  queryParameters['createTimeStart'] = parameters['createTimeStart'];
  queryParameters['createUserid'] = parameters['createUserid'];
  queryParameters['customerName'] = parameters['customerName'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['saleBill'] = parameters['saleBill'];
  queryParameters['username'] = parameters['username'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 销售明细合计-老板、财务入口 - listForAdminTotalUsingGET
 * /rest/statements/merge/sale/details/v1.0/admin/total
 * @param accountsType - 业务类型
 * @param companyId - 公司ID
 * @param createTimeEnd - 查询结束时间
 * @param createTimeStart - 查询开始时间
 * @param createUserid - 业务员ID
 * @param customerName - 客户名称
 * @param goodsName - 商品名称
 * @param saleBill - 销售单号
 * @param username - 业务员名称
 */
export const listForAdminTotal = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/sale/details/v1.0/admin/total'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountsType'] = parameters['accountsType'];
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['createTimeEnd'] = parameters['createTimeEnd'];
  queryParameters['createTimeStart'] = parameters['createTimeStart'];
  queryParameters['createUserid'] = parameters['createUserid'];
  queryParameters['customerName'] = parameters['customerName'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['saleBill'] = parameters['saleBill'];
  queryParameters['username'] = parameters['username'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 销售明细导出-销售人员入口 - exportForSaleUsingGET
 * /rest/statements/merge/sale/details/v1.0/sales/export
 * @param accountsType - 业务类型
 * @param companyId - 公司ID
 * @param createTimeEnd - 查询结束时间
 * @param createTimeStart - 查询开始时间
 * @param createUserid - 业务员ID
 * @param customerName - 客户名称
 * @param goodsName - 商品名称
 * @param saleBill - 销售单号
 * @param username - 业务员名称
 */
export const detailsSales = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/sale/details/v1.0/sales/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountsType'] = parameters['accountsType'];
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['createTimeEnd'] = parameters['createTimeEnd'];
  queryParameters['createTimeStart'] = parameters['createTimeStart'];
  queryParameters['createUserid'] = parameters['createUserid'];
  queryParameters['customerName'] = parameters['customerName'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['saleBill'] = parameters['saleBill'];
  queryParameters['username'] = parameters['username'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 销售明细列表-销售人员入口 - listForSaleUsingGET
 * /rest/statements/merge/sale/details/v1.0/sales/list
 * @param accountsType - 业务类型
 * @param companyId - 公司ID
 * @param createTimeEnd - 查询结束时间
 * @param createTimeStart - 查询开始时间
 * @param createUserid - 业务员ID
 * @param customerName - 客户名称
 * @param goodsName - 商品名称
 * @param saleBill - 销售单号
 * @param username - 业务员名称
 */
export const listForSale = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/sale/details/v1.0/sales/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountsType'] = parameters['accountsType'];
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['createTimeEnd'] = parameters['createTimeEnd'];
  queryParameters['createTimeStart'] = parameters['createTimeStart'];
  queryParameters['createUserid'] = parameters['createUserid'];
  queryParameters['customerName'] = parameters['customerName'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['saleBill'] = parameters['saleBill'];
  queryParameters['username'] = parameters['username'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 销售明细合计-销售人员入口 - listForSalesTotalUsingGET
 * /rest/statements/merge/sale/details/v1.0/sales/total
 * @param accountsType - 业务类型
 * @param companyId - 公司ID
 * @param createTimeEnd - 查询结束时间
 * @param createTimeStart - 查询开始时间
 * @param createUserid - 业务员ID
 * @param customerName - 客户名称
 * @param goodsName - 商品名称
 * @param saleBill - 销售单号
 * @param username - 业务员名称
 */
export const listForSalesTotal = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/sale/details/v1.0/sales/total'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountsType'] = parameters['accountsType'];
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['createTimeEnd'] = parameters['createTimeEnd'];
  queryParameters['createTimeStart'] = parameters['createTimeStart'];
  queryParameters['createUserid'] = parameters['createUserid'];
  queryParameters['customerName'] = parameters['customerName'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['saleBill'] = parameters['saleBill'];
  queryParameters['username'] = parameters['username'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出新销售汇总报表 - exportSalesGatheringUsingGET_1
 * /rest/statements/merge/sale/gathering/v1.0/export
 * @param fromMonth - 开始会计属期（yyyy-MM）
 * @param toMonth - 结束会计属期（yyyy-MM）
 */
export const gatheringExport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/sale/gathering/v1.0/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['fromMonth'] = parameters['fromMonth'];
  queryParameters['toMonth'] = parameters['toMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询新销售汇总报表 - findSalesGatheringUsingGET_1
 * /rest/statements/merge/sale/gathering/v1.0/list
 * @param fromMonth - 开始会计属期（yyyy-MM）
 * @param toMonth - 结束会计属期（yyyy-MM）
 */
export const gatheringList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/sale/gathering/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['fromMonth'] = parameters['fromMonth'];
  queryParameters['toMonth'] = parameters['toMonth'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导出收入统计表 - 
 * /rest/statements/merge/sale/summary/incomeSummary/{fromPeriod}/{toPeriod}/export/v1.0
 * @param fromPeriod - 开始月份(yyyy-MM)
 * @param toPeriod - 结束月份(yyyy-MM)
 */
export const exportIncomeSummaryUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/sale/summary/incomeSummary/{fromPeriod}/{toPeriod}/export/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{fromPeriod}', `${parameters['fromPeriod']}`)
  path = path.replace('{toPeriod}', `${parameters['toPeriod']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询收入统计表 - 
 * /rest/statements/merge/sale/summary/incomeSummary/{fromPeriod}/{toPeriod}/v1.0
 * @param fromPeriod - 开始月份(yyyy-MM)
 * @param toPeriod - 结束月份(yyyy-MM)
 */
export const getIncomeSummaryUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/sale/summary/incomeSummary/{fromPeriod}/{toPeriod}/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{fromPeriod}', `${parameters['fromPeriod']}`)
  path = path.replace('{toPeriod}', `${parameters['toPeriod']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 销售汇总列表-管理员、财务人员入口 - listForAdminUsingGET_1
 * /rest/statements/merge/sale/summary/v1.0/admin/list
 * @param businessTimeStr - 业务发生时间，格式yyyy-MM
 * @param colourName - 颜色名称
 * @param dimentionName - 尺码名称
 * @param goodsClassName - 商品分类名称（对商品类型表的商品类型名）
 * @param goodsName - 商品名称
 * @param saleunitName - 商品单位
 */
export const summaryAdmin = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/sale/summary/v1.0/admin/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['businessTimeStr'] = parameters['businessTimeStr'];
  queryParameters['colourName'] = parameters['colourName'];
  queryParameters['dimentionName'] = parameters['dimentionName'];
  queryParameters['goodsClassName'] = parameters['goodsClassName'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['saleunitName'] = parameters['saleunitName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 销售汇总列表-销售人员销售情况汇总-管理员、财务人员入口 - getSalesSummaryInfoUsingGET
 * /rest/statements/merge/sale/summary/v1.0/admin/sales/summary/info
 * @param businessTimeStr - 业务发生时间,格式yyyy-MM
 * @param userName - 用户名
 */
export const getSalesSummaryInfo = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/sale/summary/v1.0/admin/sales/summary/info'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['businessTimeStr'] = parameters['businessTimeStr'];
  queryParameters['userName'] = parameters['userName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 销售汇总列表-销售人员销售合计-管理员、财务人员入口 - totalSalesSummaryInfoUsingGET
 * /rest/statements/merge/sale/summary/v1.0/admin/sales/summary/total
 * @param businessTimeStr - 业务发生时间,格式yyyy-MM
 * @param userName - 用户名
 */
export const totalSalesSummaryInfo = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/sale/summary/v1.0/admin/sales/summary/total'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['businessTimeStr'] = parameters['businessTimeStr'];
  queryParameters['userName'] = parameters['userName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 销售汇总列表-合计-管理员、财务入口 - totalForAdminUsingGET
 * /rest/statements/merge/sale/summary/v1.0/admin/total
 * @param businessTimeStr - 业务发生时间，格式yyyy-MM
 * @param colourName - 颜色名称
 * @param dimentionName - 尺码名称
 * @param goodsClassName - 商品分类名称（对商品类型表的商品类型名）
 * @param goodsName - 商品名称
 * @param saleunitName - 商品单位
 */
export const totalForAdmin = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/sale/summary/v1.0/admin/total'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['businessTimeStr'] = parameters['businessTimeStr'];
  queryParameters['colourName'] = parameters['colourName'];
  queryParameters['dimentionName'] = parameters['dimentionName'];
  queryParameters['goodsClassName'] = parameters['goodsClassName'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['saleunitName'] = parameters['saleunitName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 销售汇总导出-销售人员入口 - exportUsingGET_5
 * /rest/statements/merge/sale/summary/v1.0/sales/export
 * @param businessTimeStr - 业务发生时间，格式yyyy-MM
 * @param colourName - 颜色名称
 * @param dimentionName - 尺码名称
 * @param goodsClassName - 商品分类名称（对商品类型表的商品类型名）
 * @param goodsName - 商品名称
 * @param saleunitName - 商品单位
 */
export const summarySales = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/sale/summary/v1.0/sales/export'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['businessTimeStr'] = parameters['businessTimeStr'];
  queryParameters['colourName'] = parameters['colourName'];
  queryParameters['dimentionName'] = parameters['dimentionName'];
  queryParameters['goodsClassName'] = parameters['goodsClassName'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['saleunitName'] = parameters['saleunitName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 销售汇总列表-商品销售情况-销售人员入口 - listForSalesUsingGET
 * /rest/statements/merge/sale/summary/v1.0/sales/list
 * @param businessTimeStr - 业务发生时间，格式yyyy-MM
 * @param colourName - 颜色名称
 * @param dimentionName - 尺码名称
 * @param goodsClassName - 商品分类名称（对商品类型表的商品类型名）
 * @param goodsName - 商品名称
 * @param saleunitName - 商品单位
 */
export const listForSales= function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/sale/summary/v1.0/sales/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['businessTimeStr'] = parameters['businessTimeStr'];
  queryParameters['colourName'] = parameters['colourName'];
  queryParameters['dimentionName'] = parameters['dimentionName'];
  queryParameters['goodsClassName'] = parameters['goodsClassName'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['saleunitName'] = parameters['saleunitName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 销售汇总列表-合计-销售人员入口 - totalForSalesUsingGET
 * /rest/statements/merge/sale/summary/v1.0/sales/total
 * @param businessTimeStr - 业务发生时间，格式yyyy-MM
 * @param colourName - 颜色名称
 * @param dimentionName - 尺码名称
 * @param goodsClassName - 商品分类名称（对商品类型表的商品类型名）
 * @param goodsName - 商品名称
 * @param saleunitName - 商品单位
 */
export const totalForSales = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/sale/summary/v1.0/sales/total'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['businessTimeStr'] = parameters['businessTimeStr'];
  queryParameters['colourName'] = parameters['colourName'];
  queryParameters['dimentionName'] = parameters['dimentionName'];
  queryParameters['goodsClassName'] = parameters['goodsClassName'];
  queryParameters['goodsName'] = parameters['goodsName'];
  queryParameters['saleunitName'] = parameters['saleunitName'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获取汇算清缴一个单元格规则数据 - getDeclareCellDataUsingPOST_1
 * /rest/statements/merge/settlement/v1.0/data/declare/cell
 * @param queryVo - queryVo
 */
export const settlementData = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/settlement/v1.0/data/declare/cell'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['queryVo']
  if (parameters['queryVo'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: queryVo'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 获取汇算清缴固定资产取数细单 - getAssetRuleDataDetailUsingGET_1
 * /rest/statements/merge/settlement/v1.0/data/declare/cellAssetDetail/{flowNo}
 * @param flowNo - 取数流水号
 * @param position - 坐标
 */
export const getAssetRuleDataDetail= function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/settlement/v1.0/data/declare/cellAssetDetail/{flowNo}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{flowNo}', `${parameters['flowNo']}`)
  queryParameters['position'] = parameters['position'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获取汇算清缴科目余额取数细单 - getSubjectRuleDataDetailUsingGET_1
 * /rest/statements/merge/settlement/v1.0/data/declare/cellSubjectDetail/{flowNo}
 * @param flowNo - 取数流水号
 * @param position - 坐标
 */
export const getSubjectRuleDataDetail = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/settlement/v1.0/data/declare/cellSubjectDetail/{flowNo}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{flowNo}', `${parameters['flowNo']}`)
  queryParameters['position'] = parameters['position'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获取汇算清缴凭证取数细单 - getVoucherRuleDataDetailUsingGET_1
 * /rest/statements/merge/settlement/v1.0/data/declare/cellVoucherDetail/{flowNo}
 * @param flowNo - 取数流水号
 * @param position - 坐标
 */
export const getVoucherRuleDataDetail = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/settlement/v1.0/data/declare/cellVoucherDetail/{flowNo}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{flowNo}', `${parameters['flowNo']}`)
  queryParameters['position'] = parameters['position'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 获取汇算清缴简易申报数据 - getFinancialSettlementSimpleDeclareDataUsingGET_1
 * /rest/statements/merge/settlement/v1.0/data/declare/{from}/{to}
 * @param from - 会计属期开始（yyyyMM）
 * @param to - 会计属期结束（yyyyMM）
 */
export const getFinancialSettlementSimpleDeclareData = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/statements/merge/settlement/v1.0/data/declare/{from}/{to}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{from}', `${parameters['from']}`)
  path = path.replace('{to}', `${parameters['to']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}