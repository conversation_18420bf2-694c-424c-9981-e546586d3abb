/*
 * @Description:
 * @version:
 * @Company: 海闻软件
 * @Author: 马赛
 * @Date: 2019-01-14 15:13:41
 * @LastEditors: 肖泽涛
 * @LastEditTime: 2019-04-15 13:54:20
 */
import request from '@/utils/request';

/**
 *查询报销类型列表
 *
 * @export
 * @param {*}
 * @returns
 */
export function getClaimContentList(params) {
  return request.get('/rest/proxy/account/cmb/claim/content/v1.8.1/list', { params });
}

/**
 * 新增报销类型
 *
 * @export
 * @param {*}
 * @returns
 */
export function postClaimContent(params) {
  return request.post('/rest/proxy/account/cmb/claim/content/v1.8.1/insert', params);
}

/**
 * 修改报销类型
 *
 * @export
 * @param {*}
 * @returns
 */
export function updateClaimContent(sequenceId, params) {
  return request.put(`/rest/proxy/account/cmb/claim/content/v1.8.1/update/${sequenceId}`, params);
}

/**
 * 删除报销类型
 *
 * @export
 * @param {*}
 * @returns
 */
export function deleteClaimContent(sequenceId) {
  return request.delete(`/rest/proxy/account/cmb/claim/content/v1.8.1/delete/${sequenceId}`);
}
