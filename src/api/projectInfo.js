/*
 * @Description:  项目信息api
 *
 * @version:
 * @Company: 海闻软件
 * @Author: 郑晓荣
 * @Date: 2019-01-11 17:08:23
 * @LastEditors: 肖泽涛
 * @LastEditTime: 2019-04-18 11:25:07
 */
import request from '@/utils/request';

/**
 *新增项目
 *
 * @export
 * @param {*} params：{
    "companyId": 0,
    "createTime": "2019-01-11T09:11:49.873Z",
    "createUserid": 0,
    "modifyTime": "2019-01-11T09:11:49.873Z",
    "modifyUserid": 0,
    "projectContent": "string",
    "projectId": 0,
    "projectName": "string",
    "projectStatus": 0
  }
 * @returns
 */
export function insertProject(params) {
  return request.post('/rest/companyConfig/goods/project/v1.0/insert', params);
}

/**
 *查询项目信息--小程序
 *
 * @export
 * @param {*} {companyId--公司ID, projectName--项目名称, projectNameLike--项目名称模糊}
 * @returns
 */

// export function getProjectList({companyId="", projectName="", projectNameLike=""}) {

//   let params = objToUrl({companyId, projectName, projectNameLike});
//   return request.get(`/rest/companyConfig/goods/project/v1.0/list?${params}`);
// }
export function getProjectList(params) {
  return request.get('/rest/companyConfig/goods/project/v1.0/list', params);
}

/**
 *查询项目信息--WEB端
 *
 * @export
 * @param {*} {companyId--公司ID, projectName--项目名称, projectNameLike--项目名称模糊}
 * @returns
 */

// export function getProjectList({companyId="", projectName="", projectNameLike=""}) {

//   let params = objToUrl({companyId, projectName, projectNameLike});
//   return request.get(`/rest/companyConfig/goods/project/v1.0/list?${params}`);
// }
export function getProjectAllList(params) {
  return request.get('/rest/companyConfig/goods/project/v1.0/alllist', params);
}
/**
 *查询项目信息-单记录查询
 *
 * @export
 * @param {*} projectId 项目ID
 * @returns
 */
export function getProjectSingle(projectId) {
  return request.get(`/rest/companyConfig/goods/project/v1.0/${projectId}`);
}

/**
 *修改项目:
 *
 * @export
 * @param {*} params:
 * {
      "companyId": 0,
      "createTime": "2019-01-11T09:24:31.210Z",
      "createUserid": 0,
      "modifyTime": "2019-01-11T09:24:31.210Z",
      "modifyUserid": 0,
      "projectContent": "string",
      "projectId": 0,
      "projectName": "string",
      "projectStatus": 0
    }
 * @returns
 */
export function editProject(params) {
  return request.put(`/rest/companyConfig/goods/project/v1.0/${params.projectId}`, params);
}
