/*
 * @Description:
 * @version:
 * @Company: 海闻软件
 * @Author: 马赛
 * @Date: 2019-01-14 15:13:41
 * @LastEditors: 郑晓荣
 * @LastEditTime: 2019-02-13 15:16:59
 */
import request from '@/utils/request';

/**
 *查询员工可用余额
 *
 * @export
 * @param {*}
 * @returns
 */
export function getMemberStaffbalance() {
  return request.get('/rest/proxy/account/staffbalance/v1.0/debitbalance');
}

/**
 *冲借支查询列表
 *
 * @export
 * @param {*}
 * @returns
 */
export function getStaffBalanceList(params) {
  return request.get('/rest/proxy/account/staffbalance/v1.0/list', { params });
}

/**
 * 冲借支生成凭证
 *
 * @export
 * @param {*} fromPeriod  属期头 YYYYMM toPeriod 属期尾
 * @returns
 */
export function generateCertificate(fromPeriod, toPeriod) {
  return request.post(`/rest/proxy/account/merge/quick/account/v1.0/claim/vouchers/${fromPeriod}/${toPeriod}`);
}

/**
 * 员工报销付款
 *
 * @export
 * @param {*}  params---staffId 员工ID billAmount 金额
 * @param {*}
 * @returns
 */
export function reverseclaim(params) {
  return request.post('/rest/proxy/account/staffbalance/v1.0/reverseclaim', params);
}
