
/*
 * @Description:  代账公司接口api
 * @Author: 晓荣
 * @Date: 2019-07-17 10:38:36
 * @LastEditors: 晓荣
 */
import request from '@/utils/request';

// 代账公司人员账套统计报表
/**
 * 查询记账公司人员账套统计报表
 * @param {*} params
 * @param {string} params.page
 * @param {string} params.pageSize
 * @param {string} params.roleIds 角色id
 * @param {number} params.accountPeriod 账期
 * @returns
 */
export function getCompanyUserStatistics(params) {
  const API = '/rest/statements/merge/bk_company/v1.0';
  return request.get(API, { params });
}

/**
 * 查询记账公司账套启用停用数量
 *
*/
export function getCompanyCount() {
  const API = '/rest/statements/merge/bk_company/v1.0/com_count';
  return request.get(API);
}