/* eslint-disable */
import request from '@/utils/request';
/**
 * 新增/修改收费 - 
 * /rest/proxy/merge/agency/charge/v1.0
 * @param charge - charge
 */
export const insertChargeUsingPOST = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/merge/agency/charge/v1.0'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['charge']
  if (parameters['charge'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: charge'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 复制月费 - copyChargeUsingPOST
 * /rest/proxy/merge/agency/charge/v1.0/copyCharge
 * @param charge - charge
 */
export const chargeCopycharge = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/merge/agency/charge/v1.0/copyCharge'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['charge']
  if (parameters['charge'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: charge'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 导出收费 - exportChargeUsingGET
 * /rest/proxy/merge/agency/charge/v1.0/exportCharge
 * @param bkCompanyCode - 代帐公司编号
 * @param chargeAmountStatus - 缴费情况
 * @param companyIdsList - 公司ids
 * @param hostId - 主办ID
 * @param yearId - 年份
 */
export const chargeExportcharge = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/merge/agency/charge/v1.0/exportCharge'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['bkCompanyCode'] = parameters['bkCompanyCode'];
  queryParameters['chargeAmountStatus'] = parameters['chargeAmountStatus'];
  queryParameters['companyIdsList'] = parameters['companyIdsList'];
  queryParameters['hostId'] = parameters['hostId'];
  queryParameters['yearId'] = parameters['yearId'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 根据年份查询合计 - getChargeTotalAmountUsingGET
 * /rest/proxy/merge/agency/charge/v1.0/getChargeTotalAmount
 * @param bkCompanyCode - 代帐公司编号
 * @param chargeAmountStatus - 缴费情况
 * @param companyIdsList - 公司ids
 * @param hostId - 主办ID
 * @param yearId - 年份
 */
export const chargeGetchargetotalamount = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/merge/agency/charge/v1.0/getChargeTotalAmount'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['bkCompanyCode'] = parameters['bkCompanyCode'];
  queryParameters['chargeAmountStatus'] = parameters['chargeAmountStatus'];
  queryParameters['companyIdsList'] = parameters['companyIdsList'];
  queryParameters['hostId'] = parameters['hostId'];
  queryParameters['yearId'] = parameters['yearId'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导入收费 - importChargeUsingPOST
 * /rest/proxy/merge/agency/charge/v1.0/importCharge
 * @param requestHeadersETag - 
 * @param requestHeadersAcceptCharset0Registered - 
 * @param requestHeadersAccept0CharSetRegistered - 
 * @param requestHeadersAccept0CharsetRegistered - 
 * @param requestHeadersAccept0Concrete - 
 * @param requestHeadersAccept0QualityValue - 
 * @param requestHeadersAccept0Subtype - 
 * @param requestHeadersAccept0Type - 
 * @param requestHeadersAccept0WildcardSubtype - 
 * @param requestHeadersAccept0WildcardType - 
 * @param requestHeadersAccessControlAllowCredentials - 
 * @param requestHeadersAccessControlAllowHeaders - 
 * @param requestHeadersAccessControlAllowMethods - 
 * @param requestHeadersAccessControlAllowOrigin - 
 * @param requestHeadersAccessControlExposeHeaders - 
 * @param requestHeadersAccessControlMaxAge - 
 * @param requestHeadersAccessControlRequestHeaders - 
 * @param requestHeadersAccessControlRequestMethod - 
 * @param requestHeadersAllow - 
 * @param requestHeadersCacheControl - 
 * @param requestHeadersConnection - 
 * @param requestHeadersContentLength - 
 * @param requestHeadersContentTypeCharSetRegistered - 
 * @param requestHeadersContentTypeCharsetRegistered - 
 * @param requestHeadersContentTypeConcrete - 
 * @param requestHeadersContentTypeQualityValue - 
 * @param requestHeadersContentTypeSubtype - 
 * @param requestHeadersContentTypeType - 
 * @param requestHeadersContentTypeWildcardSubtype - 
 * @param requestHeadersContentTypeWildcardType - 
 * @param requestHeadersDate - 
 * @param requestHeadersExpires - 
 * @param requestHeadersIfMatch - 
 * @param requestHeadersIfModifiedSince - 
 * @param requestHeadersIfNoneMatch - 
 * @param requestHeadersIfUnmodifiedSince - 
 * @param requestHeadersLastModified - 
 * @param requestHeadersLocationAbsolute - 
 * @param requestHeadersLocationAuthority - 
 * @param requestHeadersLocationFragment - 
 * @param requestHeadersLocationHost - 
 * @param requestHeadersLocationOpaque - 
 * @param requestHeadersLocationPath - 
 * @param requestHeadersLocationPort - 
 * @param requestHeadersLocationQuery - 
 * @param requestHeadersLocationRawAuthority - 
 * @param requestHeadersLocationRawFragment - 
 * @param requestHeadersLocationRawPath - 
 * @param requestHeadersLocationRawQuery - 
 * @param requestHeadersLocationRawSchemeSpecificPart - 
 * @param requestHeadersLocationRawUserInfo - 
 * @param requestHeadersLocationScheme - 
 * @param requestHeadersLocationSchemeSpecificPart - 
 * @param requestHeadersLocationUserInfo - 
 * @param requestHeadersOrigin - 
 * @param requestHeadersPragma - 
 * @param requestHeadersUpgrade - 
 * @param requestHeadersVary - 
 * @param requestMethod - 
 */
export const chargeImportcharge = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/merge/agency/charge/v1.0/importCharge'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['requestHeaders.ETag'] = parameters['requestHeadersETag'];
  queryParameters['requestHeaders.acceptCharset[0].registered'] = parameters['requestHeadersAcceptCharset0Registered'];
  queryParameters['requestHeaders.accept[0].charSet.registered'] = parameters['requestHeadersAccept0CharSetRegistered'];
  queryParameters['requestHeaders.accept[0].charset.registered'] = parameters['requestHeadersAccept0CharsetRegistered'];
  queryParameters['requestHeaders.accept[0].concrete'] = parameters['requestHeadersAccept0Concrete'];
  queryParameters['requestHeaders.accept[0].qualityValue'] = parameters['requestHeadersAccept0QualityValue'];
  queryParameters['requestHeaders.accept[0].subtype'] = parameters['requestHeadersAccept0Subtype'];
  queryParameters['requestHeaders.accept[0].type'] = parameters['requestHeadersAccept0Type'];
  queryParameters['requestHeaders.accept[0].wildcardSubtype'] = parameters['requestHeadersAccept0WildcardSubtype'];
  queryParameters['requestHeaders.accept[0].wildcardType'] = parameters['requestHeadersAccept0WildcardType'];
  queryParameters['requestHeaders.accessControlAllowCredentials'] = parameters['requestHeadersAccessControlAllowCredentials'];
  queryParameters['requestHeaders.accessControlAllowHeaders'] = parameters['requestHeadersAccessControlAllowHeaders'];
  queryParameters['requestHeaders.accessControlAllowMethods'] = parameters['requestHeadersAccessControlAllowMethods'];
  queryParameters['requestHeaders.accessControlAllowOrigin'] = parameters['requestHeadersAccessControlAllowOrigin'];
  queryParameters['requestHeaders.accessControlExposeHeaders'] = parameters['requestHeadersAccessControlExposeHeaders'];
  queryParameters['requestHeaders.accessControlMaxAge'] = parameters['requestHeadersAccessControlMaxAge'];
  queryParameters['requestHeaders.accessControlRequestHeaders'] = parameters['requestHeadersAccessControlRequestHeaders'];
  queryParameters['requestHeaders.accessControlRequestMethod'] = parameters['requestHeadersAccessControlRequestMethod'];
  queryParameters['requestHeaders.allow'] = parameters['requestHeadersAllow'];
  queryParameters['requestHeaders.cacheControl'] = parameters['requestHeadersCacheControl'];
  queryParameters['requestHeaders.connection'] = parameters['requestHeadersConnection'];
  queryParameters['requestHeaders.contentLength'] = parameters['requestHeadersContentLength'];
  queryParameters['requestHeaders.contentType.charSet.registered'] = parameters['requestHeadersContentTypeCharSetRegistered'];
  queryParameters['requestHeaders.contentType.charset.registered'] = parameters['requestHeadersContentTypeCharsetRegistered'];
  queryParameters['requestHeaders.contentType.concrete'] = parameters['requestHeadersContentTypeConcrete'];
  queryParameters['requestHeaders.contentType.qualityValue'] = parameters['requestHeadersContentTypeQualityValue'];
  queryParameters['requestHeaders.contentType.subtype'] = parameters['requestHeadersContentTypeSubtype'];
  queryParameters['requestHeaders.contentType.type'] = parameters['requestHeadersContentTypeType'];
  queryParameters['requestHeaders.contentType.wildcardSubtype'] = parameters['requestHeadersContentTypeWildcardSubtype'];
  queryParameters['requestHeaders.contentType.wildcardType'] = parameters['requestHeadersContentTypeWildcardType'];
  queryParameters['requestHeaders.date'] = parameters['requestHeadersDate'];
  queryParameters['requestHeaders.expires'] = parameters['requestHeadersExpires'];
  queryParameters['requestHeaders.ifMatch'] = parameters['requestHeadersIfMatch'];
  queryParameters['requestHeaders.ifModifiedSince'] = parameters['requestHeadersIfModifiedSince'];
  queryParameters['requestHeaders.ifNoneMatch'] = parameters['requestHeadersIfNoneMatch'];
  queryParameters['requestHeaders.ifUnmodifiedSince'] = parameters['requestHeadersIfUnmodifiedSince'];
  queryParameters['requestHeaders.lastModified'] = parameters['requestHeadersLastModified'];
  queryParameters['requestHeaders.location.absolute'] = parameters['requestHeadersLocationAbsolute'];
  queryParameters['requestHeaders.location.authority'] = parameters['requestHeadersLocationAuthority'];
  queryParameters['requestHeaders.location.fragment'] = parameters['requestHeadersLocationFragment'];
  queryParameters['requestHeaders.location.host'] = parameters['requestHeadersLocationHost'];
  queryParameters['requestHeaders.location.opaque'] = parameters['requestHeadersLocationOpaque'];
  queryParameters['requestHeaders.location.path'] = parameters['requestHeadersLocationPath'];
  queryParameters['requestHeaders.location.port'] = parameters['requestHeadersLocationPort'];
  queryParameters['requestHeaders.location.query'] = parameters['requestHeadersLocationQuery'];
  queryParameters['requestHeaders.location.rawAuthority'] = parameters['requestHeadersLocationRawAuthority'];
  queryParameters['requestHeaders.location.rawFragment'] = parameters['requestHeadersLocationRawFragment'];
  queryParameters['requestHeaders.location.rawPath'] = parameters['requestHeadersLocationRawPath'];
  queryParameters['requestHeaders.location.rawQuery'] = parameters['requestHeadersLocationRawQuery'];
  queryParameters['requestHeaders.location.rawSchemeSpecificPart'] = parameters['requestHeadersLocationRawSchemeSpecificPart'];
  queryParameters['requestHeaders.location.rawUserInfo'] = parameters['requestHeadersLocationRawUserInfo'];
  queryParameters['requestHeaders.location.scheme'] = parameters['requestHeadersLocationScheme'];
  queryParameters['requestHeaders.location.schemeSpecificPart'] = parameters['requestHeadersLocationSchemeSpecificPart'];
  queryParameters['requestHeaders.location.userInfo'] = parameters['requestHeadersLocationUserInfo'];
  queryParameters['requestHeaders.origin'] = parameters['requestHeadersOrigin'];
  queryParameters['requestHeaders.pragma'] = parameters['requestHeadersPragma'];
  queryParameters['requestHeaders.upgrade'] = parameters['requestHeadersUpgrade'];
  queryParameters['requestHeaders.vary'] = parameters['requestHeadersVary'];
  queryParameters['requestMethod'] = parameters['requestMethod'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 查询收费管理列表 - findChargeUsingGET
 * /rest/proxy/merge/agency/charge/v1.0/list
 * @param bkCompanyCode - 代帐公司编号
 * @param chargeAmountStatus - 缴费情况
 * @param companyIdsList - 公司ids
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param hostId - 主办ID
 * @param hostName - 主办名称
 * @param invoiceDateFrom - 开票开始日期
 * @param invoiceDateTo - 开票结束日期
 * @param isAccount - 是否做账
 * @param page - 
 * @param pageSize - 
 * @param sellerName - 销方名称
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 * @param yearId - 年份
 */
export const chargeList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/proxy/merge/agency/charge/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['bkCompanyCode'] = parameters['bkCompanyCode'];
  queryParameters['chargeAmountStatus'] = parameters['chargeAmountStatus'];
  queryParameters['companyIdsList'] = parameters['companyIdsList'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['hostId'] = parameters['hostId'];
  queryParameters['hostName'] = parameters['hostName'];
  queryParameters['invoiceDateFrom'] = parameters['invoiceDateFrom'];
  queryParameters['invoiceDateTo'] = parameters['invoiceDateTo'];
  queryParameters['isAccount'] = parameters['isAccount'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['sellerName'] = parameters['sellerName'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  queryParameters['yearId'] = parameters['yearId'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}