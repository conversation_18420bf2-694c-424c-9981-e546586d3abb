/*
 * @Description: 事项日历相关api
 * @version:
 * @Company: 海闻软件
 * @Author: 肖泽涛
 * @Date: 2019-01-05 16:34:35
 * @LastEditors: 肖泽涛
 * @LastEditTime: 2019-01-26 14:27:36
 */

import request from '@/utils/request';
// 获取当月的事项
export const getCurMonAllItem = ({
  beginDate,
  endDate,
}) => request.get('/rest/proxy/account/calendarItem/v1.0/getCurMonAllItem', {
  params: {
    beginDate,
    endDate,
  },
});
// 获取当日的事项列表
export const getCurDayItem = ({
  itemDate,
}) => request.get('/rest/proxy/account/calendarItem/v1.0/list', {
  params: {
    itemDate,
  },
});
// 添加事项
export const insertCalendarItem = ({
  baseItemType,
  itemContent,
  itemType,
  itemDate,
}) => request.post(`/rest/proxy/account/calendarItem/v1.0/insert?num=${Math.random()}`, {
  baseItemType,
  itemContent,
  itemType,
  itemDate,
});
// 修改事项
export const updateCalendarItem = (itemId, {
  baseItemType,
  itemContent,
  itemType,
  itemDate,
}) => request.put(`/rest/proxy/account/calendarItem/v1.0/update/${itemId}`, {
  baseItemType,
  itemContent,
  itemType,
  itemDate,
});
// 删除事项
export const deleteCalendarItem = (itemId) => request.delete(`/rest/proxy/account/calendarItem/v1.0/delete/${itemId}`);
