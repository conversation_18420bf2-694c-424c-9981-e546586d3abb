/**
 * 文件管理相关接口
 */
import request from '@/utils/request'

export const businessFileManageApi = {
  /**
   * 获取aliOSS  查看权限临时token
   * @returns
   **/
  getAliOssToken() {
    return request.get('/rest/companyArchive/parkfile/v1.0/getSecurityToken/1')
  },

  /**
   * 提交文件信息，获取上传的STS token 以及上传的文件路径
   * @param {string} accountPeriod
   * @param {object} parkFileItemVoList - 文件列表 {fileName: string, fileSize: number}[]
   * @returns
   */
  getUploadFileSTSTokenAndPath(parkFileItemVoList, accountPeriod) {
    const API = `/rest/companyArchive/parkfile/v1.0/handleParkFileList`
    return request.post(API, { accountPeriod, parkFileItemVoList})
  },

  /**
   * 删除文件 - 通过文件id
   * @param {string} parkFileItemIds - 文件id，多个文件id用逗号分隔
   * @returns
   */
  deleteFileById(parkFileItemIds) {
    const API = `/rest/companyArchive/parkfile/v1.0/delete`
    return request.delete(API, {params: {parkFileItemIds}})
  },

  /**
   * 获取文件列表
   * @param {string} params
   * @returns
   */
  getParkFileList(params) {
    const API = `/rest/companyArchive/parkfile/v1.0/getParkFileList`
    return request.get(API, {params})
  },

  /**
   * 对某个属期文件进行归档，归档后的属期不可删除文件和上传文件
   * @param {string} accountPeriod - YYYYMM 
   * @param {string} status - 0 未归档 1 已归档
   * @returns
   */
  archiveParkFile(status, accountPeriod) {
    const API = `/rest/companyArchive/parkfile/v1.0/updateUploadStatus/${status}/${accountPeriod}`
    return request.put(API)
  },

  /**
   * http://192.168.56.42:10102/swagger-ui.html#/park-file-controller/getUploadStatusByAccountPeriod_ParkFile_UsingGET
   * /rest/companyArchive/parkfile/v1.0/updateUploadStatus/{accountPeriod}
   * 获取传入属期的文件归档状态
   * @param {string} accountPeriod - YYYYMM 
   */
  getArchiveStatusByAccountPeriod(accountPeriod) {
    const API = `/rest/companyArchive/parkfile/v1.0/updateUploadStatus/${accountPeriod}`
    return request.get(API)
  },

  /**
   * 签收或者退回文件
   * http://192.168.56.42:10102/swagger-ui.html#/park-file-controller/updateSendBackOrSign_ParkFile_UsingPUT
   * 
   * @param {string} operation - sing 签收  sendback 退回
   * @param {array} data - 文件签收或者退回的表单数据 - [{ "parkFileItemId": 0, "signatureExplain": "string"}]
   * @returns {}
   * @returns
   */
  updateSendBackOrSign(operation, data) {
    const API = `/rest/companyArchive/parkfile/v1.0/${operation}`
    return request.put(API, data)
  },

  /**
   * 批量更新文件信息
   * 
   * @param {object} data - 文件列表 {
   *   "fileType": 0,
   *   "parkFileItemIds": [],
   *   "signatureExplain": ""
   * }
   * 
   */
  updateParkFileList(data) {
    const API = `/rest/companyArchive/parkfile/v1.0/update`
    return request.put(API, data)
  },

  /**
   * http://192.168.56.42:10102/swagger-ui.html#/park-file-controller/addParkFileList_ParkFile_UsingPOST
   * 新增文件数据
   * @param {array} parkFileList - 文件列表
   * @returns
   */
  addParkFileList(parkFileList) {
    const API = `/rest/companyArchive/parkfile/v1.0/addParkFileList`
    return request.post(API, parkFileList)
  }
}

export const initManageApi = {
  /**
   * 获取aliOSS  查看权限临时token
   * @returns
   **/
  getAliOssToken() {
    return request.get('/rest/companyArchive/initFile/v1.0/getSecurityToken/1')
  },

  /**
   * 提交文件信息，获取上传的STS token 以及上传的文件路径
   * @param {object} parkFileItemVoList - 文件列表 {fileName: string, fileSize: number}[]
   * @returns
   */
  getUploadFileSTSTokenAndPath(parkFileItemVoList) {
    const API = `/rest/companyArchive/initFile/v1.0/handleParkFileList`
    return request.post(API, {parkFileItemVoList})
  },

  /**
   * 删除文件 - 通过文件id
   * @param {string} parkFileItemIds - 文件id，多个文件id用逗号分隔
   * @returns
   */
  deleteFileById(parkFileItemIds) {
    const API = `/rest/companyArchive/initFile/v1.0/delete`
    return request.delete(API, {params: {parkFileItemIds}})
  },

  /**
   * 获取文件列表
   * @returns
   */
  getParkFileList() {
    const API = `/rest/companyArchive/initFile/v1.0/getParkFileList`
    // 
    return request.get(API)
  },

  /**
   * 对某个属期文件进行归档，归档后的属期不可删除文件和上传文件
   * @param {string} accountPeriod - YYYYMM 
   * @param {string} status - 0 未归档 1 已归档
   * @returns
   */
  archiveParkFile(status) {
    const API = `/rest/companyArchive/initFile/v1.0/updateUploadStatus/${status}`
    return request.put(API)
  },

  /**
   * http://192.168.56.42:10102/swagger-ui.html#/park-file-controller/getUploadStatusByAccountPeriod_ParkFile_UsingGET
   * /rest/companyArchive/parkfile/v1.0/updateUploadStatus/{accountPeriod}
   * 获取传入属期的文件归档状态
   */
  getArchiveStatusByAccountPeriod() {
    const API = `/rest/companyArchive/initFile/v1.0/updateUploadStatus`
    return request.get(API)
  },

  /**
   * 签收或者退回文件
   * http://192.168.56.42:10102/swagger-ui.html#/park-file-controller/updateSendBackOrSign_ParkFile_UsingPUT
   * 
   * @param {string} operation - sing 签收  sendback 退回
   * @param {array} data - 文件签收或者退回的表单数据 - [{ "parkFileItemId": 0, "signatureExplain": "string"}]
   * @returns {}
   * @returns
   */
  updateSendBackOrSign(operation, data) {
    const API = `/rest/companyArchive/initFile/v1.0/${operation}`
    return request.put(API, data)
  },

  /**
   * 批量更新文件信息
   * 
   * @param {object} data - 文件列表 {
   *   "fileType": 0,
   *   "parkFileItemIds": [],
   *   "signatureExplain": ""
   * }
   * 
   */
  updateParkFileList(data) {
    const API = `/rest/companyArchive/initFile/v1.0/update`
    return request.put(API, data)
  },

  /**
   * http://192.168.56.42:10102/swagger-ui.html#/park-file-controller/addParkFileList_ParkFile_UsingPOST
   * 新增文件数据
   * @param {array} parkFileList - 文件列表
   * @returns
   */
  addParkFileList(parkFileList) {
    const API = `/rest/companyArchive/initFile/v1.0/addParkFileList`
    return request.post(API, parkFileList)
  }
}
