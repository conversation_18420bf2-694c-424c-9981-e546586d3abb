/* eslint-disable */
import request from '@/utils/request';
/**
 * 删除商品颜色分组缓存 - 
 * /rest/companyConfig/goods/colourGrp/cacheclear
 */
export const cacheClearUsingGET_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/colourGrp/cacheclear'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 新增商品颜色分组 - addColorGrpUsingPOST
 * /rest/companyConfig/goods/colourGrp/v1.0/insert
 * @param accountNonExpired - 
 * @param accountNonLocked - 
 * @param authorities0Authority - 
 * @param bkCompanyCode - 
 * @param bkCompanyName - 
 * @param bkUserStatus - 
 * @param captcha - 
 * @param clientIp - 
 * @param colourGrp - 商品颜色分组实体
 * @param companyId - 
 * @param companyName - 
 * @param confirmPwd - 
 * @param createTime - 
 * @param credentialsNonExpired - 
 * @param email - 
 * @param enabled - 
 * @param groupId - 
 * @param loginErrorTimes - 
 * @param mobilePhone - 
 * @param modifyTime - 
 * @param orgId - 
 * @param password - 
 * @param roleNames - 
 * @param selectCompanyId - 
 * @param sessionId - 
 * @param ucId - 
 * @param userAccount - 
 * @param userId - 
 * @param userMask - 
 * @param userSource - 
 * @param userStatus - 
 * @param userType - 
 * @param username - 
 * @param wechatCode - 
 */
export const colourGrpInsert = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/colourGrp/v1.0/insert'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['accountNonExpired'] = parameters['accountNonExpired'];
  queryParameters['accountNonLocked'] = parameters['accountNonLocked'];
  queryParameters['authorities[0].authority'] = parameters['authorities0Authority'];
  queryParameters['bkCompanyCode'] = parameters['bkCompanyCode'];
  queryParameters['bkCompanyName'] = parameters['bkCompanyName'];
  queryParameters['bkUserStatus'] = parameters['bkUserStatus'];
  queryParameters['captcha'] = parameters['captcha'];
  queryParameters['clientIp'] = parameters['clientIp'];
  body = parameters['colourGrp']
  queryParameters['companyId'] = parameters['companyId'];
  queryParameters['companyName'] = parameters['companyName'];
  queryParameters['confirmPwd'] = parameters['confirmPwd'];
  queryParameters['createTime'] = parameters['createTime'];
  queryParameters['credentialsNonExpired'] = parameters['credentialsNonExpired'];
  queryParameters['email'] = parameters['email'];
  queryParameters['enabled'] = parameters['enabled'];
  queryParameters['groupId'] = parameters['groupId'];
  queryParameters['loginErrorTimes'] = parameters['loginErrorTimes'];
  queryParameters['mobilePhone'] = parameters['mobilePhone'];
  queryParameters['modifyTime'] = parameters['modifyTime'];
  queryParameters['orgId'] = parameters['orgId'];
  queryParameters['password'] = parameters['password'];
  queryParameters['roleNames'] = parameters['roleNames'];
  queryParameters['selectCompanyId'] = parameters['selectCompanyId'];
  queryParameters['sessionId'] = parameters['sessionId'];
  queryParameters['ucId'] = parameters['ucId'];
  queryParameters['userAccount'] = parameters['userAccount'];
  queryParameters['userId'] = parameters['userId'];
  queryParameters['userMask'] = parameters['userMask'];
  queryParameters['userSource'] = parameters['userSource'];
  queryParameters['userStatus'] = parameters['userStatus'];
  queryParameters['userType'] = parameters['userType'];
  queryParameters['username'] = parameters['username'];
  queryParameters['wechatCode'] = parameters['wechatCode'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 返回全部或按颜色分组名称的颜色分组列表 - findAllUsingGET
 * /rest/companyConfig/goods/colourGrp/v1.0/list
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const colourGrpList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/colourGrp/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改商品颜色分组 - 
 * /rest/companyConfig/goods/colourGrp/v1.0/{colourGrpId}
 * @param colourGrp - 商品颜色分组实体
 * @param colourGrpId - colourGrpId
 */
export const editColorGrpUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/colourGrp/v1.0/{colourGrpId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['colourGrp']
  path = path.replace('{colourGrpId}', `${parameters['colourGrpId']}`)
  if (parameters['colourGrpId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: colourGrpId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 根据编号删除商品颜色分组 - 
 * /rest/companyConfig/goods/colourGrp/v1.0/{colourGrpId}
 * @param colourGrpId - colourGrpId
 */
export const removeColorGrpByIdUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/colourGrp/v1.0/{colourGrpId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{colourGrpId}', `${parameters['colourGrpId']}`)
  if (parameters['colourGrpId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: colourGrpId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 删除商品颜色缓存 - 
 * /rest/companyConfig/goods/colours/cacheclear
 */
export const cacheClearUsingGET_3 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/colours/cacheclear'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回全部或分组的商品颜色 - findAllUsingGET_1
 * /rest/companyConfig/goods/colours/v1.0/list
 * @param colourName - 商品颜色名称
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const coloursList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/colours/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['colourName'] = parameters['colourName'];
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 新增或修改商品颜色 - saveColoursUsingPOST
 * /rest/companyConfig/goods/colours/v1.0/save
 * @param list - 商品颜色实体
 */
export const coloursSave = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/colours/v1.0/save'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['list']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 根据编号删除商品颜色 - 
 * /rest/companyConfig/goods/colours/v1.0/{colourId}
 * @param colourId - 商品颜色ID
 */
export const removeColoursByIdUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/colours/v1.0/{colourId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{colourId}', `${parameters['colourId']}`)
  if (parameters['colourId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: colourId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 删除商品尺码缓存 - 
 * /rest/companyConfig/goods/dimensions/cacheclear
 */
export const cacheClearUsingGET_5 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/dimensions/cacheclear'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回全部或分组的商品尺码 - findAllUsingGET_2
 * /rest/companyConfig/goods/dimensions/v1.0/list
 * @param data - 
 * @param dimensionName - 商品尺码名称
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const dimensionsList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/dimensions/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['dimensionName'] = parameters['dimensionName'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 新增或修改商品尺码 - saveDimensionsUsingPOST
 * /rest/companyConfig/goods/dimensions/v1.0/save
 * @param list - 商品尺码实体
 */
export const dimensionsSave = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/dimensions/v1.0/save'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['list']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 根据编号删除商品尺码 - 
 * /rest/companyConfig/goods/dimensions/v1.0/{dimensionId}
 * @param dimensionId - 商品尺码ID
 */
export const removeDimensionByIdUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/dimensions/v1.0/{dimensionId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{dimensionId}', `${parameters['dimensionId']}`)
  if (parameters['dimensionId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: dimensionId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 删除商品尺码分组缓存 - 
 * /rest/companyConfig/goods/dimentionGrp/cacheclear
 */
export const cacheClearUsingGET_7 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/dimentionGrp/cacheclear'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 新增商品尺码分组 - addDimentionGrpUsingPOST
 * /rest/companyConfig/goods/dimentionGrp/v1.0/insert
 * @param dimentionGrp - 商品尺码分组实体
 */
export const dimentionGrpInsert = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/dimentionGrp/v1.0/insert'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['dimentionGrp']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回全部商品尺码分组 - findAllUsingGET_3
 * /rest/companyConfig/goods/dimentionGrp/v1.0/list
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const dimentionGrpList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/dimentionGrp/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 更新商品尺码分组 - 
 * /rest/companyConfig/goods/dimentionGrp/v1.0/{dimensionGrpId}
 * @param dimensionGrpId - dimensionGrpId
 * @param dimentionGrp - 商品尺码分组实体
 */
export const editDimentionGrpUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/dimentionGrp/v1.0/{dimensionGrpId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{dimensionGrpId}', `${parameters['dimensionGrpId']}`)
  if (parameters['dimensionGrpId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: dimensionGrpId'))
  }
  body = parameters['dimentionGrp']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 根据编号删除商品尺码分组 - 
 * /rest/companyConfig/goods/dimentionGrp/v1.0/{dimensionGrpId}
 * @param dimensionGrpId - dimensionGrpId
 */
export const removeDimentionGrpByIdUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/dimentionGrp/v1.0/{dimensionGrpId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{dimensionGrpId}', `${parameters['dimensionGrpId']}`)
  if (parameters['dimensionGrpId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: dimensionGrpId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 删除商品缓存 - 
 * /rest/companyConfig/goods/goods/cacheclear
 */
export const cacheClearUsingGET_11 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/goods/cacheclear'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 商品下拉菜单查询 - getAllUsingGET
 * /rest/companyConfig/goods/goods/v1.0/all
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const goodsAll = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/goods/v1.0/all'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 导入 - importGoodsUsingPOST
 * 
 * @param params - params
 */
export const goodsImport = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = ''
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['params']
  if (parameters['params'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: params'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 新增商品 - addGoodsUsingPOST
 * /rest/companyConfig/goods/goods/v1.0/insert
 * @param cdGoods - 商品实体
 */
export const goodsInsert = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/goods/v1.0/insert'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['cdGoods']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回全部或部分商品 - findAllUsingGET_5
 * /rest/companyConfig/goods/goods/v1.0/list
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param goodsClassId - 商品分类id
 * @param goodsNameOrGoodsCode - 商品名称或货号
 * @param goodsStatus - 商品状态
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const goodsList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/goods/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['goodsClassId'] = parameters['goodsClassId'];
  queryParameters['goodsNameOrGoodsCode'] = parameters['goodsNameOrGoodsCode'];
  queryParameters['goodsStatus'] = parameters['goodsStatus'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 查询构成材料 - findMaterialUsingGET
 * /rest/companyConfig/goods/goods/v1.0/material/list
 */
export const goodsMaterial = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/goods/v1.0/material/list'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 单个商品查询 - 
 * /rest/companyConfig/goods/goods/v1.0/{goodsId}
 * @param goodsId - 商品id
 */
export const findOneGoodsUsingGET = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/goods/v1.0/{goodsId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{goodsId}', `${parameters['goodsId']}`)
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 修改商品 - 
 * /rest/companyConfig/goods/goods/v1.0/{goodsId}
 * @param cdGoods - 商品实体
 * @param goodsId - goodsId
 */
export const editGoodsUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/goods/v1.0/{goodsId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['cdGoods']
  path = path.replace('{goodsId}', `${parameters['goodsId']}`)
  if (parameters['goodsId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: goodsId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 删除商品分类缓存 - 
 * /rest/companyConfig/goods/goodsClass/cacheclear
 */
export const cacheClearUsingGET_9 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/goodsClass/cacheclear'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 新增商品分类 - addGoodsClassGrpUsingPOST
 * /rest/companyConfig/goods/goodsClass/v1.0/insert
 * @param goodsClass - 商品分类实体
 */
export const goodsClassInsert = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/goodsClass/v1.0/insert'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['goodsClass']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回商品分类 - findAllUsingGET_4
 * /rest/companyConfig/goods/goodsClass/v1.0/list
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param parentClassId - 商品分类父类Id
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const goodsClassList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/goodsClass/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['parentClassId'] = parameters['parentClassId'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 更新商品分类 - 
 * /rest/companyConfig/goods/goodsClass/v1.0/{goodsClassId}
 * @param goodsClass - 商品分类实体
 * @param goodsClassId - goodsClassId
 */
export const editGoodsClassGrpUsingPUT = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/goodsClass/v1.0/{goodsClassId}'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['goodsClass']
  path = path.replace('{goodsClassId}', `${parameters['goodsClassId']}`)
  if (parameters['goodsClassId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: goodsClassId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('put', domain + path, body, queryParameters, form, config)
}
/**
 * 根据编号删除商品分类 - 
 * /rest/companyConfig/goods/goodsClass/v1.0/{goodsClassId}
 * @param goodsClassId - 商品分类ID
 */
export const removeGoodsClassByIdUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/goodsClass/v1.0/{goodsClassId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{goodsClassId}', `${parameters['goodsClassId']}`)
  if (parameters['goodsClassId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: goodsClassId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 删除商品销售单位缓存 - 
 * /rest/companyConfig/goods/saleUnits/cacheclear
 */
export const clearCacheUsingGET_1 = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/saleUnits/cacheclear'
  let body
  let queryParameters = {}
  let form = {}
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 以列表的方式返回全部商品销售单位 - findAllUsingGET_6
 * /rest/companyConfig/goods/saleUnits/v1.0/list
 * @param data - 
 * @param filterField - 
 * @param filterIgnoreCase - 
 * @param filterLogic - 
 * @param filterOperator - 
 * @param filterValue - 
 * @param group0Aggregates0Aggregate - 
 * @param group0Aggregates0Field - 
 * @param group0Dir - 
 * @param group0Field - 
 * @param page - 
 * @param pageSize - 
 * @param saleunitName - 商品销售单位名称
 * @param skip - 
 * @param sort2String - 
 * @param sortJsonStr - 
 * @param take - 
 */
export const saleUnitsList = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/saleUnits/v1.0/list'
  let body
  let queryParameters = {}
  let form = {}
  queryParameters['data'] = parameters['data'];
  queryParameters['filter.field'] = parameters['filterField'];
  queryParameters['filter.ignoreCase'] = parameters['filterIgnoreCase'];
  queryParameters['filter.logic'] = parameters['filterLogic'];
  queryParameters['filter.operator'] = parameters['filterOperator'];
  queryParameters['filter.value'] = parameters['filterValue'];
  queryParameters['group[0].aggregates[0].aggregate'] = parameters['group0Aggregates0Aggregate'];
  queryParameters['group[0].aggregates[0].field'] = parameters['group0Aggregates0Field'];
  queryParameters['group[0].dir'] = parameters['group0Dir'];
  queryParameters['group[0].field'] = parameters['group0Field'];
  queryParameters['page'] = parameters['page'];
  queryParameters['pageSize'] = parameters['pageSize'];
  queryParameters['saleunitName'] = parameters['saleunitName'];
  queryParameters['skip'] = parameters['skip'];
  queryParameters['sort2String'] = parameters['sort2String'];
  queryParameters['sortJSONStr'] = parameters['sortJsonStr'];
  queryParameters['take'] = parameters['take'];
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('get', domain + path, body, queryParameters, form, config)
}
/**
 * 单位新增或修改 - saveSaleUnitsUsingPOST
 * /rest/companyConfig/goods/saleUnits/v1.0/save
 * @param list - 商品销售单位实体集合
 */
export const saleUnitsSave = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/saleUnits/v1.0/save'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['list']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 根据编号删除商品销售单位 - 
 * /rest/companyConfig/goods/saleUnits/v1.0/{saleunitId}
 * @param saleunitId - saleunitId
 */
export const removeSaleUnitByIdUsingDELETE = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/rest/companyConfig/goods/saleUnits/v1.0/{saleunitId}'
  let body
  let queryParameters = {}
  let form = {}
  path = path.replace('{saleunitId}', `${parameters['saleunitId']}`)
  if (parameters['saleunitId'] === undefined) {
    return Promise.reject(new Error('Missing required  parameter: saleunitId'))
  }
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('delete', domain + path, body, queryParameters, form, config)
}
/**
 * 批量新增商品 - insertBatchGoodsUsingPOST_1
 * /restsafe/companyConfig/goods/goods/v1.0/insertBatch
 * @param goodsList - 商品实体
 */
export const goodsInsertbatch = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/goods/goods/v1.0/insertBatch'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['goodsList']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}
/**
 * 批量新增商品(科目反写) - insertBatchGoodsReUsingPOST_1
 * /restsafe/companyConfig/goods/goods/v1.0/reverseInserts
 * @param goodsList - 商品实体
 */
export const goodsReverseinserts = function(parameters = {}) {
  const domain = parameters.$domain || '';
  const config = parameters.$config
  let path = '/restsafe/companyConfig/goods/goods/v1.0/reverseInserts'
  let body
  let queryParameters = {}
  let form = {}
  body = parameters['goodsList']
  if (parameters.$queryParameters) {
    Object.keys(parameters.$queryParameters).forEach(function(parameterName) {
      queryParameters[parameterName] = parameters.$queryParameters[parameterName]
    });
  }
  return request('post', domain + path, body, queryParameters, form, config)
}