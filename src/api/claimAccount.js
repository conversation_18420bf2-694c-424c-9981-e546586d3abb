/*
 * @Description:
 * @version:
 * @Company: 海闻软件
 * @Author: 马赛
 * @Date: 2019-01-14 15:13:41
 * @LastEditors: 郑启旭
 * @LastEditTime: 2019-01-23 16:37:34
 */
import request from '@/utils/request';

/**
 *删除报销单
 *
 * @export
 * @param {*} claimId 报销单id
 * @returns
 */
// export function deleteClaim(claimId) {
//   return request.delete(`/rest/proxy/account/ClaimAccount/v1.0/delete/${claimId}`);
// };

/**
 *获取记录(单条)
 *
 * @export
 * @param {*} claimId 报销id
 * @returns
 */
export function getClaim(claimId) {
  return request.get(`/rest/proxy/account/ClaimAccount/v1.0/get/${claimId}`);
}

/**
* 新增报销单
*/
// export function postClaim(params) {
//   return request.post('/rest/proxy/account/ClaimAccount/v1.0/insert', params);
// };

/**
 *报销费用列表
 *
 * @export
 * @param {*} approvalStatusArray 费用状态： 全部 1 未提交 2审批中 3 审批通过 4  审批拒绝
 * @returns
 */
export function getClaimList(params) {
  return request.get('/rest/proxy/account/ClaimAccount/v1.0/list', { params });
}

/**
* 修改报销单
*/
export function putClaim(params) {
  return request.put(`/rest/proxy/account/ClaimAccount/v1.0/update/${params.claimId}`, params);
}

/**
* 报销单付款
*/
export function updatePay(params) {
  return request.put(`/rest/proxy/account/ClaimAccount/v1.0/updatePay/${params.claimId}`, params);
}

/**
* 修改报销单报销内容
*/
export function updateContent(params) {
  return request.put('/rest/proxy/account/ClaimAccount/v1.0/updateContent', params);
}
