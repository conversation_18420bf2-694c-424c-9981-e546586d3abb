<!--
 * @LastEditors: 启旭
 * @Author: 启旭
 * @Description: 科目标签搜索组件
 * @Date: 2019-11-14 16:00:49
 * @LastEditTime: 2019-12-02 17:24:09
 -->

<template>
  <div class="tree-select-wrapper">
    <div class="header clearfix">
      <div class="treebox">
        <div class="content clearfix">
          <div class="tree_box">
            <div class="tree_box_header">
              <slot name="filter">
                <el-input
                  v-model.trim="inputFilterText"
                  placeholder="输入名称后点击图标或按下Enter键"
                  clearable
                  @keydown.enter.native.stop="filterByClient"
                  @clear="filterByClient"
                >
                  <i
                    slot="suffix"
                    class="el-input__icon el-icon-search"
                    @click="filterByClient"
                    style='cursor:pointer'
                  ></i></el-input>
              </slot>
            </div>
            <VTree
              ref="tree"
              class="tree"
              v-bind="$attrs"
              :data="data"
              checkable="multiple"
              :parentIdMethod="parentIdMethod"
              :id-field="idField"
              :isRoot="isRoot"
              :label-field="labelField"
              :check-strictly="checkStrictly"
              checkOnClickNode
              :defaultFoldAll="defaultFoldAll"
              @check="handleCheckChange($event, true)"
              @uncheck="handleCheckChange($event, false)"
              :height="height"
              :filterMethod="defaultFilterMethod"
              :disableCheckMethod="disableCheckMethod"
            ></VTree>
          </div>
          <div class="tree_box">
            <div class="clearfix tree_box_header">
              <div
                class="fl"
                style="font-size: 14px;line-height:30px;"
              >
                <strong>已选择</strong>
                <span style="color:#66ccff;padding-left:4px;">{{selectedNodes.length}}/{{maxLength}}</span>
              </div>
              <div class="fr">
                <el-button
                  @click="clearSelection"
                  type="text"
                >
                  <i
                    class="fa fa-trash-o"
                    aria-hidden="true"
                  ></i>
                  清空已选择
                </el-button>
              </div>
            </div>
            <ul class="chosen_node_list">
              <template v-if="$refs.tree">
                <li
                  v-for="node in selectedNodes"
                  class="chosen_node"
                  :key="node.subjectId"
                >
                  <span :title="getFullName(node)">{{ getFullName(node) }}</span>
                  <i
                    class="el-icon-close"
                    @click="handleCancelSelectedNode(node)"
                  ></i>
                </li>
              </template>
            </ul>
          </div>
        </div>
        <div>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
import { VTree } from '@hw/virtual-list';
import '@hw/virtual-list/dist/virtual-list.css';
import { isEqual } from 'lodash-es';

export default {
  components: { VTree },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Array,
      default: () => [],
    },
    defaultExpandedKeys: {
      type: Array,
      default: () => [],
    },
    checkStrictly: {
      type: Boolean,
      default: true,
    },
    defaultFoldAll: {
      type: Boolean,
      default: true,
    },
    disableCheckMethod: {
      type: Function,
    },
    // 过滤方法 filterMethod(nodeData, filterText) => boolean
    filterMethod: {
      type: Function,
    },
    maxLength: {
      type: Number,
      default: 5,
    },
    idField: {
      type: String,
    },
    isRoot: {
      type: Function,
    },
    parentIdMethod: {
      type: Function,
    },
    labelField: {
      type: String,
    },
    beforeCheck: {
      type: Function,
    },
    afterCheck: {
      type: Function,
    },
    fullNameMethod: {
      type: Function,
    },
    // 树的高度
    height: {
      type: Number,
      default: 350,
    },
  },
  model: {
    value: 'value',
    event: 'change',
  },
  data() {
    return {
      inputFilterText: '', // 输入中的过滤查询字段
      filterText: '', // 过滤树查询字段
      visible: false, // 弹窗展示状态
      selectedNodes: [],
      currentTypeId: '',
      treeEmptyStr: '无数据',
    };
  },

  computed: {
    selectedNodesLength() {
      return this.selectedNodes.length;
    },
  },

  watch: {
    value: {
      immediate: true,
      handler: function handleValueChange(val, oldVal) {
        if (!val && !oldVal) return;
        this.$nextTick(() => {
          if (!isEqual(val, oldVal)) {
            this.setDefaultValue(val);
          }
        });
      },
    },
    defaultExpandedKeys: {
      immediate: true,
      handler: function handleValueChange(val) {
        if (val.length === 0 && this.$refs.tree) {
          val.forEach((item) => {
            this.$refs.tree.expand(item);
          });
        }
      },
    },
    // 当defaultFoldAll 发生变化时， 界面会错乱，所以加定时器重置他
    defaultFoldAll() {
      setTimeout(() => {
        this.filterByClient();
      }, 50);
    },
  },

  mounted() {
  },

  methods: {
    defaultFilterMethod(data) {
      if (typeof this.filterMethod === 'function') return this.filterMethod(data, this.filterText);
      if (!this.filterText) { return true; }
      return data[this.labelField].indexOf(this.filterText) !== -1;
    },
    // 获取树已勾选项
    getCheckedKeys() {
      const value = this.$refs.tree.checkedKeys();
      return value;
    },
    // 设置树勾选项
    setCheckedKeys(cKeys = []) {
      this.$refs.tree.batchCheck(cKeys);
    },
    setUnCheckedKeys(cKeys = []) {
      this.$refs.tree.batchUncheck(cKeys);
    },
    // 根据搜索值，过滤标签显示项
    filterByClient() {
      this.$refs.tree.reset();
      console.log(this.defaultFoldAll, 'this.defaultFoldAll');
      if (this.inputFilterText || !this.defaultFoldAll) {
        this.$refs.tree.expandAll();
      } else {
        this.$refs.tree.foldAll();
      }

      this.$nextTick(() => {
        this.filterText = this.inputFilterText;
      });
    },

    // 点击树的项时，可以直接勾选上
    handleNodeClick(nodeKey) {
      if (this.value.includes(nodeKey)) {
        this.$refs.tree.uncheck(nodeKey);
      } else {
        this.$refs.tree.check(nodeKey);
      }
    },

    handleCheckClick() {
      this.visible = false;
      this.$emit('search', this.getCheckedKeys());
    },
    // 限制最多勾选项200条
    handleCheckChange([checkedKey], isCheck) {
      console.log(checkedKey, isCheck);
      if (isCheck) {
        // 勾选情况下需要校验当前勾选是否能够生效， 不能生效的话需要清除本次勾选
        if (!this.validate(checkedKey)) {
          this.$refs.tree.uncheck(checkedKey);
          return;
        }
      }
      this.selectedNodes = this.$refs.tree.checkedItems();
      this.$emit('change', this.getCheckedKeys());
      if (typeof this.afterCheck === 'function') {
        this.afterCheck(checkedKey);
      }
    },
    validate(checkedKey) {
      if (typeof this.beforeCheck === 'function') {
        if (!this.beforeCheck(checkedKey)) return false;
      }
      return this.validLength();
    },
    validLength() {
      if (this.selectedNodesLength > this.maxLength - 1) {
        this.$message.error(`选择数量超过${this.maxLength}条，无法继续勾选`);
        return false;
      }
      return true;
    },
    handleCancelSelectedNode(node) {
      this.$refs.tree.uncheck(node[this.idField]);
    },
    clearSelection() {
      this.selectedNodes = [];
      this.$refs.tree.uncheckAll({ filtered: true });
      this.$emit('clear');
    },
    getFullName(data) {
      const dataPath = this.$refs.tree.ancestor(data[this.idField]).reverse();
      dataPath.push(data);
      if (typeof this.fullNameMethod === 'function') {
        return this.fullNameMethod(dataPath);
      }

      const pathNames = dataPath.map((item) => item[this.labelField]);
      return `${pathNames.join('-')}`;
    },
    setDefaultValue(value) {
      this.$nextTick(() => {
        this.$refs.tree.uncheckAll({ filtered: true });
        this.selectedNodes = [];
        this.setCheckedKeys(value);
      });
    },
    reset() {
      this.inputFilterText = '';
      this.filterByClient();
    },
  },
};
</script>

<style lang="scss"   scoped>
.tagul {
  padding-right: 10px;
  height:360px;
  overflow-y:auto;
  li {
    height: 18px;
    line-height: 18px;
    padding: 2px 8px;
    cursor: pointer;
    margin-top: 6px;
    &.disableli {
      cursor: not-allowed;
      color: #ddd;
    }
    &.currentTag {
      color: #fff;
      background: $color-theme-base;
      border-radius: 2px;
    }
  }
}
.emptytagbtn {
  color: #aaa;
}

::-webkit-scrollbar-track-piece {
  background-color: white;
}
.tree {
  max-width: 400px;
}
</style>

<style>
.el_popover_area {
  display: inline-block;
  vertical-align: top;
  line-height: 1;
}
.tagContent-btn{
  padding: 0 !important;
}

.tree-select-wrapper .header {
  margin-bottom: 10px;
}
.tagType{
  line-height: 30px;
}
.tree-select-wrapper .content {
  height: 410px;
}
.tree-select-wrapper .content > .tree_box {
  float: left;
  width: 270px;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;
}

.tree-select-wrapper .content > .first_tree_box {
  min-width: 80px;
}

.tree-select-wrapper .content > .tree_box:first-child {
  border-right: 1px solid rgb(209, 219, 229);
}
.tree-select-wrapper .content > .tree_box > .tree_box_header {
  height: 30px;
  border-bottom: 1px solid rgb(209, 219, 229);
  padding: 5px;
  margin-bottom: 5px;
}
.tree-select-wrapper .content > .tree_box > .tree {
  height: 360px;
  box-sizing: border-box;
  overflow: auto;
  border: none;
}
.tree-select-wrapper
  .content
  > .tree_box
  > .tree
  .el-tree-node
  > .el-tree-node__children {
  overflow: inherit;
}
.tree-select-wrapper .content > .tree_box > .chosen_node_list {
  height: 360px;
  background: #f0f0f0;
  box-sizing: border-box;
  overflow: auto;
}

.tree-select-wrapper .content .chosen_node_list > .chosen_node {
  position: relative;
  padding-left: 15px;
  padding-right: 30px;
  line-height: 28px;
  background: white;
  border-top: 1px solid rgb(209, 219, 229);
  border-right: 1px solid rgb(209, 219, 229);
  border-left: 1px solid rgb(209, 219, 229);
  text-overflow: ellipsis;
  max-width: 300px;
  margin-left: 3px;
  overflow: hidden;
}

.tree-select-wrapper .content .chosen_node_list > .chosen_node:last-child {
  border-bottom: 1px solid rgb(209, 219, 229);
}

.tree-select-wrapper .content .chosen_node_list > .chosen_node > span {
  cursor: pointer;
}

.tree-select-wrapper .content .chosen_node_list > .chosen_node > i {
  position: absolute;
  top: 50%;
  right: 15px;
  cursor: pointer;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
}
.el-badge.tree_badge {
  margin-right: 8px;
  /* width: 80px; */
}
.el-badge.tree_badge button {
  /* width: 80px; */
  overflow: hidden;
}
.el-badge.tree_badge .el-badge__content.is-fixed {
  top: 0;
  right: 0;
  position: absolute;
  transform: translateY(-36%) translateX(40%);
}
</style>
