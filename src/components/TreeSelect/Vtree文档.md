# VTree 组件使用文档

## 介绍

`VTree` 组件是基于 `VList` 实现的高性能树形组件，主要用于展示大量节点的树形结构内容，如组织机构等。该组件使用虚拟滚动技术，只渲染可视范围内的数据，从而避免性能问题。

## 使用示例

```vue
<template>
  <VTree
    :data="treeData"
    :width="400"
    :height="600"
    :checkable="'multiple'"
    :expandOnClickNode="true"
    :defaultFoldAll="false"
    @expand="handleExpand"
    @fold="handleFold"
    @check="handleCheck"
    @uncheck="handleUncheck"
  />
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import VTree from '@/components/VTree/index.vue';

export default defineComponent({
  components: { VTree },
  data() {
    return {
      treeData: [
        { id: '1', pid: null, label: 'Node 1' },
        { id: '2', pid: '1', label: 'Node 1-1' },
        { id: '3', pid: '1', label: 'Node 1-2' },
        // 更多数据...
      ],
    };
  },
  methods: {
    handleExpand(nodeKey) {
      console.log('Node expanded:', nodeKey);
    },
    handleFold(nodeKey) {
      console.log('Node folded:', nodeKey);
    },
    handleCheck(checkedKeys) {
      console.log('Nodes checked:', checkedKeys);
    },
    handleUncheck(uncheckedKeys) {
      console.log('Nodes unchecked:', uncheckedKeys);
    },
  },
});
</script>
```

## Props

### 基本属性

- `width` (Number, 必需): 组件宽度，默认是容器的 100%。
- `height` (Number, 可选): 组件高度，默认是容器的 100%。
- `indentUnit` (Number, 默认: 16): 每层级的缩进基数（像素），建议 24 或 16。
- `defaultFoldAll` (Boolean, 默认: false): 是否默认折叠所有树节点。
- `checkable` (String, 默认: 'none'): 选择区类型，'multiple' 为多选，'single' 为单选，'none' 为不可选。
- `expandOnClickNode` (Boolean, 默认: false): 是否点击节点时，切换展开、折叠状态。
- `checkOnClickNode` (Boolean, 默认: false): 是否点击节点时，切换选中状态。
- `checkStrictly` (Boolean, 默认: true): 父子节点是否使用严格的不关联模式。
- `data` (Array, 默认: []): 数据列表。
- `levelFilter` (Number, 默认: 0): 层级过滤器，0 代表不过滤，其他数值代表需要显示到哪一级的数据。
- `filterMethod` (Function, 可选): 数据过滤方法。
- `sortMethod` (Function, 可选): 数据排序方法。
- `highlightText` (String | Array, 可选): 高亮文本。
- `disableCheckMethod` (Function, 默认: () => false): 数据禁止勾选的检查方法。
- `disableToggleMethod` (Function, 默认: () => false): 数据禁止切换折叠的检查方法（仅作用于鼠标点击时）。
- `disableActiveMethod` (Function, 默认: () => false): 数据禁止切换激活的检查方法（仅作用于鼠标点击时）。
- `labelField` (String, 可选): 获取节点文本的字段。
- `labelMethod` (Function, 可选): 获取节点文本的方法，优先级比 `labelField` 高。
- `labelClassMethod` (Function, 默认: () => ''): 节点的自定义 class 方法。
- `idField` (String, 默认: 'id'): 转换树形数据 id 的字段。
- `idMethod` (Function, 可选): 转换树形数据提取 id 的方法，优先级比 `idField` 高。
- `parentIdField` (String, 默认: 'pid'): 转换树形数据的父节点 id 字段。
- `parentIdMethod` (Function, 可选): 转换树形数据提取父节点 id 的方法。
- `hasChildMethod` (Function, 可选): 判断节点是否有子节点的方法。
- `isRoot` (Function, 可选): 判断节点是否为根节点的方法。

## 事件

- `expand(nodeKey: string)`: 节点展开时触发，返回节点的 key。
- `fold(nodeKey: string)`: 节点折叠时触发，返回节点的 key。
- `check(checkedKeys: string[])`: 节点选中时触发，返回选中节点的 key 数组。
- `uncheck(uncheckedKeys: string[])`: 节点取消选中时触发，返回取消选中节点的 key 数组。
- `click(nodeKey: string, event: Event)`: 节点点击时触发，返回节点的 key 和事件对象。
- `data-bound()`: 数据首次绑定时触发。

## 方法

- `toggle(treeNodeKey: string)`: 切换节点的展开、折叠状态。
- `expand(treeNodeKey: string)`: 展开指定节点。
- `fold(treeNodeKey: string)`: 折叠指定节点。
- `check(treeNodeKey: string)`: 选中指定节点。
- `uncheck(treeNodeKey: string)`: 取消选中指定节点。
- `active(treeNodeKey: string)`: 激活指定节点。
- `inactive(treeNodeKey: string)`: 取消激活指定节点。
- `foldAll()`: 折叠所有节点。
- `expandAll()`: 展开所有节点。
- `getCheckedKeys()`: 获取所有选中的节点 key 数组。

## 插槽

### 默认插槽

用于自定义节点内容，提供以下作用域插槽参数：

- `vlistItemData`: 当前渲染节点的数据。
- `vlistItemKey`: 当前渲染节点的 key。

```vue
<template v-slot:default="{ vlistItemData, vlistItemKey }">
  <!-- 自定义节点内容 -->
</template>
```

## 结语

`VTree` 组件提供了高性能的树形结构展示能力，结合虚拟滚动技术，适合用于展示大量节点的场景。通过丰富的属性和事件，可以灵活地定制树形组件的行为和样式。