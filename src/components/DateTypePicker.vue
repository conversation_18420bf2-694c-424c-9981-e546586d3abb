<template>
  <div class="date-type-picker">
    <el-select v-if="showTypeSelector" v-model="currentType" :clearable="clearable" placeholder="请选择类型"
      @change="handleTypeChange" style="width: 100px;" class="date-type-picker__type-select">
      <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
    </el-select>

    <template v-if="currentType === 'year'">
      <el-date-picker v-model="pickerValue" type="year" placeholder="选择年度" :clearable="clearable" value-format="yyyy"
        :picker-options="yearPickerOptions" @change="handleYearChange"
        class="date-type-picker__year-picker"></el-date-picker>
    </template>

    <template v-if="currentType === 'month'">
      <el-date-picker v-model="pickerValue" type="month" :clearable="clearable" placeholder="选择月份" value-format="yyyyMM"
        :picker-options="monthPickerOptions" @change="handleMonthChange"
        class="date-type-picker__month-picker"></el-date-picker>
    </template>

    <template v-if="currentType === 'quarter'">
      <el-date-picker v-model="quarterYear" type="year" :clearable="clearable" placeholder="选择年份" value-format="yyyy"
        style="width: 100px;" :picker-options="quarterYearPickerOptions" @change="handleQuarterYearChange"
        class="date-type-picker__quarter-year-picker"></el-date-picker>
      <el-select v-model="selectedQuarterValue" placeholder="选择季度" style="width: 100px; margin-left: 0px;"
        @change="handleQuarterSelectChange" :disabled="!quarterYear" class="date-type-picker__quarter-select"
        :clearable="clearable" @clear="handleQuarterClear">
        <el-option v-for="q in quarterOptions" :key="q.value" :label="q.label" :value="q.value"
          :disabled="isQuarterOptionDisabled(q.value)"></el-option>
      </el-select>
    </template>
  </div>
</template>

<script>
export default {
  name: 'DateTypePicker',
  props: {
    value: {
      type: String,
      default: null, // 使用 null 表示显式空状态
    },
    allowedTypes: {
      type: Array,
      default: () => ['year', 'month', 'quarter'],
      validator: function (value) {
        if (!Array.isArray(value) || value.length === 0) return false; // Must have at least one type
        return value.every(type => ['year', 'month', 'quarter'].includes(type));
      },
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    disabledDate: {
      type: Function,
      default: () => false,
    },
  },
  data() {
    return {
      currentType: '', // 将在初始化时设置
      pickerValue: null, // 存储年份类型(YYYY)或月份类型(YYYYMM)的值
      quarterYear: null, // 存储季度类型的年份部分(YYYY)
      selectedQuarterValue: null, // 存储季度值(1,2,3,4)
      quarterOptions: [
        { label: '第一季度', value: 1 },
        { label: '第二季度', value: 2 },
        { label: '第三季度', value: 3 },
        { label: '第四季度', value: 4 },
      ],
      _internalEmittedValue: null, // 用于防止值观察器中的反馈循环
    };
  },
  computed: {
    showTypeSelector() {
      return this.allowedTypes && this.allowedTypes.length > 1;
    },
    typeOptions() {
      const typeMap = {
        year: '年度',
        month: '月度',
        quarter: '季度',
      };
      return this.allowedTypes.map(type => ({
        value: type,
        label: typeMap[type] || type,
      }));
    },
    yearPickerOptions() {
      return {
        disabledDate: time => { // time is a Date object
          return this.disabledDate(time);
        },
      };
    },
    monthPickerOptions() {
      return {
        disabledDate: time => { // time is a Date object
          return this.disabledDate(time);
        },
      };
    },
    quarterYearPickerOptions() {
      return {
        disabledDate: time => { // time 是该年1月1日的 Date 对象
          const year = time.getFullYear();
          let yearDisabledByDateObject = false;
          try {
            yearDisabledByDateObject = this.disabledDate(new Date(time.getTime()));
          } catch (e) {
            // 如果 disabledDate 不处理 Date 对象或抛出错误，则忽略
          }

          let allQuartersInYearDisabled = true;
          // 这个检查适用于当年份的所有季度都被禁用时，应该禁用整个年份的情况。
          // 但是，如果 disabledDate 也可以直接禁用年份（通过 Date 对象），则优先使用该方式。
          for (let i = 1; i <= 4; i++) {
            if (!this.disabledDate(`${year}Q${i}`)) {
              allQuartersInYearDisabled = false;
              break;
            }
          }
          return yearDisabledByDateObject || allQuartersInYearDisabled;
        },
      };
    },
    isSingleTypeMode() {
      return this.allowedTypes && this.allowedTypes.length === 1;
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (newVal === this._internalEmittedValue) {
          this._internalEmittedValue = null; // Reset after check
          return;
        }
        this.updateInternalStateFromValue(newVal);
      },
    },
    allowedTypes: {
      immediate: true,
      deep: true,
      handler() {
        this.initializeType();
        this.updateInternalStateFromValue(this.value); // Re-evaluate value with new types
      },
    },
  },
  created() {
    // 初始化由 `allowedTypes` 和 `value` 的立即执行的观察器处理。
    // `allowedTypes` 观察器会调用 `initializeType()`。
    // `value` 观察器会调用 `updateInternalStateFromValue()`。
    // 执行顺序：allowedTypes 先运行，设置 currentType，然后运行 value 观察器。
  },
  methods: {
    emitInput(val) {
      this._internalEmittedValue = val; // 标记即将发生的值变更是内部的
      this.$emit('input', val);
      this.$emit('change', val);
    },

    getTypeFromValue(val) {
      if (!val) return null;
      if (/^\d{4}$/.test(val)) return 'year';
      if (/^\d{6}$/.test(val)) return 'month';
      if (/^\d{4}Q[1-4]$/.test(val)) return 'quarter';
      return null;
    },

    initializeType() {
      let newCurrentType = '';
      const typeFromCurrentValue = this.getTypeFromValue(this.value);

      if (this.isSingleTypeMode) {
        newCurrentType = this.allowedTypes[0];
      } else if (this.allowedTypes && this.allowedTypes.length > 0) {
        if (typeFromCurrentValue && this.allowedTypes.includes(typeFromCurrentValue)) {
          newCurrentType = typeFromCurrentValue;
        } else if (this.currentType && this.allowedTypes.includes(this.currentType)) {
          newCurrentType = this.currentType; // Preserve current type if still allowed
        } else {
          newCurrentType = this.allowedTypes[0]; // Default to first allowed type
        }
      }

      if (this.currentType !== newCurrentType) {
        this.currentType = newCurrentType;
        // If type changes, the existing value might be incompatible.
        // updateInternalStateFromValue will handle this by trying to parse or clearing.
      }
    },

    updateInternalStateFromValue(val) {
      const typeFromValue = this.getTypeFromValue(val);

      // 确保 currentType 有效后再继续
      if (!this.currentType && this.allowedTypes && this.allowedTypes.length > 0) {
        this.currentType = this.allowedTypes[0]; // Default if not set
      } else if (this.currentType && this.allowedTypes && !this.allowedTypes.includes(this.currentType)) {
        // 当前类型不再允许，切换到默认值并清除值
        this.currentType = this.allowedTypes[0];
        this.clearAllPickers();
        if (val !== null) this.emitInput(null);
        return;
      }


      if (val === null || val === '') {
        this.clearAllPickers();
        return;
      }

      if (this.isSingleTypeMode) {
        if (typeFromValue === this.currentType) {
          this.setPickersByType(val, this.currentType);
        } else { // 值的类型与唯一允许的类型不匹配
          this.clearAllPickers();
          this.emitInput(null); // 值对于此配置无效
        }
      } else { // 多种类型允许
        if (typeFromValue && this.allowedTypes.includes(typeFromValue)) {
          if (this.currentType !== typeFromValue) {
            this.currentType = typeFromValue; // 同步 currentType 与值的类型
          }
          this.setPickersByType(val, typeFromValue);
        } else { // 值格式错误或其类型当前不被允许
          // 不改变 currentType，但清除当前类型的值选择器，因为对该类型无效
          // 如果该值在所有允许的类型中都不可用，可以选择发出 null
          // 目前，只需清除当前视图，期望用户/属性提供有效的类型值。
        }
      }
    },

    setPickersByType(val, type) {
      this.clearAllPickers(); // 重新开始，清除所有选择器
      if (type === 'year') {
        this.pickerValue = val;
      } else if (type === 'month') {
        this.pickerValue = val;
      } else if (type === 'quarter') {
        this.quarterYear = val.substring(0, 4);
        this.selectedQuarterValue = parseInt(val.substring(5, 6), 10);
      }
    },

    clearAllPickers() {
      this.pickerValue = null;
      this.quarterYear = null;
      this.selectedQuarterValue = null;
    },

    clearPickersForCurrentType() {
      if (this.currentType === 'year' || this.currentType === 'month') {
        this.pickerValue = null;
      } else if (this.currentType === 'quarter') {
        this.quarterYear = null;
        this.selectedQuarterValue = null;
      }
    },

    convertValueBetweenTypes(value, fromType, toType) {
      if (!value || fromType === toType) return value;

      try {
        if (toType === 'year') {
          // 切换为年度时，值改为当前值的年度
          if (fromType === 'month') {
            // 从月度(YYYYMM)提取年份
            return value.substring(0, 4);
          }
          if (fromType === 'quarter') {
            // 从季度(YYYYQ1)提取年份
            return value.substring(0, 4);
          }
        }
        if (toType === 'quarter') {
          // 切换为季度时
          if (fromType === 'month') {
            // 月度改季度就取当前季度值
            const year = value.substring(0, 4);
            const month = parseInt(value.substring(4, 6), 10);
            const quarter = Math.ceil(month / 3);
            return `${year}Q${quarter}`;
          }
          if (fromType === 'year') {
            // 年度改季度就取当年的第一季度
            return `${value}Q1`;
          }
        }
        if (toType === 'month') {
          // 切换为月度时
          if (fromType === 'year') {
            // 年度改月度就取当年第一个月
            return `${value}01`;
          }
          if (fromType === 'quarter') {
            // 季度改月度就取当前季度的第一个月
            const year = value.substring(0, 4);
            const quarter = parseInt(value.substring(5, 6), 10);
            const firstMonthOfQuarter = (quarter - 1) * 3 + 1;
            const monthStr = firstMonthOfQuarter.toString().padStart(2, '0');
            return `${year}${monthStr}`;
          }
        }
      } catch (error) {
        console.warn('日期类型转换失败:', error);
        return null;
      }

      return null;
    },

    handleTypeChange(newType) {
      // currentType 已经通过 el-select 的 v-model 更新
      const oldValue = this.value;
      const oldType = this.getTypeFromValue(oldValue);

      // 清除所有选择器
      this.clearAllPickers();

      // 如果有旧值，尝试智能转换
      if (oldValue && oldType) {
        const convertedValue = this.convertValueBetweenTypes(oldValue, oldType, newType);
        if (convertedValue) {
          this.setPickersByType(convertedValue, newType);
          this.emitInput(convertedValue);
          return;
        }
      }

      // 如果没有旧值或转换失败，清除值
      this.emitInput(null);
    },

    handleYearChange(val) { // val 是 YYYY 或 null
      this.pickerValue = val;
      this.emitInput(val);
    },

    handleMonthChange(val) { // val 是 YYYYMM 或 null
      this.pickerValue = val;
      this.emitInput(val);
    },

    handleQuarterYearChange(yearVal) { // yearVal 是 YYYY 或 null
      this.quarterYear = yearVal;
      if (yearVal && this.selectedQuarterValue) {
        this.emitInput(`${yearVal}Q${this.selectedQuarterValue}`);
      } else if (!yearVal) {
        // 如果年份被清除，或者季度未选择，则季度值不完整/无效。
        this.selectedQuarterValue = null; // 如果年份被清除，则清除季度
      }
    },

    handleQuarterSelectChange(quarterVal) { // quarterVal 是 1,2,3,4 或 null（如果可清除）
      this.selectedQuarterValue = quarterVal;
      if (this.quarterYear && quarterVal) {
        this.emitInput(`${this.quarterYear}Q${quarterVal}`);
      } else if (this.quarterYear && !quarterVal) { // 清除了季度，但存在年份
        this.emitInput(null); // 不完整的季度
      }
      // 如果 quarterYear 为 null，handleQuarterYearChange 应该已经发出了 null。
    },

    handleQuarterClear() {
      this.selectedQuarterValue = null;
      if (this.quarterYear) { // 只有在设置了年份且当前清除了季度时才发出
        this.emitInput(null);
      }
    },

    isQuarterOptionDisabled(qValue) { // qValue 是 1, 2, 3, 4
      if (!this.quarterYear) return true; // 必须先选择年份
      return this.disabledDate(`${this.quarterYear}Q${qValue}`);
    },
  },
};
</script>

<style scoped>
.date-type-picker {
  display: flex;
  align-items: center;
}

/* 添加类以便通过全局样式或深度选择器更轻松地定位元素 */
.date-type-picker__type-select,
.date-type-picker__year-picker,
.date-type-picker__month-picker,
.date-type-picker__quarter-year-picker,
.date-type-picker__quarter-select {
  /* 添加通用样式（如果有）或用于特定调整 */
}
</style>