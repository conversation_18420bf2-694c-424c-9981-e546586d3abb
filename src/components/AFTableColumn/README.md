### 简介
基于 `element-ui` 组件库的 `el-table-column` 组件, 支持自适应列宽功能

- 默认用法, 全部自适应列宽
```
// list.vue
<template>
  <el-table :data="data">
    
    <af-table-column label="列1" prop="field1"></af-table-column>
    <af-table-column label="列2" prop="field2"></af-table-column>
    
    <!--也支持简单的自定义内容-->
    <af-table-column label="列3">
      <template slot-scope="scope">
        <div>自定义显示值31: {{ scope.row.field31 }}</div>
        <div>自定义显示值32: {{ scope.row.field32 }}</div>
      </template>
    </af-table-column>
    <af-table-column label="操作">
      <template slot-scope="scope">
        <el-button @click="removeItem">删除</el-button>
      </template>
    </af-table-column>
    
  </el-table>
</template>
```

- 部分自适应列宽:
```
// list.vue
// 实现仅有 列2 自适应
<template>
  <!--在 table 上设置 autoFit 属性为 false-->
  <el-table :data="data" :autoFit="false">
    <af-table-column label="列1" prop="field1"></af-table-column>
    
    <!--在 column 上设置 fit 属性为 true-->
    <af-table-column label="列2" prop="field2" fit></af-table-column>
  </el-table>
</template>

<!--或者其他列使用 ElementUI 原有的 el-table-column-->
<template>
  <el-table :data="data">
    <el-table-column label="列1" prop="field1"></el-table-column>
    <af-table-column label="列2" prop="field2"></af-table-column>
  </el-table>
</template>
```

### Attributes
| 参数      | 说明    | 类型      | 可选值       | 默认值   |
|---------- |-------- |---------- |-------------  |-------- |
| computedForDom     | 计算方式采用cell渲染后的dom的实际宽度，一般用于自定义列内容的情况，性能差   | boolean  |   true/false            |    false    |