<template>
<div>
      <div
      class="helpcenterwindow"
      v-show="meHelpCenterWindowVisable"
      v-draggable="draggableValue"
    >
      <div class="title" ref="draggableHandler">
        <div class="text">帮助中心</div>
        <div class="popover">
          <el-popover
            popper-class="helpcenter-popper"
            v-model="popoverval"
            ref="popover"
            placement="right-start"
            width="168"
            trigger="click"
          >
            <ul
              v-for="item in helpCenterModule"
              :key="item.id"
            >
              <li @click="changeTitle(item.title,item.id)">{{item.title}}</li>
            </ul>
          </el-popover>
          <el-button
            v-popover:popover
            class="select_menu"
            type="text"
          >
            <span class="select_subject_title">{{subject_title}}</span>
            <span class="fa fa-angle-down"></span>
          </el-button>
        </div>
        <div style="flex: 1"></div>
        <div class="help-links">
          <a target="_blank" href="https://evpt68nxb9n.feishu.cn/wiki/ICl4wZPrXirvZakYlGlc9V0EnMc?from=from_copylink" class="help-link">
            <i class="fa fa-book"></i>
            <span>操作文档</span>
          </a>
          <a  target="_blank" href="https://evpt68nxb9n.feishu.cn/wiki/IIzRwLmIticNblkfUwUcMcUZnOe?from=from_copylink" class="help-link">
            <i class="fa fa-history"></i>
            <span>更新日志</span>
          </a>
        </div>
        <div class="closebtn">
          <el-button
            type="text"
            @click="meHelpCenterWindowVisable=false"
          >
            <i class="el-icon-close"></i>
          </el-button>
        </div>
      </div>
      <div class="searchbar">
        <el-input
          placeholder="请输入内容"
          clearable
          v-model="searchFullText"
          @keyup.enter.native="getHelpCenterArticel()"
        >
          <el-button
            slot="append"
            icon="el-icon-search"
            :disable="searchFullText==''"
            @click="getHelpCenterArticel()"
          ></el-button>
        </el-input>
      </div>
      <div class="listcontent">
        <div
          v-for="(item,i) in helpContentArr"
          :key="i"
          class="list"
        >
          <div class="question">{{item.question}}</div>
          <div class="answer">{{item.answer}}</div>
        </div>
      </div>
    </div>
</div>
</template>
<script>
import { Draggable } from 'draggable-vue-directive';

export default {
  directives: { Draggable },
  props: {
    helpCenterWindowVisible: {
      required: true,
      type: Boolean,
    },
  },
  data() {
    return {
      helpCenterModule: [
        {
          title: '全部',
          id: '0',
        },
        {
          path: 'outputTax',
          title: '销项',
          id: '1',
        },
        {
          path: 'inputTax',
          title: '进项',
          id: '2',
        },
        {
          path: 'salary',
          title: '工资',
          id: '3',
        },
        {
          path: 'trade',
          title: '资金',
          id: '4',
        },
        {
          path: 'documentManagement',
          title: '单据',
          id: '5',
        },
        {
          path: 'createVoucher',
          title: '凭证',
          id: '6',
        },
        {
          path: 'endCheckout',
          title: '期末结转',
          id: '7',
        },
        {
          path: 'simpleCostAccount',
          title: '成本核算',
          id: '8',
        },
        {
          path: 'voucherDetails',
          title: '凭证明细表',
          id: '9',
        },
        {
          path: '/assets',
          title: '资产',
          id: '10',
        },
        {
          path: 'commodityWater',
          title: '仓账',
          id: '11',
        },
        {
          path: 'valueAddedTax',
          title: '增值税申报',
          id: '12',
        },
        {
          path: 'businessIncomeTax',
          title: '所得税申报',
          id: '13',
        },
        {
          path: '/options',
          title: '设置',
          id: '14',
        },
        {
          title: '费用',
          id: '15',
        },
      ],
      draggableValue: {
        handle: undefined,
      },
      popoverval: false,
      subject_title: '全部',
      searchFullText: '',
      helpContentArr: [],
    };
  },
  computed: {
    meHelpCenterWindowVisable: {
      get() {
        return this.helpCenterWindowVisible || false;
      },
      set(Val) {
        this.$emit('update:helpCenterWindowVisible', Val);
      },
    },
  },
  watch: {
    meHelpCenterWindowVisable(val) {
      let pathid = '0';
      let pathname = '全部';
      const that = this;
      if (val) {
        this.helpCenterModule.some((i) => {
          if (that.$route.path.indexOf(i.path) !== -1) {
            pathid = i.id;
            pathname = i.title;
            return true;
          }
          return false;
        });
        this.getHelpCenterArticel(pathid);
        this.subject_title = pathname;
      }
    },
  },
  mounted() {
    this.draggableValue.handle = this.$refs.draggableHandler;
  },
  methods: {
    async getHelpCenterArticel(val) {
      const params = {};
      params.searchFullText = this.searchFullText;
      params.pageSize = 99999;
      if (val !== '0' && val !== undefined) params.moduleId = val;
      const res = await this.$http.get(
        '/rest/global/dimensionality/helpCentre/list',
        { params },
      );
      this.helpContentArr = res.data.data;
      this.helpContentArr.forEach((i) => {
        i.question = `Q:${i.question}`;
        i.answer = `A:${i.answer}`;
      });
    },
    changeTitle(title, id) {
      this.subject_title = title;
      this.popoverval = false;
      this.getHelpCenterArticel(id);
    },
  },

};
</script>
<style lang="scss" scoped>
  .lastLi {
    cursor: pointer;
  }
  .lastLi:hover {
    color: #333;
    background-color: #ddd;
  }
  .lastLi .fa-angle-down {
    margin-right: 18px;
  }
.helpcenter-popper {
  width: 80px;
  li {
    height: 22px;
    line-height: 22px;
    padding: 0px 5px;
    cursor: pointer;
    &:hover {
      background: #ddd;
    }
  }
}
.helpcenterwindow {
  position: absolute !important;
  width: 440px;
  height: 560px;
  bottom: 0px;
  right: 0px;
  background: #f9f9f9;
  z-index: 200;
  border: 1px solid #ddd;
  border-top: none;
  .title {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 40px;
    background-color: var(--color-theme-base);
    color: #fff;
    padding: 0 10px;
    .select_menu{
      color: #fff;
    }
    .text {
      font-size: 14px;
      font-weight: bold;
    }

    .popover {
      margin-left: 10px;
    }

    .help-links {
      display: flex;
      gap: 16px;
      margin-right: 16px;

      .help-link {
        color: #fff;
        cursor: pointer;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 4px;
        text-decoration: none;

        i {
          font-size: 14px;
        }

        &:hover {
          opacity: 0.8;
        }
      }
    }

    .closebtn {
      .el-button {
        color: #fff;
        font-size: 16px;
      }
    }
  }
  .searchbar {
    padding: 5px 20px;
  }
  .listcontent {
    padding: 5px 20px;
    overflow-y: auto;
    height: 420px;
    .list {
      margin-bottom: 10px;
      .question {
        font-size: 14px;
        color: #cc0033;
        line-height: 22px;
      }
      .answer {
        font-size: 14px;
        color: #333;
        line-height: 22px;
      }
    }
  }
}
</style>
