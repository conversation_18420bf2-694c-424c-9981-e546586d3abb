<template>
  <el-tooltip
    v-bind="$attrs"
    v-model="tooltipVisible"
    :disabled="disabledTooltip"
  >
    <template #content>
      <slot :name="$slots.content ? 'content' : 'default'"></slot>
    </template>
    <span
      ref="triggerRef"
      :class="{
        'text-ellipsis': true,
        'text-ellipsis-line-clamp': lineClamp
      }"
      @mouseenter="setTooltipDisabled"
    >
      <span
        ref="triggerInnerRef"
        class="text-ellipsis-inner"
        :style="lineClampStyle"
      >
        <slot></slot>
      </span>
    </span>
  </el-tooltip>
</template>

<script>
export default {
  props: {
    lineClamp: Number,
  },
  data() {
    return {
      tooltipVisible: false,
      disabledTooltip: true,
    };
  },
  computed: {
    lineClampStyle() {
      return this.lineClamp
        ? {
            display: '-webkit-inline-box',
            '-webkit-box-orient': 'vertical',
            '-webkit-line-clamp': this.lineClamp,
          }
        : {};
    },
  },
  methods: {
    setTooltipDisabled() {
      const $trigger = this.$refs.triggerRef;
      if ($trigger) {
        if (this.lineClamp) {
          this.disabledTooltip = $trigger.scrollHeight <= $trigger.offsetHeight;
        } else {
          const $triggerInner = this.$refs.triggerInnerRef;
          if ($triggerInner) {
            this.disabledTooltip = $triggerInner.getBoundingClientRect().width
              <= $trigger.getBoundingClientRect().width;
          }
        }
      }
    },
  },
};
</script>

<style scoped>
.text-ellipsis {
  max-width: 100%;
  display: inline-block;
  overflow: hidden;
}
.text-ellipsis:not(.text-ellipsis-line-clamp) {
  white-space: nowrap;
  vertical-align: bottom;
  text-overflow: ellipsis;
}
</style>
