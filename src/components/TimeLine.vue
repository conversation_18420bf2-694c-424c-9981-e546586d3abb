<template>
  <div class="time-line">
    <span class="time-line-item" v-for="day in dayNum" :key="day">{{ day }}</span>
  </div>
</template>

<script>
export default {
  props: {
    accountPeriod: {
      type: Date,
      required: true,
    },
  },

  data() {
    return {
      dayNum: 0,
    };
  },

  mounted() {
    this.setDayNum();
  },

  watch: {
    accountPeriod() {
      this.setDayNum();
    },
  },

  methods: {
    setDayNum() {
      const accountPeriod = new Date(
        this.accountPeriod.getFullYear(),
        this.accountPeriod.getMonth() + 1,
        0,
      );
      this.dayNum = accountPeriod.getDate();
    },
  },
};
</script>

<style lang="scss"  >
.time-line .time-line-item {
  cursor: pointer;
}
</style>
