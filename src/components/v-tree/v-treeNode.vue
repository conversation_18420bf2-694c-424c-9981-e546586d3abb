<template>
  <ElTreeNode
    v-bind="$attrs"
    v-on="$listeners"
    :node="source"
    :showCheckbox="showCheckbox"
  />
</template>
<script>
// import { Tree } from 'element-ui';
import ElTreeNode from './treeNode.vue';

export default {
  components: { ElTreeNode },
  props: {
    source: {
      default() {
        return {};
      },
    },
    renderContent: Function,
    showCheckbox: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isTree: false,
    };
  },
  computed: {
    tree() {
      return this.$parent.$parent.$parent;
    },
  },
};
</script>
