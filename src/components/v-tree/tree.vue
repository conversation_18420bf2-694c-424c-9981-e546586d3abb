<template>
  <div
    class="el-tree"
    :class="{
      'el-tree--highlight-current': highlightCurrent,
      'is-dragging': !!dragState.draggingNode,
      'is-drop-not-allow': !dragState.allowDrop,
      'is-drop-inner': dragState.dropType === 'inner'
    }"
    role="tree"
  >
    <virtual-list v-if="height && !isEmpty" :style="{ height: height + 'px', 'overflow-y': 'auto' }"
      :data-key="getNodeKey"
      ref="vl"
      :data-sources="visibleList"
      :data-component="itemComponent"
      :keeps="Math.ceil(height / 22) + extraLine"
      :extra-props="{
        renderAfterExpand,
        showCheckbox,
        renderContent,
        onNodeExpand: handleNodeExpand
      }"
    />
    <div class="el-tree__empty-block" v-if="isEmpty">
      <span class="el-tree__empty-text">{{ emptyText }}</span>
    </div>
    <div
      v-show="dragState.showDropIndicator"
      class="el-tree__drop-indicator"
      ref="dropIndicator">
    </div>
  </div>
</template>
<script>
import { Tree } from 'element-ui';
import VirtualList from 'vue-virtual-scroll-list';
import ElTreeVirtualNode from './v-treeNode.vue';

const flatNodes = (data, leafOnly) => data.reduce((res, node) => {
    if (node.isLeaf) {
      res.push(node);
    } else {
      !leafOnly && res.push(node);
      res = res.concat(flatNodes(node.childNodes, leafOnly));
    }
    return res;
  }, []);
export default {
  extends: Tree,
  components: { VirtualList },
  props: {
    height: {
      type: Number,
      default: 360,
    },
    extraLine: {
      type: Number,
      default: 20,
    },
    beforeCheck: {
      type: Function,
    },
  },
  data() {
    return {
      itemComponent: ElTreeVirtualNode,
    };
  },
  computed: {
     visibleList() {
        return this.flattenTree(this.root.childNodes);
      },
  },
  watch: {
    data() {
      this.resetVirtualListState();
    },
  },
  mounted() {
    // console.log(Tree.components.ElTreeNode);
  },
  methods: {
    resetVirtualListState() {
      this.$nextTick(() => {
        if (this.$refs.vl) this.$refs.vl.reset();
      });
    },
    filter(value) {
      if (!this.filterNodeMethod) throw new Error('[Tree] filterNodeMethod is required when filter');
      this.store.filter(value);
      this.resetVirtualListState();
    },
    flattenTree(datas) {
      return datas.reduce((conn, data) => {
        if (data.visible) { // 添加一个判断
          conn.push(data);
        }
        if (data.expanded && data.childNodes.length) {
          conn.push(...this.flattenTree(data.childNodes));
        }
        return conn;
      }, []);
    },
    getFlattedNodes(leafOnly) {
      return flatNodes(this.root.childNodes, leafOnly);
    },
  },
};
</script>
