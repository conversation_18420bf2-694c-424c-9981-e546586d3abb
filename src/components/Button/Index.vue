<template>
  <e-button
    :plain="isPlain"
    v-bind="$attrs"
    v-on="$listeners"
    :type="type"
    :size="size"
    :icon="icon"
    :nativeType="nativeType"
    :loading="loading"
    :autofocus="autofocus"
    :round="round"
    :circle="circle"
    :disabled="disabled"
  >
    <template #default>
      <slot></slot>
    </template>
  </e-button>
</template>
<script>
import { Button } from 'element-ui';
import store from '@/store/index';

export default {
  name: 'local-button',
  components: {
    'e-button': Button,
  },
  props: {
      type: {
        type: String,
        default: 'default',
      },
      size: {
        type: String,
        default: 'mini',
      },
      icon: {
        type: String,
        default: '',
      },
      nativeType: {
        type: String,
        default: 'button',
      },
      loading: Boolean,
      disabled: Boolean,
      plain: Boolean,
      autofocus: Boolean,
      round: Boolean,
      circle: Boolean,
    },
  data() {
    return {};
  },
  computed: {
    isPlain() {
      return (store?.state?.app.theme === 'red' && this.type !== 'text') || this.plain;
    },
  },
};
</script>
