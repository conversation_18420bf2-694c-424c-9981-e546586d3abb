<!--
 * @Date: 2019-08-29 20:16:09
 * @LastEditors  : 启旭
 * @LastEditTime : 2020-01-10 15:41:37
 * @Author: 郑晓荣
 * @Description:
 -->
<template>
  <el-dialog
    title="新增货物或应税劳务、服务名称"
    :visible.sync="dialogVisible"
    top="15vh"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @click.native.stop
  >
    <el-form>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="货物或应税劳务、服务类型">
            <el-radio-group v-model="purchaseType">
              <el-radio :label="1">商品/服务</el-radio>
              <el-radio :label="2" v-if="showAssetType">资产</el-radio>
              <el-radio :label="3" v-if="showFeeType">费用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form
      v-show="purchaseType == 1"
      ref="commodityForm"
      :model="commodityForm"
      :rules="commodityRules">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="商品类型" label-width="72px">
            <el-select
              :disabled="hasAddGoodTypeDisable"
              v-model="commodityForm.goodsType"
              placeholder="请选择"
              @change="changeTableData(commodityForm.goodsName)">
              <el-option
                v-for="{ label, value } in goodsTypes"
                :label="label"
                :value="value"
                :key="value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="goodsStatus" label="商品状态" label-width="72px">
            <el-radio-group v-model="commodityForm.goodsStatus">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="commodityForm.goodsType == 3? 12 : 24">
          <el-form-item prop="goodsName" label="商品名称" label-width="72px">
            <el-input
              :disabled="hasAddGoodNameDisable"
              v-model.trim="commodityForm.goodsName"
              placeholder="请输入商品名称"
              @blur="changeTableData(commodityForm.goodsName)">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col v-show="commodityForm.goodsType == 3" :span="12">
          <el-form-item
            prop="goodsNature"
            label="商品性质"
            :class="{ 'is-required': commodityForm.goodsType == 3 }"
            label-width="72px"
          >
            <el-select v-model="commodityForm.goodsNature" clearable placeholder="请选择">
              <el-option :value="0" label="主要材料"></el-option>
              <el-option :value="1" label="关联材料"></el-option>
              <el-option :value="2" label="其它材料"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="goodsCode" label="商品货号" label-width="72px">
            <el-input v-model.trim="commodityForm.goodsCode" placeholder="请输入商品货号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="saleunitName" label="单位" label-width="48px">
            <unit-select
              v-model="commodityForm.saleunitName"
              @input="changeSaleunitName">
            </unit-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="storageLocation" label="储存地点" label-width="72px">
            <el-input v-model.trim="commodityForm.storageLocation" placeholder="请输入储存地点"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-form
      v-show="purchaseType == 2"
      ref="assetForm"
      :model="assetForm"
      :rules="assetRules"
      label-width="72px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="categoryType" label="资产类型">
            <el-select
              v-model="assetForm.categoryType"
              placeholder="请选择资产类型"
            >
              <el-option
                label="固定资产"
                :value="0"
              ></el-option>
              <el-option
                label="无形资产"
                :value="1"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="assetTypeCode" label="类别编码">
            <el-input v-model.trim="assetForm.assetTypeCode" placeholder="请输入类别编码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="assetTypeName" label="资产类别">
            <el-input v-model.trim="assetForm.assetTypeName" placeholder="请输入资产类别"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="depreciationMethod" label="折旧方法">
            <el-select
              v-model="assetForm.depreciationMethod"
              placeholder="请选择折旧方法"
              @change="assetForm.taxLawYears = $event === 2 ? 1 : ''"
            >
              <el-option
                v-for="item in fixedAssetsTypeList"
                :label="item.keyNotes"
                :key="item.keyValue"
                :value="item.keyValue"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="taxLawYears" label="税法期限">
            <el-input
              v-model.number="assetForm.taxLawYears"
              :disabled="assetForm.depreciationMethod === 2"
              type="number"
              min="1"
              max="99"
              placeholder="请输入税法期限"
              onKeypress="return Lib.Utils.isCharAllowedInDigitalBox(event);"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-form
      v-show="purchaseType == 3"
      ref="expenseForm"
      :model="expenseForm"
      :rules="expensesRules"
      label-width="72px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="费用类型">
            <el-radio-group v-model="expenseForm.expensesType">
              <el-col :span="8" v-for="expense in filterExpensesTypeList" :key="expense.keyValue">
                <el-radio :label="expense.keyValue">
                  {{ expense.keyNotes }}
                </el-radio>
              </el-col>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="14">
          <el-form-item prop="expensesNameLv1" label="费用名称">
            <el-input v-model.trim="expenseForm.expensesNameLv1" placeholder="请输入费用名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <unit-select v-model="expenseForm.saleunitName"></unit-select>
        </el-col>
      </el-row>
    </el-form>
    <div v-if="purchaseType ===1">
      <h3>是否同时新增科目：</h3>
      <el-table
        border
        ref="subjectTable"
        :data="tableData"
        :auto-width="false"
        @select="handleSelectRow"
        @selection-change="setSelection">
        <el-table-column type="selection" align="center" :selectable="selectEnabled" />
        <el-table-column prop="subjectFullName" label="科目" />
        <el-table-column prop="saleunitName" label="单位" >
          <template slot-scope="{row}">
            <el-checkbox v-model="row.checked" :disabled="!selectionList.includes(row)">{{ row.saleunitName }}</el-checkbox>
          </template>
        </el-table-column>
      </el-table>
      <p style="color: red">未勾选的科目在生成自动凭证时, 如需使用, 会自动新增</p>
    </div>
    <div slot="footer">
      <el-button @click.stop="dialogVisible = false;">取 消</el-button>
      <el-button type="primary" @click.stop="addPurchase">确 认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex';
import clone from 'lodash-es/clone';
import UnitSelect from '@/components/commonSelect/unitSelect/UnitSelect.vue';
import { postCostType } from '@/api/companyConfig';
import { insertSubject } from '@/helper/subject';

const tableDataModel = [
  [
    {
      subjectFullName: '库存商品-', subjectParentCode: '1405', subjectName: '', saleunitName: '', defaultCheck: true,
    },
    {
      subjectFullName: '发出商品-', subjectParentCode: '1406', subjectName: '', saleunitName: '',
    },
    {
      subjectFullName: '主营业务收入-', subjectParentCode: '6001', subjectName: '', saleunitName: '', defaultCheck: true,
    },
    {
      subjectFullName: '主营业务收入-外（免）收入-', subjectParentCode: '60010001', subjectName: '', saleunitName: '',
    },
    {
      subjectFullName: '主营业务成本-', subjectParentCode: '6401', subjectName: '', saleunitName: '', defaultCheck: true,
    },
    {
      subjectFullName: '主营业务成本-外（免）成本-', subjectParentCode: '64010001', subjectName: '', saleunitName: '',
    },
  ],
  [
    {
      subjectFullName: '半成品-', subjectParentCode: '1955', subjectName: '', saleunitName: '', defaultCheck: true,
    },
    {
      subjectFullName: '其他业务收入-', subjectParentCode: '6051', subjectName: '', saleunitName: '',
    },
    {
      subjectFullName: '其他业务成本-', subjectParentCode: '6402', subjectName: '', saleunitName: '',
    },
  ],
  [
    {
      subjectFullName: '原材料-', subjectParentCode: '1403', subjectName: '', saleunitName: '', defaultCheck: true,
    },
    {
      subjectFullName: '其他业务收入-', subjectParentCode: '6051', subjectName: '', saleunitName: '',
    },
    {
      subjectFullName: '其他业务成本-', subjectParentCode: '6402', subjectName: '', saleunitName: '',
    },
  ],
  [
    {
      subjectFullName: '包装物-', subjectParentCode: '1952', subjectName: '', saleunitName: '', defaultCheck: true,
    },
    {
      subjectFullName: '其他业务收入-', subjectParentCode: '6051', subjectName: '', saleunitName: '',
    },
    {
      subjectFullName: '其他业务成本-', subjectParentCode: '6402', subjectName: '', saleunitName: '',
    },
  ],
  [
    {
      subjectFullName: '低值易耗品-', subjectParentCode: '1953', subjectName: '', saleunitName: '', defaultCheck: true,
    },
    {
      subjectFullName: '其他业务收入-', subjectParentCode: '6051', subjectName: '', saleunitName: '',
    },
    {
      subjectFullName: '其他业务成本-', subjectParentCode: '6402', subjectName: '', saleunitName: '',
    },
  ],
  [],
  [
    {
      subjectFullName: '主营业务收入-', subjectParentCode: '6001', subjectName: '', saleunitName: '', defaultCheck: true,
    },
    {
      subjectFullName: '主营业务收入-外（免）收入-', subjectParentCode: '60010001', subjectName: '', saleunitName: '',
    },
    {
      subjectFullName: '主营业务成本-', subjectParentCode: '6401', subjectName: '', saleunitName: '', defaultCheck: true,
    },
    {
      subjectFullName: '主营业务成本-外（免）成本-', subjectParentCode: '64010001', subjectName: '', saleunitName: '',
    },
  ],
  [
    {
      subjectFullName: '主营业务收入-', subjectParentCode: '6001', subjectName: '', saleunitName: '', defaultCheck: true,
    },
    {
      subjectFullName: '主营业务收入-外（免）收入-', subjectParentCode: '60010001', subjectName: '', saleunitName: '',
    },
    {
      subjectFullName: '主营业务成本-', subjectName: '', subjectParentCode: '6401', saleunitName: '', defaultCheck: true,
    },
    {
      subjectFullName: '主营业务成本-外（免）成本-', subjectParentCode: '64010001', subjectName: '', saleunitName: '',
    },
  ],
];
export default {
  props: {
    purchaseVisible: {
      type: Boolean,
      required: true,
    },
    assets: {
      type: Array,
      default: () => [],
    },
    showAssetType: {
      type: Boolean,
      default: true,
    },
    showFeeType: {
      type: Boolean,
      default: true,
    },
    formData: {
      type: Object,
    },
    invoiceGoodsName: {
      type: String,
      default: '',
    },
    addTypeCode: {
      type: String,
      default: '',
    },
    addType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      goodsTypes: [
        {
          label: '库存商品',
          value: 1,
        },
        {
          label: '半成品',
          value: 2,
        },
        {
          label: '原材料',
          value: 3,
        },
        {
          label: '包装物',
          value: 4,
        },
        {
          label: '低值易耗品',
          value: 5,
        },
        {
          label: '劳务',
          value: 7,
        },
        {
          label: '服务',
          value: 8,
        },
      ],
      copyGoodsTypes: [],
      purchaseType: 1,
      commodityForm: {
        goodsType: 1,
        goodsName: '',
        goodsNature: 0,
        goodsCode: '',
        goodsStatus: true,
        saleunitName: '',
        storageLocation: '',
      },
      assetForm: {
        assetTypeCode: '',
        assetTypeName: '',
        depreciationMethod: 1,
        categoryType: 0,
        taxLawYears: '',
        enabledModify: 1,
        insert: true,
        remark: '',
      },
      expenseForm: {
        expensesType: 1,
        saleunitName: '',
        expensesNameLv1: '',
      },
      assetRules: {
        assetTypeCode: [
          { required: true, message: '请输入类别编码', trigger: 'change' },
          { max: 8, message: '类别编码不能超过8个字符！', trigger: 'change' },
          { validator: this.assetTypeCodeValidator, trigger: 'change' },
        ],
        assetTypeName: [
          { required: true, message: '请输入资产类别', trigger: 'change' },
          { validator: this.assetTypeNameValidator, trigger: 'change' },
        ],
        depreciationMethod: [
          {
            required: true,
            type: 'number',
            message: '请选择折旧方法',
            trigger: 'change',
          },
        ],
        taxLawYears: [
          {
            required: true,
            type: 'number',
            message: '请输入税法期限',
            trigger: 'change',
          },
          { validator: this.taxLawYearsValidator, trigger: 'change' },
        ],
      },
      expensesRules: {
        expensesNameLv1: [
          { required: true, message: '请输入费用名称', trigger: 'change' },
        ],
      },
      tableData: [],
      selectionList: [],
      hasAddGoodNameDisable: false,
      hasAddGoodTypeDisable: false,
    };
  },
  components: {
    UnitSelect,
  },
  mounted() {},
  computed: {
    ...mapState('staticJson', {
      fixedAssetsTypeList: (state) => state.depreciationMethods,
    }),
    commodityRules() {
      // 实现，选择劳务与服务时商品单位为非必填项
      let arr = [{ required: true, message: '请选择单位', trigger: 'change' }];
      if (this.commodityForm.goodsType === 7 || this.commodityForm.goodsType === 8) { arr = [{ required: false, trigger: 'change' }]; }
      return {
        goodsName: [
          { required: true, message: '请输入商品名称', trigger: 'change' },
        ],
        goodsCode: [
          { required: true, message: '请输入商品货号', trigger: 'change' },
        ],
        saleunitName: arr,
      };
    },
    expensesTypeList() {
      return this.$store.state.staticJson.expensesTypeList;
    },
    filterExpensesTypeList() {
      return this.$store.state.staticJson.expensesTypeList.filter((item) => item.keyValue !== 4);
    },
    currencyList() {
      return this.$store.state.selectData.currencyList;
    },
    functionalCurrency() {
      return this.$store.state.user.companyInfo.functionalCurrency;
    },
    dialogVisible: {
      get() {
        return this.purchaseVisible;
      },
      set(show) {
        if (!show && this.purchaseVisible) {
          this.$emit('hide');
          this.goodsTypes = this.copyGoodsTypes;
        }
      },
    },
  },
  watch: {
    purchaseVisible(show) {
      if (!show) {
        return;
      }
      const { commodityForm, assetForm, expenseForm } = this.$refs;
      this.purchaseType = 1;
      if (commodityForm) commodityForm.resetFields();
      if (assetForm) assetForm.resetFields();
      if (expenseForm) expenseForm.resetFields();
      this.commodityForm.goodsName = (this.formData && this.formData.subjectName)
        || this.invoiceGoodsName;
      this.commodityForm.saleunitName = this.formData && this.formData.unit;
      this.copyGoodsTypes = clone(this.goodsTypes);
      this.initFormGoodType();
      this.changeSaleunitName(this.commodityForm.saleunitName);
      this.$http
        .get('/rest/companyConfig/goods/goods/v1.0/generate/goodsCode')
        .then(({ data: json }) => {
          if (json.returnCode === 0) { this.commodityForm.goodsCode = json.goodsCode; }
        });
    },
  },
  methods: {
    // 需求316 最后一点  库存商品， 必勾
    selectEnabled(row) {
      return !(row.subjectParentCode === '1405' || row.subjectParentCode === this.addTypeCode);
    },
    initFormGoodType() {
      const {
        addType, addTypeCode, commodityForm,
      } = this;
      if (addType === 'goodType') {
        this.hasAddGoodNameDisable = true;
        this.hasAddGoodTypeDisable = true;
        if (addTypeCode === '1405' || addTypeCode === '1406') {
          commodityForm.goodsType = 1;
        } else if (['6001', '60010001', '6401', '64010001'].indexOf(addTypeCode) !== -1) {
          this.hasAddGoodTypeDisable = false;
          this.commodityForm.goodsType = 1;
          this.goodsTypes = this.goodsTypes.filter((item) => [1, 7, 8].indexOf(item.value) !== -1);
        } else if (['6051', '6402'].indexOf(addTypeCode) !== -1) {
          this.hasAddGoodTypeDisable = false;
          this.commodityForm.goodsType = 2;
          this.goodsTypes = this.goodsTypes.filter((item) => [2, 3, 4, 5].indexOf(item.value) !== -1);
        } else {
          switch (addTypeCode) {
            case '1955':
              commodityForm.goodsType = 2;
              break;
            case '1403':
              commodityForm.goodsType = 3;
              break;
            case '1952':
              commodityForm.goodsType = 4;
              break;
            case '1953':
              commodityForm.goodsType = 5;
              break;
            default:
              commodityForm.goodsType = 1;
          }
        }
      }
      // else if (addType === 'assetsType') {  资产 不用跳
      //   if (['1701', '1702'].indexOf(addTypeCode) !== -1) {
      //     assetForm.categoryType = 1;
      //   }
      // }
    },
    // 失焦 手动生成表格数据
    changeTableData(value) {
      const { goodsType } = this.commodityForm;
      if (value) {
        this.tableData = tableDataModel[goodsType - 1].map((item) => ({
          ...item,
          subjectFullName: item.subjectFullName + value,
          subjectName: value,
          checked: item.defaultCheck && this.commodityForm.saleunitName !== '无',
          checkedDisabled: true,
        }));
        this.$nextTick(() => {
          this.tableData.forEach((item) => {
            if (this.addTypeCode === item.subjectParentCode || item.defaultCheck) {
              this.$refs.subjectTable.toggleRowSelection(item, true);
              item.checkedDisabled = false;
            }
          });
        });
      } else {
        this.tableData = [];
      }
    },
    changeSaleunitName(name) {
      tableDataModel.forEach((item) => {
        item.forEach((val) => {
          val.saleunitName = name;
        });
      });
      this.changeTableData(this.commodityForm.goodsName);
    },
    // 勾选的表格数据
    setSelection(selection) {
      this.selectionList = selection;
    },
    handleSelectRow(select, row) {
      row.checked = false;
    },
    goodsNatureValidator(rule, value, callback) {
      if (value === '' && this.commodityForm.goodsType === 3) {
        callback(new Error());
      } else {
        callback();
      }
    },
    addPurchase() {
      switch (this.purchaseType) {
        case 1:
          this.addCommodity();
          break;
        case 2:
          this.addAsset();
          break;
        default:
          this.addExpense();
      }
    },
    addCommodity() {
      this.$refs.commodityForm.validate((valid) => {
        if (!valid) {
          return;
        }
        const { formData } = this;
        this.$http.post('/rest/companyConfig/goods/goods/v1.0/insert', JSON.stringify(this.commodityForm))
          .then(async ({ data: json }) => {
            const { commodityForm } = this;
            const { goodsType } = commodityForm;
            if (commodityForm.goodsStatus) {
              let type;
              if (goodsType < 4) {
                type = goodsType;
              } else if (goodsType >= 4 && goodsType < 7) {
                type = goodsType + 2;
              } else {
                type = goodsType + 1;
              }
              const paramsList = this.selectionList.map((item) => {
                const newItem = Object.assign(item, {
                  relatedTable: 1,
                  insertSubject: true,
                  endingExchange: !!(formData && formData.endingExchange),
                  currencies: formData 
                  && this.currencyList.filter((thing) => thing.currencyCode !== this.functionalCurrency  && formData.currencyType.indexOf(thing.currencyName) !== -1),
                });
                console.log(newItem, formData, this.functionalCurrency, this.currencyList.filter((thing) => thing.currencyCode !== this.functionalCurrency  && formData.currencyType.indexOf(thing.currencyName) !== -1));
                if (!item.checked) {
                  Reflect.deleteProperty(newItem, 'saleunitName');
                }
                return newItem;
              });

              let subjectData;
              try {
                const subjectRsp = await insertSubject(paramsList);
                subjectData = subjectRsp.data.data || '';
              } catch (error) {
                console.error(error);
              }
              this.$emit('insertSuccess', {
                type,
                id: json.data[0].goodsId,
                name: json.data[0].goodsName,
                saleunitName: commodityForm.saleunitName,
                storageLocation: commodityForm.storageLocation,
              }, subjectData);
            }

            this.dialogVisible = false;
            this.$message.success(json.returnMessage || '新增商品成功');
          });
      });
    },
    addAsset() {
      this.$refs.assetForm.validate((valid) => {
        if (!valid) {
          return;
        }
        this.$http
          .post(
            '/rest/proxy/account/companyAccount/merge/fixedAssetsCard/v0.1/typeAdd',
            JSON.stringify(this.assetForm),
          )
          .then(({ data: json }) => {
            this.$emit('insertSuccess', {
              type: Number(this.assetForm.categoryType) === 0 ? 4 : 10,
              id: json.data[0].assetTypeId,
              name: json.data[0].assetTypeName,
            });
            this.dialogVisible = false;
            this.$message.success(json.returnMessage || '新增固定资产成功');
          });
      });
    },
    addExpense() {
      const { expensesTypeList } = this;
      this.$refs.expenseForm.validate((valid) => {
        if (!valid) {
          return;
        }
        const {
          expenseForm: { expensesType, expensesNameLv1, saleunitName },
        } = this;
        const formDataList = [];
        const forDataObject = {
          expensesNameLv1,
          expensesType,
          saleunitName,
        };
        if (!this.IsAgent) {
          this.$set(forDataObject, 'claimContentStatus', false);
        }
        formDataList.push(forDataObject);
        postCostType(formDataList).then(({ data: json }) => {
          const name = (
            expensesTypeList.find((item) => String(item.keyValue) === String(expensesType))
              || {}
          ).keyNotes || '';
          this.$emit('insertSuccess', {
            type: 5,
            id: json.data[0].expensesId,
            expensesType,
            expensesTypeName: name,
            name: (name && `${name}-`) + json.data[0].expensesNameLv1,
            saleunitName,
          });
          this.dialogVisible = false;
          this.$message.success(json.returnMessage || '新增费用成功');
        });
      });
    },
    assetTypeCodeValidator(rule, value, callback) {
      value = value.trim();
      if (this.assets.find((item) => item.assetTypeCode === value)) {
        callback(new Error('类别编码已存在！'));
      } else {
        callback();
      }
    },
    assetTypeNameValidator(rule, value, callback) {
      value = value.trim();
      if (this.assets.find((item) => item.assetTypeName === value)) {
        callback(new Error('资产类别已存在！'));
      } else {
        callback();
      }
    },
    taxLawYearsValidator(rule, value, callback) {
      if (value < 0 || value > 1200) {
        callback(new Error('税法期限范围为0到1200！'));
      } else {
        callback();
      }
    },
  },
};
</script>

<style scoped>
.el-select {
  width: 100%;
}
.highlight {
  color: red;
}
</style>
