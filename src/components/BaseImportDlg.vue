<template>
  <div>
    <el-dialog
    :title="title"
    :modal-append-to-body="false"
    :close-on-click-modal='false'
    :visible.sync="dialogVisible"
    width="840px"
    @close="doClose"
    close-on-press-escape>
      <el-row :gutter="20" class="dialog-body import-form">
        <el-col :span="hasLeftPanel ? 13 : 24">
          <el-upload
            drag
            ref="upload"
            :accept="acceptType"
            :action="actionUrl"
            :mutiple="true"
            :auto-upload="false"
            :on-success="handleSuccess"
            :on-error="handleError"
            :on-change="handleUploadChange"
            :on-remove="handleRemove"
            :file-list="fileList"
            >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击选择文件</em></div>
            <div class="el-upload__tip" slot="tip">
              <div>{{tipsHtml}}</div>
            </div>
          </el-upload>
          <div v-show="showMsg" class="uploaded-file">
            <i title="成功" v-if="isSuccess" class="el-icon-upload-success el-icon-circle-check"></i>
            <i title="失败" class="el-icon-upload-fail el-icon-warning" v-else></i>
            <span class="uploaded-file-name">{{ message }}</span>
          </div>
        </el-col>
        <el-col :span="11" v-if="hasLeftPanel" class="import-dlg-right">
          <slot name="importDlgRright" :$this="this"></slot>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button v-if="isLoading" type="primary" :loading="true">上传中</el-button>
        <el-button v-else type="primary" @click="handleImport">上传</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

export default {
  props: {
    importVisible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      default: '文件导入',
    },
    beforeUpload: {
      type: Function,
    },
  },

  data() {
    return {
      hasLeftPanel: true,
      fileList: [],
      showMsg: false,
      isSuccess: true,
      message: '',
      isLoading: false,
      needUpdate: false,
    };
  },

  watch: {
    importVisible(newValue) {
      this.dialogVisible = newValue;
    },
  },

  computed: {
    dialogVisible: {
      get() {
        return this.importVisible;
      },
      set(isVisible) {
        if (this.importVisible && !isVisible) {
          this.showMsg = false;
          this.fileList = [];
          this.$emit('hide', false);
        }
      },
    },
    acceptType: () => '',

    actionUrl: () => '',

    tipsHtml: () => '',
  },

  methods: {

    handleImport() {
      if (!this.$refs.upload.uploadFiles.length) {
        this.showMsg = true;
        this.isSuccess = false;
        this.message = '请先选择文件';
        return;
      }
      if (typeof this.beforeUpload === 'function') {
        if (this.beforeUpload() === false) {
          return;
        }
      }
      this.isLoading = true;
      this.$refs.upload.submit();
    },
    // 获取初始数据
    handleSuccess(response) {
      this.message = '';
      this.showMsg = true;
      this.isSuccess = true;
      this.isLoading = false;
      const h = this.$createElement;
      const returnMessage = response.returnMessage || '操作成功';
      const message = returnMessage.split('\r\n');
      const span = []; const
        count = message.length;
      for (let i = 0; i < count; i += 1) {
        span.push(h('span', message[i]));
        span.push(h('br'));
      }
      span.pop();
      this.$message({
        type: 'success',
        message: h('p', span),
        showClose: true,
      });
      setTimeout(() => {
        this.$emit('success', response);
        this.$emit('getmaintable', response);
        this.$emit('insertSubject', response);
        this.doClose();
      }, 500);
    },

    handleError(err, file, fileList) {
      const response = JSON.parse(err.message);
      this.showMsg = true;
      this.isSuccess = false;
      this.isLoading = false;
      const errorId = response.errorId ? `-${response.errorId}` : '';
      this.message = response.errorMessage ? response.errorMessage + errorId : `文件上传失败${errorId}`;
      if (file.status === 'fail') fileList.push(file);
    },

    handleUploadChange(file, fileList) {
      if (fileList.length > 1) fileList.shift();
      this.showMsg = file.status === 'fail';
      if (fileList.length && this.showMsg) Object.assign(file, { status: 'ready' });
    },

    handleRemove() {
      this.showMsg = false;
    },

    // 关闭
    doClose() {
      this.dialogVisible = false;
      this.$emit('close');
      this.isLoading = false;
    },
  },
};
</script>

<style lang="scss" scoped>
  .uploaded-file{
    display: flex;
    display: -ms-flexbox;
    display: -webkit-flex;
    padding: 5px 0;
    align-items: flex-start;
    -ms-flex-align: start;
    .uploaded-file-name{
      display: inline-block;
      line-height: 1;
      flex: 1;
      padding-left: 7px;
    }
    .el-icon-upload-success{
      color: #13ce66;
    }
    .el-icon-upload-fail{
      color: #F7BA2A;
    }
  }
</style>

<style lang="scss"  >
  .import-form .el-progress.el-progress--line{
    display: none;
  }
  .formItemClass:first-child {
    .el-checkbox-group {
      margin-top: 5px;
      .el-checkbox:last-child {
        margin-left: 0px;
      }
    }
  }
  .formItemClass:not(:first-child) {
    .el-checkbox-group {
      margin-top: -3px;
    }
  }
</style>
