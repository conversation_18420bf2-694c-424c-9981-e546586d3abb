<!--
 * @LastEditors: 启旭
 * @Author: 启旭
 * @Description: 科目标签搜索组件
 * @Date: 2019-11-14 16:00:49
 * @LastEditTime: 2019-12-02 17:24:09
 -->

<template>
  <el-popover
    class="el_popover_area"
    placement="bottom-start"
    popper-class="tag_popover"
    v-model="visible"
    trigger="click"
  >
    <div class="wrapper clearfix">
      <div
        class="first_tree_box tree_box"
        v-if="!multipleRoot"
      >
        <div class="tree_box_header tagType">
          标签类别
        </div>
        <ul class="tagul">
          <li
            v-for="tag in subjectTagTypeList"
            :key="tag.tagTypeId"
            :class="{currentTag:tag.tagTypeId===currentTypeId,disableli:selectedTagLength>0 || disabledTagTypeIds.indexOf(tag.tagTypeId) !== -1}"
            @click="getTree(tag)"
          >
            <span>{{tag.tagTypeName}}</span>
            <span
              v-if="tag.tagTypeId===currentTypeId && isCheckTagType"
              @click="clearSelection"
              style="color: #fff; line-height: 18px"
              class="el-icon-close fr"
            ></span>
          </li>
        </ul>
        <div style="margin-top:5px;padding:0 10px;">
          <el-button
            v-if="hasCheckTagType"
            type="primary"
            @click="handleTypeCheckClick"
            style="width:94%; margin-top: 25px"
          >
            <span>按类别查询</span>
          </el-button>
        </div>
      </div>
      <div>
        <TagTree
          v-model="selectedTag"
          ref="tagTree"
          :checkStrictly="checkStrictly"
          :maxLength="200"
          :defaultFoldAll="defaultFoldAll"
          noDisable
          :enableOnlyOne="false"
          :tagTypeNames="filterType"
        />

        <el-button
          type="primary"
          @click="handleCheckClick"
          style="width:50%;"
        >
          <span>按勾选的标签内容查询</span>
        </el-button>
      </div>
    </div>
    <template slot="reference">

      <el-badge
        class="tree_badge"
        :value="selectedTagLength"
        :max="99"
      >
        <slot>
          <el-button
            plain
            class="tagContent-btn"
            :class="{ emptytagbtn:selectedTagLength == 0 && !isCheckTagType }"
          >{{ isCheckTagType || selectedTagLength !== 0 ? currentTypeName : '选择条件' }}</el-button>
        </slot>

      </el-badge>
    </template>
  </el-popover>
</template>

<script>
import { cloneDeep } from 'lodash-es';
import toTree from '@/utils/toTree';
import TagTree from './TagTreeSelector.vue';

export default {
  components: { TagTree },
  props: {
    tagIdsStr: {
      type: String,
      default: '',
    },
    multipleRoot: Boolean,
    checkStrictly: {
      type: Boolean,
      default: true,
    },
    disabledTagTypeIds: {
      type: Array,
      default: () => [],
    },
    hasCheckTagType: {
      type: Boolean,
      default: false,
    },
    // 默认收起全部选项
    defaultFoldAll: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      filterMsg: '', // 过滤树查询字段
      visible: false, // 弹窗展示状态
      selectedTag: '',
      currentTypeId: '',
      treeEmptyStr: '无标签数据',
      currentTypeName: '',
      isCheckTagType: false,
      // subjectTagTreeData: [],
    };
  },

  computed: {
    selectedTagLength() {
      if (!this.selectedTag) return 0;
      return this.selectedTag.split(',').length;
    },
    subjectTagTypeList() {
      return (
        this.$store.state.selectData?.subjectTag?.subjectTagTypeList?.filter(
          (item) => item.deleteFlag !== 1,
        ) || []
      );
    },
    tagMap() {
      return this.$store.state.selectData?.tagMap || new Map();
    },
    filterType() {
      return !this.multipleRoot ? [this.currentTypeName] : [];
    },
    subjectTagContentList() {
      let subjectTagContentList = this.$store.state.selectData?.subjectTag?.subjectTagContentList || [];
      if (this.multipleRoot) {
        subjectTagContentList = subjectTagContentList.concat(
          this.subjectTagTypeList.map((item) => ({
            ...item,
            tagName: item.tagTypeName,
            tagCode: item.tagTypeCode,
            tagParentCode: '',
            tagId: item.tagTypeId,
          })),
        );
      }
      return subjectTagContentList;
    },
    subjectTagTreeData() {
      console.time('subjectTagTreeData');
      const a = toTree(cloneDeep(this.subjectTagContentList), {
        idField: 'tagCode',
        parentIdField: 'tagParentCode',
        isRoot: (item) => item.tagParentCode.length === (this.multipleRoot ? 0 : 4),
        hasChild: (item) => item.blnIsDetail === false || item.tagParentCode === '',
      });
      console.timeEnd('subjectTagTreeData');
      return a;
    },
    // 根据当前类别过滤当前标签树
    currentTree() {
      // 可多选类别时不需要过滤
      if (this.multipleRoot) return this.subjectTagTreeData;
      // 当没有选中类别时， 树为空
      if (this.currentTypeId === '') return [];
      // 过滤当前类别的标签
      return this.subjectTagTreeData.filter(
        (item) => item.tagTypeId === this.currentTypeId,
      );
    },
  },

  watch: {
    disabledTagTypeIds() {
      if (this.disabledTagTypeIds.includes(this.currentTypeId)) {
        this.currentTypeName = '';
        this.currentTypeId = '';
      }
    },
    selectedTag(val) {
      this.$emit('update:tagIdsStr', val);
    },
  },

  mounted() {
    this.getSubjectTabClass();
    // this.getSubjectTagTreeData();
  },

  methods: {
    getTree(tag) {
      if (this.disabledTagTypeIds.indexOf(tag.tagTypeId) !== -1) return;
      if (!this.multipleRoot && this.selectedTagLength > 0) return;
      this.currentTypeName = tag.tagTypeName;
      this.currentTypeId = tag.tagTypeId;
    },
    // 获取标签类别
    async getSubjectTabClass() {
      // await this.$store.dispatch('selectData/throttleGetSubjectTag');
      // 在能够选择标签类别的场景， 不需要默认类别
      if (this.subjectTagTypeList.length > 0 && !this.hasCheckTagType) {
        this.currentTypeId = this.subjectTagTypeList[0].tagTypeId;
        this.currentTypeName = this.subjectTagTypeList[0].tagTypeName;
      }
    },

    handleCheckClick() {
      this.visible = false;
      this.$emit('change', this.selectedTag);

      this.isCheckTagType = false;
    },
    handleTypeCheckClick() {
      if (this.currentTypeId === '') {
        this.$message.error('当前未选中任何标签类别');
        return;
      }
      this.selectedTag = '';
      this.$nextTick(() => {
        this.visible = false;
        this.isCheckTagType = true;
        this.$emit('change', this.currentTypeId, 'tagType');
      });
    },
    clearSelection() {
      if (this.isCheckTagType) {
        this.isCheckTagType = false;
      }
      this.selectedTag = '';
      this.$emit('clear');
    },
    // 将Code字段转为id
    typeCodeToTypeId(typeCode) {
      return (
        this.subjectTagTypeList.find((item) => item.tagTypeCode === typeCode)
          ?.tagTypeId || ''
      );
    },
  },
};
</script>

<style lang="scss"   scoped>
.tagul {
  padding-right: 10px;
  height: 360px;
  overflow-y: auto;
  li {
    height: 18px;
    line-height: 18px;
    padding: 2px 8px;
    cursor: pointer;
    margin-top: 6px;
    &.disableli {
      cursor: not-allowed;
      color: #ddd;
    }
    &.currentTag {
      color: #fff;
      background: $color-theme-base;
      border-radius: 2px;
    }
  }
}
.emptytagbtn {
  color: #aaa;
}

::-webkit-scrollbar-track-piece {
  background-color: white;
}
.tree {
  max-width: 400px;
}
</style>

<style lang="scss">
.el_popover_area {
  display: inline-block;
  vertical-align: top;
  line-height: 1;
}
.tagContent-btn {
  padding: 0 !important;
}

.tag_popover .wrapper {
  margin-bottom: 10px;
  display: flex;
}
.tagType {
  line-height: 30px;
}
.el-badge.tree_badge {
  margin-right: 8px;
  // width: 80px;
}
.el-badge.tree_badge button {
  // width: 80px;
  overflow: hidden;
}
.el-badge.tree_badge .el-badge__content.is-fixed {
  top: 0;
  right: 0;
  position: absolute;
  transform: translateY(-36%) translateX(40%);
}
</style>
