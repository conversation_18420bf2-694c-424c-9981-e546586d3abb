<template>
  <div class="flex-h">
    <div class="flex-fluid tag-name-wrapper is-disabled"><tag-name :tagIds="localValue" /></div>

    <el-button @click="open" type="primary" class="flex-fixed" :disabled="disabled">选择</el-button>
    <!-- <TagEditor :visible.sync="dialogVisible" /> -->
  </div>
</template>
<script>
// import TagEditor from './TagEditor.vue';
import { showTagEditor } from './subjectTagSelectSingle';

export default {
  name: 'TagSelect',
  props: {
    value: {
      type: String,
      default: '',
    },
    // 限定可选的 标签类别名称
    tagTypeNames: {
      type: Array,
      default: () => [],
    },
    // 关联员工标签与部门标签， 为 ture 时， 选择员工标签时，会将其对应的部门标签选出来
    linkDepartmentTag: {
      type: Boolean,
    },
    disabled: {
      type: <PERSON>olean,
    },
    autoOpen: {
      type: Boolean,
    },
    // 默认收起全部选项
    defaultFoldAll: {
      type: Boolean,
      default: true,
    },
  },
  model: {
    event: 'change',
  },
  data() {
    return {
      dialogVisible: false,
      // 此处为兼容单组件渲染场景保持 value 能正常回显， 采用的写法， 不要优化他
      localValue: '',
    };
  },
  watch: {
    value: {
      immediate: true,
      handler: function handler(val) {
        this.localValue = val;
      },
    },
  },
  mounted() {
    console.log('change', this.autoOpen);
    if (this.autoOpen) {
      this.open();
    }
  },
  methods: {
    open() {
      showTagEditor({
        value: this.value,
        changeValue: (value) => {
          console.log(value);
          this.localValue = value;
          this.$emit('change', value);
        },
        hide: () => {
          this.$emit('visible-change', false);
        },
        options: {
          tagTypeNames: this.tagTypeNames,
          linkDepartmentTag: this.linkDepartmentTag,
          defaultFoldAll: this.defaultFoldAll,
        },
      });
      this.$emit('visible-change', true);
    },
  },
};
</script>
<style lang="scss">
.tag-name-wrapper{
      -webkit-appearance: none;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #E4E7ED;
    box-sizing: border-box;
    color: #262626;
    display: inline-block;
    font-size: inherit;
    height: 30px;
    line-height: 30px;
    outline: none;
    padding: 0 15px;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    width: 100%;
    background-color: #F5F7FA;
    border-color: #E4E7ED;
    color: #bfbfbf;
    cursor: not-allowed;
    word-break: keep-all;
    white-space: nowrap;
    text-overflow: ellipsis;
}
</style>
