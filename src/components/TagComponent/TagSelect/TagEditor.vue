<template>
  <el-dialog
    title="标签选择"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="620px"
  >
    <TagTreeSelector
      ref="TagTreeSelector"
      v-bind="options"
      v-model="value"
    />
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button @click="sumbit" type="primary">确定</el-button>
    </template>
  </el-dialog>
</template>
<script>
import TagTreeSelector from '../TagTreeSelector.vue';

export default {
  name: '',
  components: { TagTreeSelector },
  props: {
    disabled: Boolean,
    // 最多可选几个标签， 为 0 表示不限制
    multipleLimit: {
      type: Number,
      default: 6,
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      value: '',
      visible: false,
      handleAddItem: () => {},
      options: {
      },
      hide: () => {},
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
    subjectTagTypeList() {
      return this.$store.state.selectData?.subjectTag?.subjectTagTypeList?.filter((item) => item.deleteFlag !== 1) || [];
    },
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.$refs.TagTreeSelector.reset();
        this.hide();
        this.value = '';
      }
    },
  },
  methods: {
    sumbit() {
      if (typeof this.changeValue === 'function') {
        this.changeValue(this.value);
      }
      this.visible = false;
    },
  },
};
</script>
