import Vue from 'vue';
import store from '@/store';
import TagEditor from './TagEditor.vue';

let instance;
const TagEditorConstructor = Vue.extend(TagEditor);

const initInstance = () => {
  instance = new TagEditorConstructor({
    el: document.createElement('div'),
    store,
  });
  document.body.appendChild(instance.$el);
};
export const hideTagEditor = () => {
  if (!instance?.$el) return;
  instance.visible = false;
  // const { parentElement } = instance.$el;
  // if (parentElement) {
  //   parentElement.removeChild(instance.$el);
  // }
};
export const destroyTagEditor = () => {
  hideTagEditor();
  instance.$destroy();
  instance = null;
};
// 在指定dom元素中插入标签选择器
export const showTagEditor = ({
 value = '', changeValue = () => {}, hide = () => {}, options = {},
}) => {
  if (!instance) {
    initInstance();
  }
  instance.value = '';
  console.log(value);
  // document.body.appendChild(instance.$el);
  instance.visible = true;
  instance.value = value;
  instance.hide = () => { hide(); };
  instance.options = options;
  instance.changeValue = (val) => { changeValue(val); instance.value = val; };
  return {
    instance,
  };
};
