<!--
 * @LastEditors: 启旭
 * @Author: 启旭
 * @Description: 科目标签搜索组件
 * @Date: 2019-11-14 16:00:49
 * @LastEditTime: 2019-12-02 17:24:09
 -->

<template>
  <TreeSelect
    ref="selector"
    :data="filteredTags"
    v-model="selected"
    id-field="tagId"
    label-field="tagName"
    :checkStrictly="checkStrictly"
    parent-id-field="tagParentId"
    :defaultFoldAll="defaultFoldAll"
    :fullNameMethod="fullNameMethod"
    :disableCheckMethod="disableCheckMethod"
    :beforeCheck="validOnlyOneTagWithType"
    :afterCheck="handleAddTagItem"
    :maxLength="maxLength"
  />
</template>

<script>
import TreeSelect from '@/components/TreeSelect/index.vue';
import { isEqual } from 'lodash-es';

export default {
  components: { TreeSelect },
  props: {
    value: {
      type: String,
      default: '',
    },
    // 限定可选的 标签类别名称
    tagTypeNames: {
      type: Array,
      default: () => [],
    },
    // 默认收起全部选项
    defaultFoldAll: {
      type: Boolean,
      default: true,
    },
    // 关联员工标签与部门标签， 为 ture 时， 选择员工标签时，会将其对应的部门标签选出来
    linkDepartmentTag: {
      type: Boolean,
    },
    // 不启用停用规则
    noDisable: {
      type: Boolean,
    },
    checkStrictly: {
      type: Boolean,
      default: true,
    },
    // 启用单个类型下只能选一个标签
    enableOnlyOne: {
      type: Boolean,
      default: true,
    },
    maxLength: {
      type: Number,
      default: 6,
    },
  },
  model: {
    value: 'value',
    event: 'change',
  },

  data() {
    return {
      selected: [],
      filteredTags: [],
    };
  },

  computed: {
    subjectTagTypeList() {
      return this.$store.state.selectData?.subjectTag?.subjectTagTypeList?.filter((item) => item.deleteFlag !== 1) || [];
    },
    subjectTag() {
      return this.$store.state.selectData?.subjectTag;
    },
    tagMap() {
      return this.$store.state.selectData?.tagMap || new Map();
    },
    tagCodeMap() {
      return this.$store.state.selectData?.tagCodeMap || new Map();
    },
    subjectTagContentList() {
      let subjectTagContentList = this.$store.state.selectData?.subjectTag?.subjectTagContentList || [];
      subjectTagContentList = subjectTagContentList.concat(this.subjectTagTypeList.map((item) => ({
        ...item, tagName: item.tagTypeName, tagCode: item.tagTypeCode, tagParentCode: null, tagParentId: null, tagId: item.tagTypeId,
      })));
      return subjectTagContentList;
    },
  },

  watch: {
    value: {
      immediate: true,
      handler: function handleTagIdsChange(val) {
        if (!val) {
          this.selected = [];
          return;
        }
        const selectedWithoutDelete = val.split(',').filter((item) => this.tagMap.get(item).deleteFlag !== 1);
        if (this.selected.join(',') === selectedWithoutDelete.join(',')) return;
        this.selected = selectedWithoutDelete;
      },
    },
    selected: {
      handler: function handleTagIdsChange(val) {
        console.log(val, this.tagMap);
        const selectedWithoutDelete = val.filter((item) => this.tagMap.get(item) && this.tagMap.get(item).deleteFlag !== 1);
        if (selectedWithoutDelete.join(',') === this.value) return;
        const value = selectedWithoutDelete.join(',');
        this.$emit('change', value);
      },
    },
    // 使用watch 防止无意义的重新计算属性，导致tree重新渲染
    tagTypeNames: {
      immediate: true,
      handler: function handleTagTypeNames(val, oldVal) {
        if (!isEqual(val, oldVal)) {
          this.getFilteredTags();
        }
      },
    },
    subjectTagContentList() {
      this.getFilteredTags();
    },
  },

  mounted() {
  },

  methods: {
    getFilteredTags() {
      this.filteredTags = this.subjectTagContentList.filter((item) => (this.tagTypeNames.length === 0 || this.tagTypeNames.includes(item.tagTypeName)) && item.deleteFlag !== 1);
    },
    disableCheckMethod(data) {
      // 类别是一定不可选的
      if (data.tagId === data.tagTypeId) return true;
      if (this.noDisable) return false;
      const tagTypeItem = this.subjectTagTypeList.find((item) => item.tagTypeId === data.tagTypeId);
      if (!data.tagStatus) return true;
      // 但标签类别没有开启非末级可选时， 所有非末级选项 不可选
      if (tagTypeItem.onlyDetail) {
        return !data.blnIsDetail;
      }
      return false;
    },
    validOnlyOneTagWithType(checkValue) {
      if (!this.enableOnlyOne) return true;
      // 一种类别下的标签只能选择一个，所以其他的标签内容需要不允许选择
      const checkingTagCode = this.tagMap.get(checkValue).tagCode;
      const flag = !this.selected.find(
            (tagId) => {
              const checkedTagCode = this.tagMap.get(tagId)?.tagCode;
              return tagId !== checkValue
                && checkingTagCode.slice(0, 4) === checkedTagCode.slice(0, 4);
            },
          );
      if (!flag) {
        this.$message.warning('一种类别下的标签只能选择一个');
      }
      return flag;
    },
    fullNameMethod(dataPath) {
      // 标签类别[一级标签内容 - 二级标签内容 - 三级标签内容];
      const rootName = dataPath.shift()?.tagName;
      const pathNames = dataPath.map((item) => item.tagName);
      return `${rootName}[${pathNames.join('-')}]`;
    },
    // 标签变更
    handleAddTagItem(newTagId) {
      if (!this.linkDepartmentTag) return;
      const { subjectTagContentList } = this.subjectTag;
      const tag = this.tagMap.get(newTagId);
      if (!tag) return;
      // 判断当前添加标签的是否为员工类别， 是的话进行选中员工标签后，自动选中部门标签的操作
      if (tag.tagTypeName === '员工' && this.selected.length < 5) {
        const tagIds = this.selected.filter((item) => item !== newTagId);
        const hasDepartmentTag = tagIds.length !== 0 && !!subjectTagContentList.find((item) => item.tagTypeName === '部门' && tagIds.includes(item.tagId));
        if (!hasDepartmentTag) {
          const departmentTag = this.getDepartmentTagWithPerson(tag);
          console.log(departmentTag);
          if (departmentTag) {
            this.selected.push(departmentTag.tagId);
          }
        }
      }
    },
    // 根据员工标签获取部门标签
    getDepartmentTagWithPerson(personTag) {
      const { subjectTagContentList } = this.subjectTag;
      const departmentsNames = [];
        // 提高查询性能， 先将部门 与 员工 的 标签先筛选出来
      const tagListOfdepartment = [];
      const tagListOfPerson = [];
      subjectTagContentList.forEach((item) => {
        if (item.tagTypeName === '员工') {
          tagListOfPerson.push(item);
          return;
        }
        if (item.tagTypeName === '部门') {
          tagListOfdepartment.push(item);
        }
      });
      // 通过选中员工的tagCode 除去后4位的位数可以找出 他的上级部门tagCode， 例 0001(类别)- xxxx(一级部门) - xxxx(二级部门) - 0001(员工)
      for (let endIndex = -4; endIndex >= -(personTag.tagCode.length - 8); endIndex -= 4) {
        const departmentTagCodeWithPerson = personTag.tagCode.slice(0, endIndex);
        departmentsNames.unshift(tagListOfPerson.find((item) => item.tagCode === departmentTagCodeWithPerson)?.tagName);
      }
      let departmentTagCode = '';
      let departmentTag;
      for (let i = 0; i <= departmentsNames.length - 1; i += 1) {
        const currDepartmentName = departmentsNames[i];
        // eslint-disable-next-line no-loop-func
        const currDepartmentTag = tagListOfdepartment.find((item) => {
          // 查找对应每一级的部门
          if (item.tagCode.length === (i + 2) * 4) {
            // 找出对应级别部门名称一样， 且再已查找出的部门下级的
            return item.tagName === currDepartmentName && item.tagCode.indexOf(departmentTagCode) === 0;
          }
          return false;
        });
        if (currDepartmentTag) {
          departmentTagCode = currDepartmentTag.tagCode;
          if (i === departmentsNames.length - 1) {
            departmentTag = currDepartmentTag;
          }
        } else {
          // 找不到，直接中断
          return null;
        }
      }
      return departmentTag;
    },
    reset() {
      this.$refs.selector.reset();
    },
  },
};
</script>
