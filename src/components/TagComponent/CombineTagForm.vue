<!-- 标签组合查询组件 -->
<template>
  <div class="combine-tag-form">
    <span
      v-for="(item, index) in value"
      :key="index"
    >
      <SubjectTagTree
        hasCheckTagType
        ref="SubjectTagTree"
        :key="index"
        :defaultFoldAll="false"
        :disabledTagTypeIds="disabledTagTypeIds[index]"
        @change="checkTag(item, ...arguments)"
        @clear="clearTag(item)"
      />
      <span v-if="index !== value.length - 1" class="split-text">且</span>
    </span>
    <el-tooltip content="点击添加标签查询规则" placement="top-start">
      <el-button @click="addTag" class="el-icon-circle-plus-outline plus-btn" type="text"></el-button>
    </el-tooltip>

  </div>
</template>
<script>
import SubjectTagTree from '@/components/TagComponent/TagFormItem.vue';

export default {
  name: 'combine-tag-form',
  props: {
    value: {
      // 标签组合查询数据 {id: 标签id值或类型id值， 多个id用逗号分割； type: 1 标签id 2： 标签类型id}
      type: Array,
      default: () => [],
    },
  },
  components: { SubjectTagTree },
  data() {
    return {
      disabledTagTypeIds: {},
    };
  },
  computed: {
    subjectTagTypeList() {
      return this.$store.state.selectData?.subjectTag?.subjectTagTypeList || [];
    },
    subjectTagContentList() {
      return this.$store.state.selectData?.subjectTag?.subjectTagContentList || [];
    },
    tagMap() {
      return this.$store.state.selectData?.tagMap || new Map();
    },
  },
  watch: {
    value(val, oldVal) {
      if (val !== oldVal) {
        this.$nextTick(() => {
          this.setAllDefaultValue(val);
        });
      }
    },
  },
  methods: {
    addTag() {
      if (this.value.length >= 6) {
        this.$message.error('最多只能添加六组标签规则');
        return;
      }
      this.value.push({});
      this.getDisabledTagTypeIds();
    },
    //
    checkTag(tagFormItem, value, type = 'tagContent') {
      if (type === 'tagType') {
        tagFormItem.type = 2;
        tagFormItem.id = value;
      } else {
        tagFormItem.type = 1;
        tagFormItem.id = value;
      }
      this.getDisabledTagTypeIds();
    },
    clearTag(tagFormItem) {
      tagFormItem.id = '';
      tagFormItem.code = '';
      this.getDisabledTagTypeIds();
    },

    getDisabledTagTypeIds() {
      this.value.forEach((tagFormItem, index) => {
        const others = this.value.filter((item) => item !== tagFormItem && item.id).map((item) => {
          if (item.type === 2) {
            return item.id;
          }
          return this.tagMap.get(item.id.split(',')[0])?.tagTypeId || '';
        });
        this.$set(this.disabledTagTypeIds, index, others);
      });
      this.$emit('changeVal');
    },
    setAllDefaultValue(data) {
      this.$refs.SubjectTagTree.forEach((vm, index) => {
        console.log(data, index);
        const itemValue = data[index];

        if (!itemValue) return;
        vm.setDefaultValue(itemValue.id, itemValue.type === 2);
      });
      this.getDisabledTagTypeIds();
    },
  },
};
</script>
<style lang="scss" scoped>
.combine-tag-form{
  // padding-top: 2px;
  .split-text{
    line-height: 40px;
    // vertical-align: middle;
    display:inline-block;
    margin-right: 8px;
  }
  .plus-btn{
    font-size: 16px;
  }
}
</style>
