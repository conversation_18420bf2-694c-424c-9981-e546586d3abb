<template>
  <span>
    <span v-for="(item, index) in tagItems" :key="item.tagId" >
      <span :class="{'Danger': isDeleteTag(item)}">
        {{item.tagFullName}}
        <span v-if="isDeleteTag(item)">(已删除)</span>
      </span>

      <span v-if="index !== tagItems.length - 1">;</span>
    </span>
  </span>
</template>
<script>
import { getTagFullName } from '@/filters/tagIdToTagName';
import store from '@/store';

export default {
  props: {
    tagIds: {
      type: [String, Array],
      default: '',
    },
  },
  computed: {
    tagMap() {
      return store.state.selectData.tagMap || new Map();
    },
    tagItems() {
      if (!this.tagIds || this.tagIds.length === 0) return [];
      const tagIds = Array.isArray(this.tagIds) ? this.tagIds : this.tagIds.split(',');
      return tagIds.map((tagId) => {
        const tag = this.tagMap.get(tagId);
        if (!tag) return {};
        const tagFullName = getTagFullName(tag);
        return {
          tagFullName,
          deleteFlag: tag.deleteFlag,
          tagId: tag.tagId,
        };
      }).filter((item) => item.tagId);
    },
  },
  methods: {
    isDeleteTag(tag) {
      return tag.deleteFlag === 1;
    },
  },
};
</script>
