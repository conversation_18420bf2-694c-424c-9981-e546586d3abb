<!--
  行内编辑表格组件
  集成了侧边编辑的删除按钮和撤销按钮功能
-->
<template>
    <sidebar-table :style="{overflow: actionCol ? 'hidden' : 'unset'}">
      <template v-slot:button="props" v-if="!actionCol && !hasChange() && tableComponent === 'el-table'">
        <slot name="row-button" v-bind="props">
          <div class="table-button">
            <i
              v-if="!(rowTemplate && props.index === 0) && !hideDeleteBtn && !disabled"
              class="fa fa-trash"
              aria-hidden="true"
              title="删除"
              @click="deleteRow(props.row)" >
            </i>
          </div>
        </slot>
      </template>
      <component
        :is="tableComponent"
        ref="table"
        v-bind="$attrs"
        v-on="$listeners"
        :data="tableData"
        @row-click="handleRowClick"
      >
        <template #default>
          <el-table-column label="操作" fixed align="center" width="80" v-if="actionCol">
            <template slot-scope="scope">
              <div class="actionCol-box">
                <i
                v-if="!(rowTemplate && scope.$index === 0) && !hideDeleteBtn"
                class="fa fa-trash"
                aria-hidden="true"
                title="删除"
                @click="deleteRow(scope.row)" >
              </i>
              <i
                v-show="editingRow === scope.row && hasChange()"
                class="fa fa-times-circle"
                aria-hidden="true"
                title="撤销"
                @click.stop="repealEditingRow()"></i>
              </div>

            </template>
          </el-table-column>
          <slot></slot>
        </template>
        <template #append>
          <slot name="append"></slot>
        </template>
      </component>
      <i
        v-show="hasChange()"
        v-if="!actionCol && tableComponent === 'el-table'"
        class="fa fa-times-circle repeal-row-btn"
        :style="repealBtnPosition"
        aria-hidden="true"
        title="撤销"
        @click.stop="repealEditingRow()"></i>
    </sidebar-table>

</template>
<script>
import { cloneDeep, some, isEqual } from 'lodash-es';

import SidebarTable from '@/components/SidebarTable/SidebarTable.vue';

export default {
  components: { SidebarTable },
  props: {
    tableComponent: {
      type: String,
      default: 'el-table',
    },
    // 新增行的模板数据，不传或者为null, 则表格没有新增行
    rowTemplate: {
      type: [Object, null],
      default: () => (null),
    },
    // 表格的数据
    data: {
      type: Array,
      required: true,
      default: () => [],
    },
    // 需要自定义的保存行的方法， 比如需要提交到后端保存  saveRowFn(row, isAddRow:boolean), 根据返回值判断是否保存成功
    saveRowFn: {
      type: Function,
    },
    // 校验行数据，return false 则不进行保存
    validRowFn: {
      type: Function,
    },
    // 需要自定义的删除行的方法， 比如需要提交到后端删除  deleteRowFn(row), 根据返回值判断是否删除成功
    deleteRowFn: {
      type: Function,
    },
    // 判断行是否能编辑 disableRowFn(row) return true 则不进行编辑
    disableRowFn: {
      type: Function,
    },
    disabled: {
      type: Boolean,
    },
    hideDeleteBtn: {
      type: Boolean,
    },
    actionCol: {
      type: Boolean,
    },
  },
  data() {
    return {
      backupRow: null, // 备份的行数据， 用于复原行， 以及验证行是否被修改过
      editingRow: null, // 当前正在编辑的行
      addRow: null, // 当前的新增行
      repealBtnPosition: {
        top: 0,
        left: 0,
      },
    };
  },
  watch: {
    data: {
      immediate: true,
      // 表格数据刷新时，需要重新插入新增行
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.resetAddRow();
          this.setEditIngRow(this.addRow);
        }
      },
    },
    rowTemplate: {
      deep: true,
      handler() {
        this.resetAddRow();
      },
    },
  },
  computed: {
    // 在表格中插入一条新增行
    tableData() {
      if (this.addRow !== null && !this.disabled) {
        return [this.addRow, ...this.data];
      }
      return [...this.data];
    },
  },
  mounted() {
    setTimeout(() => {
      // 监听用户点击， 在编辑行以外区域时，触发保存操作
      window.addEventListener('click', this.rowClickControl, { capture: true });
    }, 100);
  },
  activated() {
    setTimeout(() => {
      // 监听用户点击， 在编辑行以外区域时，触发保存操作
      window.addEventListener('click', this.rowClickControl, { capture: true });
    }, 100);
  },
  deactivated() {
    window.removeEventListener('click', this.rowClickControl, { capture: true });
  },
  beforeDestroy() {
    window.removeEventListener('click', this.rowClickControl, { capture: true });
  },
  methods: {
    resetAddRow() {
      const addRow = cloneDeep(this.rowTemplate);
      // 如果当前编辑行为新增行， 那么重置新增行后， 需要把编辑行指向新的新增行数据
      if (this.isAddRow(this.editingRow)) {
        this.setEditIngRow(addRow);
      }
      this.addRow = addRow;
    },
    isEditingRow(row) {
      return this.editingRow === row;
    },
    isAddRow(row) {
      console.log(this.addRow, row);
      return this.addRow && this.addRow === row;
    },
    repealEditingRow() {
      if (!this.editingRow) return;
      Object.keys(this.editingRow).forEach((key) => {
        this.editingRow[key] = this.backupRow[key];
      });
    },
    async handleRowClick(row, column, event) {
      console.dir(event.target);
      this.$emit('row-click', row, column, event);

      if (this.isEditingRow(row)) {
        return;
      }
      // 执行保存，保证正在编辑的行合法保存再编辑下条
      setTimeout(async () => {
        const saveFlag = await this.saveRow();
        if (saveFlag) {
          this.setEditIngRow(row);
        }
      }, 100);
    },
    setEditIngRow(row) {
      if (typeof this.disableRowFn === 'function' && this.disableRowFn(row)) return;
      if (this.disabled) {
        this.backupRow = null;
        this.editingRow = null;
        return;
      }
      this.backupRow = Object.freeze(cloneDeep(row));
      this.editingRow = row;
      // 行切换到编辑模式后， tr的高度可能会变化， 所以需要在渲染完成之后，再设置定位
      this.$nextTick(() => {
        this.setRepealBtnPosition(row);
      });
    },
    // 保存行数据
    saveRow() {
      if (this.hasChange()) {
        if (typeof this.validRowFn === 'function' && !this.validRowFn(this.editingRow)) return false;
        const isAddRow = this.isAddRow(this.editingRow);
        if (typeof this.saveRowFn === 'function') {
          this.saveRowFn(cloneDeep(this.editingRow), isAddRow);
        } else if (isAddRow) {
          this.data.push(cloneDeep(this.addRow));
          // 将行数据添加进表格数据后， 需要重置新增行
          this.resetAddRow();
        }
      }
      return true;
    },
    async deleteRow(row) {
      if (typeof this.deleteRowFn === 'function') {
        await this.deleteRowFn(row);
      } else {
        this.data.splice(this.data.indexOf(row), 1);
      }
    },
    rowClickControl(event) {
      if (!this.$refs.table) return;
      const $currentTable = this.$refs.table.$el;
      const $app = document.querySelector('#app');
      const { target } = event;
      // 是否点击了当前表格的行， 点击当前表格行的事件由其他函数处理，在这里应该忽略
      function isClickTbodyRow() {
        if (some(!$currentTable.querySelectorAll('.el-table__body'), (tbody) => tbody.contains(event.target))) return false;
        if ($(target).parents('tr.el-table__row').length !== 0) return true;
        return false;
      }
      if (isClickTbodyRow()) return;
      setTimeout(async () => {
        // 点击到特定按钮上时不要触发保存， 避免错误数据出现
        const excludeKeyword = [
          '确定',
          '取消',
          '保存',
          '保存草稿',
          '删除',
          '退出',
          '上一步',
          '下一步',
        ];
        const $target = event.target;
        if (excludeKeyword.indexOf($target.innerText) !== -1) {
          return;
        }
        if (!$app.contains(target)) return;
        // 执行保存，保证正在编辑的行合法保存再编辑下条
        const saveFlag = await this.saveRow();
        // 保存成功之后， 激活新增行 为当前编辑行
        if (saveFlag) {
          this.setEditIngRow(this.addRow);
        }
      }, 50);
    },
    setRepealBtnPosition(row) {
      const rowIndex = this.tableData.indexOf(row);
      if (rowIndex === -1 || !this.$refs.table) return;
      // 获取编辑行相对于tbody的定位
      const rowEl = this.$refs.table.$el.querySelector(`tr.el-table__row:nth-child(${rowIndex + 1})`);
      console.log(rowEl);
      const rowTop = rowEl.offsetTop;
      const rowHeight = rowEl.offsetHeight;
      const header = this.$refs.table.$el.querySelector('.el-table__header-wrapper');
      this.repealBtnPosition.top = `${rowTop + header.offsetHeight + rowHeight / 2}px`;
    },
    // 判断编辑行是否被修改过
    hasChange() {
      // console.log(this.editingRow, this.backupRow, isEqual(this.editingRow, this.backupRow))
      return !isEqual(this.editingRow, this.backupRow);
    },
    clearSelection() {
      this.$refs.table.clearSelection();
    },
    doLayout() {
      this.$refs.table.doLayout();
    },
  },
};
</script>

<style lang="scss" scoped>
.repeal-row-btn{
  position: absolute;
  top: 0;
  left: 0;
  transform: translate(-100%, -50%);
}
.actionCol-box{
  text-align: center;
  display: flex;
  justify-content: space-around;

  height: 100%;
  i{
    cursor: pointer;
    color: #c0192a;
    line-height: 24px;
  }
}
</style>
