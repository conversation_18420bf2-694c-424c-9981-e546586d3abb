<!--
 * @Description:
 * @version:
 * @Company: 海闻软件
 * @Author: 启旭
 * @Date: 2019-12-30 19:47:06
 * @LastEditors: 启旭
 * @LastEditTime: 2019-12-30 21:03:09
 -->
<template>
  <div class="notice">
    <div class="notice_title" v-if="tabListData.length === 0">
      <span>暂无消息通知…</span>
    </div>
    <div style="padding: 10px">
      <el-tabs v-model="activeName">
        <el-tab-pane v-for="tab in tabListData" :key="tab.id" :name="tab.id">
          <template #label>
            <p class="text-center">{{tab.name}}({{tab.list.length}})</p>
          </template>
          <div class="notice_body">
            <ul>
              <li v-for="(item, index) in tab.list" :key="index" class="notice_body_item">
                <div class="notice_content" :title="item.msgTitle"> {{ item.msgTitle }} </div>

                <div
                :class="['notice_details', item === expandingItem ? 'active':'hidden']"
                >
                  <span v-if="item.msgType === 'M001'">
                    <p class="" :key="subject.subjectName" v-for="subject in formatSubjectMessage(item.msgContent)">
                      <span>{{subject.subjectName}}</span>
                      <span class="Success" v-if="subject.subjectUnit"> | 单位：{{subject.subjectUnit}}</span>
                    </p>
                  </span>
                  <span v-else>
                    <div v-if="item.targetType === 'SYS'" v-dompurify-html="item.msgContent" class="mainHtml"></div>
                    <span v-else>
                      {{item.msgContent}}
                    </span>

                    <!--  -->
                  </span>

                </div>
                <div class="notice_footer">
                  <el-button type="text" @click="handleExpandClick(item)">
                    {{ item === expandingItem ? '收起' : '展开'}}
                  </el-button>
                  <el-button type="text" v-if="item.msgId" @click="setReadMessage(item)">
                    标记已读
                  </el-button>
                  <span class="notice_time">{{ item.msgTime | timestamp2Natlang }}</span>
                </div>
              </li>
            </ul>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div class="notice_body_footer">
      <el-button @click="showAllMessage" type="text">查看所有</el-button>
      <el-button type="text" v-if="tabListData.length !== 0" @click="setAllRead">全部标记已读</el-button>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    // sysTips: {
    //   type: Array,
    //   required: true,
    // },
    targetType: {
      type: String, // BK&USER or COMPANY  代账公司和用户消息 或者 账套消息
    },
    tabListData: {
    },
  },

  data() {
    return {
      expandingItem: null,
      activeName: 'USER',
    };
  },

  watch: {
    tabListData(val, oldVal) {
      if (val !== oldVal && this.tabListData.length) {
        if (this.activeName === '' || !this.tabListData.find((item) => item.id === this.activeName)) {
          this.activeName = this.tabListData[0].id;
        }
      }
    },
  },

  methods: {
    handleExpandClick(item) {
      this.expandingItem = this.expandingItem === item ? null : item;
    },
    // 查看所有消息为跳转到消息中心
    showAllMessage() {
      this.$router.push({ name: 'messageCenter' });
    },
    // 将消息设置为已读
    setReadMessage(message) {
      this.$emit('setReadMessage', message);
    },
    // 将所有消息设置为已读
    setAllRead() {
      this.$emit('setAllRead');
    },
    // 格式化科目提示内容
    formatSubjectMessage(msgContent) {
      const subjectList = msgContent.split(String.fromCharCode(27)).map((item) => {
        const [subjectName, subjectUnit] = item.split(String.fromCharCode(31));
        return { subjectName, subjectUnit };
      });
      return subjectList;
    },
  },
};
</script>

<style scoped lang="scss"  >
.notice {
  .notice_title{
    height: 40px;
    font-size: 14px;
    background: #EBEDF1;
    padding: 0 10px;
    line-height: 40px;
  }
  &::v-deep .el-tabs__nav-wrap::after{
    z-index: -1;
  }
  .notice_body{
    width: 100%;
  }
  .notice_body_footer{
    height: 30px;
    line-height: 30px;
    padding-left: 10px;
  }
  ul{
    max-height: 600px;
    overflow-y: auto;
    li.notice_body_item {
      padding: 10px;
      border-bottom: 1px solid #EBEDF1;
      overflow-y: auto;
      .notice_content{
        color: #2d4d7b;
        word-break: keep-all;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

      }
      .notice_footer {
        text-align: right;
        .notice_expand {
          color: #2d4d7b;
          cursor: pointer;
        }
        .notice_time{
          margin-left: 10px;
        }
      }
      .notice_details {
        // max-height: 300px;
        transition: max-height .3s cubic-bezier(0, 0, 1, 1);

        cursor: pointer;
        max-height: 50px;
      }
      .notice_details.hidden {
        overflow : hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
      .notice_details.active {
        overflow-y: auto;
        max-height: 300px;
      }
    }
  }
}
</style>
