<!--
 * @LastEditors: 肖泽涛
 * @Author: 肖泽涛
 * @Description: 登录弹窗，当登录失效是随时打开
 * @Date: 2019-03-29 10:24:00
 * @LastEditTime: 2019-03-29 10:40:37
 -->
<template>
  <div class="relogin_dialog_body">
    <div class="login_box">
      <el-tabs type="card" class="login_box_main" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane id="qr_login" class="qr_login" label="扫码登录" name="qr_login" >
          <img style="width:200px;height:200px;padding:16px"  class="qr_img" alt="二维码">
          <div class="wechat_text"><i class="iconfont icon-weixin2"></i>微信扫码登录</div>
        </el-tab-pane>
        <el-tab-pane label="密码登录" name="pw_login" class="pw_login">
          <div class="row">
            <el-input
            name="token"
            size="large"
            placeholder="手机/已认证邮箱"
            class="login_input_token login_input_stand"
            v-model="token"
            :maxlength="50">
              <template slot="prepend"><i class="fa fa-user-o"></i></template>
            </el-input>
          </div>
          <div class="row">
            <el-input
            type="password"
            name="password"
            size="large"
            placeholder="登录密码"
            class="login_input_password login_input_stand"
            v-model="password"
            :maxlength="50"
            @keyup.enter.native="login">
              <template slot="prepend">
                <i class="fa fa-lock" style='font-size: 21px;'></i>
              </template>
            </el-input>
          </div>
          <div class="row clearfix" v-if="showCaptcha">
            <div class="login_input_verify">
              <el-input
              name="verify"
              size="large"
              placeholder="请输入验证码"
              class="login_input_stand"
              v-model="verify"
              :maxlength="4"
              @keyup.enter.native="login"></el-input>
              <span>请按箭头方向依次输入4位验证码</span>
            </div>
            <img class="login_img_verify" :src="captcha" alt="验证码">
            <span class="login_span_verify" title="刷新验证码">
              <i
              class="fa fa-refresh fa-2x"
              aria-hidden="true"
              @click="captcha =
              `/auth/generate/captcha?t=${Math.random()}&codeType=authServer:login:captcha`"></i>
            </span>
          </div>
          <div>
            <span style="color:#c0192a">{{loginError}}</span>
          </div>
          <div class="row row_opt clearfix">
            <el-checkbox
            class="login_checkbox"
            v-model="remberMe"
            @change="doRemberMe">记住登录信息</el-checkbox>
          </div>
          <div class="row">
            <el-button type="primary" @click="login" class="login_btn_lg">登录</el-button>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      token: '',
      password: '',
      remberMe: true,
      activeName: 'pw_login',
      remberPw: true,
      verify: '',
      loginError: '',
      showCaptcha: false,
      captcha: '/auth/generate/captcha',
    };
  },
  computed: {

  },
  components: {
  },
  mounted() {
    // 初始化微信扫码登录
    const obj = new WxLogin({
      id: 'qr_login', // div的id
      appid: 'wx54fdc467c0004e20',
      scope: 'snsapi_login,snsapi_userinfo', // 写死
      response_type: 'code',
      // redirect_uri: encodeURI(`${ this.$store.state.webBasePath }/auth/wechat/login`),
      redirect_uri: encodeURI('https://www.quickf.cn/auth/wechat/login'),
      state: 'STATE',
      style: 'black', // 二维码黑白风格
      href: encodeURI(this.$store.state.wxQrStylePath),
    });
    console.log(obj);
    this.initRemberMe();
  },
  methods: {
    doRemberMe() {
      if (this.remberMe) {
        localStorage.setItem('token', this.token);
        localStorage.setItem('password', this.password);
      } else {
        localStorage.removeItem('token');
        localStorage.removeItem('password');
      }
    },
    // 获取用户在本地存储的密码
    initRemberMe() {
      if (localStorage.getItem('token') && localStorage.getItem('token') !== '') {
        this.token = localStorage.getItem('token');
        this.password = localStorage.getItem('password');
        this.remberMe = true;
      } else {
        this.password = '';
        this.remberMe = false;
      }
    },
    async login(url) {
      const currUserId = this.$store.state.user.userInfo.userId;
      this.loginError = '';
      if (this.token === '') {
        this.loginError = '账号不能为空';
        return;
      }
      if (this.password === '') {
        this.loginError = '密码不能为空';
        return;
      }
      if (this.verify === '' && this.showCaptcha) {
        this.loginError = '请输入验证码';
        return;
      }
      this.doRemberMe();
      this.noCertificate = false;
      if (typeof url === 'string' && this.$store.state.explorer === 'ie') {
        await this.$http.get(url);
      }
      this.$http.post(`${typeof url === 'string' ? url : '/auth/login/qf'}?username=${this.token}&password=${this.password}&captcha_code=${this.verify}`).then(async () => {
        const userinfo = await this.$http.get('/auth/usercenter/userinfo');
        const userData = userinfo.data;
        this.$store.state.showLogin = false;
        if (userData.selectCompanyId) {
          if (currUserId !== userData.userId) {
            this.$router.push('/manager/guidance');
            this.$nextTick(() => {
              window.location.reload();
            });
            return;
          }
          this.$confirm('登录成功，是否刷新本页面?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(() => {
            window.location.reload();
          }).catch(() => {
          });
        } else if (userData.userType === 2) { // 莲蓉
          this.$router.push('/manager/companys');
        } else {
          this.$router.push({ name: 'booksSetList' });
        }
      }, (err) => {
        const { data } = err;
        if (data.error) {
          const errorMsgs = data.error.split('|');
          console.log(errorMsgs);
          if (errorMsgs[0] === 'invalid_spsoft_auth0092') {
            const host = window.location.host.replace('www', 'user');
            this.login(`https://${host}/auth/login/qf`);
          }
          if (errorMsgs[1] === 'captcha') {
            this.showCaptcha = true;
          }
          if (this.showCaptcha) {
            this.captcha = `/auth/generate/captcha?t=${Math.random()}&codeType=authServer:login:captcha`;
          }
          this.loginError = data.error_description;
        }
      });
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
  },
};
</script>

<style lang="scss"  >
$link-color: #2d4d7b;
$imgs: "/static/imgs";

%half-float {
  width: 50%;
  float: left;
}

a {
  color: $link-color;
}

.wechat_text {
  color:#333;
  font-size: 18px;
  line-height: 50px;
  i {
    color: #44b549;
    font-size: inherit;
    margin-right: 3px;
  }
}

.login_input_stand  {
    input.el-input__inner {
      height: 48px;
      border-radius: 0;
      font-size: inherit;
    }
}

/*主体样式*/

.relogin_dialog_body {
  padding-top: 10px;
  .login_box {
    width:480px;
    margin:0 auto;
    background:#fff;
    padding-bottom: 30px;
    .el-tabs {
      padding: 0 30px;
      .el-tabs__header {
        .el-tabs__nav {
          margin-top: 5px;
          width: 100%;
          .is-active{
            color:#ff5629;
          }
          .el-tabs__active-bar {
            height: 2px;
            background:#ff5629;
          }
          .el-tabs__item {
            width: 50%;
            text-align: center;
            font-size: 18px;
            margin-bottom:6px;
          }
        }
      }
      .el-tabs__content .el-tab-pane.qr_login {
        text-align: center;
        .qr_text {
          font-size: 1.4em;
          font-weight: bold;
          line-height: 40px;
          padding-bottom: 10px;
        }
        .qr_state {
          padding: 10px 0px;
          >img {
            vertical-align: middle;
          }
        }
      }
      .el-tabs__content .el-tab-pane {
        // height: 310px;
        overflow: hidden;
        .row {
          padding: 8px 0;
          .login_input_token,.login_input_password{
            font-size: 16px;
            height: 48px;
          }
          .login_input_password
          .el-input-group__prepend,.login_input_token .el-input-group__prepend {
            padding-right: 11px;
            padding-left: 11px;
            font-size: 16px;
            height:46px;
           border-radius: 0;
          }
          .login_input_verify {
            width: 40%;
            float: left;
            span {
              font-size: 12px;
            }
          }
          .login_img_verify {
            display: block;
            float: left;
            width: 90px;
            height: 90px;
            margin-left: 5%;
          }
          .login_span_verify {
            display: block;
            float: left;
            width: 20%;
            margin-left: 5%;
            padding-top: 10px;
            font-size: 10px;
            color: rgb(131, 145, 165);
            cursor: pointer;
            i.fa-refresh {
              transition: transform 0.5s;
              &:hover {
                -webkit-transform: rotate(360deg);
              }
            }
          }

          .login_btn_lg {
            width: 100%;
            padding: 12px 15px;
            height:48px;
            // line-height:40px;
            font-size: 16px;
            background:#ff5629;
            border-color:#ff5629;
            border-radius: 0;
          }
        }
        .row.row_opt {
          padding: 10px 8px 20px 8px;
          .login_checkbox {
            .half-float{
              @extend %half-float;
            }
            display: block;
            span.el-checkbox__inner{
              background:#44b549;
              border-color: #6fd474;
            }
          }
        }
      }
    }
  }
}
</style>
