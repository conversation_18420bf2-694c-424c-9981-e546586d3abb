<!--
 * @LastEditors: 启旭
 * @Author: 肖泽涛
 * @Description: 全局跨月属期控件
 * @Date: 2019-03-20 16:51:45
 * @LastEditTime: 2020-06-29 09:58:46
 -->
<template>
    <el-date-picker
      :clearable='false'
      :disabled="disabled"
      v-model="monthRange"
      type="monthrange"
      :editable='false'
      :picker-options="$store.getters.pickerOptionsForAccountDateAndNowDate"
      @change='handlerChange'
    >
    </el-date-picker>
</template>

<script>
export default {
  props: {
    incomingDate: {
      type: Array,
      default() {
        return [this.$store.state.user.accountPeriod, this.$store.state.user.accountPeriod];
      },
    },
    // 是否允许跨年(标签汇总表需要跨年查询)
    crossYear: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
    };
  },
  computed: {
    monthRange: {
      get() {
        return this.incomingDate;
      },
      set(val) {
        this.$emit('update:incomingDate', val);
      },
    },
    // 当前账套建账月
    accountDate() {
      return new Date(this.$store.state.user.companyInfo.accountDate);
    },
  },
  methods: {
    handlerChange(val) {
      if (val[0] && val[1]) {
        if (val[0].Format('yyyy') !== val[1].Format('yyyy')) {
          this.monthRange = [val[0], val[0]];
          this.$message.warning('日期选择范围暂不支持跨越年份');
          this.$emit('reset-date');
          return;
        }
      }
      this.$emit('change');
    },
  },
};
</script>
