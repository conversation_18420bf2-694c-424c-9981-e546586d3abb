<!--
 * @LastEditors: 肖泽涛
 * @Author: 肖泽涛
 * @Description: 省市区选择组件
 * @Date: 2019-03-29 09:45:26
 * @LastEditTime: 2019-03-29 09:53:21
 -->
<template>
  <div style="display:inline-block">
    <el-select
     v-model="meprovinceCode"
     :disabled="disabled || disabledProvinceCode"
     placeholder="----省----"
     style="width:130px">
      <el-option
        v-for="item in provinces"
        :label="item.provinceName"
        :value="item.provinceCode"
        :key="item.provinceCode"
        >
      </el-option>
    </el-select>
    <el-select
    v-model="mecityCode"
    :disabled="disabled || disabledCityCode"
    placeholder="----市----"
    style="width:100px">
      <el-option
        v-for="item in cities"
        :label="item.cityName"
        :value="item.cityCode"
        :key="item.cityCode"
        >
      </el-option>
    </el-select>
    <el-select
    v-if="!noMedistrict"
    v-model="medistrictCode"
    :disabled="disabled || disabledDistrictCode"
    placeholder="----区----"
    style="width:100px">
      <el-option
        v-for="item in districts"
        :label="item.districtName"
        :value="item.districtCode"
        :key="item.districtCode"
        >
      </el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  props: {
    provinceCode: { type: String },
    cityCode: { type: String },
    districtCode: { type: String },
    noMedistrict: { type: Boolean }, // 是否需要到区
    disabled: { type: Boolean, default: false },
    disabledProvinceCode: { type: Boolean, default: false },
    disabledCityCode: { type: Boolean, default: false },
    disabledDistrictCode: { type: Boolean, default: false },

  },
  data() {
    return {
      provinces: [],
      cities: [],
      districts: [],
      options2: [],
    };
  },
  mounted() {
    this.getprovinces();
    if (this.meprovinceCode) {
      this.$http
        .get(
          `/rest/global/dimensionality/locality/v0.1/cities/${
            this.meprovinceCode}`,
        )
        .then((res) => {
          const { data } = res.data;
          this.cities = data;
        });
      if (this.mecityCode) {
        this.$http
          .get(
            `/rest/global/dimensionality/locality/v0.1/districts/${
              this.mecityCode}`,
          )
          .then((res) => {
            const { data } = res.data;
            this.districts = data;
          });
      }
    }
  },
  computed: {
    meprovinceCode: {
      get() {
        console.log(this.provinceCode);
        return this.provinceCode || '';
      },
      set(val) {
        this.$emit('update:provinceCode', val);
      },
    },
    mecityCode: {
      get() {
        return this.cityCode || '';
      },
      set(val) {
        this.$emit('update:cityCode', val);
      },
    },
    medistrictCode: {
      get() {
        return this.districtCode || '';
      },
      set(val) {
        this.$emit('update:districtCode', val);
      },
    },
  },
  watch: {
    meprovinceCode(val, oldVal) {
      if (val !== oldVal) {
        this.getcities();
      }
    },
    mecityCode(val, oldVal) {
      if (val !== oldVal) {
        this.getdistricts();
        const params = {
          provinceName: this.provinces.find((item) => item.provinceCode === this.meprovinceCode)?.provinceName,
          cityName: this.cities.find((item) => item.cityCode === this.mecityCode)?.cityName,
        };
        this.$emit('getCityName', params);
      }
    },
    medistrictCode(val, oldVal) {
      if (val !== oldVal) {
        const params = {
          provinceName: this.provinces.find((item) => item.provinceCode === this.meprovinceCode)?.provinceName,
          cityName: this.cities.find((item) => item.cityCode === this.mecityCode)?.cityName,
          districtName: this.districts.find((item) => item.districtCode === this.medistrictCode)?.districtName,
        };
        this.$emit('getAddress', params);
      }
    },
  },
  methods: {
    // 获取城市列表
    getcities() {
      if (this.mecityCode.indexOf(this.meprovinceCode.slice(0, 2)) !== 0) {
        this.mecityCode = '';
        this.medistrictCode = '';
      }
      if (!this.meprovinceCode) {
        this.cities = [];
        return;
      }
      this.$http.get(`/rest/global/dimensionality/locality/v0.1/cities/${this.meprovinceCode}`)
        .then((res) => {
          const { data } = res.data;
          this.cities = data;
        });
    },
    // 获取区列表
    getdistricts() {
      if (this.noMedistrict || !this.mecityCode) {
        this.districts = [];
        return;
      }
      if (this.medistrictCode.indexOf(this.mecityCode.slice(0, 4)) !== 0) {
        this.medistrictCode = '';
      }
      this.$http
        .get(
          `/rest/global/dimensionality/locality/v0.1/districts/${
            this.mecityCode}`,
        )
        .then((res) => {
          const { data } = res.data;
          this.districts = data;
        });
    },
    // 获取省份列表
    getprovinces() {
      this.$http
        .get('/rest/global/dimensionality/locality/v0.1/provinces')
        .then((res) => {
          const { data } = res.data;
          this.provinces = data;
        });
    },
  },
};
</script>

<style scoped>
</style>
