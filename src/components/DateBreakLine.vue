<template>
  <div class="date-break-line">
    <div class="date-break-line-text">{{ day }}</div>
    <div class="date-break-line-text">{{ time }}</div>
  </div>
</template>
<script setup>
import dayjs from 'dayjs';
import { computed, defineProps } from 'vue';

const props = defineProps({
  date: {
    type: String,
    default: '',
  },
});

const day = computed(() => dayjs(props.date).format('YYYY-MM-DD'));
const time = computed(() => dayjs(props.date).format('HH:mm:ss'));

</script>
