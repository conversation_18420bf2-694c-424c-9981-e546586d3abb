<!--
 * @LastEditors: 晓荣
 * @Author: 肖泽涛
 * @Description: 科目条件搜索组件
 * @Date: 2019-03-23 11:08:00
 * @LastEditTime: 2021-12-30 10:24:43
 -->

<template>
  <el-popover
    class="el_popover_area"
    placement="bottom-start"
    popper-class="tree_popover"
    v-model="visible"
    trigger="click"
  >
    <div class="header clearfix">
      <div v-show="showType === 'range'">
        <div>
          <div>
            起始科目：
            <subject-select
              no-disabled-item
              :show-levels="showLeves"
              :noParent="false"
              :filterSubjectCodes="filterSubjectCodes"
              :hasLocalMemory="false"
              :value.sync="startSubjectCode"
            ></subject-select>
          </div>
          <div style="margin-top: 5px;">
            结束科目：
            <subject-select
              :filterSubjectCodes="filterSubjectCodes"
              :noParent="false"
              :show-levels="showLeves"
              no-disabled-item
              :hasLocalMemory="false"
              :value.sync="endSubjectCode"
            ></subject-select>
          </div>
        </div>
        <div style="margin-top:10px;padding:0 10px;">
          <el-button
            type="primary"
            @click="handleSearch"
            style="width:100%"
          >
            <span><i class="el-icon-search"></i> {{ isSearch ? '搜索' : '确定'}}</span>
          </el-button>
        </div>
        <div style="margin-top:5px;padding:0 10px;">
          <el-button
            @click="changeShowType"
            style="width:100%;"
          >
            <span>
              <i
                class="fa fa-exchange"
                style="font-size:16px;"
                aria-hidden="true"
              ></i>
              切换至多项搜索</span>
          </el-button>
        </div>
      </div>
      <div
        v-show="showType === 'tree'"
        class="treebox"
      >
        <!-- <div class="content clearfix">
          <div class="tree_box">
            <div class="tree_box_header">
              <slot name="filter">
                <el-input
                v-model.trim="filterMsg"
                placeholder="输入代码、名称后点击图标或按下Enter键"
                @keyup.enter.native="filterSubjectByClient">
                <i slot="suffix"
                class="el-input__icon el-icon-search"
                style='cursor:pointer'
                @click="filterSubjectByClient"></i></el-input>
              </slot>
            </div>
             <el-tree
             ref="tree"
             class="tree"
             :data="treeData"
             :props="treeProps"
             node-key="subjectCode"
             @node-click="nodeClick"
             check-strictly
             show-checkbox
             @check-change="handleCheckChange"
             :default-expanded-keys="defaultExpandedKeys"
             :filter-node-method="filterNode"></el-tree>
            <div style="margin-top:5px;padding:0 10px;">
              <el-button type="warning" @click="handleSearch" style="width:100%">
                <span><i class="el-icon-search"></i> {{ isSearch ? '搜索' : '确定'}}</span>
              </el-button>
            </div>
          </div>
          <div class="tree_box">
            <div class="clearfix tree_box_header">
              <div class="fl" style="font-size: 14px;line-height:26px;">
                <strong>已选择科目</strong>
                <span style="color:#66ccff">{{$refs.tree && selectSubjectCodesLength}}/200</span>
              </div>
              <div  class="fr">
                <el-button @click="cleanTree()" type="text">
                  <i class="fa fa-trash-o" aria-hidden="true"></i>
                  清空已选择
                </el-button>
              </div>
            </div>
            <ul class="chosen_node_list">
              <template v-if="$refs.tree">
                <li v-for="node in selectSubjectCodes" class="chosen_node" :key="node.subjectId">
                  <span :title="node.subjectName">{{ node.subjectName }}</span>
                  <i class="el-icon-close" @click="$refs.tree.setChecked(node, false);"></i>
                </li>
              </template>
            </ul>
            <div style="margin-top:5px;padding:0 10px;">
                  <el-button @click="changeShowType" style="width:100%; background:#ddd" >
                    <span>
                      <i class="fa fa-exchange" style="font-size:16px;" aria-hidden="true"></i>
                      切换至区间搜索
                    </span>
                  </el-button>
              </div>
          </div>
        </div> -->
        <TreeSelect
          ref="tree"
          :data="treeData"
          id-field="subjectCode"
          parent-id-field="subjectParentCode"
          :is-root="isRoot"
          :maxLength="200"
          :filterMethod="filterMethod"
          :fullNameMethod="fullNameMethod"
          v-model="selectSubjectCodes"
          :beforeCheck="beforeCheck"
          :default-expanded-keys="defaultExpandedKeys"
          :labelMethod="(data) => `${data.subjectCode} ${data.subjectFullName}`"
        />
        <div style="margin-top:5px;padding:0 10px;">
          <el-button type="primary" @click="handleSearch" style="width:100%">
            <span><i class="el-icon-search"></i> {{ isSearch ? '搜索' : '确定'}}</span>
          </el-button>
        </div>
        <div style="margin-top:5px;padding:0 10px;" v-if="showIntervalSearch">
          <el-button
            @click="changeShowType"
            style="width:100%;"
          >
            <span>
              <i
                class="fa fa-exchange"
                style="font-size:16px;"
                aria-hidden="true"
              ></i>
              切换至区间搜索
            </span>
          </el-button>
        </div>
      </div>
    </div>
    <template slot="reference">
      <el-badge
        class="tree_badge"
        :value="selectSubjectCodesLength"
        :max="99"
      >
        <el-button type="primary">选择科目</el-button>
      </el-badge>
    </template>
  </el-popover>
</template>

<script>
import { isEqual } from 'lodash-es';
import SubjectSelect from '@/components/commonSelect/SubjectSelect.vue';
import TreeSelect from '@/components/TreeSelect/index.vue';

/**
 * 过滤出需要搜索的科目，
 * 业务规则限定 当下级科目被选中时，上级科目不需要添加到搜索条件中
 */
function filterCodes(codes = []) {
  // 抽出二级科目和三级科目
  const copyCodes = codes.filter((item) => item.length >= 8);
  return codes.filter((code) => {
    if (code.length === 12) {
      return true;
    }
    // 当下级科目被选中时，上级科目排除
    return !copyCodes.find(
      (item) => item.length > code.length && item.indexOf(code) === 0,
    );
  });
}
export default {
  props: {
    expandedKey: {
      // 默认展开的树
      type: Array,
      default: () => [],
    },
    showLeves: {
      // 科目树级数过滤
      type: Array,
      default: () => [1, 2, 3],
    },
    filterSubjectCodes: {
      // 过滤需要的科目数据， 传入科目代码，默认为空，不过滤
      type: Array,
      default: () => [],
    },
    subjectBeginCode: {
      // 科目范围搜索的起始字段
      type: String,
      default: '',
    },
    subjectCodes: {
      // 科目多选搜索字段
      type: Array,
      default: () => [],
    },
    subjectEndCode: {
      // 科目范围搜索结束字段
      type: String,
      default: '',
    },
    canSelectLeve1: {
      // 科目树是否能选择一级
      type: Boolean,
      default: false,
    },
    isSearch: {
      // 是否是搜索
      type: Boolean,
      default: true,
    },
    showIntervalSearch: {
      // 是否有区间搜素
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      filterMsg: '', // 过滤树查询字段
      showType: 'tree', // 展示状态 range 范围搜索  tree 树搜索
      visible: false, // 弹窗展示状态
      defaultExpandedKeys: [...this.expandedKey], // 默认展示的树结构
      treeData: [], // 树列表
      treeProps: {
        // 树的字段属性
        label: 'subjectNameCode',
        children: 'children',
      },
      selectSubjectCodes: [],
    };
  },

  components: { SubjectSelect, TreeSelect },

  computed: {
    subjectList() {
      return this.$store.state.selectData.subjectList;
    },
    startSubjectCode: {
      get() {
        return this.subjectBeginCode;
      },
      set(val) {
        this.$emit('update:subjectBeginCode', val);
      },
    },
    endSubjectCode: {
      get() {
        return this.subjectEndCode;
      },
      set(val) {
        this.$emit('update:subjectEndCode', val);
      },
    },
    selectSubjectCodesLength() {
      return this.selectSubjectCodes.length;
    },
  },

  watch: {
    // 显示状态切换时，需要把隐藏模式的数据清空
    showType(val) {
      if (val === 'tree') {
        this.startSubjectCode = '';
        this.endSubjectCode = '';
      } else {
        this.selectSubjectCodes = [];
        this.$emit('update:subjectCodes', []);
      }
      this.$emit('update:showType', val);
    },
    subjectList() {
      this.filterSubject();
    },
    subjectCodes: {
      immediate: true,
      handler: function handleSubjectCodesChange(val) {
        if (!isEqual(this.selectSubjectCodes, val)) {
          this.selectSubjectCodes = [...val];
        }
      },
    },
    selectSubjectCodes(val) {
      this.$emit('update:subjectCodes', filterCodes(val));
    },
  },

  async mounted() {
    this.filterSubject();
    // 当外部传入数据时，显示树模式
    if (this.subjectCodes.length > 0) {
      this.showTree = true;
       this.selectSubjectCodes = [...this.subjectCodes];
    }
  },

  methods: {
    beforeCheck(checkedKey) {
      const flag = !this.selectSubjectCodes.find((item) => item !== checkedKey && item.indexOf(checkedKey) === 0);
      if (!flag) {
        this.$message.warning('当前选择的科目，存在已勾选的下级科目， 如需勾选请先取消已选择的下级科目');
      }
      return flag;
    },
    isRoot(item) {
      return item.data.subjectParentCode === 'top';
    },
    fullNameMethod(dataPath) {
      const data = dataPath.pop();
      return `${data.subjectCode} ${data.subjectFullName}`;
    },
    // 获取科目树数据
    filterSubject() {
      // 从vuex中获取缓存的科目列表
      let subjectList = this.subjectList || [];
      // 根据过滤条件过滤需要的数据
      subjectList = subjectList.filter((item) => {
        if (
          this.showLeves.find((leve) => leve === item.subjectCode.length / 4)
        ) {
          if (this.filterSubjectCodes.length > 0) {
            return this.filterSubjectCodes.find(
              (subjectCode) => item.subjectCode.indexOf(subjectCode) === 0,
            );
          }
          return true;
        }
        return false;
      });
      this.treeData = subjectList;
    },
    // 出发搜索
    handleSearch() {
      this.visible = false;
      this.$emit('update:showType', this.showType);
      this.$emit('search');
    },
    // 自定义科目树过滤方法
    filterMethod(data, value) {
      if (!value) return true;
      // 此处可与通过科目代码区间查询， 以-分割，起止
      const filters = value.split('-');
      if (
        filters.length === 2
        && /[1-9][0-9]{0,3}/.test(filters[0])
        && /[1-9][0-9]{0,3}/.test(filters[1])
      ) {
        const subjectCodeTop = Number(data.subjectCode.slice(0, 4));
        const min = Number(filters[0]);
        const max = Number(filters[1]);
        if (subjectCodeTop >= min && subjectCodeTop <= max) {
          // node.checked = true;
          return true;
        }
      }
      return (
        value === ''
        || data.subjectFullName.indexOf(value) > -1
        || data.subjectCode.indexOf(value) > -1
      );
    },
    // 切换显示模式
    changeShowType() {
      if (this.showType === 'tree') {
        this.showType = 'range';
      } else {
        this.showType = 'tree';
      }
    },
  },
};
</script>

<style scoped>
::-webkit-scrollbar-track-piece {
  background-color: white;
}
.tree {
  max-width: 400px;
}
</style>

<style>
.el_popover_area {
  display: inline-block;
  vertical-align: top;
  line-height: 1;
}

.tree_popover .header {
  margin-bottom: 10px;
}

.el-badge.tree_badge .el-badge__content.is-fixed {
  top: 0;
  right: 0;
  position: absolute;
  transform: translateY(-36%) translateX(40%);
}
</style>
