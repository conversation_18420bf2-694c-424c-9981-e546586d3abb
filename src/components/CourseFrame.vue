<!--
 * @Description:
 * @version:
 * @Company: 海闻软件
 * @Author: 启旭
 * @Date: 2019-09-29 17:09:00
 * @LastEditors: 启旭
 * @LastEditTime: 2019-10-25 10:14:25
 -->
<template>
  <div v-if="visible" id="coursePlayer"></div>
</template>

<script>
export default {
  props: {},
  data() {
    return {};
  },
  components: {},
  mounted() {
    const s = document.createElement('script');
    s.type = 'text/javascript';
    s.onerror = () => this.$message.error('优酷插件加载失败');
    s.onload = () => {
      this.$watch('visible', (show) => {
        if (show) {
          this.$nextTick(() => {
            const { videoId } = this;
            if (videoId) {
              const yku = new YKU.Player('coursePlayer', {
                client_id: '4c5b196122f07990',
                vid: videoId,
                newPlayer: true,
                autoplay: true,
              });
              console.log(yku);
            }
          });
        }
      }, { immediate: true });
    };
    s.src = 'https://player.youku.com/jsapi';
    document.body.appendChild(s);
  },
  computed: {
    visible() {
      return this.$route.query.visible;
    },
    videoId() {
      return this.$route.params.id;
    },
  },
  methods: {},
};
</script>

<style scoped>
  #coursePlayer {
    width: 100%;
    height: 100%;
  }
</style>
