<template>
  <keep-alive :include="cachedViews" :max="maxTagsView">
    <router-view :key="key"></router-view>
  </keep-alive>
</template>
<script>
import { get } from 'lodash-es';

export default {
  name: 'RouteView',
  computed: {
    key() {
      const { $route } = this;
      const { name } = $route;
      return (name || 'route');
    },
    maxTagsView() {
      return get(this.$store.state.app.appConfig, 'tagsView.max');
    },
    cachedViews() {
      return this.$store.state.tagsView.cachedViews;
    },
  },
};
</script>
