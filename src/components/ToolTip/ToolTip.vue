<template>
  <el-tooltip v-show="content" :effect="effect" :placement="placement" popper-class="help_tooltip">
    <div slot="content" class="content">{{ content }}</div>
    <slot name="carrier">
      <i class="fa fa-question-circle"></i>
    </slot>
  </el-tooltip>
</template>

<script>
import HelpText from './helpText';

const effects = ['dark', 'light'];
const placements = [
  'top', 'top-start', 'top-end', 'bottom', 'bottom-start', 'bottom-end',
  'left', 'left-start', 'left-end', 'right', 'right-start', 'right-end',
];

export default {
  props: {
    effect: {
      type: String,
      // default: 'light',
      validator(value) {
        return effects.indexOf(value) > -1;
      },
    },
    placement: {
      type: String,
      default: 'top',
      validator(value) {
        return placements.indexOf(value) > -1;
      },
    },
    contentType: {
      type: [String, Number],
      required: true,
    },
  },

  data() {
    return {};
  },

  computed: {
    content() {
      return HelpText[this.contentType];
    },
  },

  mounted() {},

  methods: {},
};
</script>

<style scoped>
  .content {
    width: 130px;
    line-height: 1.5;
  }
  .fa.fa-question-circle {
    color: rgb(204,204,204);
    font-size: 16px;
    cursor:help;
  }
</style>

<style lang="scss">
  .help_tooltip {
    background:#777 !important;
    .content {
      width: 280px;
    }
  }

  .el-tooltip__popper .popper__arrow,
  .el-tooltip__popper .popper__arrow:after {
    border-right-color: #777 !important;
  }
</style>
