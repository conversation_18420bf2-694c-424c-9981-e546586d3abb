export default {
  H0001:
    '目前系统提供10种币别可供选择，用户可在币别模块添加用户可能使用到的所有币别，将其从左边添加至右边即可。用户如有外销业务需首先添加币别，再前往科目管理，勾选对应的应收账款科目外币核算，并添加币别。',
  H0002:
    '用户可在“单位设置”界面，添加新的单位名称，并选择能否为小数。添加新单位后，用户方可进入“商品管理”界面录入商品名称。另外，用户也可在“销项”与“进项”模块逐个添加商品名称时，添加单位名称。',
  H0003:
    '费用类型管理”模块中，用户可新增费用类型。当然，也可在“进项”“资金”模块手动添加费用类型，对应的“费用类型管理”“科目管理”模块也会自动生成。',
  H0004:
    '公司基本信息界面，公司名称与所属行业在建立账套时已确定，不可修改。在此界面填写的其他基本信息，在税务申报首页可新增及修改。',
  H0005:
    '用户在“进项”模块生成凭证后，“供应商”模块相关信息自动生成，无需手动新增，大大节省用户时间。当然，用户也可选择在“供应商管理”页面对客户信息进行编辑。',
  H0006:
    '点击供应商名称可进供应商编辑界面，对供应商信息进行修改和补充。需要注意的是，系统默认状态栏为启用，用户也可依据实际情况，选择“失效”选项，不再使用该供应商。',
  H0007:
    '若生产新商品，在“加工环节维护”模块新增加工环节后，方可进行“商品构成”操作。加工环节状态可选择是否启用。',
  H0008:
    '”科目管理”可添加二级与三级科目，在“商品管理”、“费用类型管理”、“供应商管理”及“客户管理”等模块新增相关信息，相对应“科目管理”模块自动生成二级科目。例如，在“商品管理”新增库存商品名称，可生成“库存商品—商品名称”二级科目。',
  H0009:
    '用户在“销项”模块生成凭证后，“客户管理”模块相关信息自动生成，无需手动新增，大大节省用户时间。当然，用户也可选择在“客户管理”页面对客户信息进行编辑。',
  H0010:
    '在“客户管理”界面，用户可在左上方搜索栏，搜索目标客户。界面的右上方绿色按钮点击可“新增客户”。',
  H0011:
    '点击客户名称可进入客户编辑界面，对客户信息进行修改和补充。需要注意的是，系统默认状态栏为启用，用户也可依据实际情况，选择“失效”选项，不再使用该客户。',
  H0012:
    '用户期初余额数据来自以前会计周期期末余额，用户若需要修改，可点击期初余额与数量修改数据。用户需要关注界面右上方借贷是否平衡。',
  H0013:
    '商品管理包括“原材料”“半成品”“库存商品”。用户可选择在商品管理界面点击“新增”手动输入商品名称。',
  H0014:
    '新商品较多时，用户可以点击界面右上方“导入”按钮，下载商品导入模板，批量导入商品名称，“科目管理”模块自动生成二级科目。新商品较少时，用户可以选择直接在“销项”与“进项”模块发票清单导入后，补充发票明细时，直接逐个手动添加商品名称及单位名称，对应的“科目名称”“单位名称”“商品名称”自动生成。',
  H0015: '点击商品编号，可对商品基本信息进行修改。库存商品及半成品也可在此界面编辑材料与工序。',
  H0016:
  '银行账户界面可查看用户当前银行账户及现金账户，并对其进行编辑。银行账户对于系统的直接影响有两个。其一，如果没有录入相应的银行账户，用户无法导入银行流水。其二，录入的银行账户影响“税务”申报，不可在申报界面修改。用户只能选择一个开票账户，账户不再使用后可在状态选择“失效”选项。',
  H0017: '1、计算对象为左边第一个查询框选择的标签内容 2、当先选了“标签在科目前面”，“显示合计行”将不能被选中；3、当先选了“显示合计行”，“标签在科目前面”将不能被选中。',
  H0018: '暂适用于CRM子系统，其他系统待开发',
};
