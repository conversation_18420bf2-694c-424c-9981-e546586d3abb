<!--
 * @LastEditors: 启旭
 * @Author: 肖泽涛
 * @Description: 公共搜索组件
 * @Date: 2019-03-29 10:51:17
 * @LastEditTime: 2019-09-06 14:12:55
 -->
<template>
  <div class="MoreSearch flex-h more_search" :class="{'MoreSearch--with-date': isDateSlot}">
    <div class="MoreSearch__box more_search_box">
      <slot name="date"></slot>

      <div
        class="MoreSearch__more more_search_button_box"
        :class="{'MoreSearch__more--with-date': isDateSlot }">
        <span class="MoreSearch__vertical">|</span>

        <el-popover
          v-model="visible"
          :visible-arrow="false"
          trigger="click"
          popper-class="MoreSearch__popover"
          placement="bottom"
          :width="popoverWidth"
          @hide="hide"
          @show="show">
          <div class="MoreSearch__popover__container">
            <div class="MoreSearch__popover__content" >
              <!-- 弹出层关闭按钮 -->
              <button
                type="button"
                class="MoreSearch__popover__close"
                @mouseenter="triggerClass='el-icon-circle-close'"
                @mouseleave="triggerClass='el-icon-close'"
                @click="visible = false">
                <i :class="triggerClass" class="el-dialog__close el-icon"></i>
              </button>

              <!-- 弹出层表单嵌入位置 -->
              <el-form
                ref="form"
                :model="searchFormData"
                slot="search"
                label-width="100px"
                label-position="right"
                style="margin-top:8px;">
                <slot></slot>
              </el-form>
            </div>

            <!-- 弹出层底部按钮区 -->
            <div class="MoreSearch__popover__actions search_footer_button">
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetForm">重置</el-button>
            </div>
          </div>

          <span slot="reference" class="MoreSearch__more__text">
            更多搜索
            <i class="el-icon-arrow-down"></i>
          </span>
        </el-popover>
      </div>
    </div>

    <el-button icon="el-icon-search" class="MoreSearch__search-out out_button" type="primary" @click="handleSearch"></el-button>
  </div>

</template>
<script>
import { sleep } from '@/assets/Utils';

export default {
  props: {
    searchFormData: {
      type: Object,
      required: true,
    },
    popoverWidth: {
      type: [String, Number],
      default: 380,
    },
  },

  data() {
    return {
      visible: false,
      hoverEnable: true,
      copyFromData: null,
      noReset: false,
      triggerClass: 'el-icon-close',
      isReEnter: false, // 在鼠标划出之后是否重新回来， 用于popper组件划出时流出缓冲时间， 可以判断用户是否把鼠标又移回Popper
    };
  },

  computed: {
    searchFrom() {
      return this.$refs.form;
    },

    isDateSlot() {
      return !!this.$slots.date;
    },
  },

  mounted() {
    // 该组件的行为，当鼠标移至更多搜索时，展示所有搜索条件的弹窗，当不做任何操作移开时，自动关闭，
    // 如果聚焦了弹窗内的输入框后，鼠标移开不会自动关闭，需要在弹窗区域外点击才会关闭

    // 使右边的更多搜索按钮更属期框边框样式一致
    if (this.isDateSlot) {
      $(this.$slots.date[0].$el).find('.el-date-editor .el-input__inner').focus(() => {
        $(this.$el).find('.MoreSearch__more').addClass('blur_border');
      }).blur(() => {
        $(this.$el).find('.MoreSearch__more').removeClass('blur_border');
      });
    }
  },

  // 组件销毁时，清除所有事件
  beforeDestroy() {
    $('.MoreSearch__popover__content input').off();
    document.removeEventListener('keydown', this.handlerKeyDown);
    // $('.MoreSearch__popover').remove();
    $('.MoreSearch__popover').off();

    if (this.$slots.date && this.$slots.date[0]) {
      $(this.$slots.date[0].$el).find('.el-date-editor .el-input__inner').off();
    }

    $(this.$el).remove();
  },

  methods: {
    handleSearch() {
      this.$emit('search');
      this.visible = false;
      this.noReset = true;
    },

    handleJQueryInterAction() {
      document.removeEventListener('keydown', this.handlerKeyDown);
      // 聚焦了弹窗内的输入框， 切换popover触发方式为click
      $('.MoreSearch__popover__content input').focus(() => {
        this.hoverEnable = false;
      });

      // 点击了输入框内的图标， 切换popover触发方式为click
      $('.MoreSearch__popover__content .el-input__icon').click(() => {
        this.hoverEnable = false;
      });
      // 在按下回车触发搜索
      document.addEventListener('keydown', this.handlerKeyDown);
    },

    handlerKeyDown(e) {
      if (e.keyCode === 13 && $('.MoreSearch__popover__content:visible').length) {
        // 当搜索框的弹框打开时，则不触发，避免按键事件冲突
        if ($('.el-autocomplete-suggestion:not(.el-autocomplete-suggestion-auto):visible').length || $('.el-select-dropdown:visible').length || $('.el-picker-panel:visible').length) {
          e.preventDefault();
          return true;
        }
        e.stopPropagation();
        // 针对数字输入框组件做特殊处理， 由于数字输入框组件需要触发blur事件，值才会真正发生修改
        // 在回车触发搜索之前，手动触发blur事件，保证输入框的值，已经修改成功
        $(e.target).trigger('blur');
        this.$nextTick(() => {
          this.handleSearch();
        });

        return false;
      }
      return true;
    },

    // 关闭时，需要将组件切换回hoverEnable， 如果norest标识，则恢复搜索条件
    hide() {
      this.hoverEnable = true;
      if (this.noReset) return;
      Lib.Utils.deepClone(this.copyFromData, this.searchFrom.model);
    },

    // 展示时，被份当前内容，用于恢复搜索条件
    show() {
      this.copyFromData = Lib.Utils.deepClone(this.searchFrom.model);
      this.noReset = false;
      this.handleJQueryInterAction();
    },

    showForEnter() {
      this.isReEnter = true;
      this.visible = true;
    },

    async hideForLeave() {
      if (this.hoverEnable) {
        this.isReEnter = false;
        await sleep(400);
        if (!this.isReEnter) {
          this.visible = false;
        }
      }
    },

    // 重置所有搜索条件
    resetForm() {
      // 使用from组件的重置方法
      this.searchFrom.resetFields();
      // 有一些条件无法通过from组件重置，需要传递事件出去，手动重置
      this.$emit('reset-from');
    },
  },
};
</script>

<style lang="scss">
// 更多搜索组件本身样式
.MoreSearch.more_search {
  align-items: center;
  .MoreSearch__box {
    // 消除间距，避免排版垂直错位以及水平空隙
    display: flex;
    align-items: center;
    .el-form-item {
      margin-right: 0;
      margin-bottom: 0;
    }

    // date slot 中 input 类组件的宽度
    .el-input {
      width: 180px;
      font-size: 12px !important;
      // 只显示月份的日期组件，只需要 92 px 即可
      &.el-date-editor--month {
        width: 90px ;
        .el-input__inner{
          padding-right: 0;
        }
      }
    }
    .el-select>.el-input {
      width: 100%;
    }

    .el-input__inner,
    .el-date-editor .el-input__inner, .el-range-editor {
      vertical-align: middle;
      height: 30px;
      border: 1px solid #d5d5d5;
      border-radius: 3px 0px 0 3px;
      border-right: none;
      &:focus, &.is-active {
        border-color: #d5d5d5;
      }
    }

    .blur_border {
      border-color: $color-theme-base;
    }

    &:hover {
      .MoreSearch__more, .el-input__inner, .el-range-editor
      {
        border-color: $color-theme-base;
      }
    }
  }

  // 更多搜索文案部分
  .MoreSearch__more {
    box-sizing: border-box;
    display: inline-block;
    width: 104px;
    height: 30px;
    border-left: none;
    vertical-align: middle;
    text-align: left;
    transition: border-color .2s cubic-bezier(.645,.045,.355,1);

    // 垂直分割线
    .MoreSearch__vertical {
      line-height: 28px;
      color: #bbb;
    }

    .MoreSearch__more__text {
      display: inline-block;
      color: #222222;
      line-height: 20px;
      width:80px;
      text-align: center;
      margin-left: 2px;
      border-radius: 2px;
      cursor: pointer;
    }

    // 使用了 date slot，【更多搜索】下拉渲染边框
    &.MoreSearch__more--with-date {
      width: 90px;
      border: 1px solid #d5d5d5;
      border-left: 0;
      background-color: #fff;
    }
  }

  // 表单右侧搜索按钮
  .MoreSearch__search-out.MoreSearch__search-out.MoreSearch__search-out {
    height: 30px;
    margin: 0 0 0 -1px;
    padding: 0 8px;
    border-radius: 0 3px 3px 0;
  }
}

// 更多表单弹出层内部的样式
.MoreSearch__popover {
  margin-top: 0px !important;
  padding:8px !important;
  border:1px solid #ecdfdf;
  .el-date-editor--daterange.el-input,
  .el-date-editor--daterange.el-input__inner,
  .el-date-editor--timerange.el-input,
  .el-autocomplete,
  .el-range-editor.el-input__inner,
  .el-select,
  .el-cascader,
  .el-date-editor.el-input,
  .el-date-editor--timerange.el-input__inner{
    width: 100%;
  }

  .MoreSearch__popover__content {
    max-height: 350px;
    padding-right: 20px;
    overflow-y: auto;
    padding-top: 10px;
    position: relative;

    // 弹出层的右上角关闭按钮
    .MoreSearch__popover__close {
      position: absolute;
      top: 0;
      right: 0;
      padding: 0;
      background: 0 0;
      outline: 0;
      border:none;
      font-size: 16px;
      height: 16px;
      color: #666;
      cursor: pointer;
    }
  }

  // 底部按钮栏
  .MoreSearch__popover__actions {
    margin-top: 10px;
    text-align: center;
    .el-button {
      padding: 7px 30px;
    }
  }
}

</style>
