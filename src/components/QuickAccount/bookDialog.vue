<template>
  <div class="cmb-dialog-wapper" v-if="cardVisible">
    <div class="cmb-dialog">
      <div class="cmb-dialog-header">
        <div class="header-item header-ico"> </div>
        <div class="header-item header-info">
          <div class="fz16">您好! </div>
          <div class="fz12">我们已经为您收集到以下原始数据 </div>
        </div>
        <div class="header-item header-date">
          <span class="input">{{ accountPeriod.Format('yyyy-MM') }}</span>
          <!-- <span class="text">至</span> -->

        </div>
        <span class="input fr zhiying" @click="showZhiYin">操作指引</span>
        <span class="header-close" @click="closeQuick"><img class="header-icon" src="/static/cmb/cmbdlgclose.png"
            alt=""></span>
      </div>
      <div class="cmb-dialog-body clearfix">
        <div class="body-item left">
          <!-- 此处不止cmb使用， 故不显示logo -->
          <!-- <div class="left-logo">
            <img
              src="/static/cmb/cmbdlglogo.png"
              alt=""
            >
          </div> -->
          <div class="left-ctx"> <img src="/static/cmb/cmbdlgleft.png" alt=""> </div>
        </div>
        <div class="body-item right">
          <div class="right-item border" :class="{ 'red_text': outputCount === 0 }">
            <div class="item-content">
              <span class="point"></span>
              收入销项发票<span class="xc">{{ outputCount }}</span>张，合计
              <span class="xc">{{ outputSum }}</span>元，税额
              <span class="xc">{{ outputTaxAmount | moneyFilter }}</span>元
            </div>
            <p class="button-group">
              <el-button size="mini" @click="jumpAndImport('outputTax')">导入</el-button>
              <el-button size="mini" @click="jumpAndInput('outputTax')">手录</el-button>
              <el-button size="mini" @click="jump('outputTax')">查看</el-button>
            </p>
          </div>
          <div class="right-item border" :class="{ 'red_text': inputCount === 0 }">
            <div class="item-content">
              <span class="point"></span>
              采购进项发票<span class="xc">{{ inputCount }}</span>张，合计
              <span class="xc">{{ inputSum }}</span>元，税额
              <span class="xc">{{ inputTaxAmount | moneyFilter }}</span>元
            </div>
            <p class="button-group">
              <el-button size="mini" @click="jumpAndImport('inputTax')">导入</el-button>
              <el-button size="mini" @click="jumpAndInput('inputTax')">手录</el-button>
              <el-button size="mini" @click="jump('inputTax')">查看</el-button>
            </p>
          </div>
          <div class="right-item border" v-if="!isAgent" :class="{ 'red_text': costAmount === 0 }">
            <div class="item-content">
              <span class="point"></span>费用报销<span class="xc">{{ costAmount | moneyFilter }}</span>元
            </div>
            <p class="button-group">
              <el-button size="mini" @click="jump('baoxiaoBill')">查看</el-button>
            </p>
          </div>
          <div class="right-item border">
            <div>
              <div class="item-content" :class="{ 'red_text': bankIncomeCount === 0 }">
                <span class="point"></span>
                账户银行收入流水
                <span class="xc">{{ bankIncomeCount }}</span>
                条，合计
                <span class="xc">{{ bankIncomeAmount | moneyFilter }}</span>元
              </div>
              <p class="button-group">
                <el-button size="mini" @click="jumpAndImport('trade')">导入</el-button>
                <el-button size="mini" @click="jump('trade')">手录</el-button>
                <el-button size="mini" @click="jump('trade')">查看</el-button>
              </p>
            </div>
            <div style="padding-left: 87px; margin-top: -15px;" :class="{ 'red_text': bankExpenditureCount === 0 }">
              支出流水<span class="xc">{{ bankExpenditureCount }}</span>条，合计
              <span class="xc">{{ bankExpenditureAmount | moneyFilter }}</span>元
            </div>
          </div>
          <div class="right-item">
            <div>
              <div class="item-content" :class="{ 'red_text': cashIncomeCount === 0 }">
                <span class="point"></span>现金收入流水<span class="xc">{{ cashIncomeCount }}</span>条，合计
                <span class="xc">{{ cashIncomeAmount | moneyFilter }}</span>元
              </div>
              <p class="button-group">
                <el-button size="mini" @click="jumpAndImport('trade', true)">导入</el-button>
                <el-button size="mini" @click="jump('trade', true)">手录</el-button>
                <el-button size="mini" @click="jump('trade', true)">查看</el-button>
              </p>
            </div>
            <div style="margin-top: -15px;" :class="{ 'red_text': cashExpenditureCount === 0 }">支出流水<span
                class="xc">{{ cashExpenditureCount }}</span>条，合计
              <span class="xc">{{ cashExpenditureAmount | moneyFilter }}</span>元
            </div>
          </div>
          <div class="right-btns">
            <div class="btns-item" @click="showRule"> 当前凭证规则 </div>
            <AccessCtrl :lockDataMonth="accountPeriod" isCUD>
              <button class="btns-item" @click="showLoad"> 一键生成凭证 </button>
            </AccessCtrl>
          </div>
        </div>
      </div>
      <div class="cmb-dialog-footer">
      </div>
    </div>
    <div class="v-modal" tabindex="2" style="z-index: 2026;"></div>
  </div>
</template>
<script>
import introConfig from '@/assets/introConfig';

export default {
  props: {
    bookVisible: {
      type: Boolean,
      required: true,
    },
    outputCount: {
      type: Number,
      default: 0,
    },
    outputSum: {
      type: Number,
      default: 0,
    },
    outputTaxAmount: {
      type: Number,
      default: 0,
    },
    inputCount: {
      type: Number,
      default: 0,
    },
    inputSum: {
      type: Number,
      default: 0,
    },
    inputTaxAmount: {
      type: Number,
      default: 0,
    },
    bankIncomeAmount: {
      type: Number,
      default: 0,
    },
    bankIncomeCount: {
      type: Number,
      default: 0,
    },
    bankExpenditureAmount: {
      type: Number,
      default: 0,
    },
    bankExpenditureCount: {
      type: Number,
      default: 0,
    },
    cashIncomeAmount: {
      type: Number,
      default: 0,
    },
    cashIncomeCount: {
      type: Number,
      default: 0,
    },
    cashExpenditureAmount: {
      type: Number,
      default: 0,
    },
    cashExpenditureCount: {
      type: Number,
      default: 0,
    },
    costAmount: {
      type: Number,
      default: 0,
    },
    accountPeriod: {
      type: Date,
    },
  },
  computed: {
    cardVisible: {
      get() {
        return this.bookVisible;
      },
      set(isVisible) {
        if (this.bookVisible && !isVisible) { this.$emit('hide'); }
      },
    },
    isAgent() {
      return this.$store.state.app.PROJECT_TYPE === 'agent';
    },
  },
  methods: {
    // 退出智能凭证
    closeQuick() {
      this.$store.commit('app/SET_QUICK_ACCOUNT', 0);
    },
    // 进入load状态
    showLoad() {
      this.cardVisible = false;
      this.$nextTick(() => {
        this.$emit('showLoad');
      });
    },
    // 打开规则配置页
    showRule() {
      this.cardVisible = false;
      this.$nextTick(() => {
        this.$emit('showRule');
      });
    },
    // 跳转至模块页面，查看明细
    jump(name, isCash, query) {
      this.$router.push({ name, query: { ...query, isCash } });
    },
    // 跳转至模块页面，并打开导入窗口
    jumpAndImport(name, isCash) {
      this.jump(name, isCash, { execute: 'import' });
    },
    // 跳转至模块页面，并打开新增窗口
    jumpAndInput(name, isCash) {
      this.jump(name, isCash, { execute: 'input' });
    },
    // 打开智能凭证的新手指引
    showZhiYin() {
      const path = 'quickAccount';
      // 获取智能凭证相关的指引内容
      const intros = introConfig.filter((item) => {
        if (item.showPath) {
          return item.showPath.indexOf(path) !== -1;
        }
        if (item.element) {
          return $(item.element).length && !$(item.element).is(':hidden');
        }
        return false;
      });
      if (intros.length !== 0) {
        this.$intro()
          .setOptions({
            steps: intros,
            nextLabel: '下一步',
            prevLabel: '上一步',
            doneLabel: '完成',
            skipLabel: '退出',
          })
          .start();
      } else {
        this.$message.info('当前页面无指引内容');
      }
    },
  },
};
</script>

<style lang="scss"   scoped>
.red_text {
  color: red;
}

.cmb-dialog-wapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
}

.cmb-dialog {
  position: relative;
  top: 10vh;
  margin: 0 auto;
  background: #fff;
  width: 880px;
  z-index: 2027;
  box-sizing: border-box;
}

.cmb-dialog .cmb-dialog-header {
  height: 106px;
  background: -webkit-linear-gradient(6deg,
      rgba(24, 138, 226, 1) 0%,
      rgba(61, 193, 248, 1) 100%);
  background: -o-linear-gradient(6deg,
      rgba(24, 138, 226, 1) 0%,
      rgba(61, 193, 248, 1) 100%);
  background: linear-gradient(96deg,
      rgba(24, 138, 226, 1) 0%,
      rgba(61, 193, 248, 1) 100%);
  color: #ffffff;
  font-weight: bold;
  box-sizing: border-box;
  position: relative;
  top: 0;
  right: 0;

  .header-close {
    position: absolute;
    right: -7px;
    top: -50px;
    cursor: pointer;

    img {
      width: 41px;
      opacity: 0.8;
    }
  }

  .header-item {
    display: inline-block;
  }

  .header-item.header-ico {
    position: absolute;
    top: 20px;
    left: 35px;
    width: 65px;
    height: 66px;
    background: no-repeat url("/static/cmb/cmbdlgico.png");
  }

  .header-item.header-info {
    margin-left: 127px;
    margin-top: 31px;
  }

  .header-item.header-date {
    margin-left: 127px;
    margin-top: 47px;
  }

  .header-item.header-date>span {
    display: inline-block;
  }

  .header-item.header-date .input {
    box-sizing: border-box;
    width: 104px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: rgba(30, 147, 229, 1);
    border: 1px solid rgba(255, 255, 255, 1);
    border-radius: 4px;
  }

  .header-item.header-date .text {
    padding: 0 8px;
  }
}

.cmb-dialog .cmb-dialog-body {
  .body-item {
    float: left;
  }

  .body-item.left {
    padding-left: 20px;

    .left-logo {
      margin-top: 50px;
      margin-left: 12px;
    }

    .left-ctx {
      margin-top: 82px;
    }
  }

  .body-item.right {
    padding-left: 60px;
    padding-top: 15px;

    .xc {
      color: #188ae2;
    }

    .fs {
      font-family: serif;
      cursor: pointer;
    }

    .point {
      width: 6px;
      height: 6px;
      background: rgba(172, 173, 173, 1);
      border-radius: 50%;
      display: block;
      position: absolute;
      left: -20px;
      top: 18px;
    }

    .right-item {
      line-height: 40px;
      position: relative;

      .right-line-btn {
        background: rgba(233, 152, 68, 1);
        border-radius: 2px;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        padding: 4px 14px;
        cursor: pointer;
      }
    }

    .right-item.border {
      border-bottom: 1px solid rgba(238, 238, 238, 1);
    }

    .right-btns .btns-item {
      display: inline-block;
      width: 162px;
      height: 38px;
      border: 1px solid rgba(24, 138, 226, 1);
      border-radius: 4px;
      line-height: 38px;
      text-align: center;
      font-weight: 400;
      margin-top: 30px;
      font-size: 14px;
      box-sizing: border-box;
      padding: 0;
      cursor: pointer;

      &:first-child {
        color: rgba(24, 138, 226, 1);
      }

      &:last-child {
        color: rgba(254, 254, 254, 1);
        background: rgba(24, 138, 226, 1);
        margin-left: 30px;
      }

      &:disabled {
        color: #FFFFFF;
        background-color: #8cc5f1;
        border-color: #8cc5f1;
      }
    }

    .right-btns .btns-item.is-disabled {
      cursor: not-allowed;
      color: #FFFFFF;
      background-color: #8cc5f1;
      border-color: #8cc5f1;
    }
  }
}

.cmb-dialog-footer {
  height: 80px;

  .footer-tips {
    width: 706px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: rgba(226, 242, 251, 1);
    border: 1px solid rgba(179, 221, 253, 1);
    font-weight: 400;
    color: rgba(18, 73, 116, 1);
    margin: 30px auto;

    .news-item>a {
      cursor: pointer;
      color: inherit;
    }

    .news-item>a:hover {
      text-decoration: underline;
    }
  }
}

.button-group {
  display: inline-block;
}

.item-content {
  display: inline-block;
  width: 350px;
}

.zhiying {
  box-sizing: border-box;
  width: 104px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  background: #1e93e5;
  border: 1px solid #ffffff;
  border-radius: 4px;
  margin-top: 47px;
  margin-right: 20px;
  cursor: pointer;
}
</style>
