<template>
    <div>
      <book-dialog ref="book"
      :bookVisible="bookVisible"
      @hide="bookVisible = false"
      @showLoad="loadingVisible = true"
      @showRule="ruleVisible = true"
      :outputCount="outputCount"
      :outputTaxAmount="outputTaxAmount"
      :outputSum="outputSum"
      :inputCount="inputCount"
      :inputTaxAmount="inputTaxAmount"
      :inputSum="inputSum"
      :bankIncomeAmount="bankIncomeAmount"
      :bankIncomeCount="bankIncomeCount"
      :bankExpenditureAmount="bankExpenditureAmount"
      :bankExpenditureCount="bankExpenditureCount"
      :cashIncomeAmount="cashIncomeAmount"
      :cashIncomeCount="cashIncomeCount"
      :cashExpenditureAmount="cashExpenditureAmount"
      :cashExpenditureCount="cashExpenditureCount"
      :costAmount="costAmount"
      :accountPeriod="accountPeriod"
    ></book-dialog>
    <loading-dialog
    :accountPeriod="accountPeriod"
    v-if="loadingVisible"
    :loadVisible="loadingVisible"
    @hide="loadingVisible=false"
    @showResult="showResult"
    @showBook="bookVisible=true"></loading-dialog>
    <result-dialog
    @showLoad="loadingVisible=true"
    :accountPeriod="accountPeriod"
    :voucherCount="voucherCount"
    :resultVisible="resultVisible"
    @hide="resultVisible=false"
    @showBook="bookVisible = true"></result-dialog>
    <rule-dialog
    v-if="ruleVisible"
    :ruleVisible="ruleVisible"
    @hide="ruleVisible=false"
    @showBook="bookVisible=true"></rule-dialog>
    <check-init
    :registered="registered"
    :initial="initial"
    v-if="checkInitVisible"
    :checkInitVisible="checkInitVisible"
    @hide="checkInitVisible=false"
    @showBook="bookVisible = true"></check-init>
  </div>
</template>
<script>
import { fastAccCheck, checkInit } from '@/api/statement';
import BookDialog from './bookDialog.vue';
import LoadingDialog from './loadingDialog.vue';
import ResultDialog from './resultDialog.vue';
import RuleDialog from './ruleDialog.vue';
import CheckInit from './checkInit.vue';

export default {
  data() {
    return {
      bookVisible: true, // 首页
      checkInitVisible: false, // 初始化补充页
      loadingVisible: false, // 加载页
      resultVisible: false, // 结果页
      ruleVisible: false, // 规则页
      registered: 1, // 税局登记状态
      initial: 1, // 账套初始化状态
      inputSum: 0, // 进项合计
      outputCount: 0, // 销项发票数量
      outputTaxAmount: 0, // 销项税额合计
      outputSum: 0, // 销项合计
      inputCount: 0, // 进项发票合计
      inputTaxAmount: 0, // 进项税额合计
      bankIncomeAmount: 0, // 银行流水收入合计
      bankIncomeCount: 0, // 银行流水收入条数
      bankExpenditureAmount: 0, // 银行流水支出合计
      bankExpenditureCount: 0, // 银行流水支出条数
      cashIncomeAmount: 0, // 现金流水收入合计
      cashIncomeCount: 0, // 现金流水收入条数
      cashExpenditureAmount: 0, // 现金流水支出合计
      cashExpenditureCount: 0, // 现金流水支出条数
      costAmount: 0, // 费用合计
      voucherCount: 0, // 凭证生成结果条数
    };
  },
  components: {
    BookDialog,
    LoadingDialog,
    ResultDialog,
    RuleDialog,
    CheckInit,
  },
  async mounted() {
    // 将智能凭证弹窗放到body下
    document.body.appendChild(this.$el);
    // 进入前先检查用户初始化状态
    await this.checkInit();
    // 获取统计数据
    this.getTotals();
  },
  computed: {
    accountPeriod() {
      return this.$store.state.user.accountPeriod;
    },
    selectCompanyId() {
      return this.$store.state.user.userInfo.selectCompanyId;
    },
    // 智能凭证显示状态 1为展示窗口 2 为缩小至小图标  0 为关闭状态
    showQuickAccount() {
      return this.$store.state.app.showQuickAccount;
    },
  },
  destroyed() {
    // 关闭时销毁整个dom
    if (this.$el && this.$el.parentNode) {
      this.$el.parentNode.removeChild(this.$el);
    }
  },
  watch: {
    showQuickAccount(newVal, oldVal) {
      // 用户从缩小状态打开时，需要重新获取统计数据
      if (oldVal === 2 && newVal === 1) {
        this.getTotals();
        // 处理用户补充初始化内容后，回到智能凭证界面，需重新刷新初始化状态
        if (this.checkInitVisible) {
          this.checkInit();
        }
      }
    },
  },
  methods: {
    // 获取统计数据
    getTotals() {
      const accountPeriod = this.accountPeriod.Format('yyyy-MM');
      fastAccCheck(accountPeriod, 0).then((res) => {
        const { data } = res.data;
        data.forEach((item) => {
          // moduleId 对应模块 1 销项  2进项  3银行流水 4现金流水 5费用
          switch (item.moduleId) {
            case 1:
              this.outputSum = item.noTaxAmountTotal || 0;
              this.outputTaxAmount = item.taxAmountTotal || 0;
              this.outputCount = item.invoiceCounts || 0;
              break;
            case 2:
              this.inputSum = item.noTaxAmountTotal || 0;
              this.inputTaxAmount = item.taxAmountTotal || 0;
              this.inputCount = item.invoiceCounts || 0;
              break;
            case 3:
              this.bankIncomeAmount = item.incomeAmount || 0;
              this.bankIncomeCount = item.incomeCount || 0;
              this.bankExpenditureAmount = item.expenditureAmount || 0;
              this.bankExpenditureCount = item.expenditureCount || 0;
              break;
            case 4:
              this.cashIncomeAmount = item.incomeAmount || 0;
              this.cashIncomeCount = item.incomeCount || 0;
              this.cashExpenditureAmount = item.expenditureAmount || 0;
              this.cashExpenditureCount = item.expenditureCount || 0;
              break;
            case 5:
              this.costAmount = item.claimAmount || 0;
              break;
            default:
              break;
          }
        });
      });
    },
    // 打开展示页
    showResult(num) {
      this.resultVisible = true;
      this.voucherCount = num;
    },
    // 检查初始化信息
    async checkInit() {
      // 获取是否有不在提示的标志
      const checkInitFlag = window.localStorage.getItem(`checkInit${this.selectCompanyId}`);
      if (checkInitFlag !== 'false') {
        await checkInit().then((res) => {
          const { initial, registered } = res.data.totalData;
          // 当账套初始化或税局登记有一项为0（即未完成）时， 显示初始化补全窗口
          if (!(initial && registered)) {
            this.checkInitVisible = true;
            this.bookVisible = false;
            this.initial = initial;
            this.registered = registered;
          } else {
            this.checkInitVisible = false;
            this.bookVisible = true;
          }
        });
      }
    },
  },
};
</script>
<style scoped>

</style>
