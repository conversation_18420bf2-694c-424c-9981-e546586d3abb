<template>
  <div
    class="cmb-dialog-wapper"
    v-if="cardVisible"
  >
    <div class="cmb-dialog">
      <div class="cmb-dialog-header">
        <span
          class="header-close"
          @click="updateVoucherRule"
        ><img
            src="/static/imgs/CMB/返回.png"
            alt=""
          ></span>
      </div>
      <div class="cmb-dialog-body">
        <div class="ruleCard">
          <div class="rule-aside">
            <div class="rule-aside-item">凭证合并规则</div>
            <div class="rule-aside-item">凭证日期</div>
            <div class="rule-aside-item">对方科目</div>
            <div class="rule-aside-item">摘要内容</div>
          </div>
          <div class="rule-item">
            <div class="rule-title">收入</div>
            <div>
              <el-radio-group
                v-model="voucherRule[0].moduleIncome"
                class="radio-group radio-group-big"
              >
                <el-radio :label="4">
                  <span class="radio-text">一张发票做一张凭证</span>
                </el-radio>
                <el-radio :label="5">
                  <span class="radio-text">开票日期、客户相同生成同一凭证，商品合并</span>
                </el-radio>
                <el-radio :label="1">
                  <span class="radio-text">开票日期、客户相同生成同一凭证，商品不合并</span>
                </el-radio>
                <el-radio :label="2">
                  <span class="radio-text">开票日期、客户相同生成同一凭证，商品按单价相同合并</span>
                </el-radio>
                <el-radio :label="3">
                  <span class="radio-text">全部合并为一张凭证</span>
                </el-radio>
              </el-radio-group>
              <el-radio-group
                v-model="voucherRule[1].moduleIncome"
                class="radio-group"
              >
                <el-radio :label="0"><span class="radio-text">开票日期</span></el-radio>
                <el-radio :label="1"><span class="radio-text">本月最后一天</span></el-radio>
              </el-radio-group>
              <el-radio-group
                v-model="voucherRule[2].moduleIncome"
                class="radio-group"
              >
                <el-radio :label="0"><span class="radio-text">往来科目</span></el-radio>
                <el-radio :label="1"><span class="radio-text">库存现金</span></el-radio>
              </el-radio-group>
              <div class="radio-group">
                <div class="no-radio"><span class="radio-text">系统设定</span></div>
              </div>
            </div>
          </div>
          <div class="rule-item">
            <div class="rule-title">采购</div>
            <div>
              <el-radio-group
                v-model="voucherRule[0].modulePurchase"
                class="radio-group radio-group-big"
              >
                <el-radio :label="4">
                  <span class="radio-text">一张发票做一张凭证</span>
                </el-radio>
                <el-radio :label="5">
                  <span class="radio-text">开票日期、客户相同生成同一凭证，商品合并</span>
                </el-radio>
                <el-radio :label="1">
                  <span class="radio-text">开票日期、客户相同生成同一凭证，商品不合并</span>
                </el-radio>
                <el-radio :label="2">
                  <span class="radio-text">开票日期、客户相同生成同一凭证，商品按单价相同合并</span>
                </el-radio>
                <el-radio :label="3">
                  <span class="radio-text">全部合并为一张凭证</span>
                </el-radio>
              </el-radio-group>
              <el-radio-group
                v-model="voucherRule[1].modulePurchase"
                class="radio-group"
              >
                <el-radio :label="0"><span class="radio-text">开票日期</span></el-radio>
                <el-radio :label="1"><span class="radio-text">本月最后一天</span></el-radio>
              </el-radio-group>
              <el-radio-group
                v-model="voucherRule[2].modulePurchase"
                class="radio-group"
              >
                <el-radio :label="0"><span class="radio-text">往来科目</span></el-radio>
                <el-radio :label="1"><span class="radio-text">库存现金</span></el-radio>
              </el-radio-group>
              <div class="radio-group">
                <div class="no-radio"><span class="radio-text">系统设定</span></div>
              </div>
            </div>
          </div>
          <div class="rule-item">
            <div class="rule-title">银行</div>
            <div>
              <el-radio-group
                v-model="voucherRule[0].moduleBank"
                class="radio-group radio-group-big"
                style="padding-bottom:45px;"
              >
                <el-radio :label="1">
                  <span class="radio-text">一条流水做一张凭证</span>
                </el-radio>
                <el-radio :label="2">
                  <span class="radio-text">一级科目相同合并一张凭证 <br />
                  <span style="visibility: hidden;">1</span></span>
                </el-radio>
                <el-radio :label="3">
                  <span class="radio-text">明细科目相同合并一张凭证 <br />
                  <span style="visibility: hidden;">1</span></span>
                </el-radio>
                <el-radio :label="4">
                  <span class="radio-text">收款、付款各出一张凭证</span>
                </el-radio>
              </el-radio-group>
              <el-radio-group
                v-model="voucherRule[1].moduleBank"
                :disabled="voucherRule[0].moduleBank !== 1"
                class="radio-group"
              >
                <el-radio :label="0"><span class="radio-text">流水日期</span></el-radio>
                <el-radio :label="1"><span class="radio-text">本月最后一日</span></el-radio>
              </el-radio-group>
              <div class="radio-group">
                <div class="no-radio"><span class="radio-text">系统设定</span></div>
              </div>
              <el-radio-group
                v-model="voucherRule[3].moduleBank"
                :disabled="voucherRule[0].moduleBank !== 1"
                class="radio-group"
              >
                <el-radio :label="0"><span class="radio-text">按你写的</span></el-radio>
                <el-radio :label="1"><span class="radio-text">系统自动生成</span></el-radio>
              </el-radio-group>
            </div>
          </div>
          <div class="rule-item">
            <div class="rule-title">现金</div>
            <div>
              <el-radio-group
                v-model="voucherRule[0].moduleCash"
                class="radio-group radio-group-big"
                style="padding-bottom:45px;"
              >
                <el-radio :label="1">
                  <span class="radio-text">一条流水做一张凭证</span>
                </el-radio>
                <el-radio :label="2">
                  <span class="radio-text">一级科目相同合并一张凭证 <br />
                  <span style="visibility: hidden;">1</span></span>
                </el-radio>
                <el-radio :label="3">
                  <span class="radio-text">明细科目相同合并一张凭证 <br />
                  <span style="visibility: hidden;">1</span></span>
                </el-radio>
                <el-radio :label="4">
                  <span class="radio-text">收款、付款各出一张凭证</span>
                </el-radio>
              </el-radio-group>
              <el-radio-group
                v-model="voucherRule[1].moduleCash"
                :disabled="voucherRule[0].moduleCash !== 1"
                class="radio-group"
              >
                <el-radio :label="0"><span class="radio-text">流水日期</span></el-radio>
                <el-radio :label="1"><span class="radio-text">本月最后一日</span></el-radio>
              </el-radio-group>
              <div class="radio-group">
                <div class="no-radio"><span class="radio-text">系统设定</span></div>
              </div>
              <el-radio-group
                v-model="voucherRule[3].moduleCash"
                :disabled="voucherRule[0].moduleCash !== 1"
                class="radio-group"
              >
                <el-radio :label="0"><span class="radio-text">按你写的</span></el-radio>
                <el-radio :label="1"><span class="radio-text">系统自动生成</span></el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
      </div>
      <div class="cmb-dialog-footer">
      </div>
    </div>

    <div
      class="v-modal"
      tabindex="0"
      style="z-index: 2026;"
    ></div>
  </div>
</template>
<script>
import { getVoucherRule, updateVoucherRule } from '@/api/companyConfig';

export default {
  props: {
    ruleVisible: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      voucherRule: [{}, {}, {}, {}], // 凭证规则列表
    };
  },
  computed: {
    cardVisible: {
      get() {
        return this.ruleVisible;
      },
      set(isVisible) {
        if (this.ruleVisible && !isVisible) this.$emit('hide', false);
      },
    },
  },
  watch: {
    voucherRule: {
      handler() {
        // 银行现金合并规则不为一条流水做一张凭证时，凭证日期只能设为本月最后一天， 摘要内容由系统生成
        const { moduleCash, moduleBank } = this.voucherRule[0];
        if (moduleCash !== 1) {
          this.voucherRule[1].moduleCash = 1;
          this.voucherRule[3].moduleCash = 1;
        }
        if (moduleBank !== 1) {
          this.voucherRule[1].moduleBank = 1;
          this.voucherRule[3].moduleBank = 1;
        }
      },
      deep: true,
    },
  },
  mounted() {
    getVoucherRule().then((res) => {
      const { data } = res.data;
      // 将数组按行号顺序排
      this.voucherRule = Lib.Utils.quickSort(data, 'ruleId');
    });
  },
  methods: {
    showBook() {
      this.$emit('showBook');
      this.cardVisible = false;
    },
    // 保存规则并返回智能凭证主页
    updateVoucherRule() {
      updateVoucherRule(this.voucherRule).then(() => {
        this.showBook();
      });
    },
  },
};
</script>

<style lang="scss"   scoped>
$imgs: "/static/imgs/CMB";
.cmb-dialog-wapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
  .cmb-dialog {
    position: relative;
    top: 10vh;
    margin: 0 auto;
    padding-top: 60px;
    width: 1000px;
    height: 460px;
    background: linear-gradient(
      90deg,
      rgba(24, 138, 226, 1),
      rgba(24, 205, 226, 1)
    );
    z-index: 2027;
    box-sizing: border-box;
    // background-image: url("#{$imgs}/result-bg.png");
    // background-repeat: no-repeat;
    .cmb-dialog-body {
      .ruleCard {
        width: 893px;
        height: 350px;
        background: rgba(255, 255, 255, 1);
        border-radius: 4px;
        margin-left: 73px;
        position: relative;
        .rule-item {
          width: 223px;
          height: 300px;
          box-sizing: border-box;
          float: left;
          margin-top: 28px;
          margin-bottom: 21px;
          padding: 0 11px;
          position: relative;
          .rule-title {
            background-image: url("#{$imgs}/title.png");
            position: absolute;
            top: -45px;
            left: 39px;
            width: 146px;
            height: 29px;
            text-align: center;
            line-height: 29px;
            font-size: 14px;
            font-family: MicrosoftYaHei-Bold;
            font-weight: bold;
            color: rgba(255, 255, 255, 1);
          }

          .radio-text {
            display: inline-block;
            white-space: normal;
            vertical-align: middle;
            line-height: 16px;
          }

          .radio-group {
            padding-top: 9px;
            margin: 0 11px;
            width: 178px;
            &:not(:last-child) {
              border-bottom: 1px dashed rgba(189, 186, 186, 1);
            }
            .no-radio {
              margin-bottom: 9px;
              font-size: 12px;
              font-family: "Microsoft Yahei";
              color: #2b333b;
            }
          }
        }
        .rule-item:not(:last-child) {
          border-right: 1px solid rgba(221, 221, 221, 1);
        }
        .rule-aside {
          position: absolute;
          width: 46px;
          top: 20px;
          left: -38px;
          .rule-aside-item {
            width: 45px;
            height: 32px;
            background: rgba(253, 254, 255, 1);
            border-radius: 4px 0px 0px 4px;
            float: left;
            margin-bottom: 3px;
            font-size: 12px;
            padding: 0 8px;
            font-family: MicrosoftYaHei;
            font-weight: 400;
            color: rgba(85, 85, 85, 1);
            line-height: 16px;
            box-sizing: border-box;
            &:first-child {
              height: 210px;
              padding: 67px 8px;
            }
          }
        }
      }
    }
    .cmb-dialog-header {
      .header-close {
        position: absolute;
        right: -7px;
        top: -42px;
        cursor: pointer;
        img {
          width: 41px;
          opacity: 0.8;
        }
      }
    }
  }
}
</style>
<style scoped>
.rule-item::v-deep .el-radio {
  margin-left: 0px;
  margin-bottom: 9px;
  margin-right: 0px;
}
.rule-item::v-deep .el-radio__label {
  padding-left: 7px;
}
.radio-group:not(:first-child)::v-deep .el-radio:last-child {
  margin-left: 8px;
}
.radio-group-big::v-deep .el-radio:not(:last-child) {
  margin-bottom: 14px;
}
.radio-group-big::v-deep .el-radio:not(:last-child) .radio-text {
  vertical-align: text-top !important;
}
</style>
