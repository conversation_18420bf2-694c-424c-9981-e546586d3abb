<template>
  <div class="cmb-dialog-wapper" v-if="cardVisible">
    <div class="cmb-dialog">
      <div class="cmb-dialog-header">
        <span class="header-close" @click="closeQuick">
          <img src="/static/cmb/cmbdlgclose.png" alt="">
        </span>
      </div>
        <div class="cmb-dialog-body">
          <div class="resultCard">
            <div class="resultCard-header">
              <p>已经您自动生成</p>
              <h3>{{voucherCount}}张凭证</h3>
            </div>
            <div class="resultCard-body">
              <div class="button-box">
                您可能还要做：
                <router-link :to="{name: 'salary'}">
                  <el-button type="primary">计提工资</el-button>
                </router-link>
                <router-link :to="{name: 'card', query: {execute: 'depreciation'}}">
                  <el-button type="primary">计提折旧</el-button>
                </router-link>
                <router-link :to="{name: 'simpleCostAccount'}">
                  <el-button type="primary">成本核算</el-button>
                </router-link>
                <router-link :to="{name: 'endCheckout'}">
                  <el-button type="primary">期末结转</el-button>
                </router-link>
              </div>
              <div class="button-box">
                您可能还要看：
                <router-link :to="{name: 'voucherDetails'}">
                  <el-button type="primary">凭证</el-button>
                </router-link>
                <router-link :to="{name: 'accountBalanceSheet'}">
                  <el-button type="primary">余额表</el-button>
                </router-link>
                <router-link :to="{name: 'subAccount'}">
                  <el-button type="primary">明细账</el-button>
                </router-link>
                <router-link :to="{name: 'balanceSheet'}">
                  <el-button type="primary">会计报表</el-button>
                </router-link>
              </div>
              <div class="detail-box">
                <div class="detail-left fl">
                  <p>资金余额：<span>{{financial.ZiJinJinE | moneyFilter}}</span></p>
                </div>
                <div class="detail-right fr">
                  <ul>
                    <li><i>•</i> 本月累计收入：{{financial.YingYeShouRu | moneyFilter}}</li>
                    <li class="grey">
                      <i class="grey">•</i> 本月累计支出：{{financial.YingYeChengBen | moneyFilter}}
                    </li>
                    <li><i>•</i> 本月累计利润：{{financial.BenYueLiRun | moneyFilter}}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="cmb-dialog-footer">
      </div>
    </div>

    <div class="v-modal" tabindex="0" style="z-index: 2026;"></div>
  </div>
</template>
<script>
import { getFinancial } from '@/api/statement';

export default {
  props: {
    resultVisible: {
      type: Boolean,
      required: true,
    },
    voucherCount: {
      type: Number,
      default: 0,
    },
    accountPeriod: {
      type: Date,
      required: true,
    },
  },
  data() {
    return {
      // 财务数据
      financial: {

      },
    };
  },
  computed: {
    cardVisible: {
      get() {
        return this.resultVisible;
      },
      set(isVisible) {
        if (this.resultVisible && !isVisible) {
          this.$emit('hide', false);
        }
      },
    },
  },
  watch: {
    resultVisible(newValue) {
      if (newValue) {
        this.getFinancial();
      }
    },
  },
  mounted() {
  },

  destroyed() {
    this.$store.state.isShowAsideModal = false;
  },
  methods: {
    // 关闭智能凭证
    closeQuick() {
      this.$store.commit('app/SET_QUICK_ACCOUNT', 0);
    },
    // 返回load页重做凭证
    showLoad() {
      this.cardVisible = false;
      this.$emit('showLoad');
    },
    // 获取财务数据
    getFinancial() {
      const accPeriods = this.accountPeriod.Format('yyyy-MM');
      getFinancial({ accPeriods }).then((res) => {
        console.log(res);
        [this.financial] = res.data.data;
      });
    },
  },
};
</script>

<style lang="scss"   scoped>
$imgs: "/static/imgs/CMB";
.cmb-dialog-wapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
  .cmb-dialog {
    position: relative;
    top: 10vh;
    margin: 0 auto;
    background: #fff;
    padding-top:64px;
    width: 778px;
    height: 361px;
    z-index: 2027;
    box-sizing: border-box;
    background-image: url("#{$imgs}/result-bg.png");
    background-repeat: no-repeat;
    .cmb-dialog-body {
      width: 778px;
      height: 299px;
      .resultCard{
        width:628px;
        height:263px;
        background:rgba(255,255,255,1);
        border-radius:4px;
        margin: 0 auto;
        position: relative;
        &::before{
          content: '';
          position: absolute;
          width:20px;
          height:20px;
          top:62px;
          background:rgba(24,139,226,1);
          border-radius:50%;
          left: -10px;
        }
        &::after{
          content: '';
          position: absolute;
          width:20px;
          height:20px;
          top: 62px;
          right: -10px;
          background:RGBA(24, 202, 226, 1);
          border-radius:50%;
        }
        .resultCard-header{
          padding-top: 18px;
          height: 72px;
          box-sizing: border-box;
          p{
            text-align: center;
            font-size:14px;
            font-family:MicrosoftYaHei;
            font-weight:400;
            color:rgba(85,85,85,1);
            margin-bottom: 3x;
          }
          h3{
            font-size:18px;
            font-family:MicrosoftYaHei;
            font-weight:400;
            color:rgba(66,66,66,1);
            text-align: center;
            position: relative;
            .button{
              position: absolute;
              right: 198px;
              top: -2px;
            }
          }
        }
        .resultCard-body{
          margin: 0 12px;
          border-top: 1px dashed rgba(221,221,221,1);

          .button-box{
            margin-top: 18px;
            text-align: center;
            button{
              margin-left: 5px;
            }
          }
          .detail-box{
            margin-top: 20px;
            .detail-left{
              font-size:18px;
              font-family:MicrosoftYaHeiLight;
              font-weight:300;
              color:rgba(85,85,85,1);
              text-align: right;
              width: 50%;
              padding-right: 15px;
              box-sizing: border-box;
              span{
                font-size:18px;
                font-family:MicrosoftYaHei;
                font-weight:400;
                color:rgba(85,85,85,1);
              }
            }
            .detail-right{
              font-size:16px;
              font-family:MicrosoftYaHei;
              font-weight:400;
              color:rgba(24,138,226,1);
              line-height:26px;
              text-align: left;
              width: 50%;
              padding-left: 15px;
              box-sizing: border-box;
              li{
                i{
                  font-family: 'Helvetica Neue',Helvetica,Arial,Sans-serif;
                }
              }
              .grey{
                color: #fe699d;
              }
            }
          }
        }
      }
    }
    .cmb-dialog-header {
      .header-close {
        position: absolute;
        right: -7px;
        top: -42px;
        cursor: pointer;
        img{
        width: 41px;
        opacity: 0.8;
      }
      }
    }
  }
}
// .cmb-dialog-footer {
//   height: 80px;
//   .footer-tips {
//     width: 706px;
//     height: 30px;
//     line-height: 30px;
//     text-align: center;
//     background: rgba(226, 242, 251, 1);
//     border: 1px solid rgba(179, 221, 253, 1);
//     font-weight: 400;
//     color: rgba(18, 73, 116, 1);
//     margin: 0 auto;
//     margin-top: 18px;
//     .news-item > a{
//       cursor: pointer;
//       color: inherit;
//     }
//     .news-item > a:hover {
//       text-decoration: underline;
//     }
//   }
// }
</style>
<style scoped>
.button-box::v-deep .el-button--mini{
  width: 80px;
}
</style>
