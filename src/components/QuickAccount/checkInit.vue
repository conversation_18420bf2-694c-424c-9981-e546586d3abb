<template>
  <div
    class="cmb-dialog-wapper"
    v-if="cardVisible"
  >
    <div class="cmb-dialog">
      <div class="cmb-dialog-header">
        <div class="header-item header-ico"> </div>
        <p>检测到您未完成以下设置，为不影响您使用智能凭证，建议完善</p>
        <span
          class="header-close"
          @click="closeQuick"
        ><img
            src="/static/cmb/cmbdlgclose.png"
            alt=""
          ></span>
      </div>
      <div class="cmb-dialog-body clearfix">
        <div class="tip-box">
          <p>
            <i
              class="fa fa-check-square-o"
              aria-hidden="true"
              v-if="initial"
            ></i>
            <i
              class="fa fa-times"
              style="color: red"
              aria-hidden="true"
              v-else
            ></i>
            <span>账套初始化（旧账迁移或者期初录入）</span>
            <router-link to="/maintenance/ledgerDataMigration">
              <el-button
                type="primary"
                size="medium"
                style="font-size: 14px; margin-left: 10px;"
                v-if="!initial"
                @click="closeQuick"
              >旧账迁移</el-button>
            </router-link>
            <router-link to="/options/openingBalance">
              <el-button
                type="primary"
                size="medium"
                style="font-size: 14px; margin-left: 10px;"
                v-if="!initial"
                @click="closeQuick"
              >期初录入</el-button>
            </router-link>
          </p>
          <p>
            <i
              class="fa fa-check-square-o"
              aria-hidden="true"
              v-if="registered"
            ></i>
            <i
              class="fa fa-times"
              style="color: red"
              aria-hidden="true"
              v-else
            ></i>
            <span>绑定电子税务局</span>
            <router-link to="/options/companyInformation?execute=openLogin">
              <el-button
                type="primary"
                size="medium"
                style="font-size: 14px; margin-left: 10px;"
                v-if="!registered"
                @click="closeQuick"
              >前往绑定</el-button>
            </router-link>
          </p>
        </div>
        <div class="bottom-button">
          <el-button
            @click="showBook"
            type="primary"
            size="medium"
            style="font-size: 14px"
          >不了，直接做账</el-button>
          <el-checkbox
            v-model="checked"
            style="margin-left: 10px;"
          >不再提醒</el-checkbox>
        </div>
      </div>
    </div>
    <div
      class="v-modal"
      tabindex="2"
      style="z-index: 2026;"
    ></div>
  </div>
</template>
<script>
export default {
  props: {
    checkInitVisible: {
      type: Boolean,
      required: true,
    },
    registered: Number, // 税局信息登录状态 1已完成 0未完成
    initial: Number, // 账套初始状态 1已完成 0未完成
  },
  data() {
    return {
      checked: false, // 勾选不再提示
    };
  },
  computed: {
    cardVisible: {
      get() {
        return this.checkInitVisible;
      },
      set(isVisible) {
        if (this.checkInitVisible && !isVisible) this.$emit('hide');
      },
    },
    selectCompanyId() {
      return this.$store.state.user.userInfo.selectCompanyId;
    },
  },
  methods: {
    // 退出智能凭证
    closeQuick() {
      this.$store.commit('app/SET_QUICK_ACCOUNT', 2);
    },
    // 打开智能凭证首页
    showBook() {
      // 跳出去之前检查是否已勾选不再提示，有的话记录到浏览器缓存中
      if (this.checked) {
        window.localStorage.setItem(
          `checkInit${this.selectCompanyId}`,
          'false',
        );
      }
      this.$emit('showBook');
      this.cardVisible = false;
    },
  },
};
</script>

<style lang="scss"   scoped>
.red_text {
  color: red;
}
.cmb-dialog-wapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
}

.cmb-dialog {
  position: relative;
  top: 10vh;
  margin: 0 auto;
  background: #fff;
  width: 880px;
  z-index: 2027;
  box-sizing: border-box;
}

.cmb-dialog .cmb-dialog-header {
  height: 106px;
  background: -webkit-linear-gradient(
    6deg,
    rgba(24, 138, 226, 1) 0%,
    rgba(61, 193, 248, 1) 100%
  );
  background: -o-linear-gradient(
    6deg,
    rgba(24, 138, 226, 1) 0%,
    rgba(61, 193, 248, 1) 100%
  );
  background: linear-gradient(
    96deg,
    rgba(24, 138, 226, 1) 0%,
    rgba(61, 193, 248, 1) 100%
  );
  color: #ffffff;
  font-weight: bold;
  box-sizing: border-box;
  position: relative;
  top: 0;
  right: 0;
  p {
    text-align: center;
    font-size: 18px;
    line-height: 106px;
  }
  .header-close {
    position: absolute;
    right: -7px;
    top: -50px;
    cursor: pointer;
    img {
      width: 41px;
      opacity: 0.8;
    }
  }
  .header-item {
    display: inline-block;
  }
  .header-item.header-ico {
    position: absolute;
    top: 20px;
    left: 35px;
    width: 65px;
    height: 66px;
    background: no-repeat url("/static/cmb/cmbdlgico.png");
  }
  .header-item.header-info {
    margin-left: 127px;
    margin-top: 31px;
  }
  .header-item.header-date {
    margin-left: 127px;
    margin-top: 47px;
  }
  .header-item.header-date > span {
    display: inline-block;
  }
  .header-item.header-date .input {
    box-sizing: border-box;
    width: 104px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: rgba(30, 147, 229, 1);
    border: 1px solid rgba(255, 255, 255, 1);
    border-radius: 4px;
  }
  .header-item.header-date .text {
    padding: 0 8px;
  }
}
.cmb-dialog .cmb-dialog-body {
  height: 400px;
  background: #fff;
  overflow: hidden;
  .tip-box {
    margin-top: 100px;
    padding-left: 260px;
    p {
      line-height: 38px;
      font-size: 18px;
    }
  }
  .bottom-button {
    text-align: center;
    margin-top: 36px;
  }
}
</style>
