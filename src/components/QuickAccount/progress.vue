<template>
    <div class="loadingBox">
        <i class="loading" :class="{'loading-paused': !paused }"></i>
        <span>{{num}}%</span>
    </div>
</template>
<script>
export default {
  props: {
    num: {
      type: Number,
      default: 0,
    },
    paused: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {};
  },
};
</script>
<style scoped lang="scss"  >
$imgs: "/static/imgs/CMB";
@keyframes loading {
  0% {
    transform-origin: center;
    transform: rotate(360deg);
  }
  100% {
    transform-origin: center;
    transform: rotate(0deg);
  }
}
.loadingBox{
    position: relative;
    line-height: 190px;
    text-align: center;
    width: 190px;
    height: 190px;
    // background: #66ccff;
    font-size: 28px;
    color: #fff;
    background-image: url("#{$imgs}/3.png");
    background-repeat: no-repeat;
    background-position: center;
}
.loading {
  position: absolute;
  display: block;
  z-index: 13;
//   margin: 15px auto;
  width: 190px;
  height: 190px;
  border-radius: 50%;
  background-image: url("#{$imgs}/0.png");
  background-repeat: no-repeat;
  background-position: center;
  animation-play-state: running;
  animation: loading 1.9s linear infinite;
}
.loading-paused{
    animation-play-state: paused;

}
</style>
