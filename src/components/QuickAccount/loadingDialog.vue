<template>
  <div class="cmb-dialog-wapper" v-if="cardVisible">
    <div class="cmb-dialog">
      <div class="cmb-dialog-header">
        <!-- 备注：暂定时，点击项目可以进入这个项目的凭证预览状态，继续就回到这个界面继续跑。终止就是退回到上一个页面 -->
        <span class="header-close" @click="showBook">
          <img src="/static/imgs/CMB/返回.png" alt="">
        </span>
      </div>
      <div class="cmb-dialog-body">
        <div class="loadBox">
          <Progress :num="num" class="load" :paused="isPause"></Progress>
          <div class="message-left-top message animated fadeInDown" v-if="purchaseTips.length > 0" >
            <ul>
              <li v-for="(item, index) in purchaseTips" :key="item.tip" class="clearfix">
                <span>{{index+1}}.{{item.tip}}</span>
                <p class="fr">
                  <el-tooltip effect="light"  placement="top-start">
                    <div slot="content">{{item.advice}}<br/></div>
                    <router-link :to="{name: 'outputTax'}">
                      <span class="yellow">建议</span>
                    </router-link>
                  </el-tooltip>
                  <router-link :to="{name: 'outputTax'}">
                    <span class="blue">处理</span>
                  </router-link>

                    <span calss="white"  @click="removeTip(item)">忽略</span>
                </p>
              </li>
            </ul>
          </div>
          <div
          class="message-left-bottom message animated fadeInDown"
          v-if="incomeTips.length > 0" >
            <ul>
              <li v-for="(item, index) in incomeTips" :key="item.tip" class="clearfix">
                <span>{{index+1}}.{{item.tip}}</span>
                <p class="fr">
                    <el-tooltip effect="light"  placement="top-start">
                    <div slot="content">{{item.advice}}<br/></div>
                    <router-link :to="{name: 'inputTax'}">
                      <span class="yellow">建议</span>
                    </router-link>
                  </el-tooltip>
                  <router-link :to="{name: 'inputTax'}">
                    <span class="blue">处理</span>
                  </router-link>
                    <span calss="white"  @click="removeTip(item)">忽略</span>
                </p>
              </li>
            </ul>
          </div>

          <div class="message-right-bottom message animated fadeInDown" v-if="costTips.length > 0" >
              <ul>
                  <li v-for="(item, index) in costTips" :key="item.tip" class="clearfix">
                      <span>{{index+1}}.{{item.tip}}</span>
                      <p class="fr">
                            <el-tooltip effect="light"  placement="top-start">
                          <div slot="content">{{item.advice}}<br/></div>
                          <router-link :to="{name: 'trade'}">
                            <span class="yellow">建议</span>
                          </router-link>
                        </el-tooltip>
                        <router-link :to="{name: 'trade'}">
                          <span class="blue">处理</span>
                        </router-link>
                        <span calss="white"  @click="removeTip(item)">忽略</span>
                      </p>
                  </li>
              </ul>
          </div>
          <div class="message-right-top message animated fadeInDown" v-if="tradeTips.length > 0" >
              <ul>
                  <li v-for="(item, index) in tradeTips" :key="item.tip" class="clearfix">
                      <span>{{index+1}}.{{item.tip}}</span>
                      <p class="fr">
                            <el-tooltip effect="light"  placement="top-start">
                          <div slot="content">{{item.advice}}<br/></div>
                          <router-link :to="{name: 'trade'}">
                            <span class="yellow">建议</span>
                          </router-link>
                        </el-tooltip>
                            <router-link :to="{name: 'trade'}">
                            <span class="blue">处理</span>
                          </router-link>
                          <span calss="white" @click="removeTip(item)">忽略</span>
                      </p>
                  </li>
              </ul>
          </div>
        </div>
        <p class="middle-hint">正在为您生成凭证</p>
        <p class="button-box">
            <el-button v-show="!isPause" class="pause" @click="controlPlay">
              <i class="fa"
              :class="{'fa-pause': isPause, 'fa-play': !isPause}"
              aria-hidden="true"></i>
              {{'继续'}}
            </el-button>
        </p>
      </div>
    </div>

    <div class="v-modal" style="z-index: 2026;"></div>
  </div>
</template>
<script>
import { checkTips } from '@/api/statement';
import {
  quickVoucherInput, quickVoucherOutput, quickVoucherBank, quickVoucherCash, quickVoucherCost,
} from '@/api/account';
import Progress from './progress.vue';

export default {
  props: {
    loadVisible: {
      type: Boolean,
      required: true,
    },
    accountPeriod: {
      type: Date,
      required: true,
    },
  },
  data() {
    return {
      num: 0, // 生成凭证的进度
      isPause: true, // 暂停状态
      tips: [], // 提示列表
    };
  },
  components: {
    Progress,
  },
  computed: {
    cardVisible: {
      get() {
        return this.loadVisible;
      },
      set(isVisible) {
        if (this.loadVisible && !isVisible) this.$emit('hide', false);
      },
    },
    // 销项提示
    incomeTips() {
      return this.tips.filter((item) => item.moduleId === 2);
    },
    // 进项提示
    purchaseTips() {
      return this.tips.filter((item) => item.moduleId === 1);
    },
    // 资金提示
    tradeTips() {
      return this.tips.filter((item) => (item.moduleId === 4 || item.moduleId === 3));
    },
    // 费用提示
    costTips() {
      return this.tips.filter((item) => item.moduleId === 5);
    },
    // 智能凭证显示状态
    showQuickAccount() {
      return this.$store.state.showQuickAccount;
    },
    isAgent() {
      return this.$store.state.app.PROJECT_TYPE === 'agent';
    },
  },
  watch: {
    // 当智能凭证由图标状态返回loading页时，刷新提示列表
    showQuickAccount(newVal, oldVal) {
      if (oldVal === 2 && newVal === 1) {
        this.getTips();
      }
    },
  },
  async mounted() {
    // 获取提示信息
    await this.getTips();
    // 如果没有提示直接生成凭证
    if (this.tips.length === 0) {
      this.playLoad();
    } else {
      this.isPause = false;
    }
  },

  destroyed() {
    // 销毁时重置进度
    this.pauseLoad();
  },
  methods: {
    // 开始生成凭证
    playLoad() {
      this.isPause = true;
      this.quickVoucher();
    },
    // 暂停
    pauseLoad() {
      this.isPause = false;
    },
    controlPlay() {
      const played = this.isPause;
      if (played) {
        this.pauseLoad();
      } else {
        this.playLoad();
      }
    },
    // 忽略提示
    removeTip(tip) {
      const index = this.tips.indexOf(tip);
      this.tips.splice(index, 1);
    },
    // 返回做账首页
    showBook() {
      this.$emit('showBook');
      this.cardVisible = false;
    },
    // 获取提示
    async getTips() {
      const accountPeriod = this.accountPeriod.Format('yyyy-MM');
      const tipsRsp = await checkTips(accountPeriod);
      this.tips = tipsRsp.data.data;
    },
    // 创建进度增长计时器，模拟加载状态
    setInterval(num) {
      const time = window.setInterval(() => {
        if (this.num >= num) {
          window.clearInterval(time);
          return;
        }
        this.num += 1;
      }, 100);
    },
    // 生成凭证
    async quickVoucher() {
      const accountPeriod = this.accountPeriod.Format('yyyyMM');
      let voucherCount = 0; // 重置凭证数量为零
      // 将加载状态分为五个阶段，每生成一种凭证，完成20. 生成凭证的顺序不可修改
      try {
        this.setInterval(20);
        await quickVoucherOutput(accountPeriod).then((res) => {
          voucherCount += res.data.data[0];
        });
      } catch (e) {
        console.error(e);
      }
      this.num = 20;
      try {
        this.setInterval(40);
        await quickVoucherInput(accountPeriod).then((res) => {
          voucherCount += res.data.data[0];
        });
      } catch (e) {
        console.error(e);
      }
      this.num = 40;
      try {
        this.setInterval(60);
        await quickVoucherBank(accountPeriod).then((res) => {
          voucherCount += res.data.data[0];
        });
      } catch (e) {
        console.error(e);
      }
      this.num = 60;
      try {
        this.setInterval(80);
        await quickVoucherCash(accountPeriod).then((res) => {
          voucherCount += res.data.data[0];
        });
      } catch (e) {
        console.error(e);
      }
      this.num = 80;
      if (!this.isAgent) {
        try {
          this.setInterval(100);
          await quickVoucherCost(accountPeriod).then((res) => {
            voucherCount += res.data.data[0];
          });
        } catch (e) {
          console.error(e);
        }
      }

      this.num = 100;
      this.cardVisible = false;
      this.$emit('showResult', voucherCount);
    },
  },
};
</script>

<style lang="scss"   scoped>
$imgs: "/static/imgs/CMB";
.cmb-dialog-wapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
  .cmb-dialog {
    position: relative;
    top: 10vh;
    margin: 0 auto;
    background: #fff;
    width: 778px;
    height: 470px;
    z-index: 2027;
    box-sizing: border-box;
    background-image: url("#{$imgs}/white-bg.jpg");
    // background-repeat: no-repeat;
    .cmb-dialog-body {
      width: 778px;
      height: 449px;
      .loadBox {
        width: 350px;
        height: 204px;
        margin: 0 auto;
        top: 76px;
        position: relative;
        padding-top: 10px;
        .load {
          margin: 0 auto;
        }
        .message {
          position: absolute;
          background: rgba(0, 0, 0, 0.18);
          border-radius: 4px;
          min-width: 270px;
          li {
            line-height: 21.4px;
            font-size: 12px;
            padding-right: 6px;
            padding-left: 3px;
            span {
              color: #fff;
            }
            p {
              span {
                display: inline-block;
                margin-left: 6px;
                cursor: pointer;
              }
              .yellow {
                color: RGBA(128, 251, 94, 1);
              }
              .blue {
                color: RGBA(85, 229, 255, 1);
              }
              .white {
                color: #fff;
              }
            }
          }
        }
        .message-left-bottom {
          left: -82%;
          top: 140px;
        }
        .message-left-top {
          left: -82%;
          top: -32px;
        }
        .message-right-bottom {
          left: 103%;
          top: 140px;
        }
        .message-right-top {
          left: 103%;
          top: -32px;
        }
        // .left-top{
        //     top: 10px;
        //     left: 5px;
        // }
        // .line-across{
        //     background: #fff;
        //     width: 40px;
        //     height: 1px;
        // }
        // .line-vertical{
        //     background: #fff;
        //     width: 31px;
        //     height: 1px;
        //     transform: rotate(45deg);
        //     position: absolute;
        //     left: 0px;
        //     top: 0px;
        // }
      }
      .middle-hint {
        font-size: 14px;
        font-family: MicrosoftYaHei;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        text-align: center;
        margin-top: 94px;
      }
      .button-box {
        text-align: center;
        margin-top: 30px;
        .pause {
          width: 87px;
          height: 32px;
          background: rgba(255, 255, 255, 1);
          border-radius: 4px;
          color: RGBA(8, 139, 243, 1);
          font-size: 14px;
        }
        .reset {
          width: 87px;
          height: 32px;
          border: 1px solid rgba(255, 255, 255, 1);
          border-radius: 4px;
          background: transparent;
          color: #fff;
          font-size: 14px;
        }
      }
    }
    .cmb-dialog-header {
      height: 41px;
      line-height: 41px;
      text-align: center;
      color: #fff;
      font-size: 12px;
      .header-close {
        position: absolute;
        right: -7px;
        top: -42px;
        cursor: pointer;
        img{
        width: 41px;
        opacity: 0.8;
      }
      }
    }
  }
}
.cmb-dialog-footer {
  height: 80px;
  .footer-tips {
    width: 706px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: rgba(226, 242, 251, 1);
    border: 1px solid rgba(179, 221, 253, 1);
    font-weight: 400;
    color: rgba(18, 73, 116, 1);
    margin: 0 auto;
    margin-top: 18px;
    .news-item > a{
      cursor: pointer;
      color: inherit;
    }
    .news-item > a:hover {
      text-decoration: underline;
    }
  }
}
</style>
