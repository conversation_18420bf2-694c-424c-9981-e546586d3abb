<template>
  <div>
    <el-dialog
      :title="title"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :visible.sync="dialogVisible"
      :width="hasRightSlot ? '840px' : '600px'"
      @close="doClose"
      close-on-press-escape
    >
      <el-row :gutter="20" class="dialog-body import-form">
        <el-col :span="hasRightSlot ? 13 : 24" :class="{ 'center-content': !hasRightSlot }">
          <el-upload
            drag
            ref="uploadRef"
            :accept="acceptType"
            :action="actionUrl"
            :mutiple="true"
            :auto-upload="false"
            :on-success="handleSuccess"
            :on-error="handleError"
            :on-change="handleUploadChange"
            :on-remove="handleRemove"
            :file-list="fileList"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击选择文件</em></div>
            <template #tip>
              <div class="el-upload__tip">
                <slot name="tip">
                  <span>{{ tipsText }}</span>
                  <slot name="tip-extra"></slot>
                </slot>
              </div>
            </template>
          </el-upload>
          <div v-show="showMsg" class="uploaded-file">
            <i
              title="成功"
              v-if="isSuccess"
              class="el-icon-upload-success el-icon-circle-check"
            ></i>
            <i title="失败" class="el-icon-upload-fail el-icon-warning" v-else></i>
            <span class="uploaded-file-name">{{ message }}</span>
          </div>
        </el-col>
        <el-col :span="11" v-if="hasRightSlot" class="import-dlg-right">
          <slot name="importDlgRright"></slot>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button v-if="isLoading" type="primary" :loading="true">上传中</el-button>
        <el-button v-else type="primary" @click="handleImport">上传</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
 ref, computed, defineComponent, getCurrentInstance,
} from 'vue';
import { Message } from 'element-ui';

export default defineComponent({
  props: {
    importVisible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      default: '文件导入',
    },
    beforeUpload: {
      type: Function,
    },
    acceptType: {
      type: String,
      default: '',
    },
    actionUrl: {
      type: String,
      default: '',
    },
    tipsText: {
      type: String,
      default: '只能上传 xlsx/xls 文件，且不超过10MB',
    },
  },

  emits: ['hide', 'success', 'getmaintable', 'insertSubject', 'close'],

  setup(props, { emit }) {
    // refs
    const uploadRef = ref(null);
    const instance = getCurrentInstance();

    // 响应式数据
    const fileList = ref([]);
    const showMsg = ref(false);
    const isSuccess = ref(true);
    const message = ref('');
    const isLoading = ref(false);

    // 检查是否有右侧插槽内容
    const hasRightSlot = computed(() => !!instance?.proxy?.slots?.importDlgRright);

    // 计算属性
    const dialogVisible = computed({
      get: () => props.importVisible,
      set: (isVisible) => {
        if (!isVisible && isLoading.value) {
          return;
        }
        fileList.value = [];
        emit('hide', false);
      },
    });

    // 方法
    const handleImport = () => {
      if (props.beforeUpload && !props.beforeUpload()) {
        return;
      }
      isLoading.value = true;
      uploadRef.value.submit();
    };

    const handleSuccess = (response) => {
      message.value = '';
      showMsg.value = true;
      isSuccess.value = true;
      isLoading.value = false;

      const returnMessage = response.returnMessage || '操作成功';
      const h = instance.proxy.$createElement;
      const messages = returnMessage.split('\r\n');
      const span = messages.map((msg) => [
        h('span', msg),
        h('br'),
      ]).flat();
      span.pop();

      Message({
        type: 'success',
        message: h('p', span),
        showClose: true,
      });

      setTimeout(() => {
        emit('success', response);
        doClose();
      }, 500);
    };

    const handleError = (err, file, fileList) => {
      message.value = err;
      showMsg.value = true;
      isSuccess.value = false;
      isLoading.value = false;
    };

    const handleUploadChange = (file, fileList) => {
      fileList.value = fileList;
    };

    const handleRemove = () => {
      fileList.value = [];
    };

    const doClose = () => {
      fileList.value = [];
      showMsg.value = false;
      isLoading.value = false;
    };

    return {
      uploadRef,
      fileList,
      showMsg,
      isSuccess,
      message,
      isLoading,
      hasRightSlot,
      dialogVisible,
      handleImport,
      handleSuccess,
      handleError,
      handleUploadChange,
      handleRemove,
      doClose,
    };
  },
});
</script>

<style lang="scss" scoped>
.uploaded-file {
  display: flex;
  align-items: center;
  margin-top: 10px;
  padding: 8px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;

  i {
    margin-right: 8px;
    font-size: 16px;

    &.el-icon-upload-success {
      color: #67c23a;
    }

    &.el-icon-upload-fail {
      color: #f56c6c;
    }
  }

  .uploaded-file-name {
    color: #606266;
    font-size: 14px;
  }
}

.import-dlg-right {
  border-left: 1px solid #e6e6e6;
  padding-left: 20px;
}

.center-content {
  display: flex;
  flex-direction: column;
  align-items: center;

  :deep(.el-upload-dragger) {
    width: 400px;
  }
}

.import-form {
  .el-upload {
    width: 100%;
  }
  .el-upload-dragger {
    width: 100%;
  }
}

.import-dlg-right {
  border-left: 1px solid #dcdfe6;
  min-height: 250px;
}
</style>
