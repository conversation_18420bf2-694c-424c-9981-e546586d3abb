<template>
  <el-dialog
  :title="courseTitle"
  :visible.sync="dialogVisible"
  custom-class="courseDialog"
  :close-on-click-modal="false"
  destroy-on-close
  :modal-append-to-body="false">
    <iframe
    width="100%"
    height="100%"
    v-if="dialogVisible"
    :src="`/app.html#/courseFrame/${ videoId }?visible=${ visible }`"></iframe>
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    videoId: {
      type: String,
      required: true,
    },
    titleSuffix: String,
  },
  data() {
    return {};
  },
  components: {},
  mounted() {},
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(isVisible) {
        if (this.visible && !isVisible) {
          this.$emit('hide', false);
        }
      },
    },
    courseTitle() {
      const { titleSuffix } = this;
      return titleSuffix ? `操作教程--${titleSuffix}` : '操作教程';
    },
  },
  methods: {},
};
</script>

<style lang="scss"  >
  .courseDialog {
    width: 900px;
    .el-dialog__header {
      text-align: left;
    }
    .el-dialog__body {
      padding: 0;
      height: 600px;
      overflow: hidden;
    }
  }
</style>
