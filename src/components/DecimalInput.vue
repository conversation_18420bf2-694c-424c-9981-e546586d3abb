<!--
 * @Component: 通用数值录入
 * @Maintainer: 阮小文
 * @Description:
 *
 * 使用场景：
 * 数值手工录入并要求有效性检测的场景
 *
-->
<template>
  <el-input
    v-model="internalStringValue"
    class="DecimalInput"
    v-bind="$attrs"
    @blur="onBlur"
    v-on="$listeners"
  >
    <template v-slot:append>
      <slot name="append"></slot>
    </template>
  </el-input>
</template>

<script>
import { defineComponent, ref, watch } from 'vue';
import { Decimal } from 'decimal.js';

// type RoundingMode =
//   | 'ROUND_UP'
//   | 'ROUND_DOWN'
//   | 'ROUND_CEIL'
//   | 'ROUND_FLOOR'
//   | 'ROUND_HALF_UP'
//   | 'ROUND_HALF_DOWN'
//   | 'ROUND_HALF_EVEN'
//   | 'ROUND_HALF_CEIL'
//   | 'ROUND_HALF_FLOOR'

// 检测是否有效的数字前缀
// 包括合法的数字，以及 "", "-", "-0", "-0." 等等合法数字的前缀状态
function isValidPrefix(input) {
  // 0, +0, -0, 10, +10, -10
  if (/^-?(?:0|[1-9][0-9]*)?$/.test(input)) return true;
  // 0. 0.0
  if (/^-?(?:0|[1-9][0-9]*)(?:\.\d*)?$/.test(input)) return true;
  return false;
}

// 检测是否可以被 Decimal 解析
function canHandleByDecimal(input) {
  return isValidPrefix(input) && input !== '' && input !== '-';
}

// 检测两个值是否相等，包括 NaN、正负 0
const isSameValue = typeof Object.is === 'function'
    ? Object.is
    : ((a, b) => {
        if (typeof a === 'number') {
          // +0, -0 的情况
          if (a === b) return 1 / a === 1 / b;
          // a，b 均为 NaN 返回 true
          // eslint-disable-next-line no-self-compare
          if (a !== a && b !== b) return true;
          return false;
        }
        return a === b;
      });

// 尝试将非法的输入修整为合法的数字前缀（不一定成功）
// 极端情况可能需要多次调用该函数进行修复，直至变成空串为止
function tryFixupPrefix(input) {
  // 使用白名单过滤出合法的字符
  let output = input.replace(/[^\d.+-eE]/, '');

  // 负号只允许在开头出现，且只能出现一次
  let first = true;
  output = output.replace(/-/g, (m, i) => {
    if (i === 0 && first) {
      first = false;
      return m;
    }
    return '';
  });

  // 移除掉非法的开头的 0，例如 00, 01 等开头多余的 0
  output = output.replace(/^(-)?0+(\d+)/, (_, minus, tail) => (minus ?? '') + tail);

  // 小数点只能出现一次，且不能是在数字的开头
  first = true;
  output = output
    .replace(/^(-)?\.+/, (_, minus) => minus ?? '')
    .replace(/\./g, (m) => {
      if (first) {
        first = false;
        return m;
      }
      return '';
    });

  // 上面的所有修复策略有作用，则返回
  if (output !== input) return output;

  // 兜底方案（理论上不会进这里，待测试）
  // 上面的所有修复策略都无效，则尝试从末尾移除一个字符
  return output.slice(0, -1);
}

// 将数字、字符串的输入，统一转为字符串
// 可以保留 -0，以兼容 v-model.number 形式的绑定
function numberToString(input) {
  return isSameValue(input, -0) ? '-0' : String(input);
}

// 规范化输入的字符串，以确保输入是合法的数字前缀
function normalizeInput(input) {
  let output = input;
  if (isValidPrefix(output)) return output;
  // 非法数字情况，尝试规范化修复
  output = tryFixupPrefix(output);
  return normalizeInput(output);
}

// 根据组件提供的约束条件，变换输入的字符串
function transformInput({
  val,
  oldVal,
  max,
  min,
  precision,
  rounding,
}) {
  let value = normalizeInput(numberToString(val));
  const minStr = min == null ? '' : String(min);
  const maxStr = max == null ? '' : String(max);

  // 最大值最小值限制下，无合法取值，直接返回空串
  if (maxStr && minStr && new Decimal(maxStr).lessThan(minStr)) {
    console.warn('DecimalInput 的 max 和 min 设置存在矛盾');
    return '';
  }

  // 如果 min >= 0，则移除负号
  if (value.startsWith('-') && minStr && new Decimal(min).greaterThanOrEqualTo(0)) {
    value = value.slice(1);
  }

  // 如果最大值小于 0，则必须是负数
  if (!value.startsWith('-') && maxStr && new Decimal(max).lessThan(0)) {
    value = `-${value}`;
  }

  // 处理精度、小数位
  if (value.includes('.')) {
    // 如果小数点是在末尾
    if (value.endsWith('.')) {
      // 如果保留位为 0，则移除小数点
      if (precision === 0) {
        value = value.slice(0, -1);
      }
    } else {
      // 如果小数点不在末尾，则按照精度和舍入规则确保小数部分合法，其中
      // 1. 小数长度小于或等于需要保留的小数位，不做处理
      // 2. 小数长度大于需要保留的小数位，则使用 Decimal 的 toFixed 处理，但是需要注意：
      //    Decimal 会将 `-0.0` 形式的小数，转换成 `0.0`，即抛弃负号，需要补回来
      const isNegative = value.startsWith('-');
      const d = value.split('.')[1];
      if (d.length > precision) {
        value = new Decimal(value).toFixed(precision, rounding);
        if (isNegative) value = `-${value}`;
      }
    }
  }

  // 处理最小值
  if (canHandleByDecimal(value) && minStr && new Decimal(value).lessThan(minStr)) {
    // 考虑这种场景，min 为  0.5，用户输入 `0`、`0.` 此时输入比 min 小，
    // 但是用户可能还在输入中，后续可能是 0.6 这种合法的值。
    // 因此不能此时自动纠正输入，而是应当放行等待用户的后续输入。
    //
    // 如果用户相继输入 `0`, `0.`, `0.1`，此时，最后输入的这个 `1` 已经可以判断是非法的了，此时应该拒绝，（回滚到 oldVal）或者纠正（使用 min）
    // 实际上，无法判断需要等待用户继续录入的场景，就是用户的录入是 min 的前缀这种场景，
    // 如果不是前缀，要么是合法的录入，要么已经是非法的了。
    //
    // 另一种情况，如果用户一开始粘贴 `0.1` 这样的非法值进来，应当立即纠正，（回退到 oldVal 或 min）
    //
    // 负数的情况也一样
    // 如 min 为 `-0.5`, 用户相继输入 `-0`, `-0.` 等前缀时，都应当放行
    //
    // 实现：
    if (!minStr.startsWith(value)) {
      value = (oldVal != null)
        ? transformInput({
 val: oldVal, precision, rounding, max, min,
})
        : minStr;
    }
  }

  // 处理最大值
  if (canHandleByDecimal(value) && maxStr && new Decimal(value).greaterThan(maxStr)) {
    // 类似最小值的处理，最大值需要考虑负小数，例如 max 为 `-0.5`，用户在输入 `-0.` 前缀的时候，
    // 还无法判断，需要等待用户继续录入。其他情况才纠正。
    if (!maxStr.startsWith(value)) {
      return oldVal != null
      ? transformInput({
          val: oldVal, precision, rounding, max, min,
        })
      : maxStr;
    }
  }

  return value;
}

export default defineComponent({
  name: 'DecimalInputV2',

  props: {
    // 外部传入的 value
    value: {
      required: true,
      type: [Number, String],
      default: '',
    },
    // 最大值（含）
    max: { type: [Number, String], default: '1000000000' },
    // 最小值（含）
    min: { type: [Number, String], default: '-1000000000' },
    // 包含的小数位数
    precision: { type: [Number], default: 8 },
    // 舍入模式
    rounding: { type: String, default: 'ROUND_DOWN' },
  },

  setup(props, { emit }) {
    // 确保输入是合法的
    const transform = (val, oldVal) => {
      const {
        precision, min, max, rounding,
      } = props;
      return transformInput({
        val,
        oldVal,
        precision,
        rounding: Decimal[rounding],
        max,
        min,
      });
    };

    // 本地的字符串值
    const internalStringValue = ref(transform(props.value));

    watch(internalStringValue, (val, oldVal) => {
      const value = transform(val, oldVal);
      if (internalStringValue.value !== value/* && !isEqual */) {
        internalStringValue.value = value;
        emit('input', value);
      }
    });

    watch(
      () => props.value,
      (val, oldVal) => {
        // 传入的可能是 -0，如果需要修改当前组件，
        // 注意需要处理 -0 的情况，否则如果使用 v-model.number 绑定，
        // 将无法输入 -0.xxx 形式的数字
        const value = transform(val, oldVal);

        // 传入的值，如果等价于本地的字符串表示（如传入 0，本地为 0.0），则不更新，
        // 以免将本地编辑中的状态冲刷掉
        const isEqual = canHandleByDecimal(value)
          && canHandleByDecimal(internalStringValue.value)
          && new Decimal(value).equals(internalStringValue.value);
        // console.log('prop change, is not equal:', !isEqual, internalStringValue.value !== value)
        if (internalStringValue.value !== value && !isEqual) {
          internalStringValue.value = value;
        }
        // console.log('prop change', val, value)
      },
    );

    // 组件失去焦点的时候，检查输入是否合法的“结果”（而不是输入过程的合法数字前缀），
    // 不合法的需要自动纠正
    const onBlur = () => {
      const value = transform(internalStringValue.value);
      if (internalStringValue.value !== value) {
        internalStringValue.value = value;
      }
      // 纠正无效负数，移除没有后继数字，或者后继是一个 0 的负号
      if (internalStringValue.value === '-' || internalStringValue.value === '-0') {
        // eslint-disable-next-line no-shadow
        const value = internalStringValue.value.replace('-', '');
        internalStringValue.value = value;
        // 移除负号，由于计算出来的结果一致，并不会触发 emit，
        // 所以需要人工补一次，以免最终提交给表单的是个非法 '-' 字符串（表单可能要求是数字）
        emit('input', value);
        return;
      }
      // 纠正小数无效末尾零
      if (internalStringValue.value.includes('.') && internalStringValue.value.endsWith('0')) {
        internalStringValue.value = internalStringValue.value.replace(/0+$/, '');
      }
      // 纠正非法小数，移除处于末尾的小数点
      if (internalStringValue.value.endsWith('.')) {
        internalStringValue.value = internalStringValue.value.slice(0, -1);
      }
    };

    return {
      internalStringValue,
      onBlur,
    };
  },
});
</script>
