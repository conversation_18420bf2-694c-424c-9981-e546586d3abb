<!--
科目标签组件专用的 Cascader 组件
扩展搜索允许 展示被禁用项的功能
-->
<template>
  <div
    ref="reference"
    :class="[
      'el-cascader',
      realSize && `el-cascader--${realSize}`,
      { 'is-disabled': isDisabled }
    ]"
    v-clickoutside="() => toggleDropDownVisible(false)"
    @mouseenter="inputHover = true"
    @mouseleave="inputHover = false"
    @click="() => toggleDropDownVisible(readonly ? undefined : true)"
    @keydown="handleKeyDown">

    <el-input
      ref="input"
      v-model="multiple ? presentText : inputValue"
      :size="realSize"
      :placeholder="multiple && presentTags.length ? '' : placeholder"
      :readonly="readonly"
      :disabled="isDisabled"
      :validate-event="false"
      :class="{ 'is-focus': dropDownVisible }"
      @focus="handleFocus"
      @blur="handleBlur"
      @input="handleInput">
      <template slot="suffix">
        <i
          v-if="clearBtnVisible"
          key="clear"
          class="el-input__icon el-icon-circle-close"
          @click.stop="handleClear"></i>
        <i
          v-else
          key="arrow-down"
          :class="[
            'el-input__icon',
            'el-icon-arrow-down',
            dropDownVisible && 'is-reverse'
          ]"
          @click.stop="toggleDropDownVisible()"></i>
      </template>
    </el-input>

    <div v-if="multiple" class="el-cascader__tags">
      <el-tag
        v-for="tag in presentTags"
        :key="tag.key"
        type="info"
        :size="tagSize"
        :hit="tag.hitState"
        :closable="tag.closable"
        disable-transitions
        @close="deleteTag(tag)">
        <span :class="{'Danger': tag.invalid}">{{ tag.text }}</span>
      </el-tag>
      <input
        v-if="filterable && !isDisabled"
        v-model.trim="inputValue"
        type="text"
        class="el-cascader__search-input"
        :placeholder="presentTags.length ? '' : placeholder"
        @input="e => handleInput(inputValue, e)"
        @click.stop="toggleDropDownVisible(true)"
        @keydown.delete="handleDelete">
    </div>

    <transition name="el-zoom-in-top" @after-leave="handleDropdownLeave">
      <div
        v-show="dropDownVisible"
        ref="popper"
        style="min-width: 250px;"
        :class="['el-popper', 'el-cascader__dropdown', popperClass]">
        <!-- <el-cascader-panel
          ref="panel"
          v-show="!filtering"
          v-model="checkedValue"
          :options="options"
          :props="config"
          :border="false"
          :render-label="$scopedSlots.default"
          @expand-change="handleExpandChange"
          @close="toggleDropDownVisible(false)"></el-cascader-panel> -->
          <v-tree
            ref="panel"
            class="tree"
            :data="options"
            :props="config"
            v-show="!filtering"
            :default-expand-all="defaultExpandAll"
            :node-key="config.value"
            :check-strictly="config.checkStrictly"
            @check-change="handleCheckChange"
            :beforeCheck="beforeCheck"
            show-checkbox
            :height="250"
          >
            <template slot-scope="{node, data}">
              <slot v-bind="{node, data}" />
            </template>
          </v-tree>
          <template v-if="filterable && filtering">
            <virtual-list v-if="suggestions.length" class="el-cascader__suggestion-panel" :style="{ height: 250 + 'px', 'overflow-y': 'auto' }"
              data-key="key"
              :data-sources="suggestions"
              :data-component="SuggestionItem"
              :keeps="22"
            />
            <el-scrollbar
              ref="suggestionPanel"
              v-else
              tag="ul"
              class="el-cascader__suggestion-panel"
              view-class="el-cascader__suggestion-list"
              @keydown.native="handleSuggestionKeyDown">
              <slot name="empty">
                <li class="el-cascader__empty-text">无结果</li>
              </slot>
            </el-scrollbar>
          </template>

      </div>
    </transition>
  </div>
</template>

<script>
import { Cascader } from 'element-ui';
import VirtualList from 'vue-virtual-scroll-list';
import VTree from '@/components/v-tree';
import {
 isEmpty, isUndefined, isNull, isEqual, isFunction,
} from 'lodash-es';
import SuggestionItem from './suggestion-item.vue';

function getNodeAllPathLabel(node, showAllLevels, separator) {
  const labels = [node.label];
  if (showAllLevels) {
    while (node.parent && node.parent.label) {
      labels.unshift(node.parent.label);
      node = node.parent;
    }
  }

  return labels.join(separator);
}

console.log(Cascader);
export default {
  mixins: [Cascader],
  name: 'ElCascader',
  provide() {
    return {
      handleSuggestionClick: this.handleSuggestionClick,
    };
  },
  props: {
    beforeCheck: {
      type: Function,
    },
    defaultExpandAll: {
      type: Boolean,
    },
  },
  components: { VTree, VirtualList },
  data() {
    return {
      SuggestionItem,
    };
  },
  watch: {
    checkedValue: {
      handler: function handler(val) {
        const { value, dropDownVisible } = this;
        const { checkStrictly, multiple } = this.config;

        if (!isEqual(val, value) || value === undefined) {
          this.computePresentContent();
          // hide dropdown when single mode
          if (!multiple && !checkStrictly && dropDownVisible) {
            this.toggleDropDownVisible(false);
          }

          this.$emit('input', val);
          this.$emit('change', val);
          this.dispatch('ElFormItem', 'el.form.change', [val]);
        }
      },
    },
  },
  methods: {
    toggleDropDownVisible(visible) {
      if (this.isDisabled) return;

      const { dropDownVisible } = this;
      const { input } = this.$refs;
      visible = !isUndefined(visible) && !isNull(visible) ? visible : !dropDownVisible;
      if (visible !== dropDownVisible) {
        this.dropDownVisible = visible;
        if (visible) {
          this.$nextTick(() => {
            this.updatePopper();
            // this.panel.scrollIntoView();
          });
        }
        input.$refs.input.setAttribute('aria-expanded', visible);
        this.$emit('visible-change', visible);
      }
    },
    getSuggestions() {
      let { filterMethod } = this;

      if (!isFunction(filterMethod)) {
        filterMethod = (node, keyword) => node.text.includes(keyword);
      }

      const suggestions = this.panel.getFlattedNodes(this.leafOnly)
        .filter((node) => {
          // 扩展搜索允许 展示被禁用项的功能
          // if (node.isDisabled) return false;
          node.text = getNodeAllPathLabel(node, this.showAllLevels, this.separator);
          return filterMethod(node, this.inputValue);
        });

      if (this.multiple) {
        this.presentTags.forEach((tag) => {
          tag.hitState = false;
        });
      } else {
        suggestions.forEach((node) => {
          node.checked = isEqual(this.checkedValue, node.key);
        });
      }

      this.filtering = true;
      this.suggestions = suggestions;
      this.$nextTick(this.updatePopper);
    },
    handleSuggestionClick(index) {
      const { multiple } = this;
      const targetNode = this.suggestions[index];
      if (targetNode.data.isDisabled) return;
      if (multiple) {
        const { checked } = targetNode;
        targetNode.setChecked(!checked);
        this.$nextTick(() => {
          this.checkedValue = this.panel.getCheckedKeys();
        });
      } else {
        this.checkedValue = targetNode.key;
        this.toggleDropDownVisible(false);
      }
    },
    isEmptyValue(val) {
      const { multiple } = this;
      const { emitPath } = this.panel.props;
      if (multiple || emitPath) {
        return isEmpty(val);
      }
      return false;
    },
    computePresentContent() {
      const n = Math.random();
      console.time(`computePresentContent${ n}`);
       const currentValue = this?.panel?.getCheckedKeys();
        if (this.checkedValue && !isEqual(this.checkedValue, currentValue)) {
          this.resetTreeChecked(this.checkedValue);
        }
      // nextTick is required, because checked nodes may not change right now
      this.$nextTick(() => {
        if (this.config.multiple) {
          this.computePresentTags();
          this.$nextTick(() => {
            // 会导致首次录入搜索后，搜索文本消失， 意义不明
            // this.presentText = this.presentTags.length ? this.inputValue || ' ' : null;
          });
        } else {
          this.computePresentText();
        }
        console.timeEnd(`computePresentContent${ n}`);
      });
    },
    computePresentText() {
      const { checkedValue, config } = this;
      if (!isEmpty(checkedValue)) {
        const node = this.panel.getNodeByValue(checkedValue);
        if (node && (config.checkStrictly || node.isLeaf)) {
          this.presentText = getNodeAllPathLabel(node, this.showAllLevels, this.separator);
          return;
        }
      }
      this.presentText = null;
    },
    computePresentTags() {
      const {
 isDisabled, leafOnly, showAllLevels, separator, collapseTags, checkedValue,
} = this;
      const checkedNodes = this.getCheckedNodes(leafOnly);
      // 在选项树里，无匹配项的为无效数据，将其value值原样展示
      const invalidValues = checkedValue.filter((item) => !checkedNodes.find((node) => node.key === item));
      const tags = [];
      const genTag = (node) => ({
        node,
        key: node.uid,
        text: getNodeAllPathLabel(node, showAllLevels, separator),
        hitState: false,
        closable: !isDisabled && !node.isDisabled,
      });
      const genInvalidTag = (invalidValue) => ({
        node: { key: invalidValue },
        key: invalidValue,
        text: invalidValue,
        hitState: false,
        invalid: true,
        closable: !isDisabled,
      });

      if (checkedNodes.length || invalidValues.length) {
        const [first, ...rest] = checkedNodes;
        const restCount = rest.length + invalidValues.length;
        if (first) tags.push(genTag(first));

        if (restCount) {
          if (collapseTags) {
            tags.push({
              key: -1,
              text: `+ ${restCount}`,
              closable: false,
            });
          } else {
            rest.forEach((node) => tags.push(genTag(node)));
            invalidValues.forEach((invalidValue) => tags.push(genInvalidTag(invalidValue)));
          }
        }
      }

      this.checkedNodes = checkedNodes;
      this.presentTags = tags;
    },
    deleteTag(tag) {
      const { checkedValue } = this;
      const current = tag.node.key;
      const val = checkedValue.find((n) => isEqual(n, current));
      this.checkedValue = checkedValue.filter((n) => !isEqual(n, current));
      this.$emit('remove-tag', val);
    },
    handleCheckChange() {
      this.checkedValue = this.panel.getCheckedKeys();
    },
    getCheckedNodes() {
      // 这里返回的是源数据，需要转换成树的node节点
      return this.panel.getCheckedNodes().map((data) => this.panel.getNode(data));
    },
    resetTreeChecked(checkedValue) {
      if (this.multiple) {
        const currentCheckNodes = this.getCheckedNodes();
        currentCheckNodes.forEach((node) => {
          if (!checkedValue.includes(node.key)) {
            node.setChecked(false);
          }
        });
        const nodes = this.panel.getFlattedNodes(this.leafOnly);
        nodes.forEach((node) => {
          if (checkedValue.includes(node.key) && !node.checked) {
            node.setChecked(true);
          }
        });
      } else {
        this.panel.setCurrentKey(checkedValue);
      }
    },
    handleSuggestionKeyDown(event) {
      const { keyCode, target } = event;
      const KeyCode = {
        tab: 9,
        enter: 13,
        space: 32,
        left: 37,
        up: 38,
        right: 39,
        down: 40,
        esc: 27,
      };
      switch (keyCode) {
        case KeyCode.enter:
          target.click();
          break;
        case KeyCode.up:
          let prev = target.previousElementSibling;
          while (prev && prev.className.indexOf('is-disabled') !== -1) {
            prev = prev.previousElementSibling;
          }
          prev && prev.focus();
          break;
        case KeyCode.down:
          let next = target.nextElementSibling;
          while (next && next.className.indexOf('is-disabled') !== -1) {
            next = next.nextElementSibling;
          }
          next && next.focus();
          break;
        case KeyCode.esc:
        case KeyCode.tab:
          this.toggleDropDownVisible(false);
          break;
      }
    },
    updateStyle() {
      const { $el, inputInitialHeight } = this;
      if (this.$isServer || !$el) return;

      const { suggestionPanel } = this.$refs;
      const inputInner = $el.querySelector('.el-input__inner');

      if (!inputInner) return;

      const tags = $el.querySelector('.el-cascader__tags');
      let suggestionPanelEl = null;

      if (suggestionPanel && (suggestionPanelEl = suggestionPanel.$el)) {
        const suggestionList = suggestionPanelEl.querySelector('.el-cascader__suggestion-list');
        suggestionList.style.minWidth = `${inputInner.offsetWidth }px`;
      }

      if (tags) {
        const { offsetHeight } = tags;
        const height = `${Math.max(offsetHeight + 2, inputInitialHeight) }px`;
        inputInner.style.height = height;
        this.updatePopper();
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.el-cascader__search-input{
  font-size: 12px;
}
</style>
