<template>
  <li
    :key="source.uid"
    :class="[
      'el-cascader__suggestion-item',
      source.checked && 'is-checked',
      source.data.isDisabled && 'is-disabled'
    ]"
    :tabindex="-1"
    @click="handleSuggestionClick(index)">
    <span>{{ source.text }}</span>
    <i v-if="source.checked" class="el-icon-check"></i>
    <!-- 扩展搜索允许 展示被禁用项的功能 -->
    <el-tooltip v-if="source.data.isDisabled" effect="dark" content="同个类别下标签只能选择一个" placement="top-start">
      <i  class="el-icon-warning-outline" />
    </el-tooltip>
  </li>
</template>
<script>
export default {
  inject: ['handleSuggestionClick'],
  props: {
    source: {
      default() {
        return {};
      },
    },
    index: {
      type: Number,
    },
  },
};
</script>
