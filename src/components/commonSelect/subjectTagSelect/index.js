import Vue from 'vue';
import SubjectTagSelect from './SubjectTagSelect.vue';

let instance;
const SubjectTagSelectConstructor = Vue.extend(SubjectTagSelect);
const initInstance = () => {
  instance = new SubjectTagSelectConstructor({
    el: document.createElement('div'),
  });

  // instance.callback = defaultCallback;
};
const showNextMsg = (dom) => {
  if (!instance) {
    initInstance();
  }

  dom.appendChild(instance.$el);
};
export default showNextMsg;
