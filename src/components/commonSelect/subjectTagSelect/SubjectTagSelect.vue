<!--
 * @Description:
 * @version: 过验证eslint
 * @Company: 海闻软件
 * @Author: 晓荣
 * @Date: 2019-08-16 16:56:49
 * @LastEditors: 晓荣
 * @LastEditTime: 2019-08-19 14:35:25
 -->
<template>
  <ElCascader
    ref="cascader"
    v-model="selected"
    :options="subjectTagTree"
    :props="props"
    placeholder="请输入标签关键字搜索或者展开选择"
    :disabled="disabled"
    :filterMethod="filterMethod"
    size="mini"
    filterable
    :defaultExpandAll="defaultExpandAll"
    :beforeCheck="beforeCheck"
    @visible-change="$emit('visible-change', $event)"
  >
    <template slot-scope="{node, data}">
      <span>{{node.label}}</span>
    </template>
  </ElCascader>
</template>
<script>
import cloneDeep from 'lodash-es/cloneDeep';
import { traverseTree } from '@/assets/Utils';
import { difference, forEach } from 'lodash-es';
import { getTagFullName } from '@/filters/tagIdToTagName';
import ElCascader from './cascader.vue';

export default {
  components: { ElCascader },
  props: {
    value: {
      type: String,
      default: '',
    },
    disabled: Boolean,
    // 最多可选几个标签， 为 0 表示不限制
    multipleLimit: {
      type: Number,
      default: 5,
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    // 默认展开全部选项
    defaultExpandAll: {
      type: Boolean,
    },
    // 限定可选的 标签类别名称
    tagTypeNames: {
      type: Array,
      default: () => [],
    },
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  computed: {
    selected: {
      get() {
        console.log(this.value);
        return this.transformValue(this.value);
        // if (this.multiple) {
        //   // 为 null 或 空字符串时， 返回 空数组
        //   return this.value ? this.value.split(',') : [];
        // }
        // return this.value;
      },
      set(val) {
        this.$emit('change', this.multiple ? this.restoreValue(val) : val);
        // 兼容单例用法
        console.log(this.changeValue === 'function');
        if (typeof this.changeValue === 'function') {
          this.changeValue(this.multiple ? this.restoreValue(val) : val);
        }
      },
    },
    props() {
      return { ...this.defaultProps, multiple: this.multiple };
    },
    subjectTag() {
      return this.$store.state.selectData.subjectTag || {};
    },
    originTagTree() {
      return this.$store.state.selectData.tagTree || [];
    },
    tagMap() {
      return this.$store.state.selectData.tagMap || new Map();
    },
    cascader() {
      return this.$refs.cascader;
    },

  },
  data() {
    return {
      defaultProps: {
        value: 'tagId',
        label: 'tagName',
        children: 'children',
        multiple: true,
        checkStrictly: true,
        emitPath: false,
        disabled: 'isDisabled',
        expandTrigger: 'hover',
      },
      filtering: false,
      filterText: '',
      subjectTagTree: [],
      suggestions: [],
      filterHandler: null,
      changeValue: null,
      inValidValueMap: {

      },
    };
  },
  watch: {
    subjectTag() {
      this.initTree();
    },

    selected: {
      handler(val, oldVal) {
        if (!this.multiple) return;
        this.$nextTick(() => {
          if (this.multipleLimit !== 0 && val.length > this.multipleLimit) {
            this.$message.warning(`最多只能选择${this.multipleLimit}个标签`);
            this.selected = oldVal;
          } else {
            this.checkHasNewItem(val, oldVal);
          }
        });
        // this.setDisabledItem();
      },
    },
  },
  async mounted() {
    console.log('mounted');
    // await this.$store.dispatch('selectData/throttleGetSubjectTag');
    this.initTree();
  },
  methods: {
    initTree() {
      console.time('initTree');
      const subjectTagTree = this.originTagTree.filter((item) => {
        // 过滤无标签内容的项
        if (!Array.isArray(item.children) || item.children.length === 0) {
          return false;
        }
        // 过滤标签类别
        if (this.tagTypeNames.length > 0) {
          return this.tagTypeNames.includes(item.tagTypeName);
        }
        return true;
      });
      // this.addDisabledProp(subjectTagTree);
      this.setDisabledItem(subjectTagTree);
      this.subjectTagTree = subjectTagTree;
      console.timeEnd('initTree');
    },

    setDisabledItem(subjectTagTree) {
      console.time('disabled');
      // const selectedTags = this.selected.map((item) => this.tagMap.get(item));
      forEach(subjectTagTree, (tagTypeItem) => {
        tagTypeItem.isDisabled = true;
        traverseTree(tagTypeItem.children || [], (item) => {
          // 但标签类别没有开启非末级可选时， 所有非末级选项 isDisabled 为 true
          if (tagTypeItem.onlyDetail && item.children && item.children.length > 0) {
            item.isDisabled = true;
            return;
          }
          item.isDisabled = !item.tagStatus;
        });
      });
    },
    beforeCheck(node, data) {
      const selectedTags = this.selected.map((item) => this.tagMap.get(item));
      // 一种类别下的标签只能选择一个，所以其他的标签内容需要禁用
          // tagCode 前两位表示标签类别， 当标签内容与选择的标签内容的类别相同则禁选
      const flag = !selectedTags.find(
            (selectedTag) => selectedTag?.tagCode !== data.tagCode
                && data.tagCode.slice(0, 4) === selectedTag?.tagCode.slice(0, 4),
          );
      if (!flag) {
        this.$message.warning('一种类别下的标签只能选择一个');
      }
      return flag;
    },
    addDisabledProp(data) {
      data.forEach((item) => {
        if (Array.isArray(item.children) && item.children.length > 0) {
          //   只有标签类别不可选
          if (!item.tagParentCode) {
            this.$set(item, 'isDisabled', true);
          } else {
            this.$set(item, 'isDisabled', false);
          }

          this.addDisabledProp(item.children);
        } else {
          this.$set(item, 'isDisabled', false);
        }
      });
    },
    filterMethod(node, keyword) {
      // 第一级为标签类别， 不需要过滤出来
      if (node.level === 1) return false;
      return node.text.includes(keyword);
    },

    // 判断是否有添加选项， 向外抛添加的选项值， 方便用于实现一些，联动选择的功能
    checkHasNewItem(newVal, oldVal) {
      if (newVal.length > oldVal.length) {
        const [newItem] = difference(newVal, oldVal);
        this.$emit('add-item', newItem);
        // 兼容单例用法
        if (typeof this.handleAddItem === 'function') {
          this.handleAddItem(newItem);
        }
      }
    },
    transformValue() {
      if (this.multiple) {
        // 为 null 或 空字符串时， 返回 空数组
        const tagIds = this.value ? this.value.split(',') : [];
        if (tagIds.length === 0) return tagIds;
        return tagIds.map((tagId) => {
          const tag = this.tagMap.get(tagId);
          if (!(tag?.deleteFlag === 1)) return tagId;
          if (this.inValidValueMap[tagId]) return this.inValidValueMap[tagId];
          // 如果是已被删除的标签那么将value 值改为全称显示
          const tagFullName = this.getDeleteTagFullName(tag);
          this.inValidValueMap[tagId] = tagFullName;
          return tagFullName;
        });
      }
      return this.value;
    },
    // 还原被改为标签全称的值
    restoreValue(values) {
      console.log(values, this.inValidValueMap);
      Object.entries(this.inValidValueMap).forEach(([tagId, tagFullName]) => {
        if (!tagFullName) return;
        const index = values.indexOf(tagFullName);
        if (index !== -1) {
          values[index] = tagId;
        } else {
          this.inValidValueMap[tagId] = null;
        }
      });
      return values.join(',');
    },
    getDeleteTagFullName(tag) {
      return `${getTagFullName(tag)}(已删除)`;
    },
  },
};
</script>
<style lang="scss">
.is-disabled{
  color: #c8c8c8;
}
</style>
<style lang="scss" scoped>

.cascader-box{
  position: relative;
  .max-mask{
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background: rgba(255, 255, 255, 0.9);
    z-index: 9;

    p{
      font-size: 14px;
      width: 100%;
      text-align: center;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
.subjectTag-text{
  cursor: pointer;
}
.subjectTag-title{
  padding: 10px;

  height: auto;
  background: #f2f2f2;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .search-input{
    margin-right: 12px;
    vertical-align: middle;
    width: 150px;
    &::v-deep .el-input__inner {
      height: 22px;
    }
  }
  .subjectTag-item {
    position: relative;
    margin-right: 15px;
    cursor: pointer;
    line-height: 22px;
    &:not(:last-child) {
      &::before{
        content: '-';
        position: absolute;
        right: -12px;
      }
    }
    .el-icon-circle-close {
      position: absolute;
      right: -13px;
      top: -5px;
      font-size: 12px;
      cursor: pointer;
    }
    &:last-child{
      margin-right: auto;
    }
  }
}

</style>
