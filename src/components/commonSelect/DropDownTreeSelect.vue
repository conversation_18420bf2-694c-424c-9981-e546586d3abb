<!--
 * @Component: 下拉树多选组件
 * @Maintainer: 阮小文
 * @Modifier: 阮小文
 * @Description: 下拉树多选组件，优点是支持层级，缺点是单一层级时的性能。
 -->
<template>
  <el-popover
    class="DropDownTreeSelect-trigger"
    placement="bottom-start"
    popper-class="tree_popover"
    v-model="internalVisible"
    trigger="click"
    :disabled="dropDownOptions.disabled"
    >

    <!-- 默认 Trigger 是个输入框 -->
    <!-- 可以通过 slot 定制为一个 button -->
    <template slot="reference">
      <slot name="trigger">
        <el-input v-model="result">已选 {{ values.length }} 项</el-input>
      </slot>
    </template>

    <div class="DropDownTreeSelect-popover">
      <!-- 头部 -->
      <header class="DropDownTreeSelect-header" v-if="header || $slots.header">
        <slot name="header">
          <span class="el-dialog__title">{{ header }}</span>
        </slot>
      </header>

      <!-- 过滤器 -->
      <div class="DropDownTreeSelect-filter" v-if="filterable">
        <el-input
          ref="filter"
          class="filter"
          v-model="keyword"
          placeholder="输入搜索关键词"
          @keyup.native="doFilter">
          <i slot="suffix"
            @click="$el.querySelector('.filter input').focus()"
            class="el-input__icon el-icon-search"></i>
        </el-input>
      </div>

      <!-- 选项树 -->
      <div class="DropDownTreeSelect-body">
        <div class="scroll">
          <el-tree
            ref="tree"
            class="tree"
            :class="{ 'tree-flat': isFlat }"
            :show-checkbox="true"
            :node-key="nodeKey"
            :data="parsedData"
            :props="props"
            :filter-node-method="filterNodeMethod"
            :default-checked-keys="values"

            v-bind="$attrs"
            v-on="$listeners"

            @check-change="onCheckChange">
          </el-tree>
        </div>
      </div>

      <!-- 脚部 -->
      <footer class="DropDownTreeSelect-footer">
        <slot name="footer"></slot>
      </footer>
      <p class="Danger" v-if="maxSize > -1">{{this.maxErrorMessage}}</p>
    </div>
  </el-popover>
</template>

<script>
import debounce from 'lodash-es/debounce';
import isEmpty from 'lodash-es/isEmpty';
import isFunction from 'lodash-es/isFunction';
import cloneDeep from 'lodash-es/cloneDeep';
import forEach from 'lodash-es/forEach';
import map from 'lodash-es/map';
import find from 'lodash-es/find';
import get from 'lodash-es/get';
import includes from 'lodash-es/includes';
import size from 'lodash-es/size';
import every from 'lodash-es/every';

export default {
  inheritAttrs: false,

  props: {
    // 当前下拉选项框是否可见
    visible: {
      type: Boolean,
      default: false,
    },

    // 是否可搜索
    filterable: {
      type: Boolean,
      default: false,
    },

    // 标题文字，如果存在 slot，则不使用
    header: {
      type: String,
    },

    // v-model 接口
    value: {
      type: Array,
      required: true,
    },

    // 输入列表
    data: {
      type: Array,
      required: true,
    },

    // 是否直接使用原始数据（不做 clone）
    // 该行为可能会污染原始数据，确定不会扩散副作用的时候，
    // 启用可以避免 clone 开销
    useRaw: {
      type: Boolean,
      default: false,
    },

    // { label, children, disabled, isLeaf }
    props: {
      type: Object,
      required: true,
    },

    // 节点 id 字段
    nodeKey: {
      type: String,
      required: true,
    },

    // 节点父 id 字段，用于转换树形
    nodeParentKey: {
      type: String,
    },

    dropDownOptions: {
      type: Object,
      default: () => ({}),
    },
    // 最大能够勾选的选项数, -1 表示无限
    maxSize: {
      type: Number,
      default: -1,
    },
  },

  data() {
    return {
      // 树是否扁平的（是否单一层级）
      isFlat: true,
      // 树形化后的数据
      parsedData: [],
      // 弹窗展示状态
      internalVisible: !!this.visible,
      // 过滤关键词
      keyword: '',
      // 是否显示错误信息
      showErrorMessage: false,
    };
  },

  computed: {
    // 实现 v-model 接口
    values: {
      get() {
        return this.value;
      },

      set(values) {
        this.$emit('input', values);
      },
    },

    // 结果面板显示的内容
    result: {
      get() {
        const { values } = this;
        const tree = get(this, '$refs.tree');
        if (!tree) return '';
        const nodes = tree.getCheckedNodes();
        return this.resultRender(values, nodes, tree);
      },
      set() {},
    },
    maxErrorMessage() {
      return `当前最多只能选择${this.maxSize}个科目`;
    },
  },

  watch: {
    // 外部传入的可见状态，与本地联动
    visible(isVisible) {
      this.internalVisible = isVisible;
    },

    // 本地可见状态，与外部联动
    internalVisible(isVisible) {
      this.$emit('update:visible', isVisible);
      if (!isVisible) {
        this.$emit('close');
      }
    },

    // 外部传入的值变化时候，设置选中状态
    value(values) {
      const tree = get(this, '$refs.tree');
      if (tree) {
        tree.setCheckedKeys(values);
      }
    },

    // 监控外部传入的列表数据，
    // 变化时，重新生成选项数据
    data() {
      this.makeSelectOptions();
    },
  },

  created() {
    // 过滤函数 debounce
    this.doFilter = debounce(this.doFilter.bind(this), 300);
  },

  mounted() {
    // 初始化选项数据
    this.makeSelectOptions();
  },

  methods: {
    show() {
      this.internalVisible = true;
    },

    hide() {
      this.internalVisible = false;
    },

    // 生成性能优化选项数据（空间换时间）
    // TODO，考虑将选项设置成 prototype 以将对象转换成快对象
    makeSelectOptions() {
      if (isEmpty(this.data)) {
        this.parsedData = [];
        return;
      }

      const { nodeKey, nodeParentKey } = this;
      const childrenField = get(this, 'props.children', 'children');

      // 如果不指定使用原始数据，则进行拷贝以免污染原始数据
      const data = this.useRaw ? this.data : map(this.data, cloneDeep);

      // 没有传入转换需要的对应关系，则不转换，
      // 即传入的可能是预先转换的树数据或者只想使用扁平列表选项
      if (!nodeParentKey) {
        // 阻止 reactive 提高性能
        this.parsedData = Object.freeze(data);
        this.isFlat = every(data, (item) => isEmpty(item[childrenField]));
        return;
      }

      // 开始转换逻辑
      const roots = [];
      const ensureParent = (item) => {
        // 没有父 id，为根节点
        if (!item[nodeParentKey]) return roots;

        const parent = find(data, { [nodeKey]: item[nodeParentKey] });
        // 没有找到父节点，为根节点
        if (!parent) return roots;

        // 确保存在子列表
        if (!parent[childrenField]) {
          parent[childrenField] = [];
        }

        return parent[childrenField];
      };

      // 树形转换
      forEach(data, (item) => {
        const parent = ensureParent(item);
        // 6401 测试环境冻结的科目在流水拆分可以被选择的到(XR)
        if (item.subjectStatus) {
          parent.push(item);
        }
      });

      // 阻止 reactive 提高性能
      this.parsedData = Object.freeze(roots);
      // 检测树是否扁平（单一层次）
      this.isFlat = size(this.parsedData) === size(data);
    },

    // 树节点过滤方法
    filterNodeMethod(value, data, node) {
      return includes(this.getLabel(data, node), value);
    },

    // 执行过滤
    doFilter() {
      this.$refs.tree.filter(this.keyword);
    },

    // 清空选择
    clearChecked() {
      const tree = get(this, '$refs.tree');
      if (tree) {
        this.$refs.tree.setCheckedKeys([]);
      } else {
        this.values = [];
      }
    },

    // 获取节点的文本内容
    getLabel(data, node) {
      const label = get(this, 'props.label');
      return isFunction(label) ? label(data, node) : data[label];
    },

    // 勾选变化时，更新组件值
    onCheckChange(data, isChecked, isSomeChildrenBeChecked) {
      if (isChecked) {
        if (this.maxSize > 0 && this.values.length >= this.maxSize) {
          this.$refs.tree.setChecked(data, false);
          this.showErrorMessage = true;
          return;
        }
      }

      this.$nextTick(() => {
        this.values = this.$refs.tree.getCheckedKeys();
        this.$emit('check-change', data, isChecked, isSomeChildrenBeChecked);
        // this.showErrorMessage = false;
      });
    },

    // 结果渲染方法
    resultRender(values, nodes/* , tree */) {
      if (isEmpty(values)) return '';
      if (size(values) === 1) {
        const node = nodes[0];
        if (node) {
          return this.getLabel(node, node);
        }
        return '已选 1 项';
      }
      return `已选 ${size(values)} 项`;
    },
  },
};
</script>

<style lang="scss">
.DropDownTreeSelect-trigger {
  display: inline-block;
  vertical-align: top;
  line-height: 1;
}

.DropDownTreeSelect-popover {
  ::-webkit-scrollbar-track-piece {
    background-color: white;
  }
  .DropDownTreeSelect-header, .DropDownTreeSelect-filter {
    margin: 0;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
  }

  .DropDownTreeSelect-body {
    overflow: hidden;
    position: relative;
    box-sizing: border-box;
    height: 280px;
    border: none;

    &:before, &:after {
      display: block;
      content: '';
      position: absolute;
      z-index: 2;
      top: auto;
      right: 0;
      bottom: auto;
      left: 0;
      width: 100%;
      height: 5px;
    }
    &:before {
      top: 0;
      background-image: linear-gradient(to top, rgba(0,0,0,0), rgba(0,0,0,.03));
    }
    &:after {
      bottom: 0;
      background-image: linear-gradient(to bottom, rgba(0,0,0,0), rgba(0,0,0,.03));
    }

    .scroll {
      box-sizing: border-box;
      overflow: auto;
      max-width: 100%;
      max-height: 100%;
      width: auto;
      height: auto;
      padding: 5px 0;
    }

    .tree {
      position: relative;
      z-index: 1;
      width: 100%;

      &.tree-flat {
        .el-tree-node__expand-icon {
          display: none;
        }
      }

      .el-tree-node>.el-tree-node__children{
        overflow: inherit;
      }
      .chosen_node_list {
        height: 360px;
        background: #f0f0f0;
        box-sizing: border-box;
        overflow: auto;
      }
      .chosen_node_list > .chosen_node {
        position: relative;
        padding-left: 15px;
        padding-right: 30px;
        line-height: 35px;
        background: white;
        border-top: 1px solid rgb(209, 219, 229);
        border-right: 1px solid rgb(209, 219, 229);
        border-left: 1px solid rgb(209, 219, 229);
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: 300px;
        margin-left: 3px;
        overflow: hidden;
      }
      .chosen_node_list > .chosen_node:last-child {
        border-bottom: 1px solid rgb(209, 219, 229);
      }
      .chosen_node_list > .chosen_node > span {
        cursor: pointer;
      }
      .chosen_node_list > .chosen_node > i {
        position: absolute;
        top: 50%;
        right: 15px;
        transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -o-transform: translateY(-50%);
      }
      .el-badge.tree_badge .el-badge__content.is-fixed {
        top: 0;
        right: 0;
        position: absolute;
        transform: translateY(-36%) translateX(40%);
      }
    }
  }
  .DropDownTreeSelect-footer {
    margin-top: 5px;
  }
}
</style>
