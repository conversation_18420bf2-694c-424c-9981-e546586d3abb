<!--
 * @Description: 会计准则选择组件
 -->
<template>
  <el-select
        v-bind="$attrs"
        v-on="$attrs"
        v-model="modelValue">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
  </el-select>
</template>

<script setup>
import { ref, computed, onMounted, defineProps, defineEmits } from 'vue';
import { getStandardList } from '@/api/companyConfig';

// 定义props
const props = defineProps({
  value: {
    type: [String, Number, Object],
    default: ''
  },
});

// 定义事件
const emit = defineEmits(['input', 'change']);

// 选项数组
const options = ref([]);

// 实现v-model
const modelValue = computed({
  get: () => props.value,
  set: (val) => {
    emit('input', val);
  }
});

// 初始化选项
const initOptions = async () => {
  try {
    const rsp = await getStandardList();
    if (!rsp.data.data) return;
    // 按照standardId倒序排序
    console.log(rsp.data.data);
    options.value = rsp.data.data.map(item => ({
      value: item.accountingId,
      label: `${item.standardName}-${item.businessName}-${item.versionName}`,
      standardId: item.standardId
    })).sort((a, b) => a.standardId - b.standardId);
  } catch (error) {
    console.error('获取会计准则列表失败', error);
  }
};

// 组件挂载时加载数据
onMounted(() => {
  initOptions();
});
</script>
 