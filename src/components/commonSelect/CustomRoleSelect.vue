<template>
  <el-select v-bind="$attrs" :value="value" @input="handlerChange">
    <slot name="append-option"></slot>
    <el-option v-for="item in list" :label="item.roleName" :value="item.roleId" :key="item.roleId"></el-option>
  </el-select>
</template>
<script>
/**
 * TODO
 * 后续prop直接赋值
 */
export default {
  inheritAttrs: false,
  props: {
    value: {
      type: [String, Number, Array],
      required: true,
    },
    // 自定义角色类型， jz 代账的自定义角色，crm CRM的自定义角色，hk 税易平台的自定义角色
    scope: {
      type: String,
      required: true,
    },
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  data() {
    return {
    };
  },
  computed: {
    list() {
      if (this.scope === 'jz') {
        return this.$store.state.selectData.jzCustomRolesList;
      }
      if (this.scope === 'crm') {
        return this.$store.state.selectData.crmCustomRolesList;
      }
      if (this.scope === 'hk') {
        return this.$store.state.selectData.hkCustomRolesList;
      }
      return [];
    },
  },
  mounted() {
    this.$store.dispatch('selectData/throttleGetCustomRolesList');
  },
  methods: {
    handlerChange(val) {
      this.$emit('change', val);
    },
  },
};
</script>
