<template>
   <el-cascader
        v-bind="$attrs"
        filterable
        :options="tree"
        :value="value"
        @change="handlerChange"
        :props="cascaderProps" >
    </el-cascader>

</template>
<script>
import { merge } from 'lodash-es';
import toTree from '@/utils/toTree';

/**
 * TODO
 * 后续prop直接赋值
 */
export default {
  inheritAttrs: false,
  props: {
    value: {
      required: true,
    },
    props: {
      type: Object,
      default: () => ({}),
    },
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  data() {
    return {
    };
  },
  computed: {
    list() {
      return this.$store.state.selectData.groupList;
    },
    tree() {
      return toTree(this.list, {
        idField: 'groupId',
        parentIdField: 'parentGroupId',
        isRoot: (item) => !item.parentGroupId,
      });
    },
    cascaderProps() {
      return merge({
        checkStrictly: true, children: 'children', value: 'groupId', label: 'groupName', emitPath: false,
        }, this.props);
    },
  },
  mounted() {
    this.$store.dispatch('selectData/throttleGetGroupList');
  },
  methods: {
    handlerChange(val) {
      this.$emit('change', val);
    },
  },
};
</script>
