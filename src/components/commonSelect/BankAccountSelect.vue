<template>
  <el-select :value="value" v-bind="$attrs" @change="handlerChange">
    <el-option v-for="item in list"
      :key="item.bankaccountId"
      :label="formatLabel(item)"
      :value="item.bankAccount">
    </el-option>
  </el-select>

</template>
<script>
/**
 * TODO
 * 后续prop直接赋值
 */
export default {
  props: {
    value: {
      required: true,
    },
    // 过滤出指定类型的账号（1 银行，2 现金，3 其他货币资金）
    accountType: {
      type: [Number, String],
      required: false,
    },
    // 是否包含现金账户
    includesCash: {
      type: Boolean,
      default: true,
    },
    includesDisabled: {
      type: Boolean,
      default: true,
    },
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  data() {
    return {
    };
  },
  computed: {
    list() {
      let accountList = this.$store.state.selectData.bankAccountList;

      if ([1, 2, 3, '1', '2', '3'].indexOf(this.accountType) !== -1) {
        accountList = accountList.filter((item) => item.accountType === Number(this.accountType));
      }

      if (!this.includesCash) {
        accountList = accountList.filter((item) => item.accountType !== 2);
      }

      if (this.includesDisabled) {
        return accountList;
      }
      return accountList.filter((account) => account.accountStatus);
    },
  },
  mounted() {
    this.$store.dispatch('selectData/getBankAccountList');
  },
  methods: {
    formatLabel(option) {
      return `${option.bankName} ${option.bankAccount}`;
    },

    handlerChange(val) {
      this.$emit('change', val);
    },
  },
};
</script>
