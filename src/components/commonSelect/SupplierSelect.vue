<!--
 * @Component: 供应商选择组件
 * @Modifier: 阮小文
 * @Modified: 2019-03-29 14:14:00
 * @Update:
 * @Events:
 * 1. before-add 点击添加按钮触发弹窗前
 * 2. after-add 添加完毕后，新添加供应商的 id 作为参数传入回调
 *
-->

<template>
  <el-select
    class="SupplierSelect"
    placeholder="请选择供应商名称"
    v-model.trim="localValue"
    filterable
    :disabled="disabled"
    v-addBtn="{handle: onAddSupplierRequest, text: '供应商名称', permission: ['options-supplierInfo-chaxun']}">

    <el-option
      v-for="item in supplierOptions"
      :key="item.supplierId"
      :label="item.supplierName"
      :value="item.supplierId" />

  </el-select>
</template>

<script>
import find from 'lodash-es/find';
import filter from 'lodash-es/filter';
import { mapActions } from 'vuex';

export default {
  name: 'SupplierSelect',

  props: {
    // v-model 接口
    value: {
      type: String,
    },

    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      // v-model 对应值
      localValue: this.value,
    };
  },

  computed: {
    // 获取供应商选项列表
    // 1. 筛选出所有启用的条目（supplierStatus 为启用状态）
    // 2. 对于当前选中的项目，无论是否启用均需要显示
    supplierOptions() {
      const source = this.$store.state.selectData.supplierList || [];
      const selected = find(source, { supplierId: this.localValue });
      const options = filter(source, (option) => option.supplierStatus || option === selected);
      return options;
    },
  },

  watch: {
    // 实现 v-model 接口
    // prop 传进来的 value 改变时，将改变同步到本地 value
    value(newValue) {
      this.localValue = newValue;
    },

    // 实现 v-model 接口
    // 本地 value 改变时，将值修改发射出去
    localValue(newValue) {
      this.$emit('input', newValue);
    },
  },

  methods: {
    ...mapActions('selectData', ['getSupplierList']),
    // 点击增加按钮回调
    onAddSupplierRequest(event) {
      // 处理新增逻辑之前，发射事件
      this.$emit('before-add', event);

      const $root = Lib.Utils.getRootInstance(this);
      this.$msgbox({
        title: '新增供应商',
        message: $root.$createElement('create-basic-data', {
          ref: 'basicData',
          props: {
            $store: this.$store,
            formType: 'suppliers',
            defaultFormData: {
              sellerType: 'suppliers',
            },
          },
          on: {
            update: async ({ supplierId }) => {
              await this.$store.dispatch('selectData/getSupplierList');
              this.localValue = supplierId;
              // 新增完成后，发射事件
              this.$emit('after-add', supplierId);
            },
          },
        }),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        beforeClose: async (action, instance, done) => {
          const closeEnabled = action !== 'confirm' || (await $root.$refs.basicData.doSave());
          if (closeEnabled) {
            $root.$refs.basicData.resetData();
            done();
          }
        },
      });
    },
  },
};
</script>
