<template>
  <el-form ref="form" :model="form" :rules="rules" label-width="72px">
    <el-form-item prop="saleunitName" label="单位名称">
      <el-input
      v-model="form.saleunitName"
      placeholder="请输入单位名称"
      @change="ajaxErrorMsg = ''"></el-input>
    </el-form-item>
    <el-form-item prop="enabledDecimal" label="能否为小数">
      <el-radio-group v-model="form.enabledDecimal">
        <el-radio :label="true">是</el-radio>
        <el-radio :label="false">否</el-radio>
      </el-radio-group>
    </el-form-item>
  </el-form>
</template>

<script>

export default {
  data() {
    return {
      form: {
        saleunitName: '',
        enabledDecimal: true,
      },
      ajaxErrorMsg: '',
      rules: {
        saleunitName: [{
          required: true, validator: this.saleunitNameValidator, trigger: 'change',
        }],
      },
    };
  },
  components: {},
  mounted() {},
  computed: {},
  watch: {
    ajaxErrorMsg() {
      this.$refs.form.validateField('saleunitName');
    },
  },
  methods: {
    resetData() {
      this.$refs.form.resetFields();
    },
    async addSaleunit() {
      let closeEnabled = true;
      this.$refs.form.validate((valid) => {
        closeEnabled = valid;
      });
      if (closeEnabled) {
        await this.$http.post('/rest/companyConfig/goods/saleUnits/v1.0/save', JSON.stringify([this.form])).then(() => {
          this.$emit('update', this.form.saleunitName);
          this.$message.success('新增单位成功');
        }).catch((e) => {
          this.ajaxErrorMsg = e.body && e.body.errorMessage;
          closeEnabled = false;
        });
      }
      return closeEnabled;
    },
    saleunitNameValidator(rule, value, callback) {
      if (!value) { return callback(new Error('单位名称不能为空')); }
      return this.ajaxErrorMsg ? callback(new Error(this.ajaxErrorMsg)) : callback();
    },
  },
};
</script>
