<template>
  <el-select v-bind="$attrs" :value="value" placeholder="请选择单位" @input="handleChange" clearable>
    <el-option class="highlight" :value="-1" label="新增单位"></el-option>
    <el-option
    v-for="item in list"
    :value="item.saleunitName"
    :key="item.saleunitName"></el-option>
  </el-select>
</template>

<script>
import AddUnit from './AddUnit.vue';

export default {
  inheritAttrs: false,
  props: {
    value: {
      type: [String, Number, Boolean],
      default: '',
    },
    showAdd: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {

    };
  },
  components: {
    // eslint-disable-next-line vue/no-unused-components
    AddUnit,
  },
  mounted() {
    this.$store.dispatch('selectData/getUnitNameList');
  },
  computed: {
    list() {
      return this.$store.state.selectData.unitNameList;
    },
  },
  methods: {
    handleChange(value) {
      if (value !== -1) {
        this.$emit('input', value);
        return;
      }
      this.$msgbox({
        title: '新增单位',
        message: this.$createElement('add-unit', {
          ref: 'unit',
          key: Math.random(),
          on: {
            update: async (saleunitName) => {
              await this.$store.dispatch('selectData/getUnitNameList');
              this.$emit('input', saleunitName);
            },
          },
        }),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        beforeClose: async (action, instance, done) => {
          const { unit } = this.$refs;
          const closeEnabled = await (action !== 'confirm' || unit.addSaleunit());
          if (closeEnabled) {
            unit.resetData();
            done();
          }
        },
      });
    },
  },
};
</script>

<style scoped>
  .highlight {
    color: red;
  }
</style>
