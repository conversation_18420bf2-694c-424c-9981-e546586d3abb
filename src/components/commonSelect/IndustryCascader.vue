<template>
    <el-cascader
        filterable
        :disabled='isDisable'
        :options="industryList"
        placeholder='选择公司行业'
        v-model="industryPath"
        :props="props" >
    </el-cascader>
</template>
<script>
function getSon(data, curNode) {
  const children = [];
  for (let m = 0; m < data.length; m += 1) {
    if (data[m].industryPath.length === 5
    && data[m].industryPath.substring(0, 4) === curNode.industryPath) {
      children.push(data[m]);
    }
  }
  return children;
}
function getGrandson(data, curNode) { // str.substr(str.length-4)
  const childrens = [];
  for (let K = 0; K < data.length; K += 1) {
    if ((data[K].industryPath.length === 4
    || (data[K].industryPath.length === 5
    && data[K].industryPath.substring(3, 5).indexOf('0') > -1))
    && data[K].industryPath.substring(0, 3) === curNode.industryPath) {
      const children = getSon(data, data[K]);
      if (children && children.length > 0) {
        data[K].children = children;
      }
      childrens.push(data[K]);
    }
  }
  return childrens;
}
function getChildren(data, curNode) {
  const childrens = [];
  for (let j = 0; j < data.length; j += 1) {
    if (data[j].industryPath.length === 3
      && data[j].industryPath.substring(0, 1) === curNode.industryPath) {
      const children = getGrandson(data, data[j]);
      if (children && children.length > 0) {
        data[j].children = children;
      }
      childrens.push(data[j]);
    }
  }
  return childrens;
}
function covert(data) {
  const result = [];
  if (!data || !data.length || data.length === 0) return [];

  for (let i = 0; i < data.length; i += 1) {
    const obj = data[i];
    if (obj.industryPath.length === 1) { //
      const children = getChildren(data, obj);
      if (children.length > 0) {
        obj.children = children;
      }
      result.push(obj);
    }
  }
  return result;
}

export default {
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    isDisable: Boolean,
  },
  computed: {
    industryPath: {
      get() {
        console.log(this.value);
        return this.value;
      },
      set(val) {
        this.$emit('update:value', val);
      },
    },
  },
  mounted() {
    this.setIndustry();
  },
  data() {
    return {
      props: {
        label: 'industryName',
        value: 'industryPath',
        children: 'children',
      },
      industryList: [],
    };
  },
  methods: {
    async setIndustry() {
      let industryList = localStorage.getItem('industryList');
      const industryLastTime = localStorage.getItem('industryLastTime');
      const lastTimeRsp = await this.$http.get('/rest/companyConfig/companyBasis/lasttime/v1.0/list').catch(() => {
        console.error('修改时间');
      });
      const lastModifyTime = lastTimeRsp.data.data[0].industry;
      const newLastTime = lastModifyTime ? Date.parse(lastModifyTime.replace('-', '/')) : 0;
      if (industryList) {
        if (newLastTime === industryLastTime) {
          industryList = JSON.parse(industryList);
          this.industryList = industryList;
          return;
        }
      }
      const industryRsp = await this.$http.get('/rest/global/dimensionality/industry/v0.1/list?pageSize=99999&page=1');
      industryList = covert(industryRsp.data.data);
      this.industryList = industryList;
      localStorage.setItem('industryList', JSON.stringify(industryList));
      localStorage.setItem('industryLastTime', newLastTime);
    },
  },
};
</script>
