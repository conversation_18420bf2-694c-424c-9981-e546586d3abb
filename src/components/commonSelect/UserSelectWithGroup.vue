<template>
  <el-cascader
       v-bind="$attrs"
       filterable
       :options="tree"
       :value="value"
       :show-all-levels="false"
       @change="handlerChange"
       :props="{ children: 'children', value: 'id', label: 'name', emitPath: false }" >
       <template slot-scope="{ node, data }">
        <slot :node="node" :data="data">
          {{  node.label }}
        </slot>
      </template>
   </el-cascader>

</template>
<script>
import combinUserWithGroup from '@/helper/combinUserWithGroup';

export default {
 inheritAttrs: false,
 props: {
   value: {
     required: true,
   },
   roleIds: {
    type: Array,
    default: () => [],
   },
 },
 model: {
   prop: 'value',
   event: 'change',
 },
 data() {
   return {
   };
 },
 computed: {
  groupList() {
     return this.$store.state.selectData.groupList;
   },
   userList() {
    return this.$store.state.selectData.userList.filter((item) => {
      if (this.roleIds.length === 0) return true;
      return this.roleIds.some((roleId) => item.roleIds.indexOf(String(roleId)) !== -1);
    });
   },
   tree() {
    return combinUserWithGroup(this.groupList, this.userList);
   },
 },
 mounted() {
   this.$store.dispatch('selectData/throttleGetGroupList');
   this.$store.dispatch('selectData/throttleGetUserList');
 },
 methods: {
   handlerChange(val) {
     this.$emit('change', val);
   },
 },
};
</script>
