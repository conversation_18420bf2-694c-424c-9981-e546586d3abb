<!--
 * @LastEditors: 肖泽涛
 * @Author: 肖泽涛
 * @Description: 全局科目选择弹窗
 * 默认只显示末级科目
 -->
<template>
  <el-select
  v-model="subjectCode"
  :placeholder="placeholder"
  @keyup.enter.native="handleEnter"
  ref="main"
  popper-class="baseTableSelect"
  filterable
  :filter-method="filterSubject"
  :clearable="isClearable"
  :multiple="multiple"
  :multiple-limit="multipleLimit"
  :disabled="disabled"
  v-addBtn="addBtn && {handle: handleAddBtnClick, text: '添加科目', permission: ['options-subjectInfo-chaxun']}"
  @visible-change="handleCollapse"
  @change="handleChange">
        <template v-if="isFilterMenu" >
          <el-option
            v-for="item in filterOptions"
            :disabled="!noDisabledItem && !item.subjectStatus"
            :key="item.subjectCode"
            :value="item.subjectCode"
            :label="item.subjectCode + ' ' + item.subjectFullName">
              <span style="float: left">{{ item.subjectCode + ' ' + item.subjectFullName }}</span>
              <span
              v-if="accountPeriod"
              style="float: right; font-size: 13px; padding-right: 8px"
              :style="{color: currentEndingAmounts.endingAmounts[item.subjectId]
                && currentEndingAmounts.endingAmounts[item.subjectId] < 0 ? 'red' : '#8492a6'}">
                {{ currentEndingAmounts.endingAmounts[item.subjectId] | moneyFilter}}
              </span>
            </el-option>
        </template>
        <template v-else>
          <el-option
            v-for="item in currerSubjectList"
            :isCache="item.isCache"
            :disabled="!noDisabledItem && !item.subjectStatus"
            :key="item.subjectCode"
            :value="item.subjectCode"
            :label="item.subjectCode + ' ' + item.subjectFullName">
              <span style="float: left">{{ item.subjectCode + ' ' + item.subjectFullName }}</span>
              <span
              v-if="accountPeriod"
              style="float: right; font-size: 13px; padding-right: 8px"
              :style="{color: currentEndingAmounts.endingAmounts[item.subjectId]
              && currentEndingAmounts.endingAmounts[item.subjectId] < 0 ? 'red' : ''}">
              {{ currentEndingAmounts.endingAmounts[item.subjectId] | moneyFilter }}
            </span>
            </el-option>
        </template>
    </el-select>
</template>

<script>
import {
  chunk,
  cloneDeep,
  difference,
  has,
  intersectionWith,
  xorBy,
} from 'lodash-es';
import dayjs from 'dayjs';

export default {
  props: {
    addBtn: { // 是否展示新增按钮
      type: Boolean,
      default: false,
    },
    // 传入属期，根据属期查询期末余额， 同时也作为是否开启显示期末余额的开关
    accountPeriod: {
      type: Date,
    },
    placeholder: {
      type: String,
      default: '您可以输入科目关键字或者科目代码，或者滚动选择科目',
    },
    value: { // 绑定的值
      type: [String, Array],
      default: '',
    },
    disabled: { // 是否禁用
      type: Boolean,
      default: false,
    },
    showLevels: { // 可选的科目等级
      type: Array,
      default: () => [],
    },
    // 只显示非末级科目
    noLastLevel: {
      type: Boolean,
      default: false,
    },
    noParent: { // 是否显示父级
      type: Boolean,
      default: true,
    },
    filterSubjectCodes: { // 过滤科目代码，只可选择数组里传入的科目代码下的科目， 空为不过滤
      type: Array,
      default: () => [],
    },
    noDisabledItem: { // 是否显示禁用科目
      type: Boolean,
      default: false,
    },
    hasLocalMemory: { // 是否启动本地缓存，记录常用科目
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    multipleLimit: {
      type: Number,
      default: 0,
    },
    isClearable: {
      type: Boolean,
      default: true,
    },
    props: Object,
  },
  watch: {
    value(val) {
      this.subjectCode = val;
    },
    subjectList(val) {
      this.copySubjectList = val.slice();
      this.currerSubjectList = [];
      this.setSubjectList();
      // 当列表发生变化时，变化前列表没有对应subjectCode的科目，变化后可能存在，所以需要尝试添加进currerSubjectList
      this.addCurrSubject(this.subjectCode);
    },
    subjectCode(val) {
      this.addCurrSubject(val);
    },
    currerSubjectList(val, oldVal) {
      if (this.accountPeriod) {
        this.fetchSubjectBalance(difference(val, oldVal).filter((item) => !has(this.currentEndingAmounts.endingAmounts, item.subjecId)));
      }
    },
    accountPeriod(val) {
      // 月份切换时， 已经加载的余额需要刷新
      if (!this.currentEndingAmounts.accountPeriod || dayjs(val).format('YYYYMM') !== this.currentEndingAmounts.accountPeriod) {
        this.currentEndingAmounts.accountPeriod = dayjs(val).format('YYYYMM');
        this.fetchSubjectBalance(this.currerSubjectList);
      }
    },
  },
  data() {
    return {
      currerSubjectList: [], // 当前渲染的科目列表
      copySubjectList: [], // 所有的科目列表
      subjectCode: '', // 当前选中的科目代码
      preAppendSubject: [], // 提前插入项
      filterOptions: [],
      isFilterMenu: false,
      currentEndingAmounts: {
        accountPeriod: null,
        endingAmounts: {}, // {shubjectCode: 期末余额}
      },
    };
  },
  created() {
  },
  async mounted() {
    // 复制科目列表
    this.copySubjectList = this.subjectList.slice();
    this.currerSubjectList = [];
    // 设置当前渲染的科目列表
    this.setSubjectList();

    this.subjectCode = this.value;

    this.$refs.main.$watch('hoverIndex', (val) => {
      if (val > this.currerSubjectList.length - 9 && !this.isFilterMenu) {
        this.setSubjectList();
      }
    });
  },
  computed: {
    selectCompanyId() {
      return this.$store.state.user.userInfo.selectCompanyId;
    },
    subjectList() {
      if (this.noLastLevel && this.noParent) {
        console.error('[SubjectSelect] noLastLevel 和 noParent 不能同时为 true');
      }
      // 通过vuex获取科目列表
      let subjectList = this.$store.state.selectData.subjectList || [];
      subjectList = subjectList.filter((item) => {
        if (!this.noDisabledItem && !item.subjectStatus) return false;
        // 不显示父级时，childCount 大于0 的都不显示
        if (this.noParent) {
          if (item.childCount !== 0) {
            return false;
          }
        }
        // 只允许显示层级（allowLevel）不为0的科目
        if (this.noLastLevel) {
          console.log(item.allowLevel === 0);
          if (item.allowLevel === 0) {
            return false;
          }
        }
        // 根据传入的科目显示相应等级的科目
        if (this.showLevels.length > 0) {
          if (!this.showLevels.find((leve) => leve === item.subjectCode.length / 4)) {
            return false;
          }
        }
        // 根据 filterSubjectCodes 过滤想要显示的科目
        if (this.filterSubjectCodes.length > 0) {
          return this.filterSubjectCodes
            .find((subjectCode) => item.subjectCode.indexOf(subjectCode) === 0);
        }
        return true;
      });

      return cloneDeep(subjectList) || [];
    },
    subjectHashTable() {
      return this.$store.state.selectData.subjectHashTable;
    },
  },
  methods: {
    handleAddBtnClick(event) {
      this.$emit('on-add-click', event);
    },
    // 当选中项未加载时，提前插入该项，并保存起来
    addCurrSubject(val) {
      if (!val) return;
      if (!Array.isArray(val)) {
        val = [val];
      }
      const unrenderList = val.filter((item) => !this.currerSubjectList.find((subject) => subject.subjectCode === item));
      if (unrenderList.length >= 1) {
        this.preAppendSubject = intersectionWith(this.subjectList, unrenderList,
        (subject, checkedSubjectCode) => subject.subjectCode === checkedSubjectCode);
        this.subjectList
          .filter((item) => item.subjectCode === val);
        if (this.preAppendSubject.length) {
          this.currerSubjectList.push(...this.preAppendSubject);
          this.$nextTick(() => {
            this.$refs.main.setSelected();
          });
          // this.$refs.main.selectedLabel = `${this.copySubject.subjectCode} ${this.copySubject.subjectFullName}`;
        }
      }
    },
    // 分片插入列表
    setSubjectList() {
      const arr = this.copySubjectList.splice(0, 50);
      // 当提前插入项在次此插入的列表内，将其从原列表中去除
      if (this.preAppendSubject.length) {
          const outList = intersectionWith(arr, this.preAppendSubject,
          (subject, checkedSubject) => subject.subjectCode === checkedSubject.subjectCode);
          console.log(outList, this.preAppendSubject, '-----this.copySubject');
        if (outList.length) {
          outList.forEach((item) => {
            this.currerSubjectList.splice(this.currerSubjectList.indexOf(item), 1);
            this.preAppendSubject.splice(this.preAppendSubject.indexOf(item), 1);
          });
        }
      }
      this.currerSubjectList = this.currerSubjectList.concat(arr);
    },
    // 搜索科目的方法
    filterSubject(query) {
      if (query === '') {
        this.filterOptions = [];
        this.isFilterMenu = false;
        return;
      }
      const num = Number(query);
      // 当传入的是科目代码并且小于4位数时， 不执行搜索
      if ((!Number.isNaN(num) && query.length < 4)) return;
      const parsedQuery = String(query).replace(/(\^|\(|\)|\[|\]|\$|\*|\+|\.|\?|\\|\{|\}|\|)/g, '\\$1');
      const queryRegExp = new RegExp(parsedQuery, 'i');
      // 搜索时同时使用科目代码搜索，和科目名称搜索
      let restul = this.subjectList
        .filter((item) => queryRegExp.test(item.subjectCode)
        || queryRegExp.test(item.subjectFullName))
        .map((item) => {
          item.value = item.subjectCode;
          item.label = `${item.subjectCode} ${item.subjectFullName} ${this.currentEndingAmounts.endingAmounts[item.subjectId] || ''}`;
          item.disabled = !item.subjectStatus;
          return item;
        }) || [];
      if (restul.length > 200) {
        restul = restul.slice(0, 200);
        this.$message.info('搜索结果过多，为您保留200项，请精确搜索');
      }
      if (this.accountPeriod) {
        this.fetchSubjectBalance(restul.filter((item) => !has(this.currentEndingAmounts.endingAmounts, item.subjectId)));
      }
      // eslint-disable-next-line consistent-return
      this.filterOptions = restul;
      this.isFilterMenu = true;
    },
    // 当下拉框展示时获取常用科目
    handleCollapse(val) {
      if (val) {
        this.$emit('expand');
      } else {
        this.filterOptions = [];
        this.isFilterMenu = false;
        this.$emit('collapse', { target: this.$el.querySelector('input') });
      }
    },
    handleChange() {
      this.$emit('update:value', this.subjectCode);
      this.$emit('change', this.subjectCode, this.subjectList.find((item) => item.subjectCode === this.subjectCode));
      this.isFilterMenu = false;
    },
    handleEnter(e) {
      this.$emit('enter', e);
    },
    // 获取科目期末余额余额
    fetchSubjectBalance(subjects) {
      if (subjects.length === 0) return;
      if (subjects.length > 200) {
        // 科目过多时，进行切片
        chunk(subjects, 200).forEach((item) => {
          this.fetchSubjectBalance(item);
        });
        return;
      }
      const subjectIds = subjects.map((item) => item.subjectId).join(',');
      const period = dayjs(this.accountPeriod).format('YYYYMM');
      const params = {
        accountPeriod: period, subjectIds,
      };
      this.$http.get('/rest/companyAccount/merge/voucher/balanceEndingAmount/v1.0', { params }).then((res) => {
        const resData = res.data.data;
        this.currentEndingAmounts.accountPeriod = period;
        subjects.forEach((subject) => {
          const resSubject = resData.find((item) => item.subjectId === subject.subjectId);
          if (resSubject) {
            this.$set(this.currentEndingAmounts.endingAmounts, subject.subjectId, resSubject.amount);
          } else {
            this.$set(this.currentEndingAmounts.endingAmounts, subject.subjectId, 0);
          }
        });
      });
    },
    getEndingAmountWithSubjectCode(subjectId) {
      return this.currentEndingAmounts.endingAmounts[subjectId];
    },
  },
};
</script>

<style>
.percentInput input{
  text-align: right;
  padding-right:10px;
}
.percentInput{
  display: inline-block;
  width:100%;
  height: 26px;
  /* position: relative; */
}
.percent_sign {
  line-height: 36px;
  vertical-align: top;
}
</style>
