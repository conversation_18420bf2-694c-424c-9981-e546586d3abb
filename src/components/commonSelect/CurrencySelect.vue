<template>
  <el-select :value="value" placeholder="" v-bind="$attrs" @change="handlerChange">
    <el-option v-for="item in list"
      :key="item.currencyCode"
      :label="item.currencyCode"
      :value="item.currencyCode">
    </el-option>
  </el-select>

</template>
<script>
/**
 * TODO
 * 后续prop直接赋值
 */
export default {
  inheritAttrs: false,
  props: {
    value: {
      required: true,
    },
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  data() {
    return {
    };
  },
  computed: {
    list() {
      return this.$store.state.selectData.currencyList;
    },
  },
  mounted() {
    this.$store.dispatch('selectData/throttleGetCurrencyList');
  },
  methods: {
    handlerChange(val) {
      this.$emit('change', val);
    },
  },
};
</script>
