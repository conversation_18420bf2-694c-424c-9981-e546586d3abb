<template>
  <div>
    <div id="canvas" :style="{width:width+'px',height:height+'px'}"></div>
  </div>
</template>

<script>
import Konva from 'konva';

class CanvasTree {
  constructor(el, data, width = 900, height = 400) {
    this.width = width;
    this.height = height;
    this.navWidth = width / 9;
    this.canRun = true;
    this.stage = new Konva.Stage({
      container: el,
      width,
      height,
    });
    this.ctx = new Konva.Layer({
      x: this.navWidth,
      y: 0,
    });
    this.nav = new Konva.Layer({
      x: 0,
      y: 0,
    });
    this.stage.add(this.nav);
    this.stage.add(this.ctx);
    this.data = this.formatData(data);
  }

  formatData(data) {
    const arr = [];
    const yuan = [];
    function core(tree) {
      arr.push([]);
      let children = [];
      for (let i = 0; i < tree.length; i += 1) {
        if (tree[i].children && tree[i].children.length) {
          arr[arr.length - 1].push(tree[i]);
          tree[i].children.forEach((v) => {
            v.parents = [
              {
                parentId: tree[i].materialGoodsId,
                materialPercent: v.materialPercent,
              },
            ];
          });
          children = children.concat(tree[i].children);
        } else {
          let flag = true;
          for (let j = 0; j < yuan.length; j += 1) {
            if (yuan[j].materialGoodsId === tree[i].materialGoodsId) {
              flag = false;
              yuan[j].parents = yuan[j].parents.concat(tree[i].parents);
            }
          }
          if (flag) {
            if (arr.length > 1) {
              yuan.splice(parseInt(yuan.length / 2, 10), 0, tree[i]);
            } else {
              yuan.push(tree[i]);
            }
          }
        }
      }
      if (children.length) {
        core(children);
      }
    }
    core(data);
    arr[arr.length - 1] = yuan;

    return this.countCoordinate(arr, this.width - this.navWidth, this.height);
  }

  // 渲染侧边栏
  renderNav() {
    const { nav } = this;
    let txt = '';
    const x = this.navWidth / 5;
    const width = (this.navWidth / 5) * 3;
    nav.children.splice(0, nav.children.length);
    for (let i = 0; i < this.data.length; i += 1) {
      const y = (this.height / this.data.length) * i + 20;
      if (this.data[i][0].goodsType === 1) {
        txt = '产成品';
      } else if (i === this.data.length - 1) {
        txt = '原材料';
      } else {
        txt = '半成品';
      }
      const text = new Konva.Text({
        text: txt,
        x,
        y,
        fill: '#000',
        width,
        align: 'center',
        padding: 10,
      });
      if (txt !== '原材料' && this.data.length !== 1) {
        const arrow = new Konva.Arrow({
          x: 0,
          y: 0,
          points: [
            x + width / 2,
            y + this.height / this.data.length,
            x + width / 2,
            y + text.getHeight(),
          ],
          pointerLength: 8,
          pointerWidth: 8,
          fill: '#108EE9',
          stroke: '#108EE9',
          strokeWidth: 2,
        });
        nav.add(arrow);
      }
      const rect = new Konva.Rect({
        x,
        y,
        width,
        height: text.getHeight(),
        fill: '#ccc',
      });
      nav.add(rect);
      nav.add(text);
    }
    nav.draw();
  }

  // 绘制路线
  line(id) {
    const points = [];
    // 根据传入的参数储存坐标集
    for (let i = 1; i < arguments.length; i += 1) {
      // eslint-disable-next-line prefer-rest-params
      points.push(arguments[i].y, arguments[i].x);
    }
    // 由于反向是反的故将数组进行反转
    points.reverse();
    const arrow = new Konva.Arrow({
      x: 0,
      y: 0,
      points,
      pointerLength: 5,
      pointerWidth: 5,
      fill: '#108EE9',
      stroke: '#108EE9',
      strokeWidth: 1,
      name: `${id}`,
    });
    this.ctx.add(arrow);
  }

  // 计算每一项的坐标
  static countCoordinate(arr, width, height) {
    for (let k = 0; k < arr.length; k += 1) {
      const items = arr[k];
      for (let o = 0; o < items.length; o += 1) {
        const item = items[o];
        const itemText = new Konva.Text({
          text: item.materialGoodsName,
        });
        const itemWidth = itemText.getWidth() + 20;
        // 不让除了成品的项占据中间位置，避免引起界面混乱
        const left = k !== 0 && items.length === 1
          ? (width / (items.length + 1)) * (o + 1) + itemWidth
          : (width / (items.length + 1)) * (o + 1);
        const top = (height / arr.length) * k + 20;
        item.style = {
          left,
          top,
        };
      }
    }
    return arr;
  }

  getNodeById(id) {
    for (let i = 0; i < this.data.length; i += 1) {
      const items = this.data[i];
      for (let j = 0; j < items.length; j += 1) {
        const item = items[j];
        if (id === item.materialGoodsId) {
          return item;
        }
      }
    }
    return null;
  }

  static getNodeIndex(parent, children) {
    for (let i = 0; i < parent.children.length; i += 1) {
      if (parent.children[i].materialGoodsId === children.materialGoodsId) {
        return i;
      }
    }
    return -1;
  }

  render() {
    // 函数节流
    if (!this.canRun) {
      return;
    }
    this.renderNav();
    this.canRun = false;
    const { data } = this;
    // 每次进入渲染现将画布清空
    this.ctx.children.splice(0, this.ctx.children.length);
    for (let i = 0; i < data.length; i += 1) {
      const items = data[i];
      for (let j = 0; j < items.length; j += 1) {
        const item = items[j];
        const itemText = this.fillText(
          item.materialGoodsName || item.goodsName,
          item.style.left,
          item.style.top,
        );
        const width = itemText.getWidth() + 20;
        this.rect(
          item.materialGoodsId,
          item.style.left - 10,
          item.style.top - 10,
          width,
          30,
          null,
          item,
        );
        if (item.process) {
          for (let t = 0; t < item.process.length; t += 1) {
            const top = -7 + i * 14;
            const process = this.rect(
              item.process[t].processId,
              item.style.left + width,
              item.style.top - top,
              20,
              7,
              '#ccc',
            );
            this.fillText(
              item.process[i].processName,
              item.style.left + width + 25,
              item.style.top - top,
              '#ccc',
            );
            process.setFill('#ccc');
          }
        }
        if (item.parents && item.parents.length) {
          for (let p = 0; p < item.parents.length; p += 1) {
            const parentNode = this.getNodeById(item.parents[p].parentId);
            const childrenIndex = this.getNodeIndex(parentNode, item);
            const parentText = new Konva.Text({
              text: parentNode.materialGoodsName,
            });
            const parentWidth = parentText.getWidth() + 20;
            const parentSpace = parentWidth / (parentNode.children.length + 1);
            const space = width / (item.parents.length + 1);
            const left = item.style.left - width / 4 + space;
            const parentLeft = parentNode.style.left - parentWidth / 4 + parentSpace;
            const coordinate1 = {
              x: parentLeft + childrenIndex * parentSpace,
              y: parentNode.style.top + 20,
            };
            const coordinate2 = {
              x: parentLeft + childrenIndex * parentSpace,
              y: parentNode.style.top + 15 * (childrenIndex + 2),
            };
            const coordinate3 = {
              x: left + p * space,
              y: parentNode.style.top + 15 * (childrenIndex + 2),
            };
            const coordinate4 = {
              x: left + p * space,
              y: item.style.top - 10,
            };
            const materialPercentText = new Konva.Text({
              text: `${item.parents[p].materialPercent * 100}%`,
            });
            const coordinate5 = {
              x: coordinate4.x - materialPercentText.getWidth() / 2,
              y:
                coordinate4.y
                - (coordinate4.y - coordinate2.y) / 2
                - p * (space + 10),
            };
            this.line(
              item.materialGoodsId,
              coordinate1,
              coordinate2,
              coordinate3,
              coordinate4,
            );
            this.fillText(
              `${parseFloat((item.parents[p].materialPercent * 100).toFixed(2))
              }%`,
              coordinate5.x,
              coordinate5.y,
              '#339841',
            );
          }
        }
      }
    }
    this.ctx.draw();
    this.canRun = true;
  }

  fillText(text, x, y, color, padding = 0) {
    color = color || '#000';
    const konvaText = new Konva.Text({
      text,
      x,
      y,
      fill: color,
      padding,
    });
    this.ctx.add(konvaText);
    return konvaText;
  }

  rect(id, x, y, width, height, color, item) {
    color = color || '#66ccff';
    const draggable = !!item;
    const rect = new Konva.Rect({
      x,
      y,
      width,
      height,
      stroke: color,
      name: `${id}`,
      draggable,
      dragBoundFunc(pos) {
        return {
          x: pos.x,
          y: this.getAbsolutePosition().y,
        };
      },
    });
    const that = this;
    if (item) {
      const sum = item.style.left - x;
      rect.on('dragmove', (e) => {
        const offsetX = e.evt.offsetX - that.width / 9;
        const offsetY = y + 10;
        item.style.left = offsetX - sum;
        item.style.top = offsetY;
        that.ctx.clear();
        that.render();
      });
      rect.on('click', (e) => {
        const { name } = e.target.attrs;
        const shapre = that.ctx.find(`.${name}`);
        shapre.each((v) => {
          v.setStroke('red');
        });
        that.ctx.draw();
      });
    }

    this.ctx.add(rect);
    return rect;
  }
}
export default {
  props: {
    tree: Array,
    width: {
      type: Number,
      default: 900,
    },
    height: {
      type: Number,
      default: 400,
    },
  },
  components: {},
  data() {
    return {
      msg: 'Hello moto! what`s up,yaoyao',
      ctx: null,
    };
  },
  watch: {
    tree() {
      this.ctx.data = this.ctx.formatData(this.tree);
      this.ctx.render();
    },
  },
  mounted() {
    this.ctx = new CanvasTree('canvas', this.tree, this.width, this.height);
    this.ctx.render();
  },
  methods: {
    clearLine() {},
  },
};
</script>

<style scoped>
.btn_login {
  background: #04be02;
  color: #fff;
  display: block;
  height: 45px;
  text-align: center;
  line-height: 45px;
  border-radius: 10px;
  margin-top: 25px;
}
.treeBox {
  width: 500px;
  height: 600px;
  border: 1px solid #000;
  position: relative;
}
.treeBox ul {
  width: 100%;
}
.treeBox ul li {
  position: absolute;
  padding: 10px 23px;
  /* float: left; */
  border: 1px solid #66ccff;
  transform: translate(-50%, -50%);
}
#canvas {
  margin: 0 auto;
  border: 1px solid #66ccff;
}
</style>
