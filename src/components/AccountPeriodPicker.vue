<!--
 * @Author: 肖泽涛
 * @Description: 全局属期控件
 * @Date: 2019-03-20 16:51:45
 * @LastEditTime: 2019-11-21 15:55:45
 *
 * @LastEditors: 启旭
 * @Update: 新增 disabled 配置
 *
-->
<template>
  <el-date-picker
    type="month"
    v-model="localValue"
    class="picker"
    :disabled="disabled"
    :clearable="false"
    :editable="false"
    :picker-options="pickerOptions"
    @change="handlerChange" />
</template>

<script>
import dayjs from 'dayjs';

export default {
  props: {
    // 自定义禁用日期方法
    disabledDate: {
      type: Function,
    },
    disabled: {
      type: <PERSON>olean,
    },
    value: {
      type: [Date, String],
      required: true,
    },
  },
  model: {
    prop: 'value',
    event: 'changeValue',
  },

  data() {
    // 默认选项
    const pickerOptions = {
      // 所有选择月份的，建账年月之前默认为置灰
      disabledDate: (time) => !(time.getTime() < Date.now()
      && time.getTime() > this.accountDate.getTime()),
    };

    // 合并用户选项
    const { disabledDate } = this;
    if (typeof disabledDate === 'function') {
      pickerOptions.disabledDate = disabledDate;
    }

    return {
      pickerOptions,
      // accountPeriod: this.$store.state.user.accountPeriod || new Date(),
    };
  },

  computed: {
    localValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('changeValue', val);
      },
    },
    // 当前账套建账月
    accountDate() {
      return dayjs(this.$store.state.user.companyInfo.accountDate).toDate();
    },
  },

  watch: {
    // 用户选项变化时，自动合并
    disabledDate(newValue) {
      if (typeof newValue === 'function') {
        this.pickerOptions.disabledDate = newValue;
      }
    },
  },

  methods: {
    handlerChange(val) {
      // this.$store.commit('user/SET_GLOBAL_ACCOUNT_PERIOD', new Date(val));
      console.log(val, '------account');
      this.$emit('change', val);
    },
  },
};
</script>
<style lang="scss" scoped>
.picker{
  &::v-deep .el-input__inner{
    padding-right: 8px;
  }
}
</style>
