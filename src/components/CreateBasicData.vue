<template>
  <div>
    <el-form
    ref="form"
    class="create-basic-data"
    :model="formData"
    :rules="rules"
    label-width="72px"
    @keyup.enter.native="handleEnter">
      <!-- 销方： 客户供应商 -->
      <template
        v-if="formType === 'seller' || ['customers', 'suppliers'].indexOf(formData.sellerType) > -1">
        <el-form-item
          v-if="formType === 'seller' || formType.length === 2"
          v-show="!defaultFormData.sellerType" prop="sellerType"
          label="类型">
          <el-radio-group
            v-model="formData.sellerType"
            @change="changeTableData(formData.sellerName)">
            <el-radio label="customers">客户</el-radio>
            <el-radio label="suppliers">供应商</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="sellerName" label="名称">
          <el-input
            v-model.trim="formData.sellerName"
            placeholder="请输入名称"
            @blur="changeTableData(formData.sellerName)">
          </el-input>
        </el-form-item>
        <h3>是否同时新增科目：</h3>
        <el-table
          border
          ref="subjectTable"
          :data="tableData"
          :auto-width="false"
          @selection-change="setSelection" >
          <el-table-column type="selection" width="60" align="center"></el-table-column>
          <el-table-column prop="subjectFullName" label="科目"></el-table-column>
        </el-table>
        <p style="color: red">未勾选的科目在生成自动凭证时, 如需使用, 会自动新增</p>
      </template>
      <!-- 单位 -->
      <template  v-if="formType === 'unit'">
        <el-form-item prop="saleunitName" label="单位名称">
          <el-input v-model.trim="formData.saleunitName" placeholder="请输入单位名称"></el-input>
        </el-form-item>
        <el-form-item prop="enabledDecimal" label="是否为小数">
          <el-switch  v-model="formData.enabledDecimal" on-text="是"  off-text="否"> </el-switch>
        </el-form-item>
      </template>
      <!-- 摘要名称 -->
      <template  v-if="formType === 'voucherAbstract'">
        <el-form-item prop="voucherAbstract" label="摘要名称">
          <el-input v-model.trim="formData.voucherAbstract" placeholder="请输入摘要名称"></el-input>
        </el-form-item>
      </template>
      <!-- 银行账号 -->
      <template  v-if="formType === 'bank'">
        <el-form-item prop="bankName" label="开户行">
          <el-input v-model.trim="formData.bankName" placeholder="请输入开户行"></el-input>
        </el-form-item>
        <el-form-item prop="bankAccount" label="银行账号">
          <el-input v-model.trim="formData.bankAccount" placeholder="请输入银行账号"></el-input>
        </el-form-item>
        <el-form-item prop="invAccountFlag" label="开票账户">
          <el-switch v-model="formData.invAccountFlag" on-text="是" off-text="否"> </el-switch>
        </el-form-item>
      </template>
      <!-- 科目 -->
      <template v-if="formType === 'subject'">
        <el-form-item prop="subjectFullNameTpl" label="上级科目">
          <el-select v-model="formData.subjectFullNameTpl" filterable style="width: 100%">
            <el-option
            v-for="item in subjectList"
            :key="item.subjectId"
            :value="item.subjectCode"
            :label="item.subjectCode + ' ' + item.subjectFullName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="subjectName" label="科目名称" v-if="formData.subjectFullNameTpl !== '1002'">
          <el-input v-model.trim="formData.subjectName" placeholder="请输入科目名称"></el-input>
        </el-form-item>
        <el-form-item prop="bankName" label="开户行" v-if="formData.subjectFullNameTpl === '1002'">
          <el-input v-model.trim="formData.bankName" placeholder="请输入开户行"></el-input>
        </el-form-item>
        <el-form-item prop="bankAccount" label="银行账号" v-if="formData.subjectFullNameTpl === '1002'">
          <el-input type="number"
          v-model.trim="formData.bankAccount"
          placeholder="请输入银行账号"></el-input>
        </el-form-item>
        <el-form-item label="单位核算" prop="isUnit">
          <el-col :span="5" style="float: left;">
            <el-form-item prop="isUnit">
              <el-checkbox-group v-model="formData.isUnit" @change="handleisUnitChange">
                <el-checkbox label="是"></el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="11" style="float: left;">
              <el-form-item prop="unit" v-if="formData.isUnit.indexOf('是') !== -1">
              <unit-select v-model="formData.unit"></unit-select>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="外币核算" prop="isCurrency">
          <el-col :span="5" style="float: left;">
            <el-form-item prop="isCurrency">
              <el-checkbox-group v-model="formData.isCurrency" @change="handleIsCurrencyChange">
                <el-checkbox label="是"></el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="11" style="float: left;">
            <el-form-item prop="endingExchange">
              <el-checkbox-group v-model="formData.endingExchange">
                <el-checkbox label="期末调汇"></el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="币别" prop="currencyType" v-if="formData.isCurrency.indexOf('是') !== -1">
          {{ functionalCurrency }}
          <el-checkbox-group v-model="formData.currencyType">
            <el-checkbox
            v-for="item in currencyList" name="currencyType"
            :key="item.currencyCode"
            :label="item.currencyName"
            :value="item.currencyName"
            :class="{ 'display-none' : item.currencyCode === functionalCurrency }"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import merge from 'lodash-es/merge';
import UnitSelect from '@/components/commonSelect/unitSelect/UnitSelect.vue';
import { insertSubject } from '@/helper/subject';

export default {
  props: {
    formType: {
      type: [String, Array],
      default: 'seller',
    },

    defaultFormData: {
      type: Object,
      default() {
        return {};
      },
    },

    $store: {
      type: Object,
      required: true,
    },

    props: Object,
  },
  components: {
    UnitSelect,
  },
  mounted() {
    this.setDefaultFormData();
    if (this.formType === 'subject') {
      // 触发第一次进入的whater, 待优化。
      const path = this.defaultFormData.subjectFullNameTpl;
      if (path) {
        this.defaultFormData.subjectFullNameTpl = path;
      }
      // 获取单位、币种
      this.getUnitNameList();
      this.getCurrencyList();
    }
  },

  computed: {
    subjectList() {
      return this.$store.state.selectData.subjectList.filter((item) => item.subjectCode.length <= 8);
    },
    currencyList() {
      return this.$store.state.selectData.currencyList;
    },
    unitList() {
      return this.$store.state.selectData.unitNameList;
    },
    functionalCurrency() {
      return this.$store.state.user.companyInfo.functionalCurrency;
    },
  },

  data() {
    return {
      formData: {
        sellerName: '',
        sellerType: '',
        subjectFullNameTpl: '',
        subjectName: '',
        bankName: '',
        bankAccount: '',
        invAccountFlag: false,
        isUnit: [],
        unit: '',
        resource: '启用',
        isCurrency: [],
        currencyType: [],
        endingExchange: false,
        saleunitName: '',
        voucherAbstract: '',
        enabledDecimal: false,
      },
      rules: {
        sellerType: [
          { required: true, message: '请选择类型', trigger: 'blur' },
        ],
        sellerName: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          {
            type: 'string', max: 100, message: '名称长度最多100个字符', trigger: 'blur',
          },
        ],
        subjectName: [
          { required: true, message: '请输入科目名称', trigger: 'blur' },
          { validator: this.validateSubjectName, trigger: 'blur' },
        ],
        subjectFullNameTpl: [
          {
            required: true, type: 'string', message: '请选择上级科目', trigger: 'change',
          },
          // { type: 'string', validator: this.validateSubjectFullNameTpl, trigger: "change" },
        ],
        voucherAbstract: [
          {
            required: true, message: '请输入摘要名称', trigger: 'blur',
          },
          {
            type: 'string', max: 200, message: '名称长度最多200个字符', trigger: 'blur',
          },
        ],
        bankName: [
          {
            required: true, type: 'string', message: '请输入开户行', trigger: 'blur',
          },
          { validator: this.validateBankName, trigger: 'blur' },
        ],
        bankAccount: [
          {
            required: true, type: 'string', message: '请输入银行账号', trigger: 'blur',
          },
          { validator: this.validateBankAccount, trigger: 'blur' },
        ],
        unit: [
          { required: true, message: '请选择计量单位', trigger: 'change' },
        ],
        saleunitName: [
          { required: true, message: '请输入单位名称', trigger: 'blur' },
        ],
      },
      tableData: [],

      selectionList: [],
    };
  },

  methods: {
    ...mapActions('selectData', [
      'getCurrencyList',
      'getUnitNameList',
    ]),
    // 失焦 手动生成表格数据
    changeTableData(value) {
      if (value) {
        if (this.formData.sellerType === 'customers') {
          this.tableData = [
            { subjectFullName: `应收账款-${value}`, subjectName: value },
            { subjectFullName: `预收账款-${value}`, subjectName: value },
            { subjectFullName: `其他应付款-${value}`, subjectName: value },
          ];
        } else {
          this.tableData = [
            { subjectFullName: `应付账款-${value}`, subjectName: value },
            { subjectFullName: `预付账款-${value}`, subjectName: value },
            { subjectFullName: `其他应收款-${value}`, subjectName: value },
          ];
        }
        this.$nextTick(() => {
          // 需求5639
          // this.$refs.subjectTable.toggleRowSelection(this.tableData[0], true);
        });
      } else {
        this.tableData = [];
      }
    },
    // 勾选的表格数据
    setSelection(selection) {
      this.selectionList = selection;
    },
    handleEnter() {
      if (this.$parent) {
        this.$parent.handleAction('confirm', 'confirmBtn');
      }
    },

    handleIsCurrencyChange(value) {
      if (value.length === 0) {
        this.formData.currencyType = [];
        this.formData.isCurrency = [];
      }
    },

    handleisUnitChange(value) {
      if (value.length === 0) this.formData.unit = '';
    },

    handleUnitAddBtnClick() {
      this.$emit('addclick', 'unit');
    },

    makeAjaxData() {
      let d = {};
      let url = '';
      let subject;
      if (this.formType === 'seller' || ['customers', 'suppliers'].indexOf(this.formData.sellerType) > -1) {
        url = `/rest/companyConfig/companyBasis/${this.formData.sellerType}/v1.1/insert`;
        if (this.formData.sellerType === 'customers') {
          d.customerName = this.formData.sellerName;
          d.customerType = 1;
          d.customerStatus = true;
        } else {
          d.supplierName = this.formData.sellerName;
          d.supplierStatus = true;
        }
      } else if (this.formType === 'voucherAbstract') {
        url = '/rest/companyAccount/merge/voucherAbstract/v1.0';
        d.voucherAbstract = this.formData.voucherAbstract;
      } else if (this.formType === 'unit') {
        url = '/rest/companyConfig/goods/saleUnits/v1.0/save';
        d.saleunitName = this.formData.saleunitName;
        d.enabledDecimal = this.formData.enabledDecimal;
        d = [d];
      } else if (this.formType === 'bank') {
        url = '/rest/companyConfig/companyBasis/bankAccounts/v1.1/save';
        d.accountType = 1;
        d.accountStatus = true;
        d.bankName = this.formData.bankName;
        d.bankAccount = this.formData.bankAccount;
        d.invAccountFlag = this.formData.invAccountFlag;
        d = [d];
      } else if (this.formType === 'subject') {
        url = '/rest/companyConfig/companyBasis/subjectinfo/v1.1/insert?ignoreParent=1';
        subject = this.findArrayByPath(this.formData.subjectFullNameTpl);
        d.subjectName = this.formData.subjectName;
        d.subjectParentName = subject && subject.subjectFullName;
        d.subjectParentCode = subject && subject.subjectCode;

        if (this.formData.isUnit.indexOf('是') !== -1) {
          d.saleunitName = this.formData.unit;
        }

        if (this.formData.isCurrency.indexOf('是') !== -1) {
          d.currencies = this.currencyList.filter((item) => item.currencyCode !== this.functionalCurrency && this.formData.currencyType.indexOf(item.currencyName) !== -1);
        }
        if (this.formData.subjectFullNameTpl === '1002') {
          d.bankName = this.formData.bankName;
          d.bankAccount = this.formData.bankAccount;
          d.subjectName = `${this.formData.bankName}_${this.formData.bankAccount}`;
        }

        d.endingExchange = !!this.formData.endingExchange;
      }
      return { d, url };
    },

    async doSave() {
      let canClose = true;
      this.$refs.form.validate((valid) => {
        canClose = valid;
      });
      if (canClose) {
        const { d, url } = this.makeAjaxData();
        let ajaxState = true;
        let rsp;
        try {
          rsp = await this.$http.post(url, JSON.stringify(d), { headers: { noMessage: true } });
          this.$message.success('操作成功');
          if (this.formType === 'unit') {
            this.$emit('update', this.formData.saleunitName);
          } else if (this.formType === 'voucherAbstract') {
            this.$emit('update', this.formData.voucherAbstract);
          } else if (!(this.formType === 'seller' || ['customers', 'suppliers'].indexOf(this.formData.sellerType) > -1)) {
            this.$emit('update', rsp.data.data && rsp.data.data[0], { subjectFullNameTpl: this.formData.subjectFullNameTpl });
          }
        } catch (e) {
          ajaxState = false;
          canClose = false;
        }
        if (this.formType === 'seller' || ['customers', 'suppliers'].indexOf(this.formData.sellerType) > -1) {
          let successText = '';
          let messageType = 'success';
          const typeText = this.formData.sellerType === 'customers' ? '客户' : '供应商';
          if (this.selectionList.length) {
            const paramsList = this.selectionList
              .map((item) => Object.assign(item, { relatedTable: 1 }));
            try {
              await insertSubject(paramsList);
            } catch (e) {
              console.error(e);
            }

            if (ajaxState) {
              successText = '操作成功';
            } else {
              successText = `${typeText}已存在，科目新增成功。`;
            }
          } else if (ajaxState) {
            successText = `${typeText}新增成功，科目未勾选，无法生成科目。`;
          } else {
            successText = `${typeText}已存在，操作失败`;
            messageType = 'error';
          }
          this.$message[messageType](successText);
          if (rsp) {
            this.$emit('update', rsp.data.data && rsp.data.data[0]);
          } else {
            const resObj = this.formData.sellerType === 'customers'
              ? this.$store.state.selectData.customerList.find((item) => item.customerName === this.formData.sellerName) || {}
              : this.$store.state.selectData.supplierList.find((item) => item.supplierName === this.formData.sellerName) || {};
            this.$emit('update', resObj);
          }
          canClose = true;
        }
      }
      return canClose;
    },

    findArrayByPath(pathArray) {
      return this.subjectList.find((item) => item.subjectCode === pathArray);
    },

    resetData() {
      this.$refs.form.resetFields();
      this.tableData = [];
    },

    setDefaultFormData() {
      const { formData, formType, defaultFormData } = this;
      merge(formData, defaultFormData);
      formData.sellerType = ((formType instanceof Array) && formType[0]) || this.defaultFormData.sellerType || '';
    },

    validateSubjectName(rule, value, callback) {
      if (value && value.length > 100) {
        callback(new Error('科目名称不能超过100个字符'));
        return;
      }
      callback();
    },
    validateBankName(rule, value, callback) {
      if (value && (value.length > 24)) {
        callback(new Error('开户行不能超过二十四个字'));
      } else {
        callback();
      }
    },
    validateBankAccount(rule, value, callback) {
      const validateBankAccountRules = !/^\d{1,25}$/.test(value);
      if (value && validateBankAccountRules) {
        callback(new Error('请输入1位到25位数字的银行账号'));
      } else {
        callback();
      }
    },
  },

  watch: {
    'formData.currencyType.length': function handlerCurrencyType(newValue) {
      this.formData.isCurrency = newValue > 0 ? ['是'] : [];
    },

    'defaultFormData.subjectFullNameTpl': {
      handler(subjectPath) {
        if (!subjectPath) return;
        this.$set(this.formData, 'subjectFullNameTpl', subjectPath);
        // 获取单位、币种
        this.getUnitNameList();
        this.getCurrencyList();
      },
      deep: true,
    },
    'defaultFormData.sellerName': {
      handler(newVal) {
        this.$set(this.formData, 'sellerName', newVal);
      },
      deep: true,
    },

    'defaultFormData.sellerType': {
      handler(newVal) {
        this.$set(this.formData, 'sellerType', newVal);
      },
      deep: true,
    },

    formType() {
      this.setDefaultFormData();
    },
  },
};
</script>

<style lang="scss"  >
  .create-basic-data {
    .el-form-item .el-form-item.is-error {
      margin-bottom: 22px;
    }
    .el-checkbox + .el-checkbox {
      margin-left: 0;
    }
    .el-checkbox {
      padding-right: 5px;
    }
  }
</style>
