<!--
 * @Description: 导入自定义表头控件
 * @version:
 * @Company: 海闻软件
 * @Author: 卓海斌
 -->
 <template>
  <div>
    <el-row style="margin-bottom: 20px;">
      <div class="item-title">{{  `您上传的Excel是 【${fileName}】 数据所在的sheet名称是 《${sheetName}》` }}</div>
      <div class="header-list" v-if="!isMatchEnd">文件解析中，请稍后。</div>
      <div class="header-list" v-if="headerList.length !== 0">
        <span class="header-list-item" v-for="(item, index) in headerList" :key="index">{{ item.label || item.excelCell }}</span>
      </div>
      <div class="header-list" v-else>未检测到表头，请重新选择表头或重新导入文件</div>
      <div>
        <template v-if="isSpreadSheetView">
          <el-button type="primary" key="cf" @click="handleCfChooseClick">确认选中表头所在行</el-button>
          <el-button type="primary" key="cancel" @click="handleChoCancelClick">收起</el-button>
          <span class="fz12 cl-red"> 合并表头的文件 请多选行 </span>
        </template>
        <template v-else>
          <el-button type="primary" key="re" @click="handleReChooseClick" :loading="!isMatchEnd">重新选择表头</el-button>
        </template>
      </div>
      <div v-show="isSpreadSheetView">
        <div id="spreadsheet" class="spreadsheet" style="width: 100%; height: 250px; margin-top: 5px;"></div>
      </div>
    </el-row>
    <el-row v-show="!isMigrationMode">
      <slot name="selectFeature" :$this="this"></slot>
      <el-row class="item-title" :gutter="2">
        <el-col :span="4">Excel表头名称</el-col>
        <el-col :span="4" class="text-center">导入对应</el-col>
        <el-col :span="4">系统名称</el-col>
      </el-row>
      <slot name="Trade" v-if="isTradeMode"></slot>
      <el-form v-else ref="cfgForm" :model="cfgForm" :rules="formRules" label-position="right" >
        <el-row :gutter="2" v-for="(item, index) in sheetMatchRules" :key="index">
          <el-col :span="4">
            <el-form-item :prop="item.systemCell">
              <el-select v-model="cfgForm[`${item.systemCell}`]" placeholder="请选择" clearable filterable :disabled="isBusinessProp(formRules, `${item.systemCell}`)">
                <el-option v-for="(opt, oIndex) in headerList" :label="opt.excelCell" :value="opt.excelCell" :key="oIndex" v-show="getIsOptShow(opt, `${item.systemCell}`)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4" class="text-center"><span style="line-height: 36px;"> >> </span></el-col>
          <el-col :span="4">
            <span style="color: #c00;" v-if="isRequired(formRules, `${item.systemCell}`)">*</span>
            <span style="line-height: 36px;">{{ item.systemCell }}</span>
          </el-col>
        </el-row>
      </el-form>
    </el-row>
    <!-- 旧账迁移 -->
    <slot name="content" :$this="this" v-if="isMigrationMode" ></slot>
    <slot name="footerTips" :$this="this"> </slot>
  </div>
</template>

<script>
import { unionBy, isString } from 'lodash-es';
import { a1StyleNameFromRef } from '@/utils/spreadsheet';

export default {
  props: {
    cfgForm: {
      required: true,
    },
    // 表头是否必填规则
    formRules: {
      required: true,
    },
    // 默认表头
    defList: {
      required: true,
    },
    // 初始表头的url
    initHeaderUrl: {
      type: String,
      default: '',
    },
    needToJson: {
      required: true,
      type: Boolean,
    },
    // 是否需要记忆
    isMemory: {
      type: Boolean,
      default: true,
    },
    // 自定义匹配项规则,无需记忆时必填
    customValiaRule: {
      type: Array,
    },
    // 是否是旧账迁移
    isMigrationMode: {
      type: Boolean,
      default: false,
    },
    // 是否是资金模块银行导入
    isTradeMode: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      isMatchEnd: false,
      customFileList: [],
      isSpreadSheetView: false,
      hasParseError: false,
      kendoSpreadsheet: null,
      spreadsheetJSON: null, // 来自远程解析到的JSON
      headerList: [],
      matchTargetNum: 3,
      dataEndRowNum: 50,
      sheetMatchRules: this.ajaxAdapter(),
      checkRepeat: false, // 是否判断重复导入， 如果选择是， 流水号表头必须选择
      sheetName: '',
      fileName: '',

    };
  },
  methods: {
    async initSpreadSheet(fileList) {
      this.customFileList = fileList;
      if (this.kendoSpreadsheet === null) {
        this.kendoSpreadsheet = $('#spreadsheet').kendoSpreadsheet({
            toolbar: false,
            maxAnalysisCol: this.dataEndRowNum,
          })
          .data('kendoSpreadsheet');
      }

      if (this.needToJson) {
        this.initSpreadSheetFromRemote();
      } else {
        await this.initSpreadSheetFromFile(fileList[0].raw);
      }
      this.fileName = fileList[0].name;
      this.doSheetMatchRules();
      this.setCftFormValue();
    },

    initSpreadSheetFromRemote() {
      if (this.spreadsheetJSON) {
        this.kendoSpreadsheet.fromJSON(this.spreadsheetJSON);
      }
    },

    async initSpreadSheetFromFile(file) {
      try {
        // spreadsheet源码在解析出错时不会向外抛错，如要处理此情况，需要修改源码。
        await this.kendoSpreadsheet.fromFile(file);
        this.hasParseError = false;
      } catch (e) {
        this.$parent.$parent.$parent.showErrorMsg('文件解析异常，请检查导入文件！');
        this.hasParseError = true;
        throw e;
      }
    },
    doSheetMatchRules() {
      let maxColName = 'AY';
      let headerList = [];
      this.isMatchEnd = false;
      let selectSheet = this.kendoSpreadsheet.sheets()[0];
      this.kendoSpreadsheet.sheets().forEach((sheet) => {
        // 当已经找到了表格头，将不再循环其它sheet
        if (headerList.length !== 0) {
          return;
        }
        selectSheet = sheet;
        maxColName = kendo.spreadsheet.Ref.display(null, Infinity, sheet._columns._count - 1);

        for (let rowNum = 1; rowNum <= sheet._rows._count; rowNum += 1) {
          // 当已经找到了表格头，将不再循环其它行
          if (headerList.length !== 0) {
            break;
          }

          const rangeStr = `A${rowNum}:${maxColName}${rowNum}`;
          // console.log('rangeStr', rangeStr)
          headerList = this.getHeaderListByRules(sheet.range(rangeStr));
        }
      });
      this.headerList = headerList;
      this.sheetName = selectSheet.name();
      const selectOptList = [];
      headerList.forEach((item) => {
        const reference = new kendo.spreadsheet.CellRef(item.row, item.column);
        const key = a1StyleNameFromRef(reference);
        const keyValue = item.label || item.excelCell;
        selectOptList.push({
          label: `${key}列 ${keyValue}`,
          value: `${item.column + 1}`,
          item,
          sheetName: selectSheet._sheetName,
          rowNum: `${item.row + 1}`,
        });
      });
      this.$emit('getOptList', selectOptList, this.headerList);
      this.isMatchEnd = true;
    },
    setCftFormValue() {
      // 下拉框里面的值
      const optValues = this.headerList.map((item) => item.excelCell);
      this.sheetMatchRules.forEach((rule) => {
        let cfgFormValue = '';
        // 找到系统记录的关系和匹配到的表头的公共项，取排序靠前的。
        const commonOptValues = rule.excelCellList?.filter((excelCell) => optValues.indexOf(excelCell) !== -1);

        // 实际上这里能找到至少一个
        if (commonOptValues?.length > 0) {
          cfgFormValue = commonOptValues[0] || '';
        }

        this.cfgForm[`${rule.systemCell}`] = cfgFormValue;
      });

      this.$refs.cfgForm?.validate();
    },
    getHeaderListByRules(rowRange) {
      const headerList = [];

      // 建立副本
      const matchRules = this.sheetMatchRules.map((item) => item);
      let hadMatchTargetNum = 0;

      rowRange.forEachCell((row, column, cell) => {
        // console.log(row, column, cell);
        if (cell.value && typeof cell.value === 'string') {
          let systemCell = '';
          const excelCell = cell.value.trim();

          const matchIndex = matchRules.findIndex((rule) => rule.excelCellList?.find((item) => item === excelCell));

          if (matchIndex !== -1) {
            // 已匹配的数量加1
            hadMatchTargetNum += 1;

            // 从匹配规则里面删除已中的规则, 并记录
            const delArray = matchRules.splice(matchIndex, 1);
            systemCell = delArray[0] && delArray[0].systemCell;
          }

          // 将有值的项加入表头统计
          headerList.push({
            row,
            column,
            systemCell,
            excelCell,
          });
        }
      });

      // 只有有足够的命中次数才定义为表头
      return hadMatchTargetNum >= this.matchTargetNum ? headerList : [];
    },

    ajaxAdapter(data) {
      const { defList } = this;
      const systemCellList = data && data.length !== 0 ? unionBy(data, defList, 'systemCell') : defList;

      const list = systemCellList.map((item) => {
        const { excelCell, systemCell } = item;
        const excelCellList = (excelCell || '').split(',');
        return { excelCell, systemCell, excelCellList };
      });

      if (this.isTradeMode) {
        const abstractIndex = list.findIndex((item) => item.systemCell === '摘要');
        list.splice(abstractIndex + 1, 0, { excelCell: '', systemCell: '付款人名称，收款人名称是否各自一列' });

        const counterpartsNameIndex = list.findIndex((item) => item.systemCell === '对方名称');
        list.splice(counterpartsNameIndex + 1, 0,
        { excelCell: '', systemCell: '收入支出是否在同列' },
        { excelCell: '', systemCell: '正数表示' },
        { excelCell: '', systemCell: '收入/支出' });
        const payNameIndex = list.findIndex((item) => item.systemCell === '付款人名称(收入流水对方名称)');
        const payNameItem = list.splice(payNameIndex, 1);
        const payeeNameIndex = list.findIndex((item) => item.systemCell === '收款人名称(支出流水对方名称)');
        const payeeNameItem = list.splice(payeeNameIndex, 1);
        list.splice(counterpartsNameIndex, 0, ...payNameItem, ...payeeNameItem);
      }
      return list;
    },
    // 获取原表表头信息
    async getCellRule() {
      let data = [];
      if (this.isMemory) {
        const rsp = await this.$http.get(this.initHeaderUrl);
        data = rsp.data.data;
      } else {
        // 当不需要记忆时
        data = this.customValiaRule;
      }
      this.sheetMatchRules = this.ajaxAdapter(data);
    },
    // 确认该表头
    async handleCfChooseClick(titleRowNum, oldSubSelectRow) {
      let headerList = [];
      let sheetIndex = 0;
      const sheet = this.kendoSpreadsheet.activeSheet();
      const sheetList = this.kendoSpreadsheet.sheets();
      sheetList.forEach((itemSheet, index) => {
        if (sheet._sheetName === itemSheet._sheetName) {
          sheetIndex = index;
        }
      });
      const selection = sheet.selection();
      const selectionTopLeft = selection._ref.topLeft.row + 1;
      const selectionBottomRight = selection._ref.bottomRight.row + 1;
      let subSelectRow = selectionBottomRight - selectionTopLeft;
      if (subSelectRow >= 3) {
        this.$message.warning('只能选择3行或以下确认表头');
        return;
      }
      let rowNum = selectionBottomRight;

      if (titleRowNum && isString(titleRowNum)) {
        rowNum = Number(titleRowNum);
        subSelectRow = Number(oldSubSelectRow);
      }

      const maxColName = kendo.spreadsheet.Ref.display(null, Infinity, sheet._columns._count - 1);
      const rangeStr = `A${rowNum}:${maxColName}${rowNum}`;

      let rangeStrAdd1 = '';
      let rangeStrAdd2 = '';
      let rowRange1 = [];
      let rowRange2 = [];
      if (subSelectRow === 1) {
         rangeStrAdd1 = `A${rowNum - 1}:${maxColName}${rowNum - 1}`;
         rowRange1 = sheet.range(rangeStrAdd1);
      }
      if (subSelectRow === 2) {
        rangeStrAdd2 = `A${rowNum - 2}:${maxColName}${rowNum - 2}`;
        rowRange2 = sheet.range(rangeStrAdd2);
      }
      headerList = this.getHeaderListByChoose(sheet.range(rangeStr), subSelectRow, rowRange1, rowRange2);

      if (headerList.length === 0) {
        this.$message.warning('该行没有可作为表头的数据！');
        return;
      }

      this.headerList = headerList;
      this.sheetName = sheet.name();
      const selectOptList = [];
      headerList.forEach((item) => {
        const reference = new kendo.spreadsheet.CellRef(item.row, item.column);
        const key = a1StyleNameFromRef(reference);
        const keyValue = item.label || item.excelCell;
        selectOptList.push({
          label: `${key}列 ${keyValue}`,
          value: `${item.column + 1}`,
          item,
          sheetName: sheet._sheetName,
          sheetIndex,
          rowNum: `${item.row + 1}`,
        });
      });
      this.$emit('getOptList', selectOptList, this.headerList);

      this.setCftFormValue();
      this.handleChoCancelClick();
    },
    handleChoCancelClick() {
      this.isSpreadSheetView = false;
    },
    // 重新选择表头
    async handleReChooseClick() {
      const fileList = this.customFileList;
      this.isSpreadSheetView = true;

      await this.$nextTick();

      if (this.needToJson) {
        this.initSpreadSheetFromRemote();
      } else {
        await this.initSpreadSheetFromFile(fileList[0].raw);
      }

      const sheet = this.kendoSpreadsheet.activeSheet();

      if (this.headerList.length === 0) {
        sheet.select('A1');
      } else {
        const rowNum = this.headerList[0].row + 1;
        sheet.select(`A${rowNum}:AY${rowNum}`);
      }
    },
    getIsOptShow(opt, selfKey) {
      return !Object.entries(this.cfgForm)
        .map((item) => item[1])
        .filter((item) => item)
        .find((item) => item === opt.excelCell && item !== this.cfgForm[selfKey]);
    },
    getSystemCell(excelCell) {
      let systemCell = '';
      const rule = this.sheetMatchRules.find((sheetMatchRule) => !!sheetMatchRule?.excelCellList?.find((item) => item === excelCell));

      if (rule) {
        systemCell = rule.systemCell;
      }

      return systemCell;
    },

    getHeaderListByChoose(rowRange, subSelectRow, rowRange1, rowRange2) {
      const sheet = this.kendoSpreadsheet.activeSheet();
      const headerList = [];
      let topLabel1 = '';
      let topLabel12 = '';
      const isMoreSelectionRow = !!subSelectRow;
      rowRange.forEachCell((row, column, cell) => {
        let label = '';
        const nowRow = row;
        const nowColumn = column;
        const excelCell = cell.value && typeof cell.value === 'string' ? cell.value.trim() : cell.value;
        label = excelCell;

        if (!isMoreSelectionRow) {
          console.log('表头选择一行');
        } else {
          rowRange1.forEachCell((row1, column1, cell1) => {
            if (column !== column1) { return; }
            const excelCell1 = cell1.value && typeof cell1.value === 'string' ? cell1.value.trim() : cell1.value;
            if (!label && !excelCell1) { return; }
            const ref = new kendo.spreadsheet.CellRef(row1, column1);
            const isMyCellMerged1 = sheet._mergedCells.some((mergedCell) => mergedCell.intersect(ref) !== kendo.spreadsheet.NULLREF);

            if (isMyCellMerged1) { // 先判断 是不是合并单元格
              topLabel1 = excelCell1 || topLabel1;
              if (label) { // 下行有值 则直接拼一起 :  1 有之前存的合并格的值，取 拼/ 没 上行值
                label = topLabel1 ? `${topLabel1} - ${ label }` : label;
              } else { // 下行的是空的， 取当前行
                label = excelCell1;
              }
            } else {
              console.log('上行不是合并行');
              if (!label) { // 上行不是合并单元格但， 下行的是空的， 取当前行
                label = excelCell1;
              }
            }

            if (subSelectRow === 2) {
              rowRange2.forEachCell((row2, column2, cell2) => {
                if (column !== column2) { return; }
                const excelCell2 = cell2.value && typeof cell2.value === 'string' ? cell2.value.trim() : cell2.value;
                const ref2 = new kendo.spreadsheet.CellRef(row2, column2);
                const isMyCellMerged2 = sheet._mergedCells.some((mergedCell) => mergedCell.intersect(ref2) !== kendo.spreadsheet.NULLREF);
                if (isMyCellMerged2) {
                  topLabel12 = excelCell2 || topLabel12;
                  label = topLabel12
                    ? `${topLabel12} - ${ label }`
                    : label;
                }
              });
            }
          });
        }

        const systemCell = this.getSystemCell(excelCell);
        if (label && typeof label === 'string') {
          headerList.push({
            row: nowRow,
            column: nowColumn,
            label,
            subSelectRow,
            systemCell,
            excelCell,
          });
        }
      });
      return headerList;
    },
    getCustomParams() {
      let paramKey = -1;
      const paramsList = [];
      Object.entries(this.cfgForm).forEach(([key, value]) => {
        const systemCellList = this.sheetMatchRules.map((item) => item.systemCell);
        const systemCell = systemCellList[systemCellList.indexOf(key)];

        // 对空的序号要把后面的序号往前排
        if (value) {
          paramKey += 1;
        } else {
          return;
        }

        console.log(this.headerList, key, value);
        const headerListItem = this.headerList.find((item) => item.excelCell === value);

        paramsList.push({
          systemCell,
          value,
          headerListItem: headerListItem ? headerListItem.column : 0,
          titleRowNum: this.headerList[0] ? this.headerList[0].row : 0,
          rowNum: this.headerList[0] ? this.headerList[0].row + 1 : 1,
          paramKey,
        });
      });

      return paramsList;
    },
    getRemoteJSON(file) {
      const formData = new FormData();

      formData.append('file', file);

      return $.ajax({
        url: '/rest/proxy/account/bankAccount/v2.0/excelToJson',
        type: 'POST',
        cache: false,
        data: formData,
        processData: false,
        contentType: false,
      })
        .done((rsp) => {
          this.spreadsheetJSON = rsp.data[0] ? JSON.parse(rsp.data[0]) : null;
        })
        .fail((e) => {
          const errorId = e.responseJSON.errorId ? `-${e.responseJSON.errorId}` : '';
          const errorText = e.responseJSON.errorMessage ? e.responseJSON.errorMessage + errorId : `解析文档失败！${errorId}`;
          this.$emit('errorFile', errorText);
        });
    },
    isBusinessProp(rules, index) {
      if (rules[index]) {
        return rules[index][0]?.disabled || false;
      }
      return false;
    },
    isRequired(rules, index) {
      if (rules[index]) {
        return rules[index][0]?.required || false;
      }
      return false;
    },
  },
};
</script>

<style lang="scss" scoped>
.header-list {
  width: 100%;
  margin-top: 10px;
  margin-bottom: 5px;
  overflow-y: hidden;
  overflow-x: auto;
  white-space: nowrap;
}
.header-list .header-list-item {
  text-align: center;
  border: 1px solid #ccc;
  display: inline-block;
  padding: 8px 10px;
}
.item-title {
  font-weight: bold;
}
.uploaded-file {
  display: flex;
  display: -ms-flexbox;
  display: -webkit-flex;
  padding: 5px 0;
  align-items: flex-start;
  -ms-flex-align: start;
  .uploaded-file-name {
    display: inline-block;
    line-height: 1;
    flex: 1;
    padding-left: 7px;
  }
  .el-icon-upload-success {
    color: #13ce66;
  }
  .el-icon-upload-fail {
    color: #f7ba2a;
  }
}
</style>
