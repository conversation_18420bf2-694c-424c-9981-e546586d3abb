<!--
 * @Description:  富文本编辑器
 -->
<template>
  <div>
    <Editor
      v-model="localValue"
      :init="init"
    />
  </div>
</template>

<script>
import { blobToBase64 } from '@/assets/Utils';
import Editor from '@tinymce/tinymce-vue';

import tinymce from 'tinymce/tinymce';
import 'tinymce/themes/silver';
import 'tinymce/icons/default';
import 'tinymce/plugins/paste';
import 'tinymce/plugins/link';
import 'tinymce/plugins/fullscreen';
import 'tinymce/plugins/table';
import 'tinymce/plugins/image';
import 'tinymce/plugins/preview';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/advlist';

import 'tinymce/skins/ui/oxide/skin.min.css'; // 富文本样式

const toolbar = `fontsizeselect fontselect  searchreplace
  bold italic underline strikethrough alignleft aligncenter alignright outdent indent
  blockquote undo redo removeformat subscript superscript
  'bullist numlist  preview  table emoticons  fullscreen link unlink',
`;

// advlist_number_styles添加中文排序 simp-chinese-informal， 补充工具栏的按钮图标
const { icons } = tinymce.IconManager.get('default');
tinymce.IconManager.add('default', {
  icons: {
    ...icons,
    'list-num-simp-chinese-informal': `
    <svg width="48" height="48" xmlns="http://www.w3.org/2000/svg">

 <g fill-rule="evenodd">
  <path opacity=".2" d="M18 12h22v4H18zM18 22h22v4H18zM18 32h22v4H18z"/>
  <text xml:space="preserve" text-anchor="start" font-family="SimSun" style="font-size:8px" stroke-width="0.3" id="svg_3" y="16" x="6" stroke="#000" fill="#000000">一.</text>
  <text xml:space="preserve" text-anchor="start" font-family="SimSun" style="font-size:8px" id="svg_4" y="26" x="6" stroke-width="0.3" stroke="#000" fill="#000000">二.</text>
  <text xml:space="preserve" text-anchor="start" font-family="SimSun" style="font-size:8px" id="svg_5" y="37" x="6" stroke-width="0.3" stroke="#000" fill="#000000">三.</text>
 </g>
</svg>`,
  },
});
export default {
  name: 'TinyEditor',
  components: {
    Editor,
  },
  props: {
    value: {
      type: String,
      default: '',
    },
    plugins: {
      type: String,
      default: '',
    },
    toolbar: {
      type: String,
      default: toolbar,
    },
  },
  computed: {
    localValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('change', val);
      },
    },
  },
  data() {
    return {
      init: {
        height: 420,
        language: 'zh_CN',
        language_url: require('./langs/zh_CN.js'),
        plugins: 'paste fullscreen link table image preview lists advlist',
        paste_data_images: true,
        paste_retain_style_properties: 'all',
        fontsize_formats: '8pt=0.67em 10pt=0.83em 12pt=1em 14pt=1.17em 16pt=1.33em 18pt=1.5em 24pt=2em 36pt=3em',
        font_formats: '微软雅黑=\'微软雅黑\';宋体=\'宋体\';黑体=\'黑体\';仿宋=\'仿宋\';楷体=\'楷体\';隶书=\'隶书\';幼圆=\'幼圆\';Andale Mono=andale mono,times;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier;Georgia=georgia,palatino;Helvetica=helvetica;Impact=impact,chicago;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco;Times New Roman=times new roman,times;Trebuchet MS=trebuchet ms,geneva;Verdana=verdana,geneva;Webdings=webdings;Wingdings=wingdings',
        toolbar: this.toolbar || undefined,
        advlist_number_styles: 'default,lower-alpha,lower-greek,lower-roman,upper-alpha,upper-roman,simp-chinese-informal',
        async images_upload_handler(blobInfo, success, failure) {
          try {
            const base64 = await blobToBase64(blobInfo.blob());
            success(base64);
          } catch (e) {
            console.error(e);
            failure('文件转换失败');
          }
        },
      },
    };
  },
  mounted() {
    tinymce.init({}); // 这里传个空对象就可以了
  },
};
</script>
