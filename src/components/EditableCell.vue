<template>
  <div
    ref="domRef"
    class="EditableCell">
    <div
      v-if="!exposeModel.state.editing"
      tabindex="1"
      class="EditableCell__view"
      @click="onClickEdit">
      <slot v-bind="exposeModel"></slot>
    </div>

    <div
      v-if="exposeModel.state.editing"
      class="EditableCell__editor">
      <slot name="editor" v-bind="exposeModel" />
    </div>

    <div
      v-if="exposeModel.state.editing"
      class="EditableCell__actions">
      <slot name="actions" v-bind="exposeModel" />
      <i
        v-if="showConfirmButton"
        class="EditableCell__actions__confirm el-icon-check"
        :class="[exposeModel.state.loading ? 'el-icon-loading' : 'el-icon-check']"
        @click="onClickConfirm" />
    </div>
  </div>
</template>

<script>
import {
 defineComponent, reactive, ref, watch, nextTick, onMounted, onBeforeUnmount,
} from 'vue';

export default defineComponent({
  name: 'EditableCell',

  props: {
    showConfirmButton: {
      type: Boolean,
      default: false,
    },

    onConfirm: {
      type: Object,
      required: false,
    },

    focusEditor: {
      type: Function,
      required: false,
    },
  },

  setup(props, { emit }) {
    const domRef = ref(null);

    let exposeModel;

    const state = reactive({
      loading: false,
      editing: false,
    });

    const toEdit = () => {
      if (!state.editing) {
        state.editing = true;
        emit('editing-start', exposeModel);
      }
    };

    const loading = () => {
      if (!state.loading) {
        state.loading = true;
        emit('loading-start', exposeModel);
      }
    };

    const done = () => {
      if (state.loading) {
        state.loading = false;
        emit('loading-done', exposeModel);
      }
      if (state.editing) {
        state.editing = false;
        emit('editing-done', exposeModel);
      }
    };

    const confirm = async () => {
      if (state.loading) return;

      const onConfirm = props.onConfirm ?? (() => Promise.resolve());
      state.loading = true;
      try {
        await onConfirm();
        done();
      } catch (error) {
        console.log('EditableCell:onConfirm fail: ', error);
      }
      state.loading = false;
    };

    const onFocus = () => {
      if (!state.editing) return;
      if (props.focusEditor) {
        props.focusEditor();
      } else if (domRef.value) {
        const $container = domRef.value;
        const $input = $container.querySelectorAll('.EditableCell__editor input')[0];
        if ($input && $input.focus) {
          $input.focus();
        }
      }
    };

    exposeModel = {
      state,
      done,
      loading,
      toEdit,
      confirm,
    };

    const onClickEdit = () => {
      toEdit();
    };

    const onClickConfirm = () => {
      confirm();
    };

    // 切换出编辑器时，尝试聚焦
    watch(() => state.editing, (editing) => {
      if (editing) {
        nextTick(onFocus);
        // onFocus();
      }
    });

    const onClickOutside = (event) => {
      if (!state.editing) return;
      if (domRef.value.contains(event.target)) return;
      emit('click-outside', exposeModel, event);
    };
    onMounted(() => {
      document.addEventListener('click', onClickOutside);
    });
    onBeforeUnmount(() => {
      document.removeEventListener('click', onClickOutside);
    });

    return {
      domRef,
      exposeModel,
      onClickEdit,
      onClickConfirm,
    };
  },
});
</script>

<style lang="scss" scoped>
.EditableCell {
  box-sizing: border-box;
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
}
.EditableCell__view {
  flex: 1 1 100%;
  height: 100%;
  min-height: 24px;
}
.EditableCell__editor {
  flex: 1 1 100%;
  height: 100%;
}
.EditableCell__actions {
  flex: 0 0 auto;
  width: 24px;
  height: 100%;
  text-align: center;
}
.EditableCell__actions__confirm {
  font-size: 14px;
  color: #383;
  cursor: pointer;
}
</style>
