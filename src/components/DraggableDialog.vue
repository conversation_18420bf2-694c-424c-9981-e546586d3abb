<template>
  <Dialog
    v-bind="$attrs"
    v-on="$listeners"
    v-draggable
  >
    <template #title><slot name="title"></slot></template>
    <template #default><slot name="default"></slot></template>
    <template #footer><slot name="footer"></slot></template>
  </Dialog>
</template>
<script>
import { Dialog } from 'element-ui';

export default {
  directives: {
    draggable: {
      bind(el, binding, vnode) {
        const dialog = el.querySelector('.el-dialog');
        const title = el.querySelector('.el-dialog__title');
        const header = el.querySelector('.el-dialog__header');

        dialog.offsetX = 0;
        dialog.offsetY = 0;

        const headerStyle = header.style;
        headerStyle.cursor = 'move';
        headerStyle.userSelect = 'none';
        headerStyle['-ms-user-select'] = 'none';
        headerStyle['-moz-user-select'] = 'none';

        // 以 slot 传入 title 时，可能不存在
        if (title) {
          const titleStyle = title.style;
          titleStyle.userSelect = 'none';
          titleStyle['-ms-user-select'] = 'none';
          titleStyle['-moz-user-select'] = 'none';
          titleStyle.cursor = 'default';
        }

        function move(e) {
          dialog.style.marginLeft = '0px';
          dialog.style.marginTop = '0px';
          dialog.style.left = `${e.pageX - dialog.offsetX}px`;
          dialog.style.top = `${e.pageY - dialog.offsetY}px`;
        }

        function up() {
          window.removeEventListener('mousemove', move);
          window.removeEventListener('mouseup', up);
        }

        function down(e) {
          dialog.offsetX = (e.pageX - dialog.offsetLeft);
          dialog.offsetY = (e.pageY - dialog.offsetTop);
          window.addEventListener('mousemove', move);
          window.addEventListener('mouseup', up);
        }

        header.addEventListener('mousedown', down);
        (header).down = down;
      },

      unbind(el, binding, vnode) {
        const header = el.querySelector('.el-dialog__header');
        header.removeEventListener('mousedown', header.down);
        header.down = null;
      },
    },
  },
  components: { Dialog },
  watch: {
    $route() {
      this.$emit('update:visible', false); // 路由变化时关闭
    },
  },
  data() {
    return {
    };
  },
};
</script>
