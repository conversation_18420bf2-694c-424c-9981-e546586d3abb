<!--
 * @LastEditors: XR
 * @Author: XR
 * @Description: 公共打印配置
-->
<template>
  <el-form ref="form" label-width="84px" :model="printForm" class="publicPrintClass">
    <el-form-item label="打印纸张" class="custom-el-form">
      <el-select v-model="printForm.paperSize">
        <el-option value="A5" label="A5(14cm×21cm)"></el-option>
        <el-option value="A4" label="A4(21cm×29.7cm)"></el-option>
        <el-option value="B4" label="B4(25.7cm×36.4cm)"></el-option>
        <el-option value="B5" label="B5(18.2cm×25.7cm)"></el-option>
        <el-option value="INVOICE" label="发票大小(14cm×24cm)"></el-option>
        <el-option value="CUSTOM" label="自定义"></el-option>
      </el-select>
      <span v-show="printForm.paperSize==='CUSTOM'">
        <h6>宽</h6>
        <el-input-number
          v-model="printForm.paperSizeWidth"
          size="mini"
          class="custom-input-number"
          controls-position="right"
          :precision="1"
          :step="0.1"
          :max="29.7"
          :min="14">
        </el-input-number>
        <h6>厘米</h6>
        <h6>高</h6>
        <el-input-number
          v-model="printForm.paperSizeHeight"
          size="mini"
          class="custom-input-number"
          controls-position="right"
          :precision="1"
          :step="0.1"
          :max="42"
          :min="14">
        </el-input-number>
        <h6>厘米</h6>
      </span>
    </el-form-item>
    <el-form-item label="打印方向">
      <el-radio-group v-model="printForm.paperDirection">
        <el-radio :label="1" :disabled="printForm.tablePerPage === 2">横向</el-radio>
        <el-radio :label="2" :disabled="printForm.tablePerPage === 2">纵向</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="每页版数" v-if="showTablePerPage">
      <el-radio-group v-model="printForm.tablePerPage" @change="changeTablePerPage">
        <el-radio :label="1" >1</el-radio>
        <el-radio :label="2" :disabled="printForm.paperDirection === 1">2</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="页边距" class="custom-el-form">
      <h6>上</h6>
      <el-input-number
        v-model="printForm.marginsTop"
        size="mini"
        class="custom-input-number"
        controls-position="right"
        :precision="1"
        :step="0.1"
        :max="3"
        :min="0">
      </el-input-number>
      <h6>厘米</h6>
      <h6>下</h6>
      <el-input-number
        v-model="printForm.marginsBottom"
        size="mini"
        class="custom-input-number"
        controls-position="right"
        :disabled="printForm.autoSubjectHeight === 0"
        :precision="1"
        :step="0.1"
        :max="3"
        :min="0">
      </el-input-number>
      <h6>厘米</h6>
      <br/>
      <h6>左</h6>
      <el-input-number
        v-model="printForm.marginsLeft"
        size="mini"
        class="custom-input-number"
        controls-position="right"
        :precision="1"
        :step="0.1"
        :max="3"
        :min="0">
      </el-input-number>
      <h6>厘米</h6>
      <h6>右</h6>
      <el-input-number
        v-model="printForm.marginsRight"
        size="mini"
        class="custom-input-number"
        controls-position="right"
        :precision="1"
        :step="0.1"
        :max="3"
        :min="0">
      </el-input-number>
      <h6>厘米</h6>
    </el-form-item>
    <el-form-item label="分录行数" v-if="showSubjectCount">
      <el-input-number
        v-model="printForm.rowCountPrePage"
        :disabled="printForm.autoSubjectHeight=== 2"
        size="mini"
        class="custom-input-number"
        controls-position="right"
        :step="1"
        :max="30"
        :min="4">
      </el-input-number>
    </el-form-item>
    <el-form-item label="分录行高" class="custom-el-form" v-if="showAutoSubjectHeight">
      <el-radio-group v-model="printForm.autoSubjectHeight" @change="changeAutoSubjectHeight">
        <el-radio :label="1">根据纸张调整大小（行高均等）</el-radio>
        <el-radio :label="2" :disabled="disabledAutoSubjectHeight">根据内容长度调整行高（每行不等高）</el-radio>
        <el-radio :label="0">自定义</el-radio>
      </el-radio-group>
      <span v-show="printForm.autoSubjectHeight === 0">
        <el-input-number
          v-model="printForm.rowHeight"
          size="mini"
          class="custom-input-number"
          controls-position="right"
          :precision="1"
          :step="0.1"
          :max="customHeightRange"
          :min="0.6"
          @change="hangleChangeHeight">
        </el-input-number>
        <h6>厘米（行高均等）</h6>
      </span>
    </el-form-item>
    <el-form-item label="字号">
      <el-select v-model="printForm.fontSize">
        <el-option value="7" label="7号"></el-option>
        <el-option value="8" label="8号"></el-option>
        <el-option value="9" label="9号"></el-option>
        <el-option value=10 label="10号"></el-option>
        <el-option value="11" label="11号"></el-option>
        <el-option value="12" label="12号"></el-option>
        <el-option value="13" label="13号"></el-option>
        <el-option value="14" label="14号"></el-option>
        <el-option value="15" label="15号"></el-option>
        <el-option value="16" label="16号"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item >
      <span class="cl-red">
        注意: 边距过大、行数过多、字体过大，都有可能导致打印不出内容，请合理调整。
      </span>
    </el-form-item>
  </el-form>

</template>
<script>
export default {
  props: {
    defaultPrintObj: {
      type: Object,
    },
    showTablePerPage: {
      type: Boolean,
      default: true,
    },
    showSubjectCount: {
      type: Boolean,
      default: true,
    },
    showAutoSubjectHeight: {
      type: Boolean,
      default: true,
    },
    disabledAutoSubjectHeight: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      printForm: {
        paperSize: 'A5',
        paperSizeWidth: 14,
        paperSizeHeight: 14,
        paperDirection: 1,
        tablePerPage: 1,

        marginsTop: 1.2,
        marginsBottom: 1.2,
        marginsLeft: 1.6,
        marginsRight: 1.3,

        rowCountPrePage: 8,
        autoSubjectHeight: 1,
        rowHeight: 1.2,
        fontSize: '10',
      },
    };
  },
  computed: {
    // 选择自定义分录行高时能允许的最大值
    customHeightRange: {
      get() {
        let h;
        switch (this.printForm.paperSize) {
          case 'A5': h = this.printForm.paperDirection === '1' ? 14 : 21; break;
          case 'A4': h = this.printForm.paperDirection === '1' ? 21 : 29.6; break;
          case 'B4': h = this.printForm.paperDirection === '1' ? 25.6 : 36.4; break;
          case 'B5': h = this.printForm.paperDirection === '1' ? 18.2 : 25.6; break;
          case 'INVOICE': h = this.printForm.paperDirection === '1' ? 14 : 24; break;
          case 'CUSTOM': h = this.printForm.paperDirection === '1' ? this.printForm.paperSizeWidth : this.printForm.paperSizeHeight; break;
          default: h = 0.5;
        }
        if (h === 0.5) return 0.5;
        // 输入范围=（高-上边距 -h记账凭证-h凭证日期-h核算单位-h表头-h合计行-h记账）/分录行数，
        // =（高-上边距-5.2cm）/分录行数
        const num1 = h - this.printForm.marginsTop - 5.2;
        const num2 = Number(this.printForm.rowCountPrePage) || 1;
        h = (num1.div(num2)).toFixed(1);
        return Number(h);
      },
    },
  },
  mounted() {
    this.initObj();
  },
  methods: {
    initObj() {
      console.log('defaultPrintObj', this.defaultPrintObj);
      let { autoSubjectHeight, rowCountPrePage } = this.defaultPrintObj;
      if (this.defaultPrintObj.autoSubjectHeight !== 2) {
        autoSubjectHeight = this.defaultPrintObj.autoSubjectHeight ? 1 : 0;
      }
      if (!this.defaultPrintObj.rowCountPrePage) {
        rowCountPrePage = Number(this.defaultPrintObj.subjectCount);
      }
      const tablePerPage = this.defaultPrintObj.tablePerPage ? this.defaultPrintObj.tablePerPage : 1;
      this.printForm = {
        ...this.defaultPrintObj, autoSubjectHeight, rowCountPrePage, tablePerPage,
      };
    },
    // 每页版数: 当【每页版数】选择选“2”时，只能默认选中纵向，不可修改
    changeTablePerPage(val) {
      if (val === 2) {
        this.printForm.paperDirection = 2;
      }
    },
    // 行数: 当【行高】选择“根据内容长度调整行高（每行不等高）时，此项禁用，不用传值给后端
    changeAutoSubjectHeight(val) {
      if (val === 2) {
        this.printForm.rowCountPrePage = '';
      } else {
        const rowCountPrePage = this.defaultPrintObj.rowCountPrePage || this.defaultPrintObj.subjectCount;
        this.printForm.rowCountPrePage = Number(rowCountPrePage);
      }
    },
    // 行高触发改变
    hangleChangeHeight() {
      // 下边距=高-上边距-5.2cm-分录行高*分录行数
      // 最大行高不能小于0.6
      // 自定义框框默认行高为1.2
      let h;
      if (this.printForm.autoSubjectHeight === 0) {
        switch (this.printForm.paperSize) {
          case 'A5': h = this.printForm.paperDirection === '1' ? 14 : 21; break;
          case 'A4': h = this.printForm.paperDirection === '1' ? 21 : 29.6; break;
          case 'B4': h = this.printForm.paperDirection === '1' ? 25.6 : 36.4; break;
          case 'B5': h = this.printForm.paperDirection === '1' ? 18.2 : 25.6; break;
          case 'INVOICE': h = this.printForm.paperDirection === '1' ? 14 : 24; break;
          case 'CUSTOM': h = this.printForm.paperDirection === '1' ? this.printForm.paperSizeWidth : this.printForm.paperSizeHeight; break;
          default: h = 0.5;
        }
        const { marginsTop, rowCountPrePage, rowHeight } = this.printForm;
        const num1 = marginsTop.add(5.2);
        const num2 = Number(rowCountPrePage).mul(rowHeight);
        this.printForm.marginsBottom = h.sub(num1).sub(num2);
        console.log('this.printForm.marginsBottom', this.printForm.marginsBottom);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
  .publicPrintClass {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px dashed #bfbfbf;
  }
</style>
