<!--
 * @LastEditors: 晓荣
 * @Author: 肖泽涛
 * @Description: 百分比输入框
 * @Date: 2019-03-29 11:22:56
 * @LastEditTime: 2019-05-17 12:08:41
 -->
<template>
  <div class="percentInput">
    <el-input
    :disabled="disabled"
    @click.native.stop="handlClick"
    :placeholder="placeholder"
    style="width:80px;"
    @blur="valChange"
    v-model="oldValue"
    @input="handlInput"
    @focus="selectAll">
    </el-input>
    <span class="percent_sign">%</span>
  </div>
</template>

<script>

export default {
  props: {
    value: {
      type: [Number, String],
      default: 0,
    },
    placeholder: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    // 监听外部值的变化
    value(val) {
      val = parseFloat(val);
      if (Number.isNaN(val)) {
        val = 0;
      } else {
        val = Math.round(val.mul(100000000)).div(100000000);
      }
      // 内部显示值 *100 显示百分比值
      this.oldValue = val.mul(100);
      this.copyVal = val.mul(100);
    },
  },
  data() {
    return {
      oldValue: 0,
      copyVal: 0,
    };
  },
  ready() {

  },
  mounted() {
    let val = parseFloat(this.value);

    if (Number.isNaN(val)) {
      val = 0;
    } else {
      val = Math.round(val.mul(100000000)).div(100000000);
    }
    this.oldValue = val.mul(100);
    this.copyVal = val.mul(100);
  },
  methods: {
    // 当失去焦点时，触发修改外部值操作
    valChange() {
      this.$nextTick(() => {
        this.oldValue = Number(this.oldValue === '' ? 0 : this.oldValue);
        // 如果用户输入了非数字，则变回原值
        if (Number.isNaN(this.oldValue) && this.oldValue !== '') {
          this.oldValue = this.copyVal;
        }
        // 如果输入的值只能0-100
        if (this.oldValue > 100) {
          this.oldValue = 100;
        } else if (this.oldValue < 0) {
          this.oldValue = 0;
        }
        // 备份当前值为原值
        this.copyVal = this.oldValue;
        // 计算实际值，传递给外部
        const f = parseFloat(this.oldValue.toFixed(6)).div(100);
        if (this.value !== f) {
          this.$emit('update:value', f);
          this.$emit('change', f);
        }
        this.$emit('blur');
      });

      // }
    },
    handlClick(e) {
      this.$emit('click', e);
    },
    // 聚焦时自动选中全部
    selectAll(event) {
      setTimeout(() => {
        event.target.select();
        this.$emit('focus');
      }, 0);
    },
    handlInput(val) {
      this.$emit('input', val);
    },
  },
};
</script>

<style lang="scss">
.percentInput input{
  text-align: right;
  padding-right:10px;
}
.percentInput{
  display: inline-block;
  width:100%;
  height: 26px;
  .el-input--mini .el-input__inner {
    height: 26px;
  }
  /* position: relative; */
}
.percent_sign {
  line-height: 36px;
  vertical-align: top;
}
</style>
