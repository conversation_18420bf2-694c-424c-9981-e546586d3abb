<!--
 * @LastEditors: 肖泽涛
 * @Author: 肖泽涛
 * @Description: 表格数据滚动加载的加载情况展示
 * @Date: 2019-03-29 14:14:35
 * @LastEditTime: 2019-05-28 14:56:56
 -->
<template>
  <div>
    <div v-show="loadStatus == 'LOADING'" class="loading_info">
      <i class="el-icon-loading"></i>数据加载中
    </div>
    <div v-show="loadStatus == 'FAIL'" class="loading_info">
      <i class="el-icon-circle-close"></i>数据加载失败，
      <el-button
      type="text"
      title="再次加载"
      @click="$emit('load')"
      :disabled="loadStatus === 'LOADING'">
        <i class="fa fa-refresh"></i>
      </el-button>
      再次加载
    </div>
    <div v-show="loadStatus == 'SUCCESS'" class="loading_info">
      <template v-if="typeof allLoaded === 'object' && (total - loadedNum)">
        <template v-if="allLoaded.up && allLoaded.down">
          <i class="el-icon-circle-check"></i>数据加载完毕
        </template>
        <template v-else>
          <i class="el-icon-information"></i>
          <span v-if="!allLoaded.up && !allLoaded.down">向上滚动到顶端（或向下滚动到底端）</span>
          <span v-else-if="!allLoaded.up">向上滚动到顶端</span>
          <span v-else-if="!allLoaded.down">向下滚动到底端</span>加载更多数据
          <span class="highLight">
            （已加载&nbsp;{{ loadedNum }}，剩余未加载&nbsp;{{ Math.max(0, (total - loadedNum)) }}）&nbsp;
          </span>
          <el-button type="text" @click="handleLoadAll">
            加载全部
          </el-button>
        </template>
      </template>
      <template v-else>
        <span v-if="allLoaded"><i class="el-icon-circle-check"></i>数据加载完毕</span>
        <template v-else>
          <span><i class="el-icon-information"></i>滚动到底部加载更多数据</span>
          <span class="highLight">
            （已加载&nbsp;{{ loadedNum }}，剩余未加载&nbsp;{{ Math.max(0, (total - loadedNum)) }}）&nbsp;
          </span>
          <el-button type="text" @click="handleLoadAll">加载全部</el-button>
        </template>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    allLoaded: {
      type: [Object, Boolean],
      required: true,
    },
    loadStatus: {
      type: String,
      required: true,
    },
    loadedNum: {
      type: Number,
      required: true,
    },
    total: {
      type: Number,
      required: true,
    },
  },

  data() {
    return {};
  },

  mounted() {},

  watch: {},

  methods: {
    handleLoadAll() {
      if ((this.total - this.loadedNum) > 2000) {
        this.$confirm('当前加载数据量过大，可能会导致页面卡顿或崩溃，是否继续。(如果是为了导出，不需要加载全部数据，直接导出即可)', {
          title: '提示',
        }).then(() => {
          this.$emit('loadAll');
        });
      } else {
        this.$emit('loadAll');
      }
    },
  },
};
</script>

<style lang="scss"   scoped>
  .loading_info {
    font-size: 12px;
    position: relative;
    color: #000;
    margin-top: -1px;
    line-height: 30px;
    text-align: center;
    background: white;
    border: 1px solid rgb(223, 230, 236);
    & > i {
      margin-right: 5px;
    }
    & > .el-button {
      padding: 0;
    }
    .highLight {
      color: red;
    }
  }
</style>
