<template>

  <div class="status-tag-box" :title="tooltipText">
    {{statusText}}
  </div>
</template>
<script setup>
import { computed } from 'vue';

const props = defineProps({
  company: {
    type: Object,
    default: () => ({}),
  },
});
// 是否为只查看权限
const isReadonly = computed(() => props.company.isReadonly);

// 是否已过期
const isExpired = computed(() => props.company.isExpired);

// 是否已停用
const isDisabled = computed(() => props.company.isDisabled); // 账套处于停用状态;

/**
 * 账套会有几种状态：

1、停用启用状态：.停用显示为【停】，启用不显示；

2、到期状态。到期显示为【到】，未到期不显示；

3、可编辑和仅查看状态。可编辑且启用状态且未到期状态时显示为【记】，仅查看时显示为【查】；
 */
const statusText = computed(() => {
  const statusList = [];
  if (isDisabled.value) {
    statusList.push('停');
  } else if (isExpired.value) {
    statusList.push('过');
  } else if (isReadonly.value) {
    statusList.push('查');
  }

  if (statusList.length === 0) statusList.push('记');
  return statusList.join(' | ');
});

const tooltipText = computed(() => {
  const tooltipList = [];
  if (isReadonly.value) {
    tooltipList.push('账套为只查看权限');
  }
  if (isExpired.value) {
    tooltipList.push('账套已过期');
  }
  if (isDisabled.value) {
    tooltipList.push('账套已停用');
  }
  return tooltipList.join(';');
});

</script>

<style lang="scss" scoped>
.status-tag-box{
  background: var(--color-theme-base);
  color: #FFF;
  border-radius: 2px;
  padding: 3px;
  margin-right: 4px;
  font-size: 12px;
  line-height: 12px;
  height: 18px;
  display: inline-block;
  box-sizing: border-box;
}
</style>
