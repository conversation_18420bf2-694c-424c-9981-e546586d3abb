<template>
  <div class="pagination-wrapper">
    <el-pagination
      class="pagination-left"
      v-bind="$attrs"
      v-on="$listeners"
      layout="total, sizes">
    </el-pagination>
    <el-pagination
      class="pagination-right"
      v-bind="$attrs"
      v-on="$listeners"
      layout="prev, pager, next">
    </el-pagination>
  </div>
</template>
<script>
export default {
  name: 'pagination',
};
</script>
<style lang="scss">
.pagination-wrapper{
  display: flex;
  justify-content: space-between;
  background: #F6F7F9;
  line-height: 40px;
  height: 40px;
  box-shadow: inset -1px 0px 0px 0px #D8E1E8,
  inset 0px -1px 0px 0px #D8E1E8,
  // inset 0px 1px 0px 0px #D8E1E8,
  inset 1px 0px 0px 0px #D8E1E8;
  .pagination-left{
    padding-left: 14px;
    margin-top: 4px;
    .el-pagination__total{
      font-weight: 400;
      color: #999999;
      font-size: 12px;
    }
    .el-pagination__sizes{
      .el-select, .el-input{
        width: 90px;

      }
      .el-input--mini .el-input__inner{
        line-height: 22px;
        height: 22px;
        font-size: 12px;
        color: #666666;
        box-sizing: border-box;
        padding-left: 2px;
        padding-right: 20px;
        border-radius: 4px;
        border: 1px solid #EBEBEB;
      }
    }
  }
  .pagination-right{
    margin-top: 4px;
    .btn-prev, .btn-next{
      background: transparent;
    }
    .el-pager{
      li{
        background: #FBFBFB;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 400;
        border: 1px solid #D7E0F1;
        min-width: 24px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        padding: 0;
        color: #666666;
        margin-top: 2px;
        margin-right: 4px;
        &:last-of-type {
          margin-right: 0;
        }
        &.active{
          background: $color-theme-base;
          color: #fff;
        }
      }
    }
  }
}
</style>
