<template>
  <AccessCtrl v-bind="$attrs">
    <template>
      <el-button
      :type="type"
      :size="size"
      :icon="icon"
      :nativeType="nativeType"
      :loading="loading"
      :autofocus="autofocus"
      :round="round"
      :circle="circle"
      :disabled="disabled"
      v-on="$listeners">
        <slot></slot>
      </el-button>
    </template>

  </AccessCtrl>
</template>
<script>
export default {
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  props: {
      type: {
        type: String,
        default: 'default',
      },
      size: {
        type: String,
        default: 'mini',
      },
      icon: {
        type: String,
        default: '',
      },
      nativeType: {
        type: String,
        default: 'button',
      },
      loading: <PERSON><PERSON>an,
      disabled: Boolean,
      plain: Boolean,
      autofocus: Boolean,
      round: Boolean,
      circle: Boolean,
    },
};
</script>
