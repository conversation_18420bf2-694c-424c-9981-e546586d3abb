<!-- 给el-table添加侧边按钮的组件， 用这个组件把el-table包裹住就可以添加侧边按钮 -->

<template>
  <div class="table-wrap">
    <slot></slot>
    <div class="button-wrap" ref="buttonWrap" @click="handleButtonClick">
      <slot name="button" v-bind="{row: hoverRow, index: hoverRowIndex}"></slot>
    </div>

  </div>
</template>

<script>
export default {
  name: 'SidebarTable',
  data() {
    return {
      hoverRow: null,
      hoverRowIndex: null,
    };
  },
  computed: {
    tableVm() {
      return this.$slots?.default[0]?.componentInstance;
    },
  },
  mounted() {
    this.tableVm.$on('cell-mouse-enter', this.setHoverRow);
  },
  beforeDestroy() {
    this.tableVm.$off('cell-mouse-enter', this.setHoverRow);
  },
  methods: {
    setHoverRow(row, column, cell) {
      const tableComponentName = this.tableVm.$options._componentTag;
      const headerHeight = tableComponentName === 'pl-table' ? this.tableVm.$refs.singleTable.$refs.headerWrapper.clientHeight : this.tableVm.$refs.headerWrapper.clientHeight;
      const bodyWrapper = tableComponentName === 'pl-table' ? this.tableVm.$refs.singleTable.$refs.bodyWrapper : this.tableVm.$refs.bodyWrapper;
      const rowEl = cell.parentElement;
      const rowTop = rowEl.offsetTop;
      const rowHeight = rowEl.clientHeight;
      this.$refs.buttonWrap.style.top = `${rowTop + rowHeight / 2 + headerHeight - bodyWrapper.scrollTop}px`;
      this.hoverRow = row;
      this.hoverRowIndex = this.tableVm.data.indexOf(row);
    },
    // 每次点击后都重置按钮位置， 隐藏
    handleButtonClick() {
      this.$refs.buttonWrap.style.top = '-10000px';
    },
  },
};
</script>
<style lang="scss" scoped>
.table-wrap{
  position: relative;
  .button-wrap{
    position: absolute;
    left: 0px;
    z-index: 10;
    top: -10000px;
    transform: translate(-100%, -50%);
    font-size: 12px;
  }
}
</style>
