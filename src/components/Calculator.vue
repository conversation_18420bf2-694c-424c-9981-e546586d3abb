<template>
  <div class="calculator">
    <button @click="changeModeEvent"  class="toggle-button">
      <p v-if="changeMode">简易计算器 &nbsp; &#9864;</p>
      <p v-else>高级计算器 &nbsp; &#9862;</p>
    </button>
    <button size="mini" @click="setValue" class="button" style="width:50px;">
      赋值
    </button>
    <div class="results">
      <textarea
      class="input"
      :value="current"
      @input="handleInput"
      :style="{height: inputHeight, lineHeight: parseFloat(inputHeight) > 30 ? '18px' : '30px'}"
      @keyup.enter.stop.prevent="setValue"
      @keydown.stop="hanldeAlt"/>
    </div>
    <div class="mode" v-if="changeMode">
      <button class="button" @click="press">7</button>
      <button class="button" @click="press">8</button>
      <button class="button" @click="press">9</button>
      <button class="button" @click="press" style="background:red">C</button>

      <button class="button" @click="press">&#60;=</button>

      <button class="button" @click="press">4</button>
      <button class="button" @click="press($event)">5</button>
      <button class="button" @click="press">6</button>
      <button class="button" @click="press">*</button>
      <button class="button" @click="press">/</button>
      <button class="button" @click="press">1</button>
      <button class="button" @click="press">2</button>
      <button class="button" @click="press">3</button>
      <button class="button" @click="press">(</button>
      <button class="button" @click="press">)</button>
      <button class="button" @click="press">0</button>

      <button class="button" @click="press">x ²</button>
      <button class="button" @click="press">±</button>
      <button class="button" @click="press">-</button>
      <button class="button" @click="press">.</button>
      <button class="button" @click="press">%</button>
      <button class="button" @click="press">+</button>
      <button class="button equal-sign" @click="press">=</button>
    </div>
    <div class="mode" v-else>
      <button class="button" @click="press">sin</button>
      <button class="button" @click="press">cos</button>
      <button class="button" @click="press">tan</button>
      <button class="button" @click="press"  style="background:red">C</button>
      <button class="button" @click="press">&#60;=</button>
      <button class="button" @click="press">x^</button>
      <button class="button" @click="press">log</button>
      <button class="button" @click="press">ln</button>
      <button class="button" @click="press">e</button>
      <button class="button" @click="press">∘</button>
      <button class="button" @click="press">rad</button>
      <button class="button" @click="press">√</button>
      <button class="button" @click="press">%</button>

      <button class="button" @click="press">x ²</button>
      <button class="button" @click="press">x !</button>
      <button class="button" @click="press">7</button>
      <button class="button" @click="press">8</button>
      <button class="button" @click="press">9</button>

      <button class="button" @click="press">(</button>
      <button class="button" @click="press">)</button>

      <button class="button" @click="press">4</button>
      <button class="button" @click="press">5</button>
      <button class="button" @click="press">6</button>
      <button class="button" @click="press">*</button>
      <button class="button" @click="press">-</button>
      <button class="button" @click="press">1</button>
      <button class="button" @click="press">2</button>
      <button class="button" @click="press">3</button>

      <button class="button" @click="press">/</button>
      <button class="button" @click="press">+</button>

      <button class="button" @click="press">0</button>
      <button class="button" @click="press">.</button>
      <button class="button" @click="press">&#x003C0;</button>
      <button class="button" @click="press">±</button>
      <button class="button equal-sign" style="width:30px" @click="press">=</button>
    </div>
</div>
</template>
<script>
// import math from 'mathjs'
export default {
  data() {
    return {
      current: '',
      changeMode: true,
    };
  },
  computed: {
    inputHeight() {
      return `${30 + (Math.min(Math.floor(this.current.length / 20), 19) * 18)}px`;
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.$el.querySelector('.input').focus();
    });
  },
  methods: {
    press(event) {
      const me = this;
      const key = event.target.textContent;
      try {
        if (
          key !== '='
          && key !== 'C'
          && key !== '*'
          && key !== '/'
          && key !== '√'
          && key !== 'x ²'
          && key !== '%'
          && key !== '<='
          && key !== '±'
          && key !== 'sin'
          && key !== 'cos'
          && key !== 'tan'
          && key !== 'log'
          && key !== 'ln'
          && key !== 'x^'
          && key !== 'x !'
          && key !== 'π'
          && key !== 'e'
          && key !== 'rad'
          && key !== '∘'
        ) {
          me.current += key;
        } else if (key === '=') {
          if ((`${me.current}`).indexOf('^') > -1) {
            const base = (me.current).slice(0, (me.current).indexOf('^'));
            const exponent = (me.current).slice((me.current).indexOf('^') + 1);
            // eslint-disable-next-line no-eval
            me.current = eval(`Math.pow(${base},${exponent})`);
          } else {
            // eslint-disable-next-line no-eval
            me.current = parseFloat(eval(me.current).toFixed(8));
          }
        } else if (key === 'C') {
          me.current = '';
        } else if (key === '*') {
          me.current += '*';
        } else if (key === '/') {
          me.current += '/';
        } else if (key === '+') {
          me.current += '+';
        } else if (key === '-') {
          me.current += '-';
        } else if (key === '±') {
          if ((me.current).charAt(0) === '-') {
            me.current = (me.current).slice(1);
          } else {
            me.current = `-${me.current}`;
          }
        } else if (key === '<=') {
          me.current = me.current.substring(0, me.current.length - 1);
        } else if (key === '%') {
          me.current /= 100;
        } else if (key === 'π') {
          me.current *= Math.PI;
        } else if (key === 'x ²') {
          // eslint-disable-next-line no-eval
          me.current = eval(me.current * me.current);
        } else if (key === '√') {
          me.current = Math.sqrt(me.current);
        } else if (key === 'sin') {
          me.current = Math.sin(me.current);
        } else if (key === 'cos') {
          me.current = Math.cos(me.current);
        } else if (key === 'tan') {
          me.current = Math.tan(me.current);
        } else if (key === 'log') {
          me.current = Math.log10(me.current);
        } else if (key === 'ln') {
          me.current = Math.log(me.current);
        } else if (key === 'x^') {
          me.current += '^';
        } else if (key === 'x !') {
          if (me.current === 0) {
            me.current = '1';
          } else if (me.current < 0) {
            me.current = NaN;
          } else {
            let number = 1;
            for (let i = me.current; i > 0; i -= 1) {
              number *= i;
            }
            me.current = number;
          }
        } else if (key === 'e') {
          me.current = Math.exp(me.current);
        } else if (key === 'rad') {
          me.current *= (Math.PI / 180);
        } else if (key === '∘') {
          me.current *= (180 / Math.PI);
        }
      } catch (e) {
        this.$message.error('计算出错');
      }
    },
    changeModeEvent() {
      const me = this;
      me.changeMode = !me.changeMode;
    },
    setValue(e) {
      e.stopPropagation();
      e.cancelBubble = true;
      if (this.current === '') return;
      try {
        if ((`${this.current}`).indexOf('^') > -1) {
          const base = (this.current).slice(0, (this.current).indexOf('^'));
          const exponent = (this.current).slice((this.current).indexOf('^') + 1);
          // eslint-disable-next-line no-eval
          this.current = eval(`Math.pow(${base},${exponent})`);
        } else {
          // eslint-disable-next-line no-eval
          this.current = parseFloat(eval(this.current).toFixed(8));
        }
        this.$emit('setValue', this.current);
      } catch (err) {
        this.$message.error('计算出错');
      }
    },
    hanldeAlt(e) {
      if (e.keyCode === 18) {
        this.$emit('hide');
      }
    },
    // 支持输入带了千分号的数字，将其转为正常数字
    handleInput(e) {
      let { value } = e.target;
      value = value.replace(/,/g, '');
      this.current = value;
    },
  },
};
</script>
<style scoped>
.calculator {
  width: 168px;
  padding: 5px;
  border-radius: 5px;
  margin: 0px auto;
  font-size: 16px;
  background-color: hsl(0, 0%, 20%);
}
.bounce-enter-active {
  animation: bounce-in 1s reverse;
}
.bounce-leave-active {
  animation: bounce-in 1s reverse;
}
@keyframes bounce-in {
  0% {
    transform: scale(0) rotate(0deg);
  }
  50% {
    transform: scale(4.5) rotate(180deg);
  }
  100% {
    transform: scale(1.8) rotate(360deg);
  }
}
.input {
  width: 160px;
  resize: none;
  min-height: 30px;
  line-height: 30px;
  border-radius: 0px;
  overflow-x:visible;
  overflow-y:visible;
  border: 1px solid hsl(0, 68%, 47%);
  word-break:break-all;
  word-wrap: break-word;
  background-color: #333333;
  color: #d9d9d9;
  padding: 0 5px 0 5px;
  margin: 0 0px 10px 0px;
  font-size: 12px;
}

.input:focus,
.input:active {
  border-color: #03a9f4;
  box-shadow: 0 0 4px #03a9f4;
  outline: none 0;
}

.button {
  margin: 2px;
  width: 29px;
  border: 1px solid hsl(0, 0%, 5%);
  height: 23px;
  border-radius: 4px;
  color: hsl(0, 0%, 85%);
  background-color: hsl(0, 0%, 10%);
  cursor: pointer;
  outline: none;
}

.mode {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
}

.equal-sign {
  background-color: hsl(120, 100%, 25%);
  width: 96px;
}

.toggle-button {
  border: none;
  background-color: hsl(0, 0%, 20%);
  cursor: pointer;
  outline: none;
  font-size: 1rem;
  color: #fff;
  text-shadow: -1px -1px 0 rgba(0, 0, 0, 0.35);
}

.calculator p {
  margin-top: 0;
  font-size: 14px;
}

button::-moz-focus-inner {
  border-color: transparent;
}
</style>
