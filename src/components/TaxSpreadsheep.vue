<template>
  <div v-show="isShow">
    <div id="spreadsheet" class="spreadsheet jxs"></div>
    <el-dialog
      title="快速选择表格"
      :visible.sync="dialogVisible"
      width="30%">
      <el-input
      type="text"
      v-model="sheetNameFilter"
      placeholder="过滤表名"
      style="margin-bottom: 20px"></el-input>
      <el-button
      v-for="item in filterSheet"
      :key="item"
      @click="jumpSheet(item)">{{item}}</el-button>
    </el-dialog>
  </div>
</template>

<script>

export default {
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },

    taxForm: {
      type: Object,
      required: true,
    },

    rptheadData: {
      default: () => ({}),
      required: false,
    },

    taxModule: {
      type: Object,
      required: true,
    },

    exactPeriodTimeRange: {
      type: Array,
      required: false,
    },
  },

  data() {
    return {
      $spreadsheet: null,
      declarationHistrpt: null,
      tagDb: ['getPreVal', 'getSimpleVal'],
      sheetNames: [],
      dialogVisible: false,
      sheetNameFilter: '',
    };
  },

  computed: {
    filterSheet() {
      if (this.sheetNameFilter !== '') {
        const arr = this.sheetNames.filter((item) => item.indexOf(this.sheetNameFilter) > -1);
        return arr;
      }
      return this.sheetNames;
    },
  },

  watch: {
    isShow(newValue) {
      if (newValue) {
        this.init();
      }
    },
  },

  methods: {
    async init() {
      const $spreadsheet = $('#spreadsheet');

      $spreadsheet.css({ width: '100%', height: `${this.$store.state.app.tableHeight - 35}px` });

      await this.$nextTick();

      if (!this.$spreadsheet) {
        // 第一次进入
        kendo.culture('zh-CN');
        $spreadsheet.kendoSpreadsheet({
          toolbar: false,
          excel: { fileName: this.taxModule.moduleName },
          change: this.onChange,
        });
        $('.k-spreadsheet-sheets-bar').prepend("<a class='k-button k-button-icon tms_querysheet' id='sheetQueryBtn'><span class='k-icon k-font-icon k-i-search'></span></a>");
        $('#sheetQueryBtn').click(() => {
          this.showSheetQuery();
        });
        this.$spreadsheet = $spreadsheet;

        // 汇算清缴不需请求改接口
        if (this.taxForm.taxName !== 5) {
          await this.getDeclarationHistrpt();
        }

        this.$spreadsheet.data('kendoSpreadsheet').fromJSON(this.taxModule.moduleContent, true);

        // 新增时才初始化基本信息，修改时已经存在于模板里面了
        if (!this.taxForm.declarationId) this.initCommonInfo();

        if (this.taxForm.taxName !== 5) this.initTag();

        this.$emit('initend', this.$spreadsheet);
      } else {
        // 新增时才初始化基本信息，修改时已经存在于模板里面了, 防止基础信息修改
        if (!this.taxForm.declarationId) this.initCommonInfo();

        if (this.taxForm.taxName !== 5) this.initTag();

        this.$emit('initend', this.$spreadsheet);
      }
    },

    // 获取历史申报数据
    getDeclarationHistrpt() {
      const url = `/rest/companytax/merge/vatdeclare/declarationHistrpt/v1.0/list/${this.taxForm.taxName}/${this.taxForm.declarationPeriod}`;
      return this.$http.get(url).then((rsp) => { this.declarationHistrpt = rsp.data; return rsp; });
    },

    initCommonInfo() {
      if (this.taxForm.taxName === 1) {
        // 对于非企业所得税的模板
        this.initVATCommonInfo();
      } else if (this.taxForm.taxName === 2) {
        // 对于企业所得税的两个模板
        this.initBITCommonInfo();
      } else if (this.taxForm.taxName === 5) {
        // 汇算清缴
        this.initSETCommonInfo();
      }
    },

    showSheetQuery() {
      this.sheetNames = this.$spreadsheet.data('kendoSpreadsheet').sheets().map((sheet) => sheet.name());
      this.dialogVisible = true;
    },

    initSETCommonInfo() {
      const kendoSpreadsheet = this.$spreadsheet.data('kendoSpreadsheet');
      const fm = kendoSpreadsheet.sheetByName('封面');
      const gj = kendoSpreadsheet.sheetByName('研发项目可加计扣除研究开发费用情况归集表');
      const hz = kendoSpreadsheet.sheetByName('“研发支出”辅助账汇总表');
      const A109010 = kendoSpreadsheet.sheetByName('A109010企业所得税汇总纳税分支机构所得税分配表');
      const enterprise = kendoSpreadsheet.sheetByName('企业所得税年度纳税申报表填报表单');
      const { taxForm } = this;

      const periodRange = `税款所属期间：${this.exactPeriodTimeRange[0].Format('yyyy年MM月dd日')}至${this.exactPeriodTimeRange[1].Format('yyyy年MM月dd日')}`;

      fm.batch(() => {
        fm.range('A7:I7').value(periodRange);
        fm.range('A10:I10').value(`纳税人识别号(统一社会信用代码)：${taxForm.creditCode}`);
        fm.range('A12:I12').value(`纳税人名称：${taxForm.companyName}`);
      });

      gj.batch(() => {
        gj.range('B3').value(taxForm.companyName);
        gj.range('D3').value(taxForm.creditCode);
        gj.range('A4:B4').value(this.exactPeriodTimeRange[0].Format('yyyy年度'));
      });

      hz.batch(() => {
        hz.range('A3:I3').value(`纳税人识别号：${taxForm.creditCode}`);
        hz.range('L3:T3').value(`纳税人名称（盖章）：${taxForm.companyName}`);
        hz.range('U3:V3').value(this.exactPeriodTimeRange[0].Format('yyyy年度'));
      });

      A109010.batch(() => {
        A109010.range('A3:H3').value(periodRange);
        A109010.range('C4:D4').value(taxForm.companyName);
        A109010.range('C5').value(taxForm.creditCode);
      });

      enterprise.batch(() => {
        enterprise.range('G2').value(taxForm.smallEnterprise);
      });
    },

    initBITCommonInfo() {
      const sheet0 = this.$spreadsheet.data('kendoSpreadsheet').sheets()[0];
      const
        f = this.taxForm;
      const reg = /(\d*)-(\d*)-(\d*)-(\d*)-(\d*)-(\d*)/gi;
      const periodRangeStr = `${this.taxForm.periodTimeFrom}-${this.taxForm.periodTimeTo}`;
      const periodRange = periodRangeStr.replace(reg, (match, p1, p2, p3, p4, p5, p6) => `税款所属时间：自${p1}年${p2}月${p3}日至${p4}年${p5}月${p6}日`);

      sheet0.batch(() => {
        sheet0.range('A2:J2').value(periodRange);
        sheet0.range('A3:J3').value(`纳税人识别号(统一社会信用代码)：${f.creditCode}`);
        sheet0.range('A4:G4').value(`纳税人名称：${f.companyName}`);
      });

      // A类的第五个表也需要设置表头
      if (f.citLevyType === 2) {
        const sheet4 = this.$spreadsheet.data('kendoSpreadsheet').sheets()[4];
        sheet4.batch(() => {
          sheet4.range('B3:L3').value(periodRange);
          sheet4.range('D4:J4').value(f.companyName);
          sheet4.range('G5:J5').value(f.creditCode);
        });
      }
    },

    initVATCommonInfo() {
      const now = new Date();
      const sheets = this.$spreadsheet.data('kendoSpreadsheet').sheets();
      const sheet0 = sheets[0];
      const sheet1 = sheets[1];

      sheet0.batch(() => {
        const
          reg = /(\d*)-(\d*)-(\d*)-(\d*)-(\d*)-(\d*)/gi;
        const periodRangeStr = `${this.taxForm.periodTimeFrom}-${this.taxForm.periodTimeTo}`;
        const periodRange = periodRangeStr.replace(reg, (match, p1, p2, p3, p4, p5, p6) => `税款所属时间：自${p1}年${p2}月${p3}日至${p4}年${p5}月${p6}日`);
        const fullAddress = `${this.taxForm.provinceName} ${this.taxForm.cityName} ${this.taxForm.districtName} ${this.taxForm.registAddr}`;

        if (this.taxForm.vatType === 1) {
          // 一般纳税人
          // 设置每个表格的所属期
          ['A4:Q4', 'A4:R4', 'A4:G4', 'A3:H3', 'A3:G3', 'A3:F3', 'A2:H2', 'A3:Q3'].forEach((coordinate, index) => {
            if (coordinate) sheets[index].range(coordinate).value(periodRange);
          });

          ['E6:M6', 'A5:C5', 'B5:C5', 'A4:F4', 'B4', 'A4:C4', 'B3', 'B4'].forEach((coordinate, index) => {
            let companyName = '';
            if ([1, 3, 5].indexOf(index) !== -1) {
              companyName = `纳税人名称：（公章）${this.taxForm.companyName}`;
            } else if (index === 0) {
              companyName = `${this.taxForm.companyName}（公章）`;
            } else {
              companyName = this.taxForm.companyName;
            }
            if (coordinate) sheets[index].range(coordinate).value(companyName);
          });

          sheet0.range('R4:AA4').value(`填表日期：${now.Format('yyyy年MM月dd日')}`);

          this.taxForm.creditCode.split('').slice(0, 20).forEach((item, index) => {
            sheet0.range(`${String.fromCharCode(69 + index)}5`).value(item);
          });

          sheet0.range('Y6:AI6').value(this.taxForm.businessAddr);
          sheet0.range('S6:U6').value(this.taxForm.legalPerson);
          sheet0.range('E6:M6').value(`${this.taxForm.companyName}（公章）`);
          sheet0.range('E7:P7').value(`${this.taxForm.bankName} ${this.taxForm.bankAccount}`);
          sheet0.range('AO7:AQ7').value(this.taxForm.telephone);
          sheet0.range('AJ5').value(this.taxForm.industryName);
          sheet0.range('V7:AL7').value(this.taxForm.registTypeName);
          sheet0.range('AM6:AQ6').value(fullAddress);
        } else {
          // 小规模纳税人
          sheet0.range('A4:H4').value(`纳税人识别号：${this.taxForm.creditCode}`);
          sheet0.range('B5').value(this.taxForm.companyName);
          sheet1.range('B4').value(this.taxForm.companyName);
          sheet0.range('A6:E6').value(periodRange);
          sheet1.range('A3').value(periodRange);
          sheet0.range('F6:H6').value(`填表日期：${now.Format('yyyy年MM月dd日')}`);
          sheet1.range('D3').value(`填表日期：${now.Format('yyyy年MM月dd日')}`);
        }
      });
    },

    initTag() {
      let tmsCellFlag;
      let keyValue;
      const kendoJosn = this.$spreadsheet.data('kendoSpreadsheet').toJSON();

      // 循环表 循环行 循环格
      kendoJosn.sheets.forEach((sheet) => {
        sheet.rows.forEach((row) => {
          row.cells.forEach((cell) => {
            tmsCellFlag = cell.tmsCellFlag;
            // 存在tmsCellFlag的格可能需要设置值
            if (tmsCellFlag) {
              this.tagDb.some((keyName) => {
              // 遍历需要设置值的关键字数组，当tmsCellFlag中存在该关键字进行赋值
                keyValue = tmsCellFlag[keyName];
                if (keyValue) {
                  if (Reflect.has(this.rptheadData, keyValue)) {
                    cell.value = this.rptheadData[keyValue].user
                      ? Number(this.rptheadData[keyValue].user) : 0;
                    return true;// 跳出遍历
                  }
                  if (Reflect.has(this.declarationHistrpt, keyValue) && this.declarationHistrpt[keyValue] !== '') {
                    cell.value = this.declarationHistrpt[keyValue]
                      ? Number(this.declarationHistrpt[keyValue]) : 0;
                    return true;// 跳出遍历
                  }
                }
                return false;
              });
            }
          });
        });
      });

      this.$spreadsheet.data('kendoSpreadsheet').fromJSON(kendoJosn);
    },
    jumpSheet(sheetName) {
      this.$spreadsheet.data('kendoSpreadsheet').activeSheet(this.$spreadsheet.data('kendoSpreadsheet').sheetByName(sheetName));
      this.dialogVisible = false;
    },
    onChange(arg) {
      const tmsCellFlag = arg.range.tmsCellFlag(); let
        keyValue;
      console.info('tmsCellFlag: ', tmsCellFlag, '新值：', arg.range.value());

      if (tmsCellFlag) {
        this.tagDb.some((keyName) => {
          // 查看记录里面是否含有该字段
          keyValue = tmsCellFlag[keyName];
          if (keyValue && Reflect.has(this.rptheadData, keyValue)) {
            this.rptheadData[keyValue].user = arg.range.value();
            return true;// 跳出循环
          }
          return false;
        });
      }
      this.$emit('change', arg);
    },
  },
};
</script>
<style>
  #sheetQueryBtn{
    margin-left: 5px;
    margin-top: 5px;
    position: absolute;
  }
  .spreadsheet.jxs .k-spreadsheet-sheets-items {
    font-size: 12px;
    position: relative;
    left: 30px;
    width: calc(100% - 30px);
    top: 2px;
}
</style>
