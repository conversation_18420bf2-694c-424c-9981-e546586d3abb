<template>
  <div class="page-tabs">
    <slot>
      <div v-for="tab in tabs"
      :key="tab.value"
      :class="{'is-active': tab.value === value}"
      @click="onClick(tab.value)"
      >
        {{ tab.label }}
      </div>
    </slot>
  </div>
</template>
<script>
export default {
  props: {
    tabs: {
      type: Array,
      default: () => [],
    },
    value: {
      type: [String, Number, Boolean],
    },
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  data() {
    return {};
  },
  methods: {
    onClick(val) {
      this.$emit('change', val);
    },
  },
};
</script>
<style lang="scss" scoped>
.page-tabs{
    background: $layout-backgroundcolor;
    margin: 0 -16px;
    padding-bottom: 14px;
    display: flex;
    overflow: hidden;
    margin-top: -14px;
    padding-top: 14px;
  &> div{
    font-size: 14px;
    line-height: 22px;
    padding-right: 12px;
    padding-left: 12px;
    color: #9CA1A5;
    cursor: pointer;
    position: relative;
    &:first-of-type {
        &::before {
          background: transparent;
        }
      }
      &:last-of-type {
        padding-right: 15px;
      }
      &::before {
        content: '';
        background: #9CA1A7;
        display: inline-block;
        width: 1px;
        height: 14px;
        position: absolute;
        left: 0;
        top: 4px;
      }
  }
  &>.is-active{
    font-weight: 500;
    color: #222222;
    position: relative;
    &::after{
      content: '';
      width: 30px;
      height: 3px;
      background: $color-theme-base;
      border-radius: 2px;
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%)
    }
  }
}
</style>
