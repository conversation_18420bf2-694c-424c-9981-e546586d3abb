<template>
  <div ref="imageViewRef" v-show="localVisible" class="ImageView">
    <div class="ImageView__viewport" @click="onClickViewport">
      <img
        :src="currentSrc"
        :style="currentStyle"
        class="ImageView__img"
        @drag="preventDrag"
        @click.prevent.stop="onClickImage" />
    </div>

    <div v-if="!isNavPrevDisabled || !isNavNextDisabled" class="ImageView__nav">
      <button :disabled="isNavPrevDisabled" class="ImageView__nav-prev el-icon-arrow-left" @click="showPrevImage"></button>
      <button :disabled="isNavNextDisabled" class="ImageView__nav-next el-icon-arrow-right" @click="showNextImage"></button>
    </div>

    <div class="ImageView__actions">
      <i title="左旋转90°" class="ImageView__icon-button el-icon-refresh-left" @click="rotateLeft" />
      <i title="右旋转90°" class="ImageView__icon-button el-icon-refresh-right" @click="rotateRight" />
      <i title="缩小" class="ImageView__icon-button el-icon-zoom-out" @click="zoomOut" />
      <i title="放大" class="ImageView__icon-button el-icon-zoom-in" @click="zoomIn" />
      <i title="还原" class="ImageView__icon-button el-icon-c-scale-to-original" @click="resetCurrentStyle" />
      <i title="关闭" class="ImageView__icon-button el-icon-close" @click="close"></i>
    </div>
  </div>
</template>

<script>
import { computed, defineComponent, onMounted, onUnmounted, PropType, ref, watch } from 'vue'

export default defineComponent({
  name: 'ImageView',

  props: {
    /**
     * 图片查看器是否展示
     */
    visible: {
      type: Boolean,
      default: false,
    },

    /**
     * 图片 (url) 数组
     */
    value: {
      type: Array,
      default: '',
    },

    /**
     * 点击图片之外是否关闭
     */
    closeOnClickOutside: {
      type: Boolean,
      default: false,
    },

    /**
     * z-index
     */
    zIndex: {
      type: Number,
      default: 999999,
    },

    appendToBody: {
      type: Boolean,
      default: false,
    },
  },

  setup(props, { emit }) {
    const imageViewRef = ref(null)

    // 插入到 body
    let _$parent
    const appendToBody = () => {
      const viewRoot = imageViewRef.value
      if (viewRoot?.parentElement && viewRoot.parentElement !== document.body) {
        _$parent = viewRoot.parentElement
        document.body.appendChild(viewRoot)
      }
    }
    // 从 body 中移动回原来的 parent
    const appendToParent = () => {
      const viewRoot = imageViewRef.value
      if (_$parent && viewRoot?.parentElement === document.body) {
        _$parent.appendChild(imageViewRef.value)
      }
    }
    onMounted(() => {
      if (props.appendToBody) {
        appendToBody()        
      }
    })
    onUnmounted(() => {
      appendToParent()
    })
    watch(() => props.appendToBody, (value) => {
      if (value) {
        appendToBody()
      } else {
        appendToParent()
      }
    })

    const localVisible = computed({
      get() { 
        return props.visible
      }, 
      set(value) {
        emit('update:visible', value)
      }
    })
    const open = () => {
      localVisible.value = true
    }
    const close = () => {
      localVisible.value = false
    }

    const imageCount = computed(() => {
      return props.value?.length ?? 0
    })

    const imgStyles = ref([])
    const currentIndex = ref(0)
    watch(() => props.value, () => {
      imgStyles.value = (props.value ?? []).map(() => {
        return {
          rotate: 0,
          zoom: 1,
        }
      })
      currentIndex.value = 0
    }, { immediate: true })

    const currentSrc = computed(() => {
      return props.value?.[currentIndex.value] ?? ''
    })
    const currentStyle = computed(() => {
      const s = imgStyles.value[currentIndex.value] ?? { rotate: 0, zoom: 1 }
      return {
        position: 'relative',
        transition: 'all .16s',
        transform: `rotate(${s.rotate}deg) scale3d(${s.zoom}, ${s.zoom}, 1)`,
      }
    })

    // 当前旋转的角度
    const rotateLeft = () => {
      const imgStyle = imgStyles.value[currentIndex.value]
      imgStyle.rotate -= 90
    }
    const rotateRight = () => {
      const imgStyle = imgStyles.value[currentIndex.value]
      imgStyle.rotate += 90
    }

    const zoomIn = () => {
      const imgStyle = imgStyles.value[currentIndex.value]
      let value = imgStyle.zoom + 0.2
      if (value > 10) value = 10
      imgStyle.zoom = value
    }
    const zoomOut = () => {
      const imgStyle = imgStyles.value[currentIndex.value]
      let value = imgStyle.zoom - 0.2
      if (value < 0.2) value = 0.2
      imgStyle.zoom = value
    }

    const resetCurrentStyle = () => {
      const imgStyle = imgStyles.value[currentIndex.value]
      imgStyle.rotate = 0
      imgStyle.zoom = 1
    }

    const onClickImage = () => {
      //
    }

    const onClickViewport = () => {
      if (props.closeOnClickOutside) {
        localVisible.value = false
      }
    }

    const preventDrag = (e) => {
      e.preventDefault()
    }

    const isNavPrevDisabled = computed(() => {
      if (!imageCount) return true
      return currentIndex.value <= 0
    })
    const isNavNextDisabled = computed(() => {
      if (!imageCount) return true
      return currentIndex.value >= imageCount.value - 1
    })
    const showPrevImage = () => {
      if (isNavPrevDisabled.value) return
      if (currentIndex.value > 0) {
        currentIndex.value -= 1
      }
    }
    const showNextImage = () => {
      if (isNavNextDisabled.value) return
      if (currentIndex.value < imageCount.value - 1) {
        currentIndex.value += 1
      }
    }

    return {
      imageViewRef,
      localVisible,
      open,
      close,

      imageCount,

      currentSrc,
      currentIndex,
      currentStyle,

      onClickImage,
      onClickViewport,
      preventDrag,

      isNavPrevDisabled,
      isNavNextDisabled,
      showPrevImage,
      showNextImage,

      resetCurrentStyle,
      rotateLeft,
      rotateRight,
      zoomIn,
      zoomOut,
    }
  },
})
</script>

<style lang="scss" scoped>
.ImageView {
  box-sizing: border-box;
  overflow: hidden;
  position: fixed;
  z-index: 999999;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,.5);
}
.ImageView__viewport {
  overflow: auto;
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 0;
  text-align: center;
}
.ImageView__img {
  position: relative;
  z-index: 1;
  margin: 0;
  max-width: 100%;
  max-height: 100%;
}
.ImageView__nav {
  position: absolute;
  z-index: 2;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  height: 0;
  margin: auto;
}
.ImageView__nav-prev,
.ImageView__nav-next {
  overflow: hidden;
  position: absolute;
  top: 0;
  right: auto;
  bottom: 0;
  left: auto;
  width: 40px;
  height: 40px;
  margin: auto;
  line-height: 40px;
  text-align: center;
  font-size: 20px;
  border: 0 none;
  border-radius: 50%;
  background-color: rgba(0,0,0,.1);
  color: rgba(255,255,255,.5);
  cursor: pointer;

  &:hover {
    background-color: rgba(0,0,0,.5);
    color: rgba(255,255,255,1);
  }
  &[disabled] {
    display: none;
  }
}
.ImageView__nav-prev {
  left: 24px;
}
.ImageView__nav-next {
  right: 24px;
}

.ImageView__actions {
  box-sizing: border-box;
  position: absolute;
  z-index: 2;
  top: 0;
  right: 0;
  left: 0;
  width: 100%;
  padding: 8px 16px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background-color: rgba(0,0,0,.1);
}
.ImageView__icon-button {
  display: block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  margin: 0 8px;
  font-size: 24px;
  text-shadow: 0px 0px 3px rgba(0,0,0,.5);
  color: rgba(255,255,255,.5);
  cursor: pointer;

  &:hover {
    color: rgba(255,255,255,1);
  }
}

</style>
