<template>
  <div>
    <span class="editCol" @click="editCol" v-if="!edit">
      <slot></slot>
    </span>
    <el-date-picker
      v-if="edit"
      v-edit-focus="edit"
      v-model="rowdata[myKey]"
      type="date"
      v-bind="$attrs"
      value-format="yyyy-MM-dd"
      @blur="doneEdit"
      @change="$emit('change')"
      placeholder="选择日期"
    ></el-date-picker>
  </div>
</template>
<script>
export default {
  props: {
    rowdata: {
      type: Object,
      default: () => {},
    },
    myKey: String,
  },
  data() {
    return {
      edit: false,
    };
  },
  methods: {
    editCol() {
      this.edit = true;
    },
    doneEdit() {
      this.edit = false;
    },
  },
  directives: {
    'edit-focus': (el, value) => {
      console.log(el, value);
      if (value.value) {
        $(el)
          .children('input')
          .focus();
      }
    },
  },
};
</script>
