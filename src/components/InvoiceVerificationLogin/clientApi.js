/* eslint-disable */
const TIMEOUT = 12000;
function getApi(e, t, o) {
  let n = null;
  return (
    $.ajax({
      url: e,
      type: 'POST',
      async: !1,
      timeout: TIMEOUT,
      contentType: 'application/x-www-form-urlencoded;charset=UTF-8',
      data: t,
      complete(e) {
        e.status != 200 && (n = null);
      },
      success(e) {
        (n = e),
        n.code != 0
            && o == 1
            && jAlert_error(
              `<div id='popup_message'>${
                n.msg
              }  (错误代码：${
                n.code
              })</div>`,
              '提示',
            );
      },
    }),
    n
  );
}
export function getVersionApi(e) {
  const t = 'https://127.0.0.1:28000/api/getVersion';
  const o = { crosFlag: 0 };
  return getApi(t, o, e);
}
export function verifyPinApi(e, t, o) {
  e = encodeURIComponent(e);
  let n;
  const r = 'https://127.0.0.1:28000/api/verifyPin';
  return (
    (n = o == 0
      ? { password: e, dwProvType: 1 }
      : {
        password: e,
        dwProvType: 2050,
        strContainer: '//SM2/SM2CONTAINER0002',
      }),
    getApi(r, n, t)
  );
}
export function clientHelloApi(e, t) {
  let o;
  const n = 'https://127.0.0.1:28000/api/clientHello';
  return (
    (o = t == 0
      ? { authType: 0, dwProvType: 1 }
      : {
        authType: 0,
        dwProvType: 2050,
        strContainer: '//SM2/SM2CONTAINER0002',
      }),
    getApi(n, o, e)
  );
}
export function clientAuthApi(e, t, o, n) {
  t = encodeURIComponent(t);
  let r;
  const i = 'https://127.0.0.1:28000/api/clientAuth';
  return (
    (r = n == 0
      ? { password: t, serverHello: e, dwProvType: 1 }
      : {
        password: t,
        serverHello: e,
        dwProvType: 2050,
        strContainer: '//SM2/SM2CONTAINER0002',
      }),
    getApi(i, r, o)
  );
}
export function readCertInfoApi(e, t, o) {
  let n;
  const r = 'https://127.0.0.1:28000/api/readCertInfo';
  return (
    (n = o == 0
      ? { certInfoNo: e, dwProvType: 1 }
      : {
        certInfoNo: e,
        dwProvType: 2050,
        strContainer: '//SM2/SM2CONTAINER0002',
      }),
    getApi(r, n, t)
  );
}
export function jianceReadCertInfoApi(e) {
  let t;
  let o = null;
  const n = 'https://127.0.0.1:28000/api/readCertInfo';
  return (
    (t = e == 0
      ? { certInfoNo: 71, dwProvType: 1 }
      : {
        certInfoNo: 71,
        dwProvType: 2050,
        strContainer: '//SM2/SM2CONTAINER0002',
      }),
    $.ajax({
      url: n,
      type: 'POST',
      async: !1,
      timeout: TIMEOUT,
      contentType: 'application/x-www-form-urlencoded;charset=UTF-8',
      data: t,
      complete(e) {
        e.status != 200 && (o = null);
      },
      success(e) {
        (o = e),
        o.code != 0
            && o.code != 167
            && jAlert_error(
              `<div id='popup_message'>${
                o.msg
              }  (错误代码：${
                o.code
              })</div>`,
              '提示',
            );
      },
    }),
    o
  );
}
export function signDataApi(e, t, o, n) {
  t = encodeURIComponent(t);
  let r;
  const i = 'https://127.0.0.1:28000/api/signData';
  return (
    (r = n == 0
      ? {
        password: t,
        data: e,
        signAlgId: 'SHA1withRSA',
        dwFlag: '0x400000',
        dwProvType: 1,
      }
      : {
        password: t,
        data: e,
        signAlgId: 'GBECSM3',
        dwFlag: '0x400000',
        dwProvType: 2050,
        strContainer: '//SM2/SM2CONTAINER0002',
      }),
    getApi(i, r, o)
  );
}
export function getAlg() {
  const e = getCookie('alg');
  return e != ''
    ? (setCookie('alg', e, seconds), e)
    : void jAlert(
      "<div id='popup_message'>会话失效，请重新登录！</div>",
      '提示',
      (e) => {
        e && (window.location.href = getDomainName());
      },
    );
}
