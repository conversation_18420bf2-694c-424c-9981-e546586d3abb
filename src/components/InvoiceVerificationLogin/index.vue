<template>
  <div>
    <el-input v-model="password"></el-input>
  </div>
</template>
<script>
import Axios from 'axios';
import {
  getVersionApi, getAlg, verifyPinApi, readCertInfoApi, clientHelloApi, clientAuthApi,
} from './clientApi';

const TIMEOUT = 12000;
const IP = 'http://fpdk.guangdong.chinatax.gov.cn/';
let cryptType = 0;
let alg = 0;
let cert = '';
const apiversion = '1.0';
const ymbb = '3.1.01';
export default {
  data() {
    return {
      password: '',
      cryptType: 0,
    };
  },
  methods: {
    login() {
      if (this.password === '') {
        this.$message.error('请输入金税盘、税控盘或税务Ukey证书密码!');
        return;
      }
      let clientHello = '';
      if (cryptType === 0) {
        const obj = clientHelloApi(false, alg);
        if (obj.code === 0) {
          clientHello = obj.clientHello;
        } else {
          this.$message.error(`${obj.msg}  (错误代码：${obj.code})`);
        }
      }
      const param1 = {
        type: 'CLIENT-HELLO', clientHello, alg, ymbb,
      };

      const login1Response = this.firstLogin();
      const { key1 } = login1Response;
      if (key1 === '00') {
        if (login1Response.key2 === '生成ServerHello--密码算法ID错误') {
          this.$message.error('检测到您未安装税务安全证书应用客户端，请下载并安装!');
          return;
        }
        this.$message.error(`服务器调用身份认证系统失败！身份认证系统返回信息为：${login1Response.key2} 正在重试......`);
      } else if (key1 === '01') {
        const serverPacket = login1Response.key2;
        const serverRandom = login1Response.key3;
        let clientAuthCode = '';
        if (cryptType === 0) {
          const obj = clientAuthApi(serverPacket, this.password, false, alg);

          if (obj.code === 0) {
            clientAuthCode = obj.clientAuth;
          } else {
            this.$message.error(`${obj.msg}  (错误代码：${obj.code})`);
          }
        }
        const cert = this.getThisCert();
        if (!cert) {
          return;
        }
        const querymmRes = this.querymm();
        const { page } = data;
        const { ts } = data;
        let publickey = '';
        const currdate = new Date().getTime();
        if (page != '') {
          publickey = $.checkTaxno(cert, ts, '', page, serverRandom);
        }
        let param2;
        if (param1.mmtype === '1') {
          param2 = {
            type: 'CLIENT-AUTH',
            clientAuthCode,
            serverRandom,
            alg,
            password: this.password,
            cert,
            ymbb,
            ts,
            publickey,
            mmtype: '1',
            currdate,
          };
        } else if (param1.mmtype === '2') {
          param2 = {
            type: 'CLIENT-AUTH',
            clientAuthCode,
            serverRandom,
            alg,
            password: this.password,
            cert,
            ymbb,
            ts,
            publickey,
            mmtype: '2',
            answer: param1.answer,
            currdate,
          };
        } else {
          param2 = {
            type: 'CLIENT-AUTH',
            clientAuthCode,
            serverRandom,
            alg,
            password: this.password,
            ts,
            publickey,
            cert,
            ymbb,
            currdate,
          };
        }
      }
    },
    async validateLogin() {
      let count = 1;
      const vs = getVersionApi();
      if (vs === null) {
        this.$message.error('请安装最新版驱动');
        return false;
      }
      cryptType = 0;
      if (vs.version !== apiversion) {
        this.$message.error(`${count}、检测到您安装的税务安全证书应用客户端不是最新版本，请下载安装最新版本!`);
        count += 1;
        return false;
      }
      let obj = verifyPinApi(this.password, false, 1);
      if (obj.code !== 0) {
        obj = verifyPinApi(this.password, false, 0);
        if (obj.code === 0) {
          alg = 0;
        }
      } else if (obj.code === 0) {
        alg = 1;
      }
      if (obj.code === 0) {
        this.$message.success(`${count}、客户端证书密码：正确`);
        count += 1;

        obj = readCertInfoApi(71, false, alg);
        if (obj != null && obj.code === 0) {
          cert = obj.certInfo;
        }
      } else {
        this.$message.error(`${count}、${obj.msg} (错误代码：${obj.code})`);
        count += 1;
        return false;
      }
      const strRegx = /^[0-9a-zA-Z]+$/;
      if (cert === '') {
        this.$message.error(`${count}、读取证书信息失败，未获取到合法的纳税人信息,请重新提交请求或检查金税盘、税控盘或税务Ukey是否插入！`);
        count += 1;
        return false;
      }
      if (!strRegx.test(cert)) {
        this.$message.error(`${count}、读取到的纳税人信息（纳税人识别号：${cert}）不合法！请重试！`);
        count += 1;
        return false;
      }
      this.$message.error(`${count}、读取到的纳税人识别号：${cert}`);
      return true;
    },
    firstLogin(param1) {
      let data = {};
      $.ajax({
        type: 'post',
        url: `${IP}/login.do`,
        data: param1,
        timeout: TIMEOUT,
        dataType: 'jsonp',
        contentType: 'application/x-www-form-urlencoded;charset=utf-8',
        jsonp: 'callback',
        success(jsonData) {
          data = jsonData;
        },
      });
      return data;
    },
    querymm() {
      return {};
    },
    secondLogin() {
      return {};
    },
    getThisCert() {
      let nsrsbh = '';
      if (cryptType === 0) {
        const obj = readCertInfoApi(71, false, alg);
        if (obj.code === 0) {
          nsrsbh = obj.certInfo;
        } else {
          this.$message.error(`${obj.msg}  (错误代码：${obj.code})`);
        }
      }
      return nsrsbh;
    },
  },
};
</script>
