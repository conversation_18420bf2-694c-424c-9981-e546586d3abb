<!--
 * @LastEditors: 晓荣
 * @Author: 肖泽涛
 * @Description: 凭证预览及生成凭证组件
 * @Date: 2019-04-04 10:26:53
 * @LastEditTime: 2022-01-06 11:32:10
 * @event
 * success > 凭证生成成功事件
 * hide > 组件隐藏事件
 * createVoucher > 调用生成凭证事件

  待处理 凭证预览 要统一通用组件 (不可编辑：)
  /finance/invoiceMixins/VoucherCreated.vue

  (可编辑)
  /finance/endCheckout/components/VoucherCreated.vue
  /finance/documentManagement/components/VoucherCreated.vue
-->
<template>
  <el-drawer :visible.sync="cardVisible" title="生成凭证预览" append-to-body size="80%" custom-class="voucher-preview">
    <p class="summary flex-fixed">
      本次处理{{ businessTypeStr }}记录共{{ voucherData.total || 0 }}条，
      预计生成凭证记录共{{ voucherData.voucherTotal || 0 }}条。
    </p>
    <el-table :data="tableData" ref="table" border class="flex-fluid" :span-method="spanMethod" :row-style="totalRowStyle" height="100%">
      <el-table-column
        prop="voucherTime"
        label="凭证日期"
        :formatter="formatterLeaf"
        :width="$store.getters.columnSize.Buildin.Date"
      ></el-table-column>
      <el-table-column
        prop="voucherCode"
        label="凭证号"
        :width="$store.getters.columnSize.Buildin.VoucherCode"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.isLeaf">
            {{ scope.row.voucherCode }}
            <span v-if="scope.row.unBalance" class="unBalance">（不平）</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="voucherAbstract" :min-width="200" label="摘要"></el-table-column>
      <af-table-column prop="subjectFullName" label="科目" :min-width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.subjectFullName | formatSubject }}</span>
        </template>
      </af-table-column>
      <el-table-column prop="tagIds" label="标签" :min-width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>
            <tag-name :tagIds="scope.row.tagIds" />
          </span>
        </template>
      </el-table-column>
        <el-table-column
        v-if="hasCurrency"
        prop="currency"
        label="币别"
        width="150"
        class-name="currency noTooltip">
          <template slot-scope="{ row }">
            <div v-if="row.currencyCode && row.currencyCode !== functionalCurrency">
              <p>{{ row.currencyCode }}：{{ row.exchangeRate | warpFormatDecimalNum(6) }}</p>
              <p>原币：{{ row.originalAmount | moneyFilter }}</p>
            </div>
          </template>
        </el-table-column>
      <template v-if="hasUnit">
        <el-table-column prop="qty" label="数量" :width="$store.getters.columnSize.Buildin.Number - 20">
          <template slot-scope="{row}">{{ row.qty === 0 ? '' : formatDecimalNum(row.qty) }}</template>
        </el-table-column>
        <el-table-column prop="saleunitName" label="单位" :width="$store.getters.columnSize.Buildin.Unit"></el-table-column>
        <el-table-column
          prop="notaxActPrice"
          label="单价"
          align="right"
          :width="$store.getters.columnSize.Buildin.Money - 20"
          class-name="table_font_black"
        >
          <template
            slot-scope="{row}"
          >{{ row.notaxActPrice === 0 ? '' : formatMoney(row.notaxActPrice) }}</template>
        </el-table-column>
      </template>
      <el-table-column
        prop="debitAmount"
        label="借方金额"
        align="right"
        :width="$store.getters.columnSize.Buildin.Money"
      >
        <template
          slot-scope="scope"
        >{{ scope.row.isTotal ? scope.row.totalDebitAmount : (scope.row.voucherDirection === 1  ? scope.row.subjectAmount : "") | moneyFilter }}</template>
      </el-table-column>
      <el-table-column
        prop="creditAmount"
        label="贷方金额"
        align="right"
        :width="$store.getters.columnSize.Buildin.Money"
      >
        <template
          slot-scope="scope"
        >{{ scope.row.isTotal ? scope.row.totalCreditAmount : (scope.row.voucherDirection === 2 || scope.row.isTotal ? scope.row.subjectAmount : "") | moneyFilter }}</template>
      </el-table-column>
    </el-table>
    <div class="action_bar flex-fixed">
      <el-button type="primary" @click.stop="handleConfirm">确认生成</el-button>
      <el-button v-if="showResetRule" @click="cardVisible = false">重新选择生成规则</el-button>
      <el-button @click="closePreDialog">取 消</el-button>
    </div>
  </el-drawer>
</template>

<script>
import { formatMoney, formatDecimalNum } from '@/assets/Utils';

export default {
  props: {
    // 控制组件的显示
    visible: {
      type: Boolean,
      required: true,
    },
    // 生成的业务类型名字
    businessTypeStr: {
      type: String,
      required: true,
    },
    // 表格主体数据
    voucherData: {
      type: Object,
      required: true,
    },
    // 生成凭证前的提示语（可选）
    confirmMassage: {
      type: String,
    },
    // 是否显示重新选择生成规则按钮
    showResetRule: {
      type: Boolean,
      default: true,
    },
  },

  computed: {
    // 凭证数据结构 转换 预览表格数据结构
    tableData() {
      if (!this.voucherData.voucher || !this.voucherData.voucher.length) { return []; }
      return this.voucherData.voucher.reduce((acc, itemOuter) => {
        const amounts = [0, 0, 0];
        const mapData = itemOuter.items.map((itemInner) => {
          let { voucherTime } = itemOuter;
          const { voucherCode } = itemOuter;
          voucherTime = voucherTime && voucherTime.split(' ')[0];
          amounts[itemInner.voucherDirection] += itemInner.subjectAmount;
          const voucherAbstract = itemInner.voucherAbstract !== null ? itemInner.voucherAbstract : ''; // 数据有可能为null  所以需要转成‘’
          return {
            ...itemInner,
            voucherTime,
            voucherCode,
            isLeaf: true,
            voucherAbstract,
            rowspan: 0,
          };
        });
        if (mapData.length) {
          const firstData = mapData[0];
          firstData.isLeaf = false;
          firstData.unBalance = amounts[1].toFixed(2) !== amounts[2].toFixed(2);
          firstData.rowspan = mapData.length;

          const totalDebitAmount = mapData.reduce((prevValue, item) => {
            if (item.voucherDirection === 1) {
              return prevValue.add(item.subjectAmount);
            }
            return prevValue;
          }, 0);
          const totalCreditAmount = mapData.reduce((prevValue, item) => {
            if (item.voucherDirection === 2) {
              return prevValue.add(item.subjectAmount);
            }
            return prevValue;
          }, 0);
          mapData.push({
            voucherCode: '合计',
            totalDebitAmount,
            totalCreditAmount,
            isTotal: true,
            rowspan: 1,
          });
        }
        return acc.concat(mapData);
      }, []);
    },
    // 判断凭证是否包括单位
    hasUnit() {
      return this.tableData.some((item) => item.saleunitName);
    },
    functionalCurrency() {
      return this.$store.state.user.companyInfo.functionalCurrency;
    },
    // 是否外币
    hasCurrency() {
      return this.tableData.some((item) => item.currencyCode && item.currencyCode !== this.functionalCurrency);
    },

    cardVisible: {
      get() {
        return this.visible;
      },
      set(isVisible) {
        this.$emit('update:visible', isVisible);
        if (!isVisible) {
          this.$emit('hide');
        }
      },
    },
  },

  methods: {
    formatMoney,
    formatDecimalNum,
    // 确认按钮回调
    handleConfirm() {
      if (this.confirmMassage) {
        this.$confirm(this.confirmMassage, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info',
        }).then(() => {
          this.$emit('createVoucher');
        });
      } else {
        this.$emit('createVoucher');
      }
      this.$emit('closePreDialog');
    },
    spanMethod({ row, column }) {
      const mergeCols = ['voucherTime', 'voucherCode'];
      if (mergeCols.includes(column.property)) {
        return {
        rowspan: row.rowspan,
        colspan: 1,
        };
      }
      return {
        rowspan: 1,
        colspan: 1,
      };
    },

    totalRowStyle({ row }) {
      return row.isTotal ? { background: '#fffadd !important', fontWeight: 'bold' } : {};
    },

    // 格式化函数
    formatterLeaf(row, column, cellValue) {
      return row.isLeaf ? '' : cellValue;
    },

    closePreDialog() {
      this.cardVisible = false;
      this.$emit('hide');
      this.$emit('closePreDialog');
    },
  },
};
</script>
<style lang="scss">
.voucher-preview {
  .el-drawer__body {
    padding: 20px;
    box-sizing: border-box;
    width: 100%;
    // height: 100%;
    height: calc(100% - 45px);
    display: flex;
    flex-flow: nowrap column;
  }

  .action_bar {
    margin-top: 20px;
    text-align: center;
    & > .el-checkbox,
    & > .toggle_area > .el-checkbox {
      margin-right: 10px;
    }
    & > .toggle_area {
      display: inline-block;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    & > .el-button {
      margin-bottom: 10px;
    }
  }
}
</style>
<style lang="scss" scoped>
.summary {
  margin-bottom: 20px;
}
.summary > span {
  font-weight: 700;
}
.unBalance {
  color: red;
}
</style>
