/* eslint-disable import/prefer-default-export */
import { reactive } from 'vue';
import { get } from 'lodash-es';

/**
 * @typedef {'ready' | 'uploading' | 'success' | 'fail'} UploadStatus
 */

/**
 * ElUpload 的文件类型
 * 选择一个文件后，Element 会将其包装成该形式
 * @typedef {Object} UploadFile
 * @property {string} name
 * @property {number} percentage
 * @property {File & { uid: number }} raw
 * @property {number} size
 * @property {UploadStatus} status
 * @property {string} type
 * @property {string} uid
 * @property {string} [url] - for listType = picture | picture-card
 */

/**
 * 原文件
 * 初始化上传组件的时候，可能需要设置一些原文件
 * 可以用来展示从服务器加载的已上传的文件
 * @typedef {Object} OrginFileItem
 * @property {string} [downloadUrl]
 * @property {string} [previewUrl]
 * @property {string} name
 * @property {string} [uid]
 * @property {boolean} [disabled]
 */

/**
 * @typedef {Object} State
 * @property {OrginFileItem[]} orginFiles
 * @property {UploadFile[]} newFiles
 */

/**
 * @param {OrginFileItem[]} orginFiles
 * @returns {{
 *   state: State,
 *   reset: (orginFiles: OrginFileItem[]) => void,
 *   addNewFile: (files: UploadFile[]) => void,
 *   removeFile: (file: UploadFile | OrginFileItem) => void
 * }}
 */
export function useFileManage(orginFiles) {
  const state = reactive({
    orginFiles: [...orginFiles],
    newFiles: [],
  });

  /**
   * 重设【新文件】列表
   * @param {UploadFile[]} files
   */
  const addNewFile = (files) => {
    state.newFiles = files;
  };

  /**
   * 从文件列表中移除一个文件（自动判断【新文件】列表、【原文件】列表）
   * @param {UploadFile | OrginFileItem} file
   */
  const removeFile = (file) => {
    if (get(file, 'raw')) {
      state.newFiles.splice(state.newFiles.indexOf(file), 1);
    } else {
      state.orginFiles.splice(state.orginFiles.indexOf(file), 1);
    }
  };

  /**
   * 重置到 hook 调用时的初始状态
   * @param {OrginFileItem[]} orginFiles
   */
  // eslint-disable-next-line no-shadow
  const reset = (orginFiles) => {
    state.orginFiles = [...orginFiles];
    state.newFiles = [];
  };

  return {
    state,
    reset,
    addNewFile,
    removeFile,
  };
}
