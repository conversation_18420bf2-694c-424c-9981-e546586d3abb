<template>
  <div
    class="FileManage"
    :class="{
      'FileManage--border': border,
      'FileManage--stripe': stripe,
      'FileManage--readonly': readonly,
      'FileManage--justify': itemLayout === 'justify',
      'FileManage--inline': itemLayout === 'inline',
    }">
    <div class="FileList">
      <!-- 已同步到服务器的文件列表项 -->
      <div v-for="item in store.state.orginFiles" :key="item.uid" class="FileItem">
        <div class="FileItem__name">
          <span class="FileItem__name__label">{{ item.name }}</span>
        </div>

        <div class="FileItem__actions">
          <el-button
            v-if="!oldFileReadOnly && !readonly && !item.disabled"
            type="text"
            size="small"
            icon="el-icon-delete"
            @click="handleRemoveOriginFile(item)">删除</el-button>
          <el-button type="text"
            size="small" v-if="downloadFn" @click="downloadFn(item)">
            <i class="el-icon-download"></i>下载
          </el-button>
          <el-button
            v-else-if="item.downloadUrl"
            type="text"
            size="small">
            <a
              :href="item.downloadUrl"
              target="_blank"
              style="color:rgb(57, 159, 251)"><i class="el-icon-download"></i>下载</a>
          </el-button>
        </div>
      </div>

      <!-- 未同步到服务器的本地文件列表项 -->
      <div v-for="item in store.state.newFiles" :key="item.uid" class="FileItem">
        <div class="FileItem__name">
          <span class="FileItem__name__label">{{ item.name }}</span>
          <span class="FileItem__name__flag">新文件</span>
          <!-- <el-tag class="FileItem__name__flag" size="small">新文件</el-tag> -->
        </div>

        <div class="FileItem__actions">
          <el-button
            v-if="!readonly"
            type="text"
            size="small"
            @click="store.removeFile(item)">删除</el-button>
        </div>
      </div>
    </div>

    <div v-show="!readonly" class="UploadArea">
      <el-upload
        ref="uploadRef"
        action="-"
        :accept="accept"
        :file-list="store.state.newFiles"
        :on-change="onFileChange"
        :show-file-list="false"
        :auto-upload="false"
        v-bind="$attrs">
        <el-button
          v-if="!readonly"
          slot="trigger"
          size="mini"
          type="primary">选取文件</el-button>
        <div
          v-if="!readonly && showTip"
          slot="tip"
          class="el-upload__tip">{{ internalTip }}</div>
      </el-upload>
    </div>
  </div>
</template>

<script>
import { some } from 'lodash-es';
import {
 computed, defineComponent, nextTick, ref,
} from 'vue';
import { Message } from 'element-ui';

export default defineComponent({
  name: 'FileManage',

  props: {
    /**
     * store 对象，提供文件引用、以及下载，删除等功能封装
     */
    store: {
      type: Object,
      required: true,
      default: () => ({}),
    },

    /**
     * 可以选择上传的文件类型
     */
    accept: {
      type: String,
      default: 'image/png,image/jpeg,.doc,.docx,.xls,.xlsx,.pdf,.zip,.rar,.txt',
    },
    /**
     * 是否显示提示
     */
    showTip: {
      type: Boolean,
      default: true,
    },
    /**
     * 提示消息
     */
    tip: {
      type: String,
      required: false,
    },

    /**
     * 已上传到服务器的旧文件是否 readonly
     */
    oldFileReadOnly: {
      type: Boolean,
      default: false,
    },

    /**
     * 是否 readonly
     */
    readonly: {
      type: Boolean,
      default: false,
    },

    /**
     * 是否可以预览
     */
    previewable: {
      type: Boolean,
      default: true,
    },

    /**
     * 支持最大的文件体积（MB）
     */
    maxSize: {
      type: Number,
      default: 20,
    },

    /**
     * 支持最大的文件数量（含服务器、本地文件），
     * ! 注意，必须大于 0
     */
    limit: {
      type: Number,
      default: Infinity,
    },

    /**
     * 达到数量限制的时候，自动替换文件，替换策略：
     * 1. `unsynced-only` 仅替换内存中未提交到服务器的新文件
     * 2. `synced-only` 仅替换已经上传到服务器的文件
     * 3. `both` 对内存中的新文件、服务器上的文件一视同仁（优先替换服务器的文件）
     * 4. `never` 不替换，直接拒绝本次添加操作
     */
    replaceWhenLimitReached: {
      type: String,
      default: 'both',
    },

    /**
     * item 上下边沿是否显示分割线
     */
    border: {
      type: Boolean,
      default: false,
    },

    /**
     * item 是否渲染隔行换色斑马纹
     */
    stripe: {
      type: Boolean,
      default: false,
    },

    /**
     * 条目排版方式
     * justify：两端对齐
     * inline：左对齐
     */
    itemLayout: {
      type: String,
      default: 'justify',
    },
    downloadFn: {
      type: Function,
      default: () => null,
    },
  },

  setup(props, { emit }) {
    const uploadRef = ref(null);

    const internalTip = computed(() => props.tip ?? `只能上传 jpg/png/jpeg/word/excel/pdf/zip/rar/txt 文件，且不超过 ${props.maxSize}MB`);

    const getSizeInMb = (file) => file.size / 1024 / 1024;

    // 校验文件尺寸
    const fileSizeValidate = (file) => getSizeInMb(file) < props.maxSize;

    // 收集一个 tick 中得所有错误消息，下个 tick 一起打印
    const messages = new Set();

    /**
     * @param {UploadFile} file 代表当前选择的文件，multiple 时，一次选择多个文件，会多次触发 onChange，
     * 因此只需要按照单选的处理逻辑考虑即可
     *
     * @param {UploadFile[]} fileList 代表包含 `file` 参数(push 在末尾)的当前最新的文件列表状态（el 内部使用），
     * 每次组件 `:file-list` prop 更新时，这个 fileList 也会重新从 `store.state.newFiles` map 出来，即 `fileList = store.state.newFiles.map(...)`
     * 从另一个角度来说，每次 onChange 的时候，fileList 也代表着当前最新 newFiles 数组
     */
    const onFileChange = (file, fileList) => {
      // 异常情况，需要检查 limit 设置，组件打印调试信息并直接重置退出
      if (props.store.state.orginFiles.length > props.limit) {
        console.error('服务器加载回来的文件，大于组件设定的 limit ！');
        return props.store.reset([]);
      }

      // 检查当前处理的文件尺寸是否溢出，如果是，则剔除
      if (!fileSizeValidate(file)) {
        messages.add(`文件《${file.name}》大小 ${getSizeInMb(file).toFixed(2)}MB 超过 ${props.maxSize}MB`);
        fileList.splice(fileList.indexOf(file), 1);
      }

      // 文件数量溢出，根据策略选择处理逻辑
      if (props.store.state.orginFiles.length + fileList.length > props.limit) {
        // eslint-disable-next-line default-case
        switch (props.replaceWhenLimitReached) {
          case 'both': {
            if (props.store.state.orginFiles.length) {
              props.store.state.orginFiles.shift();
            } else {
              fileList.shift();
            }
            break;
          }
          case 'unsynced-only': {
            fileList.shift();
            break;
          }
          case 'synced-only': {
            if (props.store.state.orginFiles.length) {
              props.store.state.orginFiles.shift();
            } else {
              messages.add(`文件数量超出限制（${props.limit}个），请删减后再上传`);
              fileList.splice(fileList.indexOf(file), 1);
            }
            break;
          }
          case 'never': {
            messages.add(`文件数量超出限制（${props.limit}个），请删减后再上传`);
            fileList.splice(fileList.indexOf(file), 1);
            break;
          }
        }
      }

      props.store.addNewFile(fileList);

      nextTick(() => {
        if (messages.size) {
          Message({
            type: 'warning',
            dangerouslyUseHTMLString: true,
            message: [...messages].join('<br>'),
          });
          messages.clear();
        }
      });
    };

    // 文件列表是否展示预览按钮（已同步至服务器的）
    const hasPreviewBtn = (file) => props.previewable && some(['.png', '.jpg', '.gif', '.jpeg', '.pdf'], (item) => file.name.endsWith(item));

    // 点击移除文件（已同步至服务器的）
    const handleRemoveOriginFile = (file) => {
      emit('removeFile', file);
      props.store.removeFile(file);
    };

    return {
      internalTip,
      onFileChange,
      uploadRef,
      hasPreviewBtn,
      handleRemoveOriginFile,
    };
  },
});
</script>

<style lang="scss" scoped>
.FileItem {
  overflow: hidden;

  .FileManage--stripe & {
    &:nth-child(odd) {
      background-color: rgba(0,0,0,.01);
    }
  }
  .FileManage--border & {
    border-bottom: 1px dashed #DCDFE6;
    &:first-of-type {
      border-top: 1px dashed #DCDFE6;
    }
  }
}

.FileItem::after {
  display: table;
  content: '';
  clear: both;
}

.FileItem__name {
  box-sizing: border-box;
  line-height: 32px;
  padding: 0 size-unit(1);

  .FileManage--justify & {
    float: left;
    width: calc(100% - 160px);
    min-width: 160px;
  }
  .FileManage--justify.FileManage--readonly & {
    width: calc(100% - 110px);
    min-width: 110px;
  }
  .FileManage--inline & {
    display: inline-block;
    vertical-align: middle;
  }
}

.FileItem__name__label {
  align-items: center;
}

.FileItem__name__flag {
  align-items: center;
  margin-left: size-unit(.5);
  display: inline-block;
  padding: 0 4px;
  line-height: 16px;
  border: 1px solid #E4E7ED;
  border-radius: 3px;
  background-color: #E4E7ED;
  color: #E6A23C;
  font-size: 12px;
}

.FileItem__actions {
  box-sizing: border-box;
  overflow: hidden;
  white-space: nowrap;
  padding: 0 size-unit(1);

  width: 160px;
  .FileManage--readonly & {
    width: 110px;
  }

  .FileManage--justify & {
    float: left;
  }
  .FileManage--inline & {
    display: inline-block;
    vertical-align: middle;
  }

  ::v-deep .el-button [class*="el-icon-"] + span {
    margin-left: 2px;
  }
}

.UploadArea {
  margin-top: size-unit(1);
}
</style>
