<!--
 * @Author: 肖泽涛
 * @Description: 全局属期控件
 * @Date: 2019-03-20 16:51:45
 * @LastEditTime: 2019-11-21 15:55:45
 *
 * @LastEditors: 启旭
 * @Update: 新增 disabled 配置
 *
-->
<template>
  <div class="GlobalAccountPeriodPicker" :class="{'enableDataLock': enableDataLock}">
    <el-date-picker type="month" v-model="accountPeriod" ref="pickerRef" :disabled="disabled" :clearable="false"
      :editable="false" :picker-options="pickerOptions" @change="handlerChange" />
    <div v-if="enableDataLock" class="accountDoneTag" :class="{ isDone: accountPeriodIsDone }">
      {{ accountPeriodIsDone ? '已结账' : '未结账' }}
    </div>

  </div>
</template>

<script setup>
import {
  ref, computed, watch, nextTick,
  onMounted, defineProps, defineEmits,
} from 'vue';
import dayjs from 'dayjs';
import store from '@/store';

const props = defineProps({
  // 自定义禁用日期方法
  disabledDate: {
    type: Function,
  },
  disabled: {
    type: Boolean,
  },
});
const emit = defineEmits(['change']);
const pickerRef = ref();
const accountPeriod = ref(store.state.user.accountPeriod || new Date());
const accountDate = computed(() => dayjs(store.state.user.companyInfo.accountDate).toDate());
const accountPeriodIsDone = computed(() => store.getters.isLockDataMonth(accountPeriod.value));
// 是否开启了结账功能
const enableDataLock = computed(() => !!store.state.user.companyInfo.enableDataLock);

const pickerOptions = {
  disabledDate: (time) => !(time.getTime() < Date.now() && time.getTime() > accountDate.value.getTime()),
};

watch(() => props.disabledDate, (newValue) => {
  if (typeof newValue === 'function') {
    pickerOptions.disabledDate = newValue;
  }
});
watch(() => store.state.user.accountPeriod, (newValue) => {
  if (accountPeriod.value !== newValue) {
    accountPeriod.value = newValue;
  }
});
const handlerChange = (val) => {
  store.dispatch('user/setAccountPeriod', new Date(val));
  emit('change', val);
};

const mountPicker = () => {
  pickerRef.value.mountPicker();
  nextTick(() => {
    const orginCellStyle = pickerRef.value.picker.$children[2].getCellStyle;
    pickerRef.value.picker.$children[2].getCellStyle = function getCellStyle(cell) {
      const otherStyle = orginCellStyle(cell);

      const yearMonth = this.getMonthOfCell(cell.text);
      const isAccountDone = store.getters.isLockDataMonth(yearMonth);
      return { ...otherStyle, isAccountDone };
    };
  });
};

onMounted(() => {
  mountPicker();
});
// export default {
//   props: {
//     // 自定义禁用日期方法
//     disabledDate: {
//       type: Function,
//     },
//     disabled: {
//       type: Boolean,
//     },
//   },

//   data() {
//     // 默认选项
//     const pickerOptions = {
//       // 所有选择月份的，建账年月之前默认为置灰
//       disabledDate: (time) => !(time.getTime() < Date.now()
//         && time.getTime() > this.accountDate.getTime()),
//       // cellClassName: (time) => {
//       //   console.log('cellClassName', '-----------', time.getTime(), time.getTime() < Date.now() ? 'account-done-time' : '');
//       //   return (time.getTime() < Date.now() ? 'account-done-time' : '');
//       // },
//     };

//     // 合并用户选项
//     const { disabledDate } = this;
//     if (typeof disabledDate === 'function') {
//       pickerOptions.disabledDate = disabledDate;
//     }

//     return {
//       pickerOptions,
//       lastDoneMonth: 8,
//       // accountPeriod: this.$store.state.user.accountPeriod || new Date(),
//     };
//   },

//   computed: {
//     accountPeriod: {
//       get() {
//         return this.$store.state.user.accountPeriod || new Date();
//       },
//       set(val) {
//         this.$store.commit('user/SET_GLOBAL_ACCOUNT_PERIOD', new Date(val));
//       },
//     },
//     // 当前账套建账月
//     accountDate() {
//       return dayjs(this.$store.state.user.companyInfo.accountDate).toDate();
//     },
//     // 选中的所属期是否已经结账
//     accountPeriodIsDone() {
//       return store.getters.isLockDataMonth(this.accountPeriod);
//     },
//   },
//   mounted() {
//     this.$refs.pickerRef.mountPicker();
//     this.$nextTick(() => {
//       const orginCellStyle = this.$refs.pickerRef.picker.$children[2].getCellStyle;
//       this.$refs.pickerRef.picker.$children[2].getCellStyle = function getCellStyle(cell) {
//         const otherStyle = orginCellStyle(cell);

//         const yearMonth = this.getMonthOfCell(cell.text);
//         const isAccountDone = store.getters.isLockDataMonth(yearMonth);
//         return { ...otherStyle, isAccountDone };
//       };
//     });
//   },
//   watch: {
//     // 用户选项变化时，自动合并
//     disabledDate(newValue) {
//       if (typeof newValue === 'function') {
//         this.pickerOptions.disabledDate = newValue;
//       }
//     },
//   },

//   methods: {
//     handlerChange(val) {
//       this.$store.commit('user/SET_GLOBAL_ACCOUNT_PERIOD', new Date(val));
//       this.$emit('change', val);
//     },
//   },
// };
</script>
<style lang="scss">
.GlobalAccountPeriodPicker {
  display: inline-block;
  position: relative;

  &::v-deep .el-input__inner {
    padding-right: 8px;
  }

  .el-input {
    padding-right: 8px;
    width: 124px;
  }
  &.enableDataLock{
    .el-input {
      padding-right: 8px;
      width: 160px;
    }
  }

  .accountDoneTag {
    position: absolute;
    right: 6px;
    top: 20px;
    width: 42px;
    height: 17px;
    line-height: 17px;
    text-align: center;
    background: #FAEDED;
    color: #D67F7C;
    font-size: 12px;

    border-radius: 2px;

    &.isDone {
      background: #EDF3FA;
      color: #7691B3;
    }
  }
}

.el-month-table td.isAccountDone {
  position: relative;

  &::after {
    font-family: element-icons !important;
    content: "\e79c";
    position: absolute;
    left: 50%;
    font-size: 14px;
    bottom: 0px;
    color: #67C23A;
    transform: translateX(-50%);
  }
}
</style>
