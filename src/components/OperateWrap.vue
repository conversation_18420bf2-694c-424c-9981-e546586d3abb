<template>
  <div class="operate-wrap">
    <span class="el-icon-more"></span>
    <div class="operate-more">
      <div class="icon">
        <slot name="button"></slot>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'oper-btn',
  mounted() {
    // 给父元素设置允许超出显示
    $(this.$el).parent('.cell').css({ overflow: 'visible', padding: '0' });
    $(this.$el).parent('.td').css({ padding: '0' });
  },
};
</script>
<style lang="scss" scoped>
.operate-wrap{
  position: relative;
  height: 30px;
  text-align: center;
  line-height: 30px;
  .operate-more{
    // display: none;
    transition: 0.3s all;
    position: absolute;
    top: 0;
    opacity: 0;
    transform: translateX(-100%);
    left: 0;
    height: 30px;
    background: #66ccff;
    .icon{
      color: #ffffff;
      display: inline-block;
      width: 30px;
      line-height: 30px;
      text-align: center;
      cursor: pointer;
    }
  }
  &:hover{
    background: #66ccff;
    .operate-more{
      display: block;
      opacity: 1;
      height: 30px;
      z-index: 100;
      transform: translateX(200%);

    }
  }

}
</style>
