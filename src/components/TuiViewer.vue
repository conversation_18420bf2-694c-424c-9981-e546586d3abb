<!--
 * @Description:
 * @version:
 * @Company: 海闻软件
 * @Author: 启旭
 * @Date: 2020-04-21 09:26:38
 * @LastEditors: 启旭
 * @LastEditTime: 2020-05-14 11:43:05
 -->
<template>
  <div ref="tuiEditorViewer"></div>
</template>
<script>
import 'tui-editor/dist/tui-editor-contents.css';

import Viewer from 'tui-editor/dist/tui-editor-Viewer';

const editorEvents = [
  'load',
  'change',
  'stateChange',
  'focus',
  'blur',
];

export default {
  name: 'TuiEditorViewer',
  props: {
    height: {
      type: String,
    },
    value: {
      type: String,
    },
    exts: {
      type: Array,
    },
  },
  data() {
    return {
      editor: null,
    };
  },
  watch: {
    value(val, preVal) {
      if (val !== preVal) {
        this.editor.setValue(val);
      }
    },
  },
  mounted() {
    const eventOption = {};
    editorEvents.forEach((event) => {
      eventOption[event] = (...args) => {
        this.$emit(event, ...args);
      };
    });
    const instance = new Viewer({
      el: this.$refs.tuiEditorViewer,
      height: this.height,
      initialValue: this.value,
    });
    this.editor = instance;
  },
  destroyed() {
    editorEvents.forEach((event) => {
      this.editor.off(event);
    });
    this.editor.remove();
  },
  methods: {
    invoke(methodName, ...args) {
      let result = null;
      if (this.editor[methodName]) {
        result = this.editor[methodName](...args);
      }
      return result;
    },
  },
};
</script>
