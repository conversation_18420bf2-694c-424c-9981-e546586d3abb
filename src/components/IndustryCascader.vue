<template>
    <el-cascader
        filterable
        :disabled='isDisable'
        :options="industryList"
        placeholder='选择公司行业'
        v-model="industryPath"
        :props="props" >
    </el-cascader>
</template>
<script>
export default {
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    isDisable: Boolean,
  },
  computed: {
    industryPath: {
      get() {
        console.log(this.value);
        return this.value;
      },
      set(val) {
        this.$emit('update:value', val);
      },
    },
  },
  mounted() {
    this.setIndustry();
  },
  data() {
    return {
      props: {
        label: 'industryName',
        value: 'industryPath',
        children: 'children',
      },
      industryList: [],
    };
  },
  methods: {
    async setIndustry() {
      let industryList = localStorage.getItem('industryList');
      const industryLastTime = localStorage.getItem('industryLastTime');
      const lastTimeRsp = await this.$http.get('/rest/companyConfig/companyBasis/lasttime/v1.0/list').catch(() => {
        console.error('修改时间');
      });
      const lastModifyTime = lastTimeRsp.data.data[0].industry;
      console.log(lastTimeRsp.data);
      const newLastTime = lastModifyTime ? Date.parse(lastModifyTime.replace('-', '/')) : 0;
      if (industryList) {
        if (newLastTime === industryLastTime) {
          industryList = JSON.parse(industryList);
          this.industryList = industryList;
          console.log('youyou');
          return;
        }
      }
      const industryRsp = await this.$http.get('/rest/global/dimensionality/industry/v0.1/list?pageSize=99999&page=1');
      industryList = this.covert(industryRsp.data.data);
      this.industryList = industryList;
      localStorage.setItem('industryList', JSON.stringify(industryList));
      localStorage.setItem('industryLastTime', newLastTime);
    },
    covert(sourceData) {
      const result = [];
      if (!sourceData || !sourceData.length || sourceData.length === 0) return [];

      function getSon(data, curNode) {
        const children = [];
        for (let m = 0; m < data.length; m += 1) {
          if (data[m].industryPath.length === 5 && data[m].industryPath.substring(0, 4) === curNode.industryPath) {
            children.push(data[m]);
          }
        }
        return children;
      }

      function getGrandson(data, curNode) { // str.substr(str.length-4)
        const children = [];
        for (let K = 0; K < data.length; K += 1) {
          if ((data[K].industryPath.length === 4 || (data[K].industryPath.length === 5 && data[K].industryPath.substring(3, 5).indexOf('0') > -1)) && data[K].industryPath.substring(0, 3) === curNode.industryPath) {
            const _children = getSon(data, data[K]);
            if (_children && _children.length > 0) {
              data[K].children = _children;
            }
            children.push(data[K]);
          }
        }
        return children;
      }

      function getChildren(data, curNode) {
        const children = [];
        for (let j = 0; j < data.length; j += 1) {
          if (data[j].industryPath.length === 3 && data[j].industryPath.substring(0, 1) === curNode.industryPath) {
            const _children = getGrandson(data, data[j]);
            if (_children && _children.length > 0) {
              data[j].children = _children;
            }
            children.push(data[j]);
          }
        }
        return children;
      }
      for (let i = 0; i < sourceData.length; i += 1) {
        const obj = sourceData[i];
        if (obj.industryPath.length === 1) { //
          const children = getChildren(sourceData, obj);
          if (children.length > 0) {
            obj.children = children;
          }
          result.push(obj);
        }
      }
      return result;
    },
  },
};
</script>
