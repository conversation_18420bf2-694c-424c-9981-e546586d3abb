<template>
  <el-dialog title="选择企业" :visible.sync="dialogVisible" :modal-append-to-body="false">
    <el-form inline>
      <el-form-item>
        <el-input
        v-model="searchForm.businessName"
        placeholder="请输入企业名称"
        @keydown.enter.native.prevent="getBusiness"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getBusiness">查询</el-button>
      </el-form-item>
    </el-form>
    <ul class="companyBox">
      <li
      :class="{ active: chosenCompany.qybdid === item.qybdid }"
      v-for="(item,index) in filterList"
      :key="item.qybdid"
      :disabled="item.tybz !== 'N'"
      @click="clickCompany(item)">
        <span style="margin-right:10px;">{{ index+1 }}</span>
        <span>{{item.nsrmc}}（{{item.zzNsrmc}}-{{item.yhsfmc}}）</span>
        <span v-if="item.tybz !== 'N'">(停用)</span>
      </li>
    </ul>
    <div slot="footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirmClick">确 认</el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    nsrQysqVos: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      searchForm: {
        businessName: '',
      },
      chosenCompany: '',
      filterList: [],
      filterText: '',
    };
  },

  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(show) {
        if (this.visible && !show) this.$emit('hide', false);
      },
    },
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.filterList = this.nsrQysqVos;
        this.filterText = '';
      }
    },
  },

  methods: {
    getBusiness() {
      this.filterList = this.nsrQysqVos
        .filter((item) => item.nsrmc.indexOf(this.searchForm.businessName) !== -1);
    },

    clickCompany(chosenOne) {
      if (chosenOne.tybz !== 'N') {
        this.$message.warning('该企业以停用，不能选择');
        return;
      }
      this.chosenCompany = chosenOne;
    },

    handleConfirmClick() {
      if (!this.chosenCompany) {
        this.$message.error('请选择企业');
        return;
      }
      this.dialogVisible = false;
      const { qybdid: enterpriseCode, nsrmc: enterpriseName } = this.chosenCompany;
      this.$emit('choose', { enterpriseName, enterpriseCode });
    },
  },
};
</script>

<style lang="scss"   scoped>
  .el-form .el-form-item {
    vertical-align: middle;
  }
  ul {
    height: 300px;
    overflow-x: hidden;
    overflow-y: auto;
    i {width:30px; margin-right:12px;}
    li {
      padding: 0 10px;
      line-height: 30px;
      background: #F5F5F5;
      border: 1px solid #ddd;
      cursor: pointer;
      &.active {
        position: relative;
        border-color: #20a0ff;
        &:before {
          content: "";
          width: 2px;
          height: 6px;
          box-sizing: content-box;
          border: 2px solid #fff;
          border-left: none;
          border-top: none;
          position: absolute;
          right: 2px;
          bottom: 2px;
          transform: rotate(45deg) scaleY(1);
          z-index: 2;
        }
        &:after {
          content: "";
          position: absolute;
          right: 0;
          bottom: 0;
          border: 8px solid transparent;
          border-right-color: #20a0ff;
          border-bottom-color: #20a0ff;
          z-index: 1;
        }
      }
      & + li {
        margin-top: 10px;
      }
    }
  }
</style>
