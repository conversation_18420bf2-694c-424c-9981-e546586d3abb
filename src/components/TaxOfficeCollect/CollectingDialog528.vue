<template>
  <el-dialog title="发票采集" :visible.sync="dialogVisible" :modal-append-to-body="false" >
      <p v-if="!isAgain" class="highLight">首次采集，请你先完善电子税务局登录信息</p>
      <p v-else class="highLight">未绑定电子税务局账号，请你先完善电子税务局登录信息</p>
      <!-- <el-form-item label="省份">
        <el-select v-model="loginForm.provinceCode" disabled>
          <el-option label="广东省" value="440000"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="creditCode" label="社会统一信用代码">
        <el-input v-model="loginForm.creditCode"></el-input>
      </el-form-item>
      <el-form-item prop="userName" label="用户名">
        <el-input v-model="loginForm.userName"></el-input>
      </el-form-item>
      <el-form-item prop="userPasswd" label="密码">
        <el-input type="password" v-model="loginForm.userPasswd"></el-input>
      </el-form-item>
      <el-form-item>
        <el-radio-group v-model="loginForm.loginMode">
          <el-radio label="common">方式1（通用）</el-radio>
          <el-radio label="GuangDong">方式2（内测功能，未加入白名单的用户暂不可用）</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="agree">
        <el-checkbox v-model="loginForm.agree"></el-checkbox>&nbsp;
        同意{{$store.state.app.PROJECT_NAME}}系统使用您的用户名登录电子税务局获取发票
        <br>
        “{{$store.state.app.PROJECT_NAME}}”将根据服务需要保存您税务相关登录信息，
        包括用户名及密码，以令“{{$store.state.app.PROJECT_NAME}}”可以在法律、法规限定的范围内为您提供自动或者半自动的产品服务，
        包括税费申报、报表报送、获取您授权下的法定公开信息、获取与您使用相关服务有关的发票信息及其状态等。
      </el-form-item> -->
    </el-form>
    <div slot="footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirmClick">去完善</el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    isAgain: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      loginForm: {
        provinceCode: '440000',
        userName: '',
        userPasswd: '',
        agree: false,
        provinceName: '广东省',
        creditCode: '',
        loginMode: 'common',
      },
      rules: {
        creditCode: [{
          required: true,
          pattern: /^([A-Z\d]{15}|[A-Z\d]{18}|[A-Z\d]{20})$/,
          message: '请输入15、18、20位数字或大写字母',
          trigger: 'change',
        }],
        userName: [{
          required: true, message: '请输入用户名', trigger: 'change',
        }],
        userPasswd: [{
          required: true, message: '请输入密码', trigger: 'change',
        }],
        agree: [{
          pattern: /^true$/, message: '请勾选复选框', trigger: 'change',
        }],
      },
    };
  },

  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(show) {
        if (this.visible && !show) {
          this.$emit('hide', false);
          this.loginForm.userPasswd = '';
        }
      },
    },
  },

  methods: {
    handleConfirmClick() {
      this.$router.push({
        name: 'taxHome',
      });
      this.$emit('hide', false);
    },
  },
};
</script>

<style scoped>
  .el-select {
    width: 100%;
  }
  .highLight {
    color: red;
    text-align: center;
    font-size: 14px;
  }
</style>
