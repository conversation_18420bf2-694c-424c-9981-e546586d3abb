<!--
 * @Description:
 * @version:
 * @Company: 海闻软件
 * @Author: 启旭
 * @Date: 2019-09-18 15:38:03
 * @LastEditors: 启旭
 * @LastEditTime: 2020-05-28 19:33:49
 -->
<template>
  <el-dialog title="发票采集" width="1000px"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible" :modal-append-to-body="false">
    <el-form>
      <el-form-item label="属期">
        <el-date-picker
        v-model="commitForm.accountPeriod"
        :picker-options="$store.getters.pickerOptionsForAccountDateAndNowDate"
        type="month"
        @change="loadResultList"
        :clearable="false"></el-date-picker>
        <span style="color:#d00;margin-left:25px;">{{tipText}}</span>
      </el-form-item>
      <el-form-item label="企业名称">
        {{shuiwuData.nsrmc || shuiwuData.enterpriseName}}
        <!-- <span class="edit" @click="dialogVisible = false; $emit('rechoose')">切换企业</span> -->
        <!-- <span class="edit" @click="dialogVisible = false; $emit('againlogin')">重新登录</span> -->
        <span class="edit" @click="jumpToTaxHome">取消绑定</span>
      </el-form-item>
      <el-form-item label="社会统一信息代码">
        {{shuiwuData.shxydm || shuiwuData.gsshxydm}}
      </el-form-item>
      <el-form-item label="采集方式">
        <el-radio-group v-model="patternType" disabled>
          <el-radio :label="1" disabled>方式1（通用）</el-radio>
          <el-radio :label="2">方式2（内测功能，未加入白名单的用户暂不可用）</el-radio>
          <el-radio :label="3">方式3（第三方连接）</el-radio>
        </el-radio-group>
        <!-- <p class="Danger">重新登录可以切换采集方式</p> -->
      </el-form-item>
      <el-form-item label="采集内容" v-if="inputOrOutput === 'input' && patternType === 3">
        <el-checkbox-group v-model="commitForm.collectionType" >
          <el-checkbox :label="0">全量发票</el-checkbox>
          <el-checkbox :label="1">已勾选发票</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <p class="Danger" v-if="isLastYearAccountPeriod">由于本次采集的发票开票日期超过3年，系统对首次采集的发票将无法采集到发票明细，请手工补充发票明细</p>
    <p class="Danger" v-if="loginStateRemark">{{loginStateRemark}}</p>
    <el-table :data="resultList" border cell-class-name="breakCell" max-height="200px">
      <el-table-column label="采集内容" border prop="collectionType">
        <template slot-scope="{row}">{{ row.collectionType === 1 ? '已勾选发票' : '全量发票' }}</template>
      </el-table-column>
      <el-table-column label="发票类型" prop="invoiceTypeName"></el-table-column>
      <el-table-column label="采集时间" :width="$store.getters.columnSize.Buildin.Time" prop="initiatorTime" ></el-table-column>
      <af-table-column label="任务说明" prop="returnMessage"></af-table-column>
    </el-table>
    <div slot="footer">
      <el-checkbox
      v-model="shuiwuData.autoDownloadFlag"
      style="margin-right: 10px;">
      允许系统自动采集该企业每月发票（1、3、8号凌晨自动采集）
      </el-checkbox>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirmClick" :disabled="accountPeriodFlag === 0 || accountPeriodFlag === 2">确 认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import dayjs from 'dayjs';

export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    shuiwuData: {
      type: Object,
      default: () => ({}),
    },
    autoDownloadFlag: {
      type: Number,
    },
    accountPeriod: {
      type: Date,
    },
    type: {
      type: String,
    },
    patternType: {
      type: Number,
    },
  },

  data() {
    return {
      commitForm: {
        accountPeriod: new Date(),
        collectionType: [0],
      },
      resultList: [],
    };
  },

  watch: {
    accountPeriod: {
      immediate: true,
      handler(value) {
        this.commitForm.accountPeriod = value;
      },
    },
    dialogVisible(val) {
      if (val) {
        this.loadResultList();
      }
    },
  },

  computed: {
    inputOrOutput() {
      if (this.$route.path.indexOf('input') !== -1) return 'input';
      if (this.$route.path.indexOf('output') !== -1) return 'output';
      return '';
    },
    accountPeriodFlag() {
      // 仅当进项时才执行的交互
      if (this.inputOrOutput === 'input' && this.patternType === 1) {
        const date1 = dayjs(this.commitForm.accountPeriod.Format('yyyyMM'));
        const date2 = dayjs(new Date().Format('yyyyMM'));
        const num = date1.diff(date2, 'month');
        console.log(num);
        if (num === -1) return 1;
        if (num < -1) return 0;
        return 2;
      }
      return '';
    },
    tipText() {
      if (this.accountPeriodFlag === 0) return '增值税已申报无法采集进项发票';
      if (this.accountPeriodFlag === 2) return '增值税未申报无法采集进项发票';
      return '';
    },
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(show) {
        if (this.visible && !show) this.$emit('hide', false);
      },
    },
    isLastYearAccountPeriod() {
      const lastYear = dayjs().subtract(36, 'month');
      return dayjs(this.commitForm.accountPeriod).isBefore(lastYear);
    },
    loginStateRemark() {
      if (this.shuiwuData.lastLoginRemark === '检测到增值税已申报，无法采集进项增值税发票' && this.inputOrOutput === 'output') {
        return '';
      }
      return this.shuiwuData.lastLoginRemark;
    },
  },

  methods: {
    // 查询指定属期发票采集任务状态
    async loadResultList() {
      const accountPeriod = this.commitForm.accountPeriod.Format('yyyyMM');
      const inputOutputMarkers = this.inputOrOutput === 'input' ? 1 : 2;

      const API = `/rest/companytax/merge/invoice/v1.0/queryCollectionStatus/${accountPeriod}/${inputOutputMarkers}`;
      const rsp = await this.$http.post(API);
      this.resultList = rsp.data.data || [];
    },
    async handleConfirmClick() {
      const accountPeriod = this.commitForm.accountPeriod.Format('yyyyMM');
      const { collectionType } = this.commitForm;
      if (collectionType.length === 0) {
        this.$message.warning('采集内容至少需要选择一项');
        return;
      }

      await Promise.all(collectionType.map(type => {
        const API = `/rest/proxy/tax/merge/invoice/${this.inputOrOutput}/v1.0/collectionTask/${accountPeriod}/${type}`;
        return this.$http.put(API, {
        });
      }));
      this.dialogVisible = false;
      this.$emit('setAccountPeriod', this.commitForm.accountPeriod);
      this.$emit('changeAutoDownloadFlag', this.shuiwuData.autoDownloadFlag);
      this.$alert('后台正在采集，过程可能需要几分钟，请耐心等候', '提示', {
        type: 'warning',
        confirmButtonText: '我知道了',
        closeOnClickModal: false,
      });
    },
    deleteBind() {
      this.$confirm('取消绑定后，下次进行发票采集，需要重新绑定电子税务局账号，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.dialogVisible = false;
        this.$emit('deleteBind');
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作',
        });
      });
    },
    jumpToTaxHome() {
      this.$alert('请前往税务信息页面，进行解绑税局账号操作', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        this.$router.push({
          name: 'taxHome',
        });
        this.dialogVisible = false;
        this.$emit('hide');
      });
    },
  },
};
</script>

<style lang="scss"   scoped>
  .edit {
    color: #20a0ff;
    cursor: pointer;
    margin-left: 10px;
  }
</style>
