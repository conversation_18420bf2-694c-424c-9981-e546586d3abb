<template>
  <div>
    <!-- 选择税局绑定的公司 -->
    <!-- <select-dialog
      :visible="selectVisible"
      @choose="chooseSuccess"
      :nsrQysqVos="nsrQysqVos"
      @hide="selectVisible = false;"
    ></select-dialog> -->
    <!-- 税局登录窗口 -->
    <!-- <collecting-dialog
      ref="loginDialog"
      :visible="collectingVisible"
      :is-again="Boolean(shuiwuData.enterpriseCode)"
      @login="login"
      @hide="collectingVisible = false;"
    ></collecting-dialog> -->
    <!-- 税局登录窗口 -->
    <collecting-dialog528
      ref="loginDialog"
      :visible="collectingVisible"
      :is-again="Boolean(shuiwuData.enterpriseCode)"
      @login="login528"
      @hide="collectingVisible = false;"
    ></collecting-dialog528>
    <!-- 确认采集发票信息窗口 -->
    <confirm-selected-dialog
      :visible="confirmSelectedVisible"
      :accountPeriod="accountPeriod"
      :type="type"
      :patternType="shuiwuData && shuiwuData.patternType"
      @againlogin="collectingVisible = true"
      :shuiwuData="shuiwuData"
      @rechoose="rechoose"
      @deleteBind="deleteBind"
      @changeAutoDownloadFlag="changeAutoDownloadFlag"
      @hide="confirmSelectedVisible = false;"
    ></confirm-selected-dialog>
  </div>
</template>
<script>
// import SelectDialog from './SelectDialog.vue';
import dayjs from 'dayjs';
import CollectingDialog528 from './CollectingDialog528.vue';
import ConfirmSelectedDialog from './ConfirmSelectedDialog.vue';

export default {
  components: {
    CollectingDialog528,
    ConfirmSelectedDialog,
  },
  props: {
    accountPeriod: [Date, String],
    type: String,
  },
  data() {
    return {
      selectVisible: false, // 选择税局绑定的公司窗口显示状态
      collectingVisible: false, // 税局登录窗口显示状态
      confirmSelectedVisible: false, // 确认采集发票信息窗口显示状态
      shuiwuData: {
        nsrQysqVos: [], // 税局公司列表
      },
      nsrQysqVos: [], // 税局公司列表
      taxConfig: {
        patternType: 1,
      },
    };
  },
  computed: {
    inputOrOutput() {
      if (this.$route.path.indexOf('input') !== -1) return 'input';
      if (this.$route.path.indexOf('output') !== -1) return 'output';
      return '';
    },
  },
  methods: {
    // 发票采集
    async collectInvoice() {
      return this.$http
        .get(`/rest/proxy/tax/merge/invoice/${this.inputOrOutput}/v1.0/getTaxLoginInfoV2`, {
          params: {
            accountPeriod: dayjs(this.accountPeriod).format('YYYYMM'),
          },
        })
        .then(async (res) => {
          const rspDate = res.data.data || [];
          if (rspDate.length) {
            this.confirmSelectedVisible = true;
            [this.shuiwuData] = rspDate;
            // 将 autoDownloadFlag 属性转为布尔值
            this.shuiwuData.autoDownloadFlag = Boolean(this.shuiwuData.autoDownloadFlag);
          } else {
            this.collectingVisible = true;
          }
        }).catch(() => {
          this.collectingVisible = true;
        });
    },
    // 登录税局系统
    login(data = this.shuiwuData) {
      this.$http
        .post('/rest/proxy/tax/merge/invoice/input/v2.0/loginTax', data)
        .then((res) => {
          this.$refs.loginDialog.dialogVisible = false;
          this.nsrQysqVos = res.data.data;
          this.selectVisible = true;
        });
    },
    // 登录税局系统528版本
    login528(data = this.shuiwuData) {
      if (data.loginMode === 'common') {
        this.loginMethods1(data);
      } else {
        this.loginMethods2(data);
      }
    },
    // 登录方式1
    loginMethods1(data) {
      this.$http
        .post('/rest/proxy/tax/merge/invoice/input/v1.0/loginTax', data)
        .then((res) => {
          this.$refs.loginDialog.dialogVisible = false;
          [this.shuiwuData] = res.data.data;
          // this.selectVisible = true;
          this.saveTaxLoginConfig(1);
          this.confirmSelectedVisible = true;
        });
    },
    // 登录方式2
    async loginMethods2(data) {
      this.$http
        .post('/rest/proxy/tax/merge/invoice/input/v2.0/loginTax', data)
        .then(async (res) => {
          this.nsrQysqVos = res.data.data;
          // this.confirmSelectedVisible = true;
          // 获取输入信用代码的公司调用选择接口
          const selectCompany = this.nsrQysqVos.find((item) => item.shxydm === data.gsshxydm);
          if (!selectCompany) {
            this.$message.error('您当前账号与该企业不存在绑定关系，请检查输入的识别号是否有误！');
            return;
          }
          const { qybdid: enterpriseCode, nsrmc: enterpriseName } = selectCompany;
          await this.chooseSuccess({ enterpriseCode, enterpriseName });
          this.saveTaxLoginConfig(2);
          this.$refs.loginDialog.dialogVisible = false;
        });
    },
    // 查询税局登录信息 配置， 主要用来获取 采集的方式字段 patternType
    async findLoginConfig() {
      const rsp = await this.$http.get('/rest/proxy/tax/merge/taxConfig/v1.0/findByLogin');
      this.taxConfig = rsp.data.data[0] || { patternType: 1 }; // 兼容旧数据， 上个版本只有方式一登录， 所以旧数据没有这个配置
    },
    // 提交税局登录方式配置 patternType 1 方式1 2 方式二
    async saveTaxLoginConfig(patternType = 1) {
      await this.$http.post('/rest/proxy/tax/merge/taxConfig/v1.0/insertTtTaxConfig', {
        companyId: this.$store.state.user.userInfo.companyId,
        patternType,
      });
      this.taxConfig = {
        companyId: this.$store.state.user.userInfo.companyId,
        patternType,
      };
    },
    rechoose() {
      this.$http
        .get('/rest/proxy/tax/merge/invoice/input/v2.0/getGdStateCompany')
        .then((res) => {
          const data = res.data.data || [];
          if (data.length) {
            this.nsrQysqVos = data;
            this.selectVisible = true;
          }
        });
    },
    // 更新税局绑定信息
    async saveTaxLoginInfo(val) {
      this.shuiwuData.autoDownloadFlag = Number(this.shuiwuData.autoDownloadFlag);
      const params = {
        enterpriseCode: this.shuiwuData.enterpriseCode,
        enterpriseName: this.shuiwuData.enterpriseName,
      };
      if (val === 'deleteBind') {
        // this.$set(params, 'enableStatus', this.shuiwuData.enableStatus);
        this.$set(params, 'enableStatus', 0);
      } else {
        this.$set(params, 'autoDownloadFlag', Number(this.shuiwuData.autoDownloadFlag));
        this.$set(params, 'enableStatus', 1);
      }
      await this.$http
        .post('/rest/proxy/tax/merge/invoice/input/v1.0/saveTaxLoginInfoV2', params);
    },
    // 绑定公司
    async chooseSuccess({ enterpriseName, enterpriseCode }) {
      await this.$http
        .post('/rest/proxy/tax/merge/invoice/input/v1.0/saveTaxLoginInfoV2', { enterpriseName, enterpriseCode });
      await this.collectInvoice();
    },
    // 取消绑定公司
    async deleteBind() {
      this.shuiwuData.enableStatus = 0;
      await this.saveTaxLoginInfo('deleteBind');
      this.$http.delete('/rest/proxy/tax/merge/taxConfig/v1.0/deleteTtTaxConfigByCompanyId');
    },
    // 允许或取消系统每晚自动采集该企业上月发票
    changeAutoDownloadFlag(autoDownloadFlag) {
      this.shuiwuData.autoDownloadFlag = autoDownloadFlag;
      this.saveTaxLoginInfo();
    },
  },
};
</script>
