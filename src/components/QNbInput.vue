<template>
  <div @dragstart.prevent class="q-nb-input">
    <div
      class="Calculator_box"
      v-if="showCalculator"
    >
      <el-popover
        ref="popover5"
        placement="right"
        width="180"
        v-model="calculatorVisible"
      >
        <calculator
          @setValue="setValue"
          v-if="calculatorVisible"
          @hide="calculatorVisible = false"
          ref="calculator"
        ></calculator>
      </el-popover>
      <div
        v-popover:popover5
        style="line-height: 12px"
        title="点击或者按下alt唤起计算器"
      >
        <span
          class="bb"
          v-show="showIcon"
        >
          <span
            class="fa fa-calculator"
            @mousedown.stop.prevent
          ></span>
        </span>

      </div>
    </div>
    <el-tooltip  :content="errorMassges" placement="top" manual :value="showError && !inputNumberDisabled">
    <el-input
      ref="input"
      :value="displayValue"
      :placeholder="placeholder"
      :disabled="inputNumberDisabled"
      :size="inputNumberSize"
      :max="max"
      :min="min"
      :name="name"
      :label="label"
      @keydown.up.native.prevent="increase"
      @keydown.down.native.prevent="decrease"
      @blur="handleBlur"
      @focus="handleFocus"
      @input="handleInput"
      @change="handleInputChange"
    >
      <slot name="prepend" slot="prepend"></slot>
    </el-input>
    </el-tooltip>
  </div>
</template>
<script>
import { toNonExponential } from '@/assets/Utils';
import Calculator from './Calculator.vue';
/* eslint-disable no-underscore-dangle */

function Focus(ref) {
  return {
    methods: {
      focus() {
        this.$refs[ref].focus();
      },
    },
  };
}
export default {
  mixins: [Focus('input')],
  inject: {
    elForm: {
      default: '',
    },
    elFormItem: {
      default: '',
    },
  },
  components: {
    Calculator,
  },
  props: {
    step: {
      type: Number,
      default: 1,
    },
    stepStrictly: {
      type: Boolean,
      default: false,
    },
    max: {
      type: Number,
      default: 999999999.99999999,
    },
    min: {
      type: Number,
      default: -999999999.99999999,
    },
    value: {},
    disabled: Boolean,
    size: String,
    controls: {
      type: Boolean,
      default: false,
    },

    controlsPosition: {
      type: String,
      default: '',
    },
    name: String,
    label: String,
    placeholder: String,
    precision: {
      type: Number,
      validator(val) {
        return val >= 0 && val === parseInt(val, 10);
      },
      default: 8,
    },
    showCalculator: {
      type: Boolean,
      default: true,
    },
    doNotShowZero: {
      type: Boolean,
      default: false,
    },
    emptyValue: {
      default: undefined,
    },
  },
  data() {
    return {
      currentValue: 0,
      userInput: null,
      calculatorVisible: false,
      showIcon: false,
      showError: false,
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(value) {
        let newVal = value === undefined ? value : Number(value);
        if (newVal !== undefined) {
          if (Number.isNaN(newVal)) {
            return;
          }

          if (this.stepStrictly) {
            const stepPrecision = this.getPrecision(this.step);
            const precisionFactor = 10 ** stepPrecision;
            newVal = (Math.round(newVal / this.step) * precisionFactor * this.step)
              / precisionFactor;
          }

          if (this.precision !== undefined) {
            newVal = this.toPrecision(newVal, this.precision);
          }
        }
        if (newVal > this.max) {
          newVal = this.max;
          this.showError = true;
        } else if (newVal < this.min) {
          newVal = this.min;
          this.showError = true;
        } else if (newVal !== this.max && newVal !== this.min) {
          this.showError = false;
        }

        this.currentValue = newVal;
        this.userInput = null;
        if (value === '') return;
        this.$emit('input', newVal);
      },
    },
  },
  computed: {
    minDisabled() {
      return this._decrease(this.value, this.step) < this.min;
    },
    maxDisabled() {
      return this._increase(this.value, this.step) > this.max;
    },
    numPrecision() {
      const {
        value, step, getPrecision, precision,
      } = this;
      const stepPrecision = getPrecision(step);
      if (precision !== undefined) {
        if (stepPrecision > precision) {
          console.warn(
            '[Element Warn][InputNumber]precision should not be less than the decimal places of step',
          );
        }
        return precision;
      }
      return Math.max(getPrecision(value), stepPrecision);
    },
    controlsAtRight() {
      return this.controls && this.controlsPosition === 'right';
    },
    _elFormItemSize() {
      return (this.elFormItem || {}).elFormItemSize;
    },
    inputNumberSize() {
      return this.size || this._elFormItemSize || (this.$ELEMENT || {}).size;
    },
    inputNumberDisabled() {
      return this.disabled || (this.elForm || {}).disabled;
    },
    displayValue() {
      if (this.userInput !== null) {
        return this.userInput;
      }
      let { currentValue } = this;

      if (typeof currentValue === 'number') {
        if (this.stepStrictly) {
          const stepPrecision = this.getPrecision(this.step);
          const precisionFactor = 10 ** stepPrecision;
          currentValue = (Math.round(currentValue / this.step)
              * precisionFactor
              * this.step)
            / precisionFactor;
        }

        if (this.precision !== undefined) {
          currentValue = currentValue.toFixed(this.precision);
        }
      }
      // 不显示0
      if (this.doNotShowZero && currentValue === undefined) return '';
      return toNonExponential(currentValue);
    },
    errorMassges() {
      return `请输入正确的数值，可输入范围${toNonExponential(this.min)}至${toNonExponential(this.max)}${this.precision === 0 ? ',并且不可为小数' : ''}`;
    },
  },

  mounted() {
    const innerInput = this.$refs.input.$refs.input;
    innerInput.setAttribute('role', 'spinbutton');
    innerInput.setAttribute('aria-valuemax', this.max);
    innerInput.setAttribute('aria-valuemin', this.min);
    innerInput.setAttribute('aria-valuenow', this.currentValue);
    innerInput.setAttribute('aria-disabled', this.inputNumberDisabled);
  },

  updated() {
    if (!this.$refs || !this.$refs.input) return;
    const innerInput = this.$refs.input.$refs.input;
    innerInput.setAttribute('aria-valuenow', this.currentValue);
  },

  methods: {
    toPrecision(num, precision) {
      if (precision === undefined) precision = this.numPrecision;
      return parseFloat(Number(num).toFixed(precision));
    },
    getPrecision(value) {
      if (value === undefined) return 0;
      const valueString = value.toString();
      const dotPosition = valueString.indexOf('.');
      let precision = 0;
      if (dotPosition !== -1) {
        precision = valueString.length - dotPosition - 1;
      }
      return precision;
    },
    _increase(val, step) {
      if (typeof val !== 'number' && val !== undefined) {
        return this.currentValue;
      }

      const precisionFactor = 10 ** this.numPrecision;
      // Solve the accuracy problem of JS decimal calculation by converting the value to integer.
      return this.toPrecision(
        (precisionFactor * val + precisionFactor * step) / precisionFactor,
      );
    },
    _decrease(val, step) {
      if (typeof val !== 'number' && val !== undefined) {
        return this.currentValue;
      }

      const precisionFactor = 10 ** this.numPrecision;

      return this.toPrecision(
        (precisionFactor * val - precisionFactor * step) / precisionFactor,
      );
    },
    increase() {
      if (this.inputNumberDisabled || this.maxDisabled) return;
      const value = this.value || 0;
      const newVal = this._increase(value, this.step);
      this.setCurrentValue(newVal);
    },
    decrease() {
      if (this.inputNumberDisabled || this.minDisabled) return;
      const value = this.value || 0;
      const newVal = this._decrease(value, this.step);
      this.setCurrentValue(newVal);
    },
    handleBlur(event) {
      this.showIcon = this.calculatorVisible;
      this.showError = false;
      this.$emit('blur', event);
    },
    setValue(value) {
      this.setCurrentValue(value);
      this.calculatorVisible = false;
      this.$nextTick(() => {
        this.$el.querySelector('input').focus();
      });
    },
    handleFocus(event) {
      this.showIcon = true;
      this.select();
      this.$emit('focus', event);
    },
    setCurrentValue(newVal) {
      const oldVal = this.currentValue;
      if (typeof newVal === 'number' && this.precision !== undefined) {
        newVal = this.toPrecision(newVal, this.precision);
      }
      if (newVal >= this.max) newVal = this.max;
      if (newVal <= this.min) newVal = this.min;
      if (oldVal === newVal) return;
      this.userInput = null;
      this.currentValue = newVal;
      if (newVal === undefined) {
        newVal = this.emptyValue;
      }
      this.$emit('input', newVal);
      this.$emit('change', newVal, oldVal);
    },
    handleInput(value) {
      this.userInput = value;
      // 禅道Bug5313 输入框需要实现可复制填写带分号数值
      value = value.replace(/,/g, '');
      const target = [].find.call(this.$refs.input.$el.children, (item) => item.tagName === 'INPUT');
      target.value = value;
      const { validity } = target;
      let showError = validity ? !validity.valid : false;
      clearTimeout(target.time);
      this.showError = false;
      target.style.borderColor = '#57c5f7';
      if (!showError && value !== '') {
        showError = this.max < value;
        showError = showError || this.min > value;
      }
      // 不能为小数也需要报错
      if (this.precision === 0) {
        const valueDecimalsPart = String(value).split('.')[1];
        if (valueDecimalsPart && valueDecimalsPart.length > this.precision) {
          showError = true;
        }
        // if (String(value).split(',')) { this.showError = showError; }
      }
      if (showError) {
        target.style.borderColor = 'red';
        this.showError = showError;
      }
    },
    handleInputChange(value) {
      // 禅道Bug5313 输入框需要实现可复制填写带分号数值
      value = value.replace(/,/g, '');
      const newVal = value === '' ? undefined : Number(value);
      if (!Number.isNaN(newVal) || value === '') {
        this.setCurrentValue(newVal);
      }
      this.userInput = null;
    },
    select() {
      this.$refs.input.select();
    },
  },

};
</script>
<style lang="scss">
.q-nb-input {
  position: relative;
  display: inline-block;
  width: 100%;
}
.Calculator_box {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  z-index: 100;
}
.bb::before,
.bb::after {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.bb {
  /* width: 14px;
          height: 14px; */
  line-height: 12px;
  font-size: 12px;
  /* background: bule no-repeat 50%/70% rgba(0, 0, 0, 0.1); */
  /* color: #69ca62; */
  /* box-shadow: inset 0 0 0 1px rgba(105, 202, 98, 0.5); */
}
.bb::before,
.bb::after {
  content: "";
  z-index: -1;
  margin: -5%;
  box-shadow: inset 0 0 0 2px #409eff;
  animation: clipMe 1s linear infinite;
}
.bb::before {
  animation-delay: -4s;
}
.bb:hover::after,
.bb:hover::before {
  background-color: rgb(98, 243, 13);
}

@keyframes clipMe {
  0%,
  100% {
    clip: rect(0px, 16px, 2px, 0px);
  }
  25% {
    clip: rect(0px, 2px, 16px, 0px);
  }
  50% {
    clip: rect(14px, 16px, 16px, 0px);
  }
  75% {
    clip: rect(0px, 16px, 16px, 14px);
  }
}
</style>
