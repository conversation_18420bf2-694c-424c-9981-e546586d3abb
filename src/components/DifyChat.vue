<template>
  <div class="dify-chat-container">
    
    <div v-if="currentAssistant" class="chat-trigger" @click="toggleChat">
      <slot name="trigger">
        <div class="default-trigger">
          <!-- <img src="/static/imgs/userlogo.png" alt="AI助手" /> -->
          <i class="iconfont icon-u585_mouseOver"></i>
        </div>
      </slot>
    </div>
    
    <!-- 自定义浮窗 -->
    <div v-show="showChatWindow" :class="['dify-chat-custom-window', {'dify-chat-fullscreen': isFullscreen}]">
      <div class="dify-chat-header">
        <div class="dify-chat-title-wrapper">
          <div class="dify-chat-title">AI助手</div>
          <el-dropdown @command="selectAssistant">
            <span class="el-dropdown-link">
              切换助手<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="item.type" v-for="(item, index) in assistantTypes" :key="index">
                {{ item.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        
        <div class="dify-chat-actions">
          <!-- 全屏切换按钮 -->
          <div class="dify-chat-fullscreen-toggle" @click="toggleFullscreen">
            <i :class="'el-icon-full-screen'"></i>
          </div>
          <div class="dify-chat-close" @click="closeChat">
            <i class="el-icon-close"></i>
          </div>
        </div>
      </div>
      <div class="dify-chat-body">
        <!-- 为每个助手类型创建独立的 iframe -->
        <div v-for="(item, index) in assistantTypes" :key="index" 
             v-show="currentAssistant === item.type" 
             class="dify-chat-iframe-container">
          <iframe 
            allow="fullscreen;microphone" 
            title="dify chatbot bubble window" 
            :id="`dify-chatbot-bubble-window-${item.type}`" 
            :src="getIframeSrc(item.type, item.chatId)" 
            class="dify-chat-iframe">
          </iframe>
        </div>
      </div>
    </div>
    
    <!-- 遮罩层，用于点击外部关闭 -->
    <div v-show="showChatWindow && !isFullscreen" class="dify-chat-mask" @click="closeChat"></div>
  </div>
</template>

<script>
export default {
  name: 'DifyChat',
  
  props: {
    config: {
      type: Object,
      default: () => ({
        baseUrl: 'https://ai.test.www.quickf.net:8044'
      })
    },
    assistantTypes: {
      type: Array,
      default: () => [
        { type: 'financial', name: '财税专家', chatId: 'caishui-ai' },
        { type: 'accounting', name: '记账专家', chatId: 'shuiwu-ai' }
      ]
    }
  },
  
  data() {
    return {
      showMenu: false,
      currentAssistant: null,
      showChatWindow: false,
      // 记录已加载的助手类型
      loadedAssistants: {},
      // 是否全屏显示
      isFullscreen: false
    };
  },
  
  computed: {
    currentChatId() {
      if (!this.currentAssistant) return null;
      
      const assistantType = this.assistantTypes.find(type => type.type === this.currentAssistant);
      return assistantType ? assistantType.chatId : 'caishui-ai'; // 默认使用财税专家
    }
  },
  
  mounted() {
    // 添加 ESC 键监听，用于关闭聊天窗口
    document.addEventListener('keydown', this.handleKeyDown);
    this.currentAssistant = this.assistantTypes[0].type;
  },
  
  beforeDestroy() {
    // 移除事件监听
    document.removeEventListener('keydown', this.handleKeyDown);
  },
  
  methods: {
    // 获取特定助手类型的 iframe src
    getIframeSrc(assistantType, chatId) {
      // 构建 iframe 的 src URL
      const baseUrl = this.config.baseUrl || 'https://ai.test.www.quickf.net';
      const port = baseUrl.includes(':') ? '' : ':8044'; // 如果 baseUrl 已包含端口则不添加
      const token = this.assistantTypes.find(type => type.type === assistantType)?.token;
      return `${baseUrl}${port}/chatbot/${token}`;
    },
    
    toggleChat() {
      if (this.currentAssistant) {
        this.showChatWindow = !this.showChatWindow;
        
        if (this.showChatWindow) {
          // 标记当前助手为已加载
          this.$set(this.loadedAssistants, this.currentAssistant, true);
          this.$emit('chat-opened', this.currentAssistant);
        } else {
          this.$emit('chat-closed');
        }
      } else {
        this.showMenu = !this.showMenu;
      }
    },
    
    selectAssistant(type) {
      console.log(type);
      this.currentAssistant = type;
      this.showMenu = false;
      this.showChatWindow = true;
      
      // 标记当前助手为已加载
      this.$set(this.loadedAssistants, type, true);
      
      // 发送选择事件
      this.$emit('assistant-selected', type);
      this.$emit('chat-opened', type);
    },
    
    // 处理 ESC 键按下事件
    handleKeyDown(e) {
      if (e.key === 'Escape' && this.showChatWindow) {
        if (this.isFullscreen) {
          // 如果是全屏状态，先退出全屏
          this.isFullscreen = false;
        } else {
          // 否则关闭聊天窗口
          this.closeChat();
        }
      }
    },
    
    // 切换全屏显示
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen;
      this.$emit('fullscreen-changed', this.isFullscreen);
    },
    
    // 公开方法，可以从父组件调用
    openChat(type) {
      if (type) {
        this.selectAssistant(type);
      } else if (this.currentAssistant) {
        this.showChatWindow = true;
        this.$emit('chat-opened', this.currentAssistant);
      } else {
        this.showMenu = true;
      }
    },
    
    closeChat() {
      // 如果是全屏状态，先退出全屏
      if (this.isFullscreen) {
        this.isFullscreen = false;
      }
      this.showChatWindow = false;
      this.showMenu = false;
      this.$emit('chat-closed');
    }
  }
};
</script>

<style scoped>
.dify-chat-container {
  position: relative;
}
.dify-chat-title-wrapper{
  display: flex;
}
.chat-trigger {
  cursor: pointer;
  display: inline-block;
}

.default-trigger {
  /* width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden; */
}

.default-trigger img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.userMneu {
  padding: 0;
  margin: 0;
  list-style: none;
}

.userMneu li {
  padding: 10px 15px;
  cursor: pointer;
}

.userMneu li:hover {
  background-color: #f5f7fa;
}

/* 自定义浮窗样式 */
.dify-chat-custom-window {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 480px;
  height: 800px;
  max-width: 90vw;
  max-height: 90vh;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 3000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 全屏样式 */
.dify-chat-fullscreen {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  border-radius: 0;
}

.dify-chat-header {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  background-color: #fff;
  border-bottom: 1px solid #ebeef5;
}

.dify-chat-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-right: 12px;
}

.dify-chat-actions {
  display: flex;
  align-items: center;
}

.dify-chat-fullscreen-toggle {
  cursor: pointer;
  font-size: 18px;
  color: #909399;
  margin-right: 16px;
}

.dify-chat-fullscreen-toggle:hover {
  color: #409EFF;
}

.dify-chat-close {
  cursor: pointer;
  font-size: 18px;
  color: #909399;
}

.dify-chat-close:hover {
  color: #409EFF;
}

.dify-chat-body {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.dify-chat-iframe-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.dify-chat-iframe {
  border: none;
  width: 100%;
  height: 100%;
  display: block;
  background-color: rgb(243, 244, 246);
}

.dify-chat-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 2999;
}
</style>
