<!--
 * @Description: 左滑框 客户供应商通用
 * @version: 过验证eslint
 * @Company: 海闻软件
 * @Author: 晓荣
 * @Date: 2019-08-28 16:35:00
 * @LastEditors: 郑晓荣
 * @LastEditTime: 2019-09-02 15:37:06
-->
<template>
    <transition name="slide-fade">
      <el-card class="left-form goods_left_form" v-if="dialogVisible">
        <div slot="header" class="header">
          <div class="content">
            <span class="title">{{ titleWord }}</span>
            <el-button type="text" class="close" @click="dialogVisible = false">
              <i class="el-icon-close"></i>
            </el-button>
          </div>
        </div>
        <!-- 客户 -->
        <customer-content
          v-if="isCustomer"
          :ref='useType'
          @hide="dialogVisible = false;">
        </customer-content>
        <!-- 供应商 -->
        <supplier-content
          v-if="!isCustomer"
          :ref='useType'
          @hide="dialogVisible = false;">
        </supplier-content>
        <div class="action_bar">
          <el-button @click.stop="dialogVisible = false">取 消</el-button>
          <ac-button :comCode="comCode" type="primary" @click="hangleConfirm">确 认</ac-button>
        </div>
      </el-card>
    </transition>
</template>
<script>

import CustomerContent from '@/pages/options/customerManagement/components/CustomerContent.vue';
import SupplierContent from '@/pages/options/supplierInfo/components/SupplierContent.vue';

export default {
  components: { CustomerContent, SupplierContent },
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    useType: {
      type: String,
      default: () => 'customer',
    },
    leftFormType: {
      type: String,
      default: () => 'addType',
    },
    leftFormObj: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(isVisible) {
        if (this.visible && !isVisible) {
          this.$emit('hide');
        }
      },
    },
    isCustomer() {
      return this.useType === 'customer';
    },
    comCode() {
      return this.isCustomer ? ['options-customerManagement-bianji'] : ['options-supplierInfo-bianji'];
    },
    isAddType() {
      return this.leftFormType === 'addType';
    },
    titleWord() {
      let text = this.isAddType ? '新增供应商' : '修改供应商';
      if (this.isCustomer) {
        text = this.isAddType ? '新增客户' : '修改客户';
      }
      return text;
    },
  },
  watch: {
    dialogVisible(show) {
      if (show) {
        if (!this.isAddType) {
          this.$nextTick(() => {
            this.$refs[this.useType].initDialogForm(this.leftFormObj);
          });
        } else {
          this.$nextTick(() => {
            this.$refs[this.useType].closeDialog();
          });
        }
      }
    },
  },
  data() {
    return {
    };
  },
  methods: {
    hangleConfirm() {
      this.$nextTick(() => {
        this.$refs[this.useType].submitForm();
      });
    },
  },
};
</script>
