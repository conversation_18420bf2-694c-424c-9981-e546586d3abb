<template>
  <div>
    <el-dialog
      title="科目余额明细"
      :append-to-body="true"
      :close-on-click-modal='false'
      :visible.sync="accountBalanceVisible"
      width="1000px"
      center
      @opened="handleOpened"
      @close="handleClose"
      close-on-press-escape>
      <el-tabs v-model="activeName" @tab-click="handleClick(activeName)">
        <span class="textClass" v-if="activeName === 'subjectTab'"> 当前科目: {{ subjectCode + ' ' + subjectFullName}}</span>
        <span class="textClass" v-else> 当前科目标签: {{ subjectCode + ' ' + subjectFullName}}
          <span v-if="tagIds">
            ( <tag-name :tagIds="tagIds" /> )
          </span>
        </span>
        <el-form :inline="true" class='qf_query'>
          <el-form-item>
            <account-cross-month :incomingDate.sync="crossMonth" @change="handleClick(activeName)" />
          </el-form-item>
          <el-form-item>
            <template v-if="activeName === 'subjectTab'">
              <el-select v-model="currencyCode" @change="handleClick(activeName)">
                <el-option
                  v-for="item in currencyOptions"
                  :key="item.currencyCode"
                  :label="item.currencyName"
                  :value="item.currencyCode">
                </el-option>
              </el-select>
            </template>
            <template v-else>
              <el-select v-model="tagIdMatchLogic" @change="handleClick(activeName)">
                <el-option label="全等该标签" :value="1" />
                <el-option label="包含该标签" :value="2" />
              </el-select>
            </template>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="qtySwitch" >显示数量</el-checkbox>
            <el-checkbox v-model="yearSwitch" @change="handleClick(activeName)">显示本年累计</el-checkbox>
            <el-tooltip content="余额数值都是正数或者零" placement="top">
              <el-checkbox v-model="negate" @change="handleClick(activeName)">自动调节余额正负</el-checkbox>
            </el-tooltip>
          </el-form-item>
        </el-form>
        <el-table
          ref="table"
          :data="tableData"
          border
          style="width: 100%">
          <el-table-column prop="keyName" width="250"></el-table-column>
          <!-- 不是外币 -->
          <template v-if="!foreignCurrency" >
            <el-table-column v-if="qtySwitch" label="数量" align='right' prop="quantity" :formatter="$quantityFormatter">
            </el-table-column>
            <el-table-column label="金额" class-name="table_font_black" align='right'>
              <template slot-scope="{ row }">
                <span>{{ row.amount | changeMoney | moneyFilter}}</span>
              </template>
            </el-table-column>
          </template>
          <!-- 外币 -->
          <template v-if="foreignCurrency">
            <el-table-column v-if="qtySwitch" label="数量" align='right' prop="quantity" :formatter="$quantityFormatter"></el-table-column>
            <el-table-column label="原币金额" class-name="table_font_black" align='right'>
              <template slot-scope="{ row }">
                <span>{{ row.amountO | changeMoney | moneyFilter}}</span>
              </template>
            </el-table-column>
            <el-table-column label="本位币金额" class-name="table_font_black" align='right'>
              <template slot-scope="{ row }">
                <span>{{ row.amount | changeMoney | moneyFilter}}</span>
              </template>
            </el-table-column>
          </template>
        </el-table>
        <el-tab-pane label="科目余额" name="subjectTab"></el-tab-pane>
        <el-tab-pane label="科目标签余额" name="tagTab" :disabled="!tagIds"></el-tab-pane>
      </el-tabs>
      <div slot="footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAccountBalanceSheet, getSubjectTagBalanceList } from '@/api/book';
import dayjs from 'dayjs';
import { find, get } from 'lodash-es';
import cloneDeep from 'lodash-es/cloneDeep';

export default {
  props: {
    accountBalanceVisible: {
      type: Boolean,
      required: true,
    },
    subjectCode: {
      type: String,
      required: true,
    },
    tagIds: {
      type: String,
      default: '',
    },
    subjectId: {
      type: String,
      default: '',
    },
    accountPeriod: {
      type: Date,
      required: true,
    },
  },
  data() {
    return {
      tableData: [],
      defaultData: [
        {
 keyName: '期初-借方', quantity: 0, amount: 0, amountO: 0, key: 'initialDebitAmount',
},
        {
 keyName: '期初-贷方', quantity: 0, amount: 0, amountO: 0, key: 'initialCreditAmount',
},
        {
 keyName: '本期发生额-借方', quantity: 0, amount: 0, amountO: 0, key: 'currentDebitAmount',
},
        {
 keyName: '本期发生额-贷方', quantity: 0, amount: 0, amountO: 0, key: 'currentCreditAmount',
},
        {
 keyName: '本年累计发生额-借方', quantity: 0, amount: 0, amountO: 0, key: 'yearDebitAmount', showYear: true,
},
        {
 keyName: '本年累计发生额-贷方', quantity: 0, amount: 0, amountO: 0, key: 'yearCreditAmount', showYear: true,
},
        {
 keyName: '期末-借方', quantity: 0, amount: 0, amountO: 0, key: 'endingDebitAmount',
},
        {
 keyName: '期末-贷方', quantity: 0, amount: 0, amountO: 0, key: 'endingCreditAmount',
},
      ],
      showData: [],
      currencyCode: '',
      currencyOptions: [{ currencyCode: '', currencyName: '综合本位币' }],
      qtySwitch: false,
      yearSwitch: true,
      negate: false,
      crossMonth: [],
      activeName: 'subjectTab',
      tagIdMatchLogic: 1,
    };
  },
  computed: {
    subjectFullName() {
      if (!this.subjectCode) return '';
      return find(this.$store.state.selectData.subjectList, { subjectCode: this.subjectCode })?.subjectFullName ?? '';
    },
    // 本位币
    functionalCurrency() {
      return this.$store.state.user.companyInfo.functionalCurrency;
    },
    foreignCurrency() {
      return !(this.currencyCode === this.functionalCurrency || this.currencyCode === '');
    },
  },
  methods: {
    getParams() {
      const {
 subjectCode, negate, yearSwitch, qtySwitch, currencyCode,
} = this;
      const params = {
        subjectCodes: subjectCode,
        groupTotal: 1,
        currencyCode,
        beginMonth: dayjs(this.crossMonth[0]).format('YYYY-MM'),
        endMonth: dayjs(this.crossMonth[1]).format('YYYY-MM'),
        qtySwitch: qtySwitch ? 1 : 0,
        yearSwitch: yearSwitch ? 1 : 0,
        negate,
        pageSize: 99999,
      };
      return params;
    },
    handleClick(tab) {
      this.tableData = [];
      if (tab === 'tagTab') {
        const params = {
          accountPeriodBegin: dayjs(this.crossMonth[0]).format('YYYYMM'),
          accountPeriodEnd: dayjs(this.crossMonth[1]).format('YYYYMM'),
          currencyType: '',
          subjectId: this.subjectId,
          reversalNegative: this.negate,
          tagIdMatchLogic: this.tagIdMatchLogic,
          tagIds: this.tagIds,
          pageSize: 99999,
        };
        getSubjectTagBalanceList(params).then((res) => {
          const rspData = get(res, 'data.data[0]');
          if (rspData) {
            const lendingDirection = rspData?.lendingDirection || 1;
            this.handleRspData(rspData, lendingDirection);
          }
        });
      } else {
        const { subjectHashTable } = this.$store.state.selectData;
        const params = this.getParams();
        getAccountBalanceSheet(params).then((res) => {
          const rspData = get(res, 'data.data[0]');
          const subject = subjectHashTable.items(rspData?.subjectFullName) || {};
          const { lendingDirection } = subject;
          if (rspData) {
            this.handleRspData(rspData, lendingDirection);
          }
        });
      }
    },
    handleRspData(rspData, lendingDirection) {
      const { yearSwitch, defaultData, activeName } = this;
      const showData = cloneDeep(defaultData);
      const rspObj = [
        ['initialDebitAmount', 'initialDebitQty', 'initialDebitAmountO'],
        ['initialCreditAmount', 'initialCreditQty', 'initialCreditAmountO'],
        ['currentDebitAmount', 'currentDebitQty', 'currentDebitAmountO'],
        ['currentCreditAmount', 'currentCreditQty', 'currentCreditAmountO'],
        ['yearDebitAmount', 'yearDebitQty', 'yearDebitAmountO'],
        ['yearCreditAmount', 'yearCreditQty', 'yearCreditAmountO'],
        ['endingDebitAmount', 'endingDebitQty', 'endingDebitAmountO'],
        ['endingCreditAmount', 'endingCreditQty', 'endingCreditAmountO'],
      ];
      if (activeName === 'tagTab') {
          showData.forEach((item, index) => {
            const keyAry = rspObj[index];
            item.amount = rspData[keyAry[0]];
            item.quantity = rspData[keyAry[1]];
          });
      } else {
        showData.forEach((item, index) => {
          const keyAry = rspObj[index];
          item.amount = rspData[keyAry[0]];
          item.amountO = rspData[keyAry[2]];
          if (index >= 2 < 6) {
            item.quantity = rspData[keyAry[1]];
          }
        });
        // showData[0].amount = rspData.initialDebitAmount || 0    // 期初-借方: 金额、本位币金额
        // showData[0].amountO = rspData.initialDebitAmountO || 0  // 期初-借方: 原币金额
        // showData[1].amount = rspData.initialCreditAmount || 0   // 期初-贷方: 金额、本位币金额
        // showData[1].amountO = rspData.initialCreditAmountO || 0 // 期初-贷方: 原币金额
        // showData[2].amount = rspData.currentDebitAmount || 0    // 本期发生额-借方: 金额、本位币金额
        // showData[2].amountO = rspData.currentDebitAmountO || 0  // 本期发生额-借方: 原币金额
        // showData[3].amount = rspData.currentCreditAmount || 0   // 本期发生额-贷方: 金额、本位币金额
        // showData[3].amountO = rspData.currentCreditAmountO || 0 // 本期发生额-贷方: 原币金额
        // showData[4].amount = rspData.yearDebitAmount || 0     // 本年累计发生额-借方: 金额、本位币金额
        // showData[4].amountO = rspData.yearDebitAmountO || 0   // 本年累计发生额-借方: 原币金额
        // showData[5].amount = rspData.yearCreditAmount || 0    // 本年累计发生额-贷方: 金额、本位币金额
        // showData[5].amountO = rspData.yearCreditAmountO || 0  // 本年累计发生额-贷方: 原币金额
        // showData[6].amount = rspData.endingDebitAmount || 0   // 期末-借方: 金额、本位币金额
        // showData[6].amountO = rspData.endingDebitAmountO || 0  // 期末-借方: 原币金额
        // showData[7].amount = rspData.endingCreditAmount || 0  // 期末-贷方: 金额、本位币金额
        // showData[7].amountO = rspData.endingCreditAmountO || 0 // 期末-贷方: 原币金额
        if (lendingDirection === 1) { // 科目方向 （1借 2贷） 影响 期初数量 期末数量
          showData[0].quantity = rspData.initialQty; // 期初-借方+数量
          showData[6].quantity = rspData.endingQty; // 期末-借方 + 数量
        } else {
          showData[1].quantity = rspData.initialQty; // 期初-贷方+数量
          showData[7].quantity = rspData.endingQty; // 期末-贷方 + 数量
        }
        // showData[2].quantity = rspData.currentDebitQty  // 本期发生额数量-借方
        // showData[3].quantity = rspData.currentCreditQty // 本期发生额数量-贷方
        // showData[4].quantity = rspData.yearDebitQty  // 本年累计数量-借方
        // showData[5].quantity = rspData.yearCreditQty  // 本年累计数量-贷方
      }

      this.tableData = yearSwitch
        ? [...showData]
        : [...showData].filter((item) => !item.showYear);
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },
    // 币别数据
    initcurrency() {
      if (this.currencyOptions.length >= 2) return;
      this.$http.get('/rest/companyConfig/companyBasis/currency/v1.0/droplist').then(({ data: { data } }) => {
        this.currencyOptions.push(...data);
      });
    },
    async handleOpened() {
      this.crossMonth = [this.accountPeriod, this.accountPeriod];
      this.activeName = 'subjectTab';
      this.initcurrency();
      this.handleClick(this.activeName);
    },
    handleClose() {
      this.currencyCode = '';
      this.qtySwitch = false;
      this.yearSwitch = true;
      this.negate = false;
      this.tagIdMatchLogic = 1;
      this.$emit('update:accountBalanceVisible', false);
    },
  },
};

</script>
<style lang="scss" scoped>
  .textClass {
    font-size: 16px;
    font-weight: bold;
  }
</style>
