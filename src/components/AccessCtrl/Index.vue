<script>
import { get, merge } from 'lodash-es';
import store from '@/store';

export default {
  props: {
    // 收费码
    vipCode: {
      type: Array,
      default: () => [],
    },
    // 代账公司层级的功能权限码
    BKCode: {
      type: Array,
      default: () => [],
    },
    // 账套层级的功能权限码
    comCode: {
      type: Array,
      default: () => [],
    },
    // 锁定数据, 只对启用了结账功能的账套生效，表现形式为禁用
    lockDataMonth: {
      type: [Date, String, Number, Array],
      default: '',
    },
    // 额外的控制禁用，参数结构 [boolean]，false 是开放  true 是禁用
    customsD: {
      type: [Array],
      default: () => [],
    },
    // 项目配置的显示控制, 例：trade.voucher ,该配置的取值范围 'hide' | 'disabled' | 'show'
    configPath: {
      type: String,
    },
    // 是否为 增删改 类型的按钮
    // 默认为 false
    isCUD: {
      type: <PERSON>olean,
      default: false,
    },
  },
  computed: {
    state() {
      if (
          !this.checkVipCode()
          || !this.checkFrontendSeqCode()
          || this.getConfig() === 'hide'
        ) {
        return 'hide';
      }
      if (
        this.customsD.some((item) => item)
        || this.checkLockData()
        || this.getConfig() === 'disabled'
        || (this.isCUD && !this.checkCanCUD())
      ) {
        return 'disabled';
      }

      return 'show';
    },
  },
  methods: {
    checkVipCode() {
      if (this.vipCode.length === 0) return true;
      const vipCodeBkCom = store.getters['user/vipCodeBkCom'];
      const hasVipCode = this.vipCode.some((item) => vipCodeBkCom.includes(item));
      return hasVipCode;
    },
    // 检查是否能进行增删改
    checkCanCUD() {
      // 判断公司是否已经到期
      // 判断账套是否为只读模式
      const { isReadOnlyCom, isComOutOfDate } = store.getters;
      return !isReadOnlyCom && !isComOutOfDate;
    },
    checkFrontendSeqCode() {
      if (this.comCode.length === 0) return true;
      const hasFontentPermission = store.getters['user/hasFontentPermission'];
      const hasPermission = hasFontentPermission(this.comCode, false);
      return hasPermission;
    },
    checkBKSeqCode() {
      if (this.BKCode.length === 0) return true;
      const hasFontentPermission = store.getters['user/hasFontentPermission'];
      const hasPermission = hasFontentPermission(this.BKCode, true);
      return hasPermission;
    },
    getConfig() {
      if (!this.configPath) return 'show';
      return get(store.state.app.appConfig, this.configPath);
    },
    checkLockData() {
      if (!this.lockDataMonth) return false;
      return store.getters.isLockDataMonth(this.lockDataMonth);
    },
    checkCustomDisabled() {

    },
    setAllDisabled() {
      this.$slots.default.forEach((item) => {
        if (item.componentOptions?.propsData) {
          item.componentOptions.propsData.disabled = true;
        } else {
          item.data.attrs = merge(item.data.attrs, { disabled: true });
        }
      });
    },
  },
  render() {
    if (this.state === 'hide') {
      return this.$slots.skeleton || undefined;
    }
    if (this.state === 'disabled') {
      this.setAllDisabled();
      return this.$slots.default;
    }
    return this.$slots.default;
  },
};
</script>
