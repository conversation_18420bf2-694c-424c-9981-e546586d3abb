<template>
  <el-dialog
    top="15%"
    title="新增科目"
    custom-class="CreateSubjectDialog"
    :center="false"
    append-to-body
    :visible.sync="dialogVisible"
    :modal-append-to-body="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="onClickCancel">

    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="72px"
      @keyup.enter.native="onPressEnter">

      <el-form-item
        label="上级科目"
        prop="parentSubjectCode">
        <el-select
          style="width:100%"
          :filterable="true"
          v-model="formData.parentSubjectCode">
          <el-option
            v-for="item in parentSubjects"
            :key="item.subjectCode"
            :value="item.subjectCode"
            :label="labelRender(item)">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item
        label="科目名称"
        prop="subjectName"
        v-if="!isBankDeposit">
        <el-input
          placeholder="请输入科目名称"
          v-model.trim="formData.subjectName">
        </el-input>
      </el-form-item>

      <el-form-item
        label="开户行"
        prop="bankName"
        v-if="isBankDeposit">
        <el-input
          placeholder="请输入开户行"
          v-model.trim="formData.bankName">
        </el-input>
      </el-form-item>

      <el-form-item
        label="银行账号"
        prop="bankAccount"
        v-if="isBankDeposit">
        <el-input
          type="number"
          placeholder="请输入银行账号"
          v-model.trim="formData.bankAccount">
        </el-input>
      </el-form-item>

      <el-form-item label="单位核算" prop="isUnit">
        <el-col class="fl" :span="5">
          <el-form-item prop="isUnit">
            <el-checkbox-group
              v-model="formData.isUnit"
              @change="onUnitChange">
              <el-checkbox label="是"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col class="fl" :span="11">
            <el-form-item
              prop="unit"
              v-if="formData.isUnit.indexOf('是') !== -1">
              <UnitSelect v-model="formData.unit" />
          </el-form-item>
        </el-col>
      </el-form-item>

      <el-form-item label="外币核算" prop="isCurrency">
        <el-col :span="5" style="float: left;">
          <el-form-item prop="isCurrency">
            <el-checkbox-group
              v-model="formData.isCurrency"
              @change="onCurrencyChange">
              <el-checkbox label="是"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="11" style="float: left;">
          <el-form-item prop="endingExchange">
            <el-checkbox-group v-model="formData.endingExchange">
              <el-checkbox label="期末调汇"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-form-item>

      <el-form-item
        label="币别"
        prop="currencyType"
        v-if="formData.isCurrency.indexOf('是') !== -1">
        <el-checkbox-group v-model="formData.currencyType">
          <el-checkbox
            v-for="item in foreignCurrencies" name="currencyType"
            :key="item.currencyCode"
            :label="item.currencyName"
            :value="item.currencyName"
            :class="{ 'display-none' : item.currencyCode === functionalCurrency }">
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>

    <div slot="footer" style="text-align:center;">
      <el-button type="primary" @click="onClickConfirm">确 定</el-button>
      <el-button @click="onClickCancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import get from 'lodash-es/get';
import filter from 'lodash-es/filter';
import find from 'lodash-es/find';

import UnitSelect from '@/components/commonSelect/unitSelect/UnitSelect.vue';
import { insertSubject } from '@/helper/subject';

export default {
  name: 'CreateSubjectDialog',

  components: {
    UnitSelect,
  },

  props: {
    // 当前对话框是否展示
    visible: {
      type: Boolean,
      default: false,
    },

    // 父科目编号
    parentSubjectCode: {
      type: String,
    },
  },

  data() {
    return {
      dialogVisible: false,

      unitList: [],

      formData: {
        sellerName: '',
        sellerType: '',
        parentSubjectCode: this.parentSubjectCode,
        subjectName: '',
        bankName: '',
        bankAccount: '',
        invAccountFlag: false,
        isUnit: [],
        unit: '',
        resource: '启用',
        isCurrency: [],
        currencyType: [],
        endingExchange: [],
        saleunitName: '',
        enabledDecimal: false,
      },

      rules: {
        sellerType: [
          { required: true, message: '请输入类型', trigger: 'change' },
        ],
        sellerName: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          {
            type: 'string', max: 100, message: '名称长度最多100个字符', trigger: 'blur',
          },
        ],
        subjectName: [
          { required: true, message: '请输入科目名称', trigger: 'blur' },
          { validator: this.validateSubjectName, trigger: 'blur' },
        ],
        parentSubjectCode: [
          {
            required: true, type: 'string', message: '请选择上级科目', trigger: 'change',
          },
          // { type: 'string', validator: this.validateSubjectFullNameTpl, trigger: "change" },
        ],
        bankName: [
          {
            required: true, type: 'string', message: '请输入开户行', trigger: 'blur',
          },
          { validator: this.validateBankName, trigger: 'blur' },
        ],
        bankAccount: [
          {
            required: true, type: 'string', message: '请输入银行账号', trigger: 'blur',
          },
          { validator: this.validateBankAccount, trigger: 'blur' },
        ],
        unit: [
          { required: true, message: '请选择计量单位', trigger: 'change' },
        ],
        saleunitName: [
          { required: true, message: '请输入单位名称', trigger: 'blur' },
        ],
      },
    };
  },

  computed: {
    // 父科目列表（一级、二级）
    parentSubjects() {
      const isParent = (item) => item.subjectCode.length <= 8;
      const subjects = filter(this.$store.state.selectData.subjectList, isParent);
      return subjects;
    },

    // 外币列表
    foreignCurrencies() {
      const isForeign = (item) => item.currencyCode !== this.functionalCurrency;
      const currencies = filter(this.$store.state.selectData.currencyList, isForeign);
      return currencies;
    },

    // 当前父科目是否为银行存款
    isBankDeposit() {
      return (+this.formData.parentSubjectCode) === 1002;
    },
    functionalCurrency() {
      return this.$store.state.user.companyInfo.functionalCurrency;
    },
  },

  watch: {
    visible(value) {
      this.dialogVisible = value;
    },
    dialogVisible(value) {
      if (!value) {
        this.formData.parentSubjectCode = '';
        this.formData.subjectName = '';
      }
      this.$emit('update:visible', value);
    },
  },

  mounted() {
  },

  methods: {
    // 点击取消时
    onClickCancel() {
      this.dialogVisible = false;
    },

    // 点击确定时
    onClickConfirm() {
      this.create();
    },

    // 按 Enter 键时
    onPressEnter() {
      this.onClickConfirm();
    },

    // 单位变化时
    onUnitChange(value) {
      if (value.length === 0) this.formData.unit = '';
    },

    // 外币变化时
    onCurrencyChange(value) {
      if (value.length === 0) {
        this.formData.currencyType = [];
        this.formData.isCurrency = [];
      }
    },

    // 科目选项文字
    labelRender(subject) {
      return `${subject.subjectCode} ${subject.subjectFullName}`;
    },

    async create() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return;
        const subject = find(this.parentSubjects,
          { subjectCode: this.formData.parentSubjectCode });
        const subjectFullName = get(subject, 'subjectFullName');
        const subjectParentCode = get(subject, 'subjectCode');
        const data = {
          subjectName: this.formData.subjectName,
          subjectParentName: subjectFullName,
          subjectParentCode,
          subjectFullName: `${subjectFullName}-${this.formData.subjectName}`,
        };

        if (this.formData.isUnit.indexOf('是') !== -1) {
          data.saleunitName = this.formData.unit;
        }

        if (this.formData.isCurrency.indexOf('是') !== -1) {
          data.currencies = filter(this.foreignCurrencies,
            (item) => item.currencyCode !== this.functionalCurrency
              && this.formData.currencyType.indexOf(item.currencyName) !== -1);
        }

        if (this.isBankDeposit) {
          data.bankName = this.formData.bankName;
          data.bankAccount = this.formData.bankAccount;
          data.subjectName = `${this.formData.bankName}_${this.formData.bankAccount}`;
          data.subjectFullName = `银行存款-${this.formData.bankName}_${this.formData.bankAccount}`;
        }

        data.endingExchange = this.formData.endingExchange
          ? 1
          : 0;
        await insertSubject([data])
          .then(async (response) => {
            this.dialogVisible = false;
            this.$message.success('操作成功！');
            this.$emit('created',
              get(response, 'data.data[0]'),
              { parentSubjectCode: this.formData.parentSubjectCode });
          });
      });
    },

    // 表单重置
    resetData() {
      if (this.$refs.form) this.$refs.form.resetFields();
    },

    // 科目名称校验
    validateSubjectName(rule, value, callback) {
      if (value && value.length > 100) {
        callback(new Error('科目名称不能超过100个字符'));
        return;
      }
      callback();
    },

    // 开户行名称校验
    validateBankName(rule, value, callback) {
      if (value && (value.length > 24)) {
        callback(new Error('开户行不能超过二十四个字'));
      } else {
        callback();
      }
    },

    // 银行账号校验
    validateBankAccount(rule, value, callback) {
      const validateBankAccountRules = !/^\d{1,25}$/.test(value);
      if (value && validateBankAccountRules) {
        callback(new Error('请输入1位到25位数字的银行账号'));
      } else {
        callback();
      }
    },
  },
};
</script>

<style lang="scss">
.CreateSubjectDialog {
  width: 400px;
}
</style>
