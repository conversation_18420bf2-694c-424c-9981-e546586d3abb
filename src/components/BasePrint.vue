<!--
 * @Description: 明细账打印
 * @Date: 2019-08-29 20:16:45
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2019-08-30 16:38:15
 -->
<script>
import cloneDeep from 'lodash-es/cloneDeep';
import { max } from 'lodash-es';
import { deepClone } from '@/assets/Utils';

export default {
  props: {
    tableColumnName: Array,
    tableData: {
      type: Array,
    },

    breakKey: String,

    printVisible: {
      type: Boolean,
    },

    crossMonth: {
      type: [Array, Date],
    },
    // // 打印纸张的方向 横向 还是纵向  的高度 （ps 默认：明细账 纵向 true）（数量金额明细账 横向 false）
    isPortrait: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      tableDatas: [],
      pageNumber: [],
      computedTableHeight: 0,
    };
  },

  computed: {
    needMakeChunck() {
      return false; // 是否需要分块，false时外部传入的数据要处理好，不然无法正常显示是否需要分块，false时外部传入的数据要处理好，不然无法正常显示
    },
    pageNumberLastNum() {
      return this.pageNumber[this.pageNumber.length - 1] || 0;
    },
  },
  mounted() {
    // 搜狗不支持打印预览
    if (Lib.Utils.printWarning()) {
      this.$alert('当前浏览器不支持打印预览，如需打印预览功能，请更换其他浏览器', '温馨提示', {
        confirmButtonText: '确定',
      });
      return;
    }
    // 没分块
    this.printContentByComputed(this.tableData);
  },

  methods: {
    vaildPrintState() {
      return true;
    },
    // 计算表格高度， 按A4纸的比例计算， 21 * 29.7；
    computerTableHeight(bodyWidth) {
      if (!this.isPortrait) {
        return bodyWidth * 0.52;
      }
      return bodyWidth / 0.8;
    },
    printContentByComputed(tableData) {
      this.tableDatas = [tableData];
      tableData = deepClone(tableData);
      this.$nextTick(async () => {
        const dom = document.getElementById('printTable1');
        Lib.Utils.printContent(
          dom.outerHTML,
          true,
          false,
          dom.offsetWidth,
          dom.offsetHeight,
        );
        this.$nextTick(() => {
          const iframe = document.getElementById('printIframe').contentDocument;
          const bodyWrappers = iframe.querySelectorAll(
            '.el-table__body-wrapper',
          );

          // 记录每一行的高度
          let heights = [];
          bodyWrappers.forEach((bodyWrapper) => {
            if (heights.length === 0) {
              heights = [...bodyWrapper.querySelectorAll('tr')].map(
                (el) => el.offsetHeight,
              );
            } else {
              [...bodyWrapper.querySelectorAll('tr')].forEach((el, index) => {
                heights[index] = max([heights[index], el.offsetHeight]);
              });
            }
          });
          this.tableDatas = [];
          let defaultNumber = 1;
          this.pageNumber = [];
          let currArr = [];
          let currHeight = 0;
          const bodyWidth = bodyWrappers[0].querySelector('table').offsetWidth;
          this.computedTableHeight = this.computerTableHeight(bodyWidth);
          console.log(bodyWidth, this.computedTableHeight);
          heights.forEach((rowHeight, index) => {
            // 记录行高属性， 用于切分表同步行高
            this.$set(tableData[index], 'rowHeight', rowHeight);
            tableData[index].rowHeight = rowHeight;
            currArr.push(tableData[index]);
            currHeight += rowHeight;
            if (tableData[index][this.breakKey] === true || (currHeight + rowHeight) > this.computedTableHeight) {
              this.tableDatas.push([...currArr]);
              this.pageNumber.push(defaultNumber);
              defaultNumber += 1;
              currHeight = 0;
              currArr = [];
            }
          });
          if (currArr.length !== 0) {
            this.tableDatas.push([...currArr]);
            this.pageNumber.push(defaultNumber);
          }
          this.$nextTick(() => {
            if (this.vaildPrintState()) {
              Lib.Utils.printContent(
                dom.outerHTML,
                true,
                true,
                dom.offsetWidth,
                dom.offsetHeight,
              );
            }

            this.$emit('hide');
          });
        });
      });
    },
    setRowHeight({ row }) {
      return {
        height: `${row.rowHeight}px`,
      };
    },
  },
};
</script>
