<!--
 * @Description: 可编辑凭证预览
 * @version: 过验证eslint
 * @Company: 海闻软件
 * @Author: 晓荣
 * @LastEditors: 晓荣
-->
<template>
  <transition name="slide-fade">
    <el-card ref="card" v-show="cardVisible" class="left-form">
      <div slot="header" class="header">
        <div class="content">
          <div class="title">
            <span>凭证生成预览</span>
            <span v-if="hasFormBill && billAttr.billCode" class="billCode"> 单据号码：{{ billAttr.billCode }} </span>
          </div>
          <el-popover class="custom_popover" trigger="hover">
            <h3>快捷键操作</h3>
            <el-table border highlight-current-row :data="illustration">
              <el-table-column prop="funcName" label="功能" align="left" :width="$store.state.ColsWidth.w15"> </el-table-column>
              <el-table-column prop="shortcutKey" label="快捷键" align="left" :width="$store.state.ColsWidth.w15"> </el-table-column>
            </el-table>
            <template slot="reference">
              <i class="iconfont icon-jianpan"></i>
            </template>
          </el-popover>
          <el-button type="text" class="close" @click="cancelVoucher">
            <i class="el-icon-close"></i>
          </el-button>
        </div>
      </div>
      <el-form ref="form" :model="commonVInfo" :rules="rules" label-width="80px">
        <el-row :gutter="2">
          <el-col :span="6">
            <el-form-item prop="voucherCode" label="凭证编号">
              <el-input :value="commonVInfo.voucherCode" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="voucherTime" label="凭证日期">
              <el-date-picker type="date" v-model="commonVInfo.voucherTime" :editable="false" :picker-options="timePickerOptions"> </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <form-table
      ref="formTable"
      :table-data="voucherData.items"
      :fullHeight="false"
      :addFirstline="true"
      :voucher="commonVInfo"
      @saveVoucher="createVoucher"> </form-table>
      <div class="action_bar">
        <el-button type="primary" @click="createVoucher">确认生成</el-button>
        <el-button @click="cancelVoucher">取 消</el-button>
      </div>
    </el-card>
  </transition>
</template>

<script>
import dayjs from 'dayjs';
import isEmpty from 'lodash-es/isEmpty';
import cloneDeep from 'lodash-es/cloneDeep';
import FormTable from '@/pages/finance/voucherDetails/components/FormTable.vue';

export default {
  components: {
    FormTable,
  },
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    origin: {
      // 凭证预览来源
      type: String,
    },
    billAttr: {
      // 单据预览时传进来的
      type: Object,
    },
    voucherData: {
      // 凭证详情
      type: Object,
      required: true,
    },
  },
  data() {
    const voucherTime = new Date();
    voucherTime.setDate(1);
    voucherTime.setMonth(voucherTime.getMonth() + 1);
    voucherTime.setDate(0);

    return {
      subjectData: [],
      commonVInfo: {
        voucherCode: '',
        voucherTime,
        voucherType: '',
      },
      currentMonthStr: voucherTime.Format('yyyy-MM'),
      timePickerOptions: {
        disabledDate: (date) => date.Format('yyyy-MM') > this.currentMonthStr,
      },
      illustration: [
        {
          funcName: '确认生成',
          shortcutKey: 'Enter',
        },
        {
          funcName: '自动找平',
          shortcutKey: '=',
        },
        {
          funcName: '增加一行空白科目栏',
          shortcutKey: 'Insert',
        },
        {
          funcName: '下一个输入项',
          shortcutKey: 'pagedown',
        },
        {
          funcName: '上一个输入项',
          shortcutKey: 'pageup',
        },
      ],
      rules: {
        voucherTime: [
          {
            required: true,
            type: 'date',
            message: '请选择凭证日期',
            trigger: 'change',
          },
        ],
      },
    };
  },
  computed: {
    cardVisible: {
      get() {
        return this.visible;
      },
      set(isVisible) {
        if (this.visible && !isVisible) {
          this.$emit('hide');
        }
      },
    },
    hasFormBill() {
      return this.origin === 'bill';
    },
    // vouchers() {
    //   const vouchersData = cloneDeep(this.voucherData.vouchers || []);
    //   const items = isEmpty(vouchersData) ? [] : vouchersData[0].items;
    //   return this.hasFormBill ? vouchersData : items;
    // },
    $card() {
      return this.$refs.card.$el;
    },
  },
  mounted() {
    this.dKeyDown = document.onkeydown;
  },
  destroyed() {
    document.onkeydown = this.dKeyDown;
  },
  watch: {
    async cardVisible(visible) {
      if (visible) {
        this.setVoucherData();
      }
      this.$el.querySelector('.el-card__body').scrollTop = 0;
      document.onkeydown = visible ? this.keyDown : this.dKeyDown;
    },
  },
  methods: {
    setVoucherData() {
      if (this.hasFormBill) {
        this.handleBillData();
      } else if (this.origin === 'asset' || this.origin === 'assetChange') {
        const { voucherCode, voucherTime, voucherType } = this.voucherData;
        this.commonVInfo = {
          voucherCode,
          voucherTime: new Date(voucherTime),
          voucherType,
        };
      }
    },
    createVoucher(valDate) {
      if (this.origin === 'bill') {
        this.handleSaveBillData(valDate);
      } else if (this.origin === 'asset') {
        this.handleSavaAssetData();
      } else if (this.origin === 'assetChange') {
        this.assetChangeCreateVoucher();
      }
    },
    // 资产变更时的生成凭证
    async assetChangeCreateVoucher() {
      const isCheck = this.$refs.formTable.checkTableIsValid();
      if (isCheck) {
        const postData = cloneDeep(this.voucherData);
        const params = {};
        params.assetId = postData.assetId;
        params.assetItemsId = postData.assetItemsId;
        params.categoryType = postData.categoryType;
        params.changeId = postData.changeId;
        if (postData.isGreater) {
          this.$refs.formTable.subjectChanged(this.$refs.formTable.tableData[0]);
        } else {
          this.$refs.formTable.subjectChanged(this.$refs.formTable.tableData[1]);
        }
        postData.items = this.$refs.formTable.tableData.filter((items) => items.subjectCode);
        delete postData.assetId;
        delete postData.assetItemsId;
        delete postData.categoryType;
        delete postData.changeId;
        delete postData.isGreater;
        await this.$http.post('/rest/companyAccount/merge/fixedAssetsCard/v0.1/change/voucher', postData, { params });
        this.$emit('success');
        this.$message.success('操作成功');
        this.$emit('hide');
      }
    },
    // 处理数据 到凭证详细界面时， 属期， 科目名 有变化处理
    handleBillData() {
      const { billDate } = this.voucherData;
      const today = billDate ? new Date(billDate) : new Date();
      const { form, formTable } = this.$refs;
      const { subjectHashTable } = this.$store.state.selectData;
      if (form) {
        form.resetFields();
      }
      if (formTable && this.voucherData.items.length < 2) {
        this.$nextTick(() => formTable.addItem.call(formTable));
      }
      today.setDate(1);
      today.setMonth(today.getMonth() + 1);
      today.setDate(0);
      this.currentMonthStr = (this.commonVInfo.voucherTime = today).Format('yyyy-MM');
      this.getVoucherCode();
      this.$nextTick(() => {
        const isTransit = this.billAttr.isTransit || false;
        const { billType } = this.billAttr;
        if (isTransit && billType === 2) {
          const lpt = this.voucherData.items
            .filter((item) => item.subjectFullName !== '')
            .map((item) => {
              const subjectFullName = `发出商品-${item.subjectFullName.split('库存商品-')[1]}`;
              const subjectCode = (subjectHashTable.items(subjectFullName) && subjectHashTable.items(subjectFullName).subjectCode) || '';
              return {
                ...item,
                voucherDirection: 1,
                subjectFullName: `发出商品-${item.subjectFullName.split('库存商品-')[1]}`,
                subjectCode,
              };
            });
          this.voucherData.items = [...lpt, ...this.voucherData.items];
        }
      });
    },
    //  单据生成 凭证 根据凭证日期，获取凭证编号
    getVoucherCode() {
      let {
        commonVInfo: { voucherTime },
      } = this;
      voucherTime = voucherTime.Format('yyyy-MM-dd');
      this.$http.get('/rest/proxy/account/bill/v1.0/voucherCode', { params: { voucherTime } }).then(
        ({
          data: {
            data: [voucherCode],
          },
        }) => {
          this.commonVInfo.voucherCode = voucherCode;
        },
      );
    },
    //  单据生成 凭证
    handleSaveBillData(valDate) {
      let items = null;
      let coverBill = false;
      items = this.$refs.formTable.checkTableIsValid();
      this.$refs.form.validate((valid) => {
        if (!valid || !items) {
          return false;
        }
        const jsonData = Lib.Utils.deepClone(this.commonVInfo);
        jsonData.items = items.map((item) => {
          let subjectName = item.subjectName || '';
          if (!subjectName) {
            subjectName = (item.subjectFullName && item.subjectFullName.split('-')[1]) || '';
          }
          item = { ...item, subjectName };
          delete item.path;
          return item;
        });
        jsonData.voucherTime = jsonData.voucherTime.Format('yyyy-MM-dd');
        // 方法：确认操作覆盖出入仓单
        // 默认 coverBill = false； 不会覆盖
        const fn = () => {
          this.$http.post(`/rest/proxy/account/bill/v1.0/voucher/${this.billAttr.billId}?coverBill=${coverBill}`, JSON.stringify(jsonData)).then(({ data: json }) => {
            if (json.returnCode === 1) {
              this.$confirm('该操作将会覆盖原有的出入仓单据，是否继续操作', '提示', {
                confirmButtonText: '是',
                cancelButtonText: '否',
                type: 'warning',
              }).then(() => {
                this.handleSaveBillData('true');
              });
            } else {
              this.$emit('success');
              this.$message.success('凭证新增成功');
              this.$emit('hide');
            }
          });
        };
        // 传入值 ‘true’  确认覆盖 coverBill = true;
        if (valDate === 'true') {
          coverBill = true;
          fn();
        } else if (this.billAttr.billType === 5) {
          // 确认调拨单生成对应的出入仓单
          this.$confirm('调拨单生成凭证同时将根据调拨单生成对应的出入仓单，是否确认生成凭证？', '提示', {
            confirmButtonText: '是',
            cancelButtonText: '否',
            type: 'warning',
          }).then(() => {
            fn();
          });
        } else {
          fn();
        }
        return false;
      });
    },
    // 资产生成 凭证
    handleSavaAssetData() {
      const params = {
        ...this.commonVInfo,
        ...this.voucherData,
        vouchers: this.voucherData.items.filter((item) => item.subjectFullName),
        voucherTime: dayjs(this.commonVInfo.voucherTime).format('YYYY-MM-DD'),
      };
      this.$emit('success', params);
    },
    cancelVoucher() {
      this.cardVisible = false;
    },
    // 特殊按键处理
    keyDown(e) {
      const key = e.keyCode || e.which;
      const elem = e.srcElement || e.target;
      const { $card } = this;
      if (key === 13) {
        e.preventDefault();
        if (!(this.dialogVisible || $.contains($card, elem))) {
          this.createVoucher.call(this);
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.billCode {
  color: #c0192a;
  margin-left: 30px;
}
.custom_popover {
  position: relative;
  width: 30px;
  .icon-jianpan {
    position: absolute;
    top: 50%;
    color: #2d4d7b;
    font-size: 20px;
    transform: translate(6px, -50%);
    -ms-transform: translate(6px, -50%);
    -moz-transform: translate(6px, -50%);
    -webkit-transform: translate(6px, -50%);
    -o-transform: translate(6px, -50%);
  }
}
</style>
