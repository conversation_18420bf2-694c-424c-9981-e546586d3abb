<template>

  <el-popover placement="bottom" width="416" v-model="popoverVisible" popper-class="company-picker-popover"
    trigger="click">
    <div class="company-picker-content">
      <el-input class="company-search-input" placeholder="输入账套名称搜索" v-model="filterText" clearable>
        <el-select v-model="selectFilterType" clearable slot="prepend" placeholder="全部">
          <el-option label="只看“记”" value="1"></el-option>
          <el-option label="只看“查”" value="2"></el-option>
          <el-option label="只看已过期" value="3"></el-option>
          <el-option label="只看停用" value="5"></el-option>
        </el-select>
        <!-- <i slot="prefix" class="el-input__icon el-icon-search"></i> -->
      </el-input>
      <VList v-if="filteredCompanyList.length > 0" :data="filteredCompanyList" width="400px" height="300px"
        :fixedItemSize="50" v-slot:default="{ vlistItemData, vlistItemKey }">
        <div class="company-item" @click="onClickItem(vlistItemData)">
          <div class="company-item-name">
            {{ vlistItemData.companyName }}
          </div>
          <div class="company-item-status">
            <CompanyStatusTag :company="vlistItemData" />
          </div>
        </div>
      </VList>
      <div class="empty-text" v-if="filteredCompanyList.length === 0">无符合条件数据</div>
    </div>

    <div class="goListPageBtn" @click="goBookList">
      <i class="el-icon-back"></i>
      返回账套列表页
    </div>
    <div slot="reference" class="flex-h company-picker">
      <div class="activeCompanyText flex-fluid">
        {{ activeCompanyName }}
      </div>
      <div class="arrow-icon flex-fixed">
        <i class="el-icon-arrow-down"></i>
      </div>
      <div class="split-line flex-fixed"></div>

      <CompanyStatusTag :company="activeCompanyInfo" style="margin-top: 6px;" class="flex-fixed" />

    </div>
  </el-popover>

</template>
<script setup>
import {
  ref, onMounted, computed, nextTick,
} from 'vue';
import { Loading, MessageBox, Message } from 'element-ui';
import dayjs from 'dayjs';
import { getCompanyList, getCompanyListByGroup } from '@/api/auth';
import validCompanyState from '@/helper/company/validCompanyState';
import { useRouter } from 'vue-router/composables';
import store from '@/store';
import { VList } from '@hw/virtual-list';
import { copyToClipBoard } from '@/assets/Utils';
import CompanyStatusTag from './CompanyStatusTag.vue';
import '@hw/virtual-list/dist/virtual-list.css';
import useCompanySwitch from '@/hooks/useCompanySwitch';

// 是否已过期
const isExpired = (company) => {
  if (!company.companyTimeDeadline) return false;
  const companyTimeDeadline = dayjs(company.companyTimeDeadline);
  const remainDay = companyTimeDeadline.diff(dayjs().endOf('date'), 'day');
  return remainDay < 0;
};

// 是否已停用
const isDisabled = (company) => company.companyStatus === 2; // 账套处于停用状态;

const companyList = ref([]);
const router = useRouter();
const popoverVisible = ref(false);

const activeCompanyId = computed(() => store.state.user.userInfo.selectCompanyId);
const activeCompanyName = computed(() => store.state.user.userInfo.companyName);
const activeCompanyInfo = computed(() => companyList.value?.find((item) => item.companyId === activeCompanyId.value));
const selectFilterType = ref('');
const filterText = ref('');

const loadCompanyList = async () => {
  const rsp = await getCompanyListByGroup();
  const { join, permissionRead } = rsp.data.data[0];
  const joinList = join?.map((item) => ({
      ...item,
      isExpired: isExpired(item),
      isDisabled: isDisabled(item),
      isReadonly: false,
    })) || [];
  const unjoinList = permissionRead.map((item) => ({
      ...item,
      isExpired: isExpired(item),
      isDisabled: isDisabled(item),
      isReadonly: true,
    })) || [];
  companyList.value = [...joinList, ...unjoinList];
};
const goBookList = () => {
  router.push({ name: 'booksSetList' });
};
const filteredCompanyList = computed(() => {
  if (!filterText.value && !selectFilterType.value) return companyList.value;
  const regex = new RegExp(filterText.value.split('').join('.*'), 'i');
  return companyList.value.filter((item) => {
    if (filterText.value && !item.companyName.match(regex)) return false;
    const status = selectFilterType.value;
    if (status) {
      // 只看已到期
      if (status === '3') {
        return item.isExpired;
      }
      // 只看停用
      if (status === '5') {
        return item.isDisabled;
      }
      // 只看“查”
      if (status === '2') {
        return !item.isExpired && !item.isDisabled && item.isReadonly;
      }
      // 只看“记”  可编辑和仅查看状态。可编辑且启用状态且未到期状态时显示为【记】，仅查看时显示为【查】；
      if (status === '1') {
        return !item.isExpired && !item.isDisabled && !item.isReadonly;
      }
    }

    return true;
  });
});

const { switchCompany } = useCompanySwitch();

// const onSelectCompany = async (companyId) => {
//   if (companyId === activeCompanyId.value) { // 选公司操作
//     return;
//   }
//   const loadingInstance = Loading.service({
//     text: '正在切换账套中...',
//     fullscreen: false,
//   });
//   // await store.dispatch('tagsView/delAllViews');
//   const cacheCurrentAccountPeriod = store.state.user.accountPeriod;
//   // 防止切换账套过程中当前页面触发一些不必要的事件，先把当前页面卸载掉
//   router.replace('/redirect/loadingPage/loading').then(async () => {
//     await store.dispatch('tagsView/delAllViews');
//     await nextTick();
//     try {
//       await store.dispatch('user/changeCompany', { companyId });
//       store.commit('app/SET_QUICK_ACCOUNT', 0);
//       store.dispatch('user/setAccountPeriod', cacheCurrentAccountPeriod);
//       // 刷新页面
//       router.replace('/redirect/manager/guidance').finally(() => {
//         validCompanyState(store.state.user.userInfo.selectCompanyId);
//         loadingInstance.close();
//       });
//     } catch (e) {
//       console.error(e);
//       loadingInstance.close();
//       MessageBox.alert('切换账套出错，请点击【返回工作台】', '切换账套失败', {
//         confirmButtonText: '返回工作台',
//         callback: (action) => {
//           if (action === 'confirm') {
//             router.push({
//               name: 'booksSetList',
//             });
//           }
//         },
//       });
//     }
//   });
// };

const onClickItem = (company) => {
  popoverVisible.value = false;
  switchCompany(company.companyId);
};
const onCopyCompanyName = (companyName) => {
  copyToClipBoard(companyName);
  Message.success(`账套名已复制， ${companyName}`);
};
onMounted(() => {
  loadCompanyList();
});
</script>
<style lang="scss">
.company-picker-popover {
  padding: 0;
  .company-picker-content{
    padding: 8px 16px;
  }
  .company-search-input {
    margin-bottom: 8px;

    .el-input-group__prepend {
      width: 80px;
      background-color: #fff;
    }
  }

  .company-item {
    line-height: 50px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px #c8c8c8 dashed;
    font-size: var(--base-font-size);
    cursor: pointer;

    &:hover {
      background: #f2f6fb;
    }
  }

  .empty-text {
    text-align: center;
    color: #999;
    font-size: 14px;
    margin-top: 10px;
  }

  .goListPageBtn {
    height: 38px;
    background: #F2F5FA;
    line-height: 38px;
    text-align: center;
    font-size: 14px;
    color: #5C6C8A;
    margin-top: 8px;
    cursor: pointer;
  }
}

.company-picker {
  width: 304px !important;
  height: 30px !important;
  padding: 0 8px;
  border-radius: 3px;
  border: 1px solid #c8c8c8;
  line-height: 30px;
  cursor: pointer;

  .activeCompanyText {
    vertical-align: middle;
    // 文本超出时用...
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    font-size: 14px;
    color: #2B3440;
  }

  .split-line {
    margin: 5px 8px 0;
    width: 1px;
    height: 17px;
    background: #E0E5EC;
  }

}
</style>
