<template>
  <div v-show="isShow">
    <div
    id="spreadsheet"
    class='spreadsheet'
    :class="{clearTab: !spreadsheetOpt.toolbar }"
    style="max-height:calc(100% - 80px);"></div>
    <el-dialog
    title="标记设置"
    :visible.sync="dialogFormVisible"
    :close-on-click-modal="false"
    :before-close="handleBeforeClose">
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="保存标记" name="saveRuleForm">
          <el-form
          ref="saveRuleForm"
          @submit.native.prevent
          :model="saveRuleForm"
          :rules="saveFormRules"
          label-width="120px">
            <el-form-item label="保存上期数据" prop="savePreVal">
              <el-input v-model="saveRuleForm.savePreVal" placeholder="请输入标记值"></el-input>
            </el-form-item>
            <el-form-item label="保存报表取数数据" prop="saveRepVal">
              <el-input v-model="saveRuleForm.saveRepVal" placeholder="请输入标记值"></el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="取数标记" name="getRuleForm">
          <el-form
          ref="getRuleForm"
          :model="getRuleForm"
          :rules="getFormRules"
          label-width="120px"
          @submit.native.prevent>
            <el-form-item label="标记类型" prop="ruleType">
              <el-select v-model="getRuleForm.ruleType" placeholder="请选择标记类型" style="width:100%">
                <el-option label="获取上期报表数据" value="getPreVal"></el-option>
                <el-option label="获取简易申报数据" value="getSimpleVal"></el-option>
                <el-option label="获取同期关联企业报表数据（多列）" value="getRepValRelCompany"></el-option>
                <el-option
                label="获取同期关联企业报表汇总数据（多列）"
                value="getRepValRelCompany-dimensionBlock"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="标记值" prop="ruleContent">
              <el-autocomplete
              :fetch-suggestions="querySearch"
              v-model.trim="getRuleForm.ruleContent"
              placeholder="请输入标记值"
              style="width:100%"></el-autocomplete>
            </el-form-item>
            <el-form-item
            v-if="getRuleForm.ruleType === 'getRepValRelCompany-dimensionBlock'"
            label="维度组名称"
            prop="dimensionBlock">
              <el-select
              v-model.trim="getRuleForm.dimensionBlock"
              clearable
              placeholder="请选择维度组名称"
              style="width:100%;">
                <el-option
                v-for="item in dimensionList"
                :label="item.dimensionBlockName"
                :value="item.dimensionBlockName"
                :key="item.dimensionBlockId"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="tips">
            <div v-show="getRuleForm.ruleType === 'getRepValRelCompany'">
              取公司名称的标记值为 companyName
              <el-button
              type="text"
              @click="getRuleForm.ruleContent = 'companyName'">快速设置</el-button>
            </div>
            <div v-show="getRuleForm.ruleType === 'getRepValRelCompany-dimensionBlock'">
              取统计维度名称的标记值为 dimensionOption
              <el-button
              type="text"
              @click="getRuleForm.ruleContent = 'dimensionOption'">快速设置</el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDlgCancelClick">取 消</el-button>
        <el-button type="primary" @click="handleDlgConfirmClick">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable no-underscore-dangle */
import '@/assets/sheetConfig';

export default {
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },

    spreadsheetOpt: {
      type: Object,
      default: (() => ({ toolbar: true }))(),
    },
  },

  data() {
    return {
      dialogFormVisible: false,
      $spreadsheet: null,
      saveRuleForm: {
        savePreVal: '',
        saveRepVal: '',
      },
      getRuleForm: {
        ruleType: 'getPreVal',
        ruleContent: '',
        dimensionBlock: '',
      },
      activeName: 'saveRuleForm',
      cacheMap: new Map(),
      dimensionList: [],
      getFormRules: {
        // ruleContent: [
        //   { required: true, message: '请输入标记值', trigger: 'blur' }
        // ],
      },
      saveFormRules: {
        // ruleContent: [
        //   { required: true, message: '请输入标记值', trigger: 'blur' }
        // ],
      },
      spreadsheetStyle: {
        width: '100%',
      },
    };
  },

  computed: {
    filterCacheArray() {
      let filterResult = [];
      if (this.getRuleForm.ruleType.indexOf('getPreVal') !== -1) {
        filterResult = this.cacheMap.get('savePreVal');
      } else if (this.getRuleForm.ruleType.indexOf('getRepValRelCompany') !== -1) {
        filterResult = this.cacheMap.get('saveRepVal');
      }
      return filterResult || [];
    },
  },

  watch: {
    isShow(newValue) {
      if (newValue) {
        if (!this.$spreadsheet) {
          this.$nextTick(() => {
            const $spreadsheet = $('#spreadsheet');
            this.getDimension();
            this.$spreadsheet = $spreadsheet;
            kendo.culture('zh-CN');

            $spreadsheet.kendoSpreadsheet(this.spreadsheetOpt);

            // $spreadsheet.kendoSpreadsheet();
            if (this.spreadsheetOpt.toolbar) {
              $.sheetConfig({
                spreadSheetDOM: $spreadsheet[0],
                spreadSheet: $spreadsheet.data('kendoSpreadsheet'),

                sheetConfigSetBtnClick: () => {
                  const activeSheet = $spreadsheet.data('kendoSpreadsheet').activeSheet();
                  const cellRange = `${activeSheet.activeCell().topLeft}`;
                  const tmsFlag = activeSheet.range(cellRange).tmsCellFlag();
                  let tmsFlagEntries;
                  let activeName = 'saveRuleForm';
                  let saveTmsFlagEntries = [];
                  let getTmsFlagEntries = [];
                  const ruleTypeArray = [];

                  this.dialogFormVisible = true;

                  this.$nextTick(() => {
                    if (tmsFlag) {
                      tmsFlagEntries = Object.entries(tmsFlag);
                      saveTmsFlagEntries = tmsFlagEntries.filter((item) => item[0].indexOf('save') !== -1);
                      getTmsFlagEntries = tmsFlagEntries.filter((item) => item[0].indexOf('save') === -1);

                      // save标记
                      saveTmsFlagEntries.forEach(([key, value]) => {
                        this.saveRuleForm[key] = value;
                      });

                      // get标记
                      getTmsFlagEntries.forEach(([key, value], index) => {
                        // 如果tmsCellFlag存在多个key value： {key1: value, key2: value}
                        if (index === 0) {
                          this.getRuleForm.ruleContent = value;
                        } else {
                          this.getRuleForm[key] = value;
                        }
                        ruleTypeArray.push(key);
                      });
                      this.getRuleForm.ruleType = ruleTypeArray.join('-');

                      // 仅在第get标记有且save标记没有的情况下才激活get的Tab
                      activeName = (getTmsFlagEntries.length && !saveTmsFlagEntries.length) ? 'getRuleForm' : 'saveRuleForm';
                    }
                    // 修改
                    this.activeName = activeName;
                  });
                },
                deleteRowBtnClick: () => {
                  this.$prompt('请输入起始坐标', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputPlaceholder: '请输入起始坐标，例： H5',
                  }).then(({ value }) => {
                    const sheet = $spreadsheet.data('kendoSpreadsheet').activeSheet();
                    const sheetName = sheet.name();
                    let delRange;
                    try {
                      delRange = sheet.range(value);
                    } catch (e) {
                      this.$message({
                        type: 'info',
                        message: '请输入正确的坐标，例：H5',
                      });
                      return;
                    }
                    const row = delRange._ref.topLeft
                      ? delRange._ref.topLeft.row : delRange._ref.row;
                    const col = delRange._ref.topLeft
                      ? delRange._ref.topLeft.col : delRange._ref.col;
                    const json = sheet.toJSON();
                    const allJson = $spreadsheet.data('kendoSpreadsheet').toJSON();
                    for (let i = 0; i < json.columns.length; i += 1) { // 删除Sheet里的columns定义
                      if (json.columns[i].index >= col) {
                        json.columns.splice(i, 1);
                        i -= 1;
                      }
                    }
                    // 删除每一行里面，列号大于被删除列的单元格
                    for (let i = 0; json.rows && i < json.rows.length; i += 1) {
                      for (let j = 0; json.rows[i].cells && j < json.rows[i].cells.length; j += 1) {
                        if (json.rows[i].cells[j].index >= col) {
                          json.rows[i].cells.splice(j, 1);
                          j -= 1;
                        }
                      }
                    }
                    for (let i = 0; json.rows && i < json.rows.length; i += 1) {
                      if (json.rows[i].index >= row) {
                        json.rows.splice(i, 1);
                        i -= 1;
                      }
                    }

                    // 重写整个SpreadSheet的JSON，单个Sheet的fromJSON不生效？！故整个Sheet重新fromJSON
                    $(allJson.sheets).each((i) => {
                      if (this.name === sheetName) {
                        allJson.sheets[i] = json;
                      }
                    });
                    $spreadsheet.data('kendoSpreadsheet').fromJSON(allJson);
                    this.$message({
                      type: 'success',
                      message: '删除完成',
                    });
                  }).catch((e) => {
                    console.log(e);
                    this.$message({
                      type: 'info',
                      message: '取消输入',
                    });
                  });
                },
              });
            }

            this.$emit('initend', this.$spreadsheet);
          });
        } else {
          this.$emit('initend', this.$spreadsheet);
        }
      }
    },
  },

  methods: {
    handleTagSetClick(value) {
      this.getRuleForm.ruleContent = value;
    },

    // 获取维度
    async getDimension() {
      const rsp = await this.$http.get('/auth/dimension/block/list');
      this.dimensionList = rsp.data.data;
    },

    handleBeforeClose(done) {
      this.resetFields();
      done();
    },

    querySearch(queryString, cb) {
      const restaurants = this.filterCacheArray;
      const results = queryString
        ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      // 调用 callback 返回建议列表的数据
      console.log(results, 'results');
      cb(results.map((item) => ({ value: item, label: item })));
    },

    createFilter(queryString) {
      return (restaurant) => (restaurant.indexOf(queryString) !== -1);
    },

    saveSpreadsheet() {
      const data = $('#spreadsheet').data('kendoSpreadsheet').toJSON(true);
      this.$emit('save', data);
    },

    handleDlgConfirmClick() {
      const
        $spreadsheet = $('#spreadsheet');
      const cellRange = `${$spreadsheet.data('kendoSpreadsheet').activeSheet().activeCell().topLeft}`;
      let ruleObj = null;
      let ruleType;
      let ruleContent;

      // 没有标记内容 标记将清空

      // 获取标记
      if (this.getRuleForm.ruleContent) {
        let tmpArray;

        ruleType = this.getRuleForm.ruleType;
        ruleContent = this.getRuleForm.ruleContent;

        if (ruleType === 'getRepValRelCompany-dimensionBlock') {
          tmpArray = ruleType.split('-');
          ruleObj = { [tmpArray[0]]: ruleContent, [tmpArray[1]]: this.getRuleForm.dimensionBlock };
        } else {
          ruleObj = { [ruleType]: ruleContent };
        }
      }

      // 保存标记
      if (this.saveRuleForm.savePreVal || this.saveRuleForm.saveRepVal) {
        if (ruleObj === null) (ruleObj = {});
        Object.entries(this.saveRuleForm).forEach(([ruleTypeOfRule, ruleContentOfRule]) => {
          if (ruleContentOfRule) {
            if (this.cacheMap.get(ruleTypeOfRule)) {
              if (this.cacheMap.get(ruleTypeOfRule).indexOf(ruleContentOfRule) === -1) {
                this.cacheMap.get(ruleTypeOfRule).push(ruleContentOfRule);
              }
            } else {
              this.cacheMap.set(ruleTypeOfRule, [ruleContentOfRule]);
            }
            ruleObj[ruleTypeOfRule] = ruleContentOfRule;
          }
        });
      }
      $spreadsheet.data('kendoSpreadsheet').activeSheet().range(cellRange).tmsCellFlag(ruleObj);
      this.handleDlgCancelClick();
      this.$message.success('设置成功！');
    },

    handleDlgCancelClick() {
      this.resetFields();
      this.dialogFormVisible = false;
    },

    resetFields() {
      this.getRuleForm.ruleContent = '';
      // this.$refs.getRuleForm.resetFields();
      this.$refs.saveRuleForm.resetFields();
    },
  },
};
</script>
<style lang="scss"  >
  .freezing {
    .k-spreadsheet .k-resize-handle, .k-spreadsheet .k-resize-hint{
          display: none !important;
    }
    .k-animation-container{
      display: none !important;
    }
  }
</style>

<style lang="scss"   scoped>
    .tips {
      text-align: right;
    }
    .spreadsheet {
        width: 100%;
    }
    @media screen and (max-width: 1960px){
        .spreadsheet {
            height:800px;;
        }
    }
    @media screen and (max-width: 1600px){
        .spreadsheet {
            height:630px;;
        }
    }
    @media screen and (max-width: 1440px){
        .spreadsheet {
            height:600px;;
        }
    }
    @media screen and (max-width: 1366px){
        .spreadsheet {
            height:530px;
        }
    }

</style>
<style lang="scss"  >
  .clearTab{
    .k-spreadsheet-action-bar,
    .k-spreadsheet-row-header,
    .k-spreadsheet-column-header,
    .k-spreadsheet-top-corner {
      display:none
    }

    .k-spreadsheet-data{
      .k-spreadsheet-vaxis{
        border:none
      }
      .k-spreadsheet-haxis{
        border:none
      }
    }

  }
</style>
