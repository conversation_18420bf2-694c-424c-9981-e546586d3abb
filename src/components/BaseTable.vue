<template>
  <div>
    这是基础table
  </div>
</template>

<script>
import isEqual from 'lodash-es/isEqual';
import cloneDeep from 'lodash-es/cloneDeep';
import assign from 'lodash-es/assign';

export default {
  props: {},

  data() {
    return {
      // 正在编辑的表格行数据对象
      editingRow: null,
      // 正在编辑的表格行数据最后一次已同步的数据
      // （可用作比较的基准，判断数据是否被改变）
      oldRow: null,
      // 插入行的数据模板
      addRow: {},
      //
      addRowCopy: {},
      // 新行插入位置
      addRowIndex: 0,
      // 新插入的行的数据引用
      addCite: {},

      // TODO 事件节流版本存储，可以不必在此声明
      throttleBodyClick: null,
      throttleLayoutRepealBtn: null,
    };
  },

  created() {
    // 初始化节流函数
    this.throttleBodyClick = Lib.Utils.throttle(this.rowClickControl).bind(this);
    // 初始化节流函数
    this.throttleLayoutRepealBtn = Lib.Utils.throttle(this.layoutRepealBtn).bind(this);
  },

  mounted() {
    // 记住新增行的引用，在提交后聚焦新增行时使用
    // this.addCite = this.addRow;
    setTimeout(() => {
      window.addEventListener('keyup', this.keup);
      // 监听用户点击， 在编辑行以外区域时，触发保存操作
      window.addEventListener('click', this.throttleBodyClick);

      const wrap = this.$el.querySelector('.el-table.main-table .el-table__body-wrapper .el-scrollbar__wrap')
        || this.$el.querySelector('.el-table__body-wrapper .el-scrollbar__wrap');
      if (wrap) {
        // 监听表格滚动， 使恢复按钮保持紧跟编辑行
        wrap.addEventListener('scroll', this.handlerWarpScroll);
      }
    }, 100);
  },

  beforeDestroy() {
    // 组件销毁时必须取消事件监听
    window.removeEventListener('keyup', this.keup);
    window.removeEventListener('click', this.throttleBodyClick);
    const wrap = this.$el.querySelector('.el-table.main-table .el-table__body-wrapper .el-scrollbar__wrap')
        || this.$el.querySelector('.el-table__body-wrapper .el-scrollbar__wrap');
    if (wrap) {
      wrap.removeEventListener('scroll', this.handlerWarpScroll);
    }
  },

  watch: {
    editingRow() {
      this.$nextTick(() => {
        this.layoutRepealBtn();
      });
    },

    // 当侧边栏收缩时，需要重新计算恢复按钮位置
    '$store.state.app.toggleSideBar': function toggleSideBar() {
      setTimeout(() => {
        this.layoutRepealBtn();
      }, 500);
    },
  },

  methods: {
    // 回车保存 esc 恢复修改
    keup(e) {
      if (e.keyCode === 13) {
        // 防止与各种下拉控件回车事件冲突， 判断下拉控件的展示状态
        if (
          $('.el-autocomplete-suggestion:not(.el-autocomplete-suggestion-auto):visible').length
          || $('.el-select-dropdown:visible').length
          || $('.el-picker-panel:visible').length
          || $('.el-popper:visible').length
        ) {
          return;
        }
        this.saveRow();
      } else if (e.keyCode === 27) {
        this.repealRow();
      }
    },

    nextInput() {
      const el = this.getNextElement($('input:focus')[0]);
      if (!el) {
        return false;
      }
      $('input:focus').trigger('blur');
      el.focus();
      return true;
    },

    getNextElement(field, key) {
      const form = document.querySelectorAll('input');
      let e;
      for (e = 0; e < form.length; e += 1) {
        if (field === form[e]) {
          break;
        }
      }
      if (key === 33) {
        return form[e - 1];
      }
      return form[e + 1];
    },
    layoutRepealBtn() {
      const $el = $(this.$el);
      const index = this.tableData.indexOf(this.editingRow);
      const $table = $el.find('.el-table.has-repeal').length
        ? $el.find('.el-table.has-repeal')
        : $el.find('.el-table');
      const $tableHead = $table.find('.el-table__header-wrapper').eq(0);
      const $tableBody = $table.find('.el-table__body-wrapper').eq(0);

      const $editingTr = $table.find('tr.el-table__row').eq(index);
      const trOffset = $editingTr && $editingTr.offset();
      const headOffset = $tableHead && $tableHead.offset();
      const bodyOffest = $tableBody && $tableBody.offset();

      // console.log('$editingTr', $editingTr);
      // console.log('trOffset', trOffset);
      // 当撤销按钮处于table可见区域时才显示
      if (
        trOffset
        && trOffset.top >= headOffset.top + $tableHead.height()
        && trOffset.top <= (headOffset.top + $tableHead.height() + $tableBody.height())
      ) {
        $(this.$el)
          .find('.repeal-row-btn')
          .css({
            left: bodyOffest.left,
            top: trOffset.top,
            transform: 'translate(-123%, 80%)',
            opacity: 1,
          });
      }
    },

    rowClickControl($e) {
      if (!this.$refs.table) return;
      setTimeout(() => {
        const excludeKeyword = [
          '确定',
          '取消',
          '删除',
          '退出',
          '上一步',
          '下一步',
        ];
        const index = this.tableData.indexOf(this.editingRow);
        const $currentTable = this.$refs.table.$el;
        const $app = document.querySelector('#app');

        // 获取 $currentRow 的方式，优先改成直接搜索 class，因为通过 index 的方式无法支持树形 Table 的子行
        const $rows = $currentTable.querySelectorAll('.el-table__row');
        let $currentRow = $currentTable.querySelector('.el-table__row.current-row');
        if (!$currentRow) {
          $currentRow = $rows.item(index);
        }

        const $currentFixedRow = document
          .querySelectorAll('.el-table__fixed .el-table__row')
          .item(index);
        const $target = $e.target;
        let $parent = $target;
        let isContain = false;
        let isAppInner = false;
        let isTableInner = false;

        if (excludeKeyword.indexOf($target.innerText) !== -1) {
          return;
        }
        if ($currentRow) {
        // 遍历点击点的所有父节点，看是否有当前编辑行
          while ($parent) {
            if ($parent === $currentTable) {
              isTableInner = true;
            }
            if ($parent === $app) {
              isAppInner = true;
            }
            if ($parent === $currentRow || $parent === $currentFixedRow) {
              isContain = true;
              isTableInner = true;
              break;
            }
            $parent = $parent.parentNode;
          }
          if (!isContain && isAppInner) {
            this.saveRow();
          }
          if (!isTableInner && this.$refs.table) {
            this.$refs.table.setCurrentRow(null);
          }
        }
      });
    },

    isEditing(row) {
      return this.editingRow === row;
    },

    isEditable() {
      return true;
    },

    addInsertRow() {
      this.addRow.isAddRow = true;
      this.addRowCopy = cloneDeep(this.addRow);
      this.oldRow = cloneDeep(this.addRow);
      this.addCite = cloneDeep(this.addRow);
      this.tableData.splice(
        this.addRowIndex,
        0,
        this.addCite,
      );
      this.editingRow = this.tableData[this.addRowIndex];
    },
    checkRowRemovable(props) {
      const { row, index } = props;
      return (
        this.canEdit
        && index > 0
        && (this.editingRow !== row || this.isEqualObject(row, this.oldRow))
        && this.tableData.indexOf(row) !== -1
      );
    },
    async editRow(row, col, e) {
      // 阻止冒泡，防止触发window的mousedown事件
      e.stopPropagation();

      // 不能编辑或当前行已经处于编辑
      if (this.canEdit === false || this.editingRow === row) {
        return;
      }

      // 执行保存，保证正在编辑的行合法保存再编辑下条
      const saveFlag = await this.saveRow();

      if (saveFlag && this.isEditable(row)) {
        this.oldRow = cloneDeep(row);
        this.editingRow = row;
        this.$refs.table.setCurrentRow(row);
      }
    },

    deleteRow(row) {
      this.postDelete(row);
    },

    async saveRow() {
      // 没有在编辑的行，直接返回保存成功
      if (!this.editingRow) {
        return true;
      }

      if (!this.editingRow.isAddRow) {
        if (!this.validRowFn()) return false;
        if (!this.isEqualObject(this.editingRow, this.oldRow)) {
          const falg = await this.postSave(this.editingRow);
          if (!falg) return false;
        }

        if (this.addRow.isAddRow) {
          // 有新增行
          this.editingRow = this.tableData[this.addRowIndex];
          this.oldRow = cloneDeep(this.tableData[this.addRowIndex]);
        } else {
          // 没有新增行
          this.editingRow = null;
          this.oldRow = null;
        }
        if (this.$refs.table) {
          this.$refs.table.setCurrentRow(null);
        }
        return true;
      }
      // 是新增行，并与新增行的备份不同（新增行有输入、更改）
      if (!this.isEqualObject(this.editingRow, this.addRowCopy)) {
        if (!this.validRowFn()) {
          return false;
        }
        const falg = await this.postSave(this.editingRow);

        this.$nextTick(() => {
          const $table = this.$refs.table;

          $table.setCurrentRow(this.addCite);
          const firstTd = $table.$el.querySelector('.el-table__body-wrapper table tbody tr:first-child td');

          if (firstTd) {
            const input = firstTd.querySelector('input');
            if (firstTd.querySelector('.el-select')) {
              // 非输入框按回车聚焦
              $(window).one('keydown', (event) => {
                if (event.which === 13 && input) {
                  input.focus();
                }
              });
            } else if (input) {
              // 输入框直接聚焦
              input.focus();
            }
          }
          // console.log('firstTd', firstTd);
        });

        if (!falg) return false;
      }
      return true;
    },

    validRowFn() {
      return true;
    },

    getSpecialField() {
      return {};
    },

    initAddRow() {
      // TODO 该行无意义，实现 bug？
      Object.entries(([keyName, value]) => {
        this.$set(this.addRow, keyName, value);
      });
    },

    repealRow() {
      assign(this.editingRow, this.oldRow);
      this.editingRow = null;

      if (this.addRow.isAddRow) {
        // 有新增行
        this.editingRow = this.tableData[this.addRowIndex];
        this.oldRow = cloneDeep(this.addRow);
      } else {
        // 没有新增行
        this.editingRow = null;
        this.oldRow = null;
      }
      if (this.$refs.table) {
        this.$refs.table.setCurrentRow(null);
      }
    },

    handlerWarpScroll() {
      $(this.$el)
        .find('.repeal-row-btn')
        .css('opacity', 0);
      this.throttleLayoutRepealBtn();
    },

    isEqualObject(obj1, obj2) {
      return isEqual(obj1, obj2);
    },
  },
};
</script>
