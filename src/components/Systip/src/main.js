import Vue from 'vue';
import store from '@/store';
import vmMain from './main.vue';

let instance;

const Systip = (tipsMsg = [], options) => {
  options = options || {};
  if (typeof tipsMsg === 'string') {
    tipsMsg = [
      {
        noticeTitle: '系统通知',
        noticeContent: tipsMsg,
      },
    ];
  }
  const markReadIds = (localStorage.getItem('SYSTIPMARKREAD') || '').split('#'); // 1#2#3
  if (markReadIds.length) {
    tipsMsg = tipsMsg.filter((item) => markReadIds.indexOf(String(item.noticeId)) === -1);
    console.log(tipsMsg, markReadIds);
  }
  if (tipsMsg.length === 0) return;

  if (!instance) {
    const div = document.createElement('div');
    div.setAttribute('id', 'systip');

    document.body.appendChild(div);

    instance = new Vue(vmMain);
    instance.$mount('#systip');
  } else {
    // instance.$el.querySelector('.systip-dialog').removeAttribute('style');
  }

  console.log('instance', instance);
  instance.tipsMsg = tipsMsg;
  if (options.style && instance.style) Object.assign(instance.style, options.style);
  instance.visible = true;
  instance.hasMarkRead = typeof options.hasMarkRead === 'boolean' ? options.hasMarkRead : false;
  instance.$store = store;
  return instance;
};

export default Systip;
