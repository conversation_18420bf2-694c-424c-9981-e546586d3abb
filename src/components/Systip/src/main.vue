<template>
  <transition name="dialog-fade">
    <div class="systip-dialog__wrapper" v-show="visible && !!tipsMsg.length">
      <div class="systip-dialog">
        <div class="systip-dialog-header">
          <div class="systip-dialog-header_left">
            <span class="systip-dialog-header_title">公告</span>
            <span v-if="tipsMsg.length > 1" class="systip-dialog-header_count"> ({{ index + 1 }}/{{ tipsMsg.length }})</span>
          </div>
          <div class="systip-dialog-header_right" @click="handleClose">
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div class="systip-dialog__body">
          <div class="systip-container systip-list">
            <div
            :class="['systip-item']"
            >
              <div class="systip-container-title">
                {{ currentMsg.noticeTitle }}
              </div>
              <div class="systip-container-date" >
                发布时间：{{ currentMsg.noticePublicDate | timestamp2Natlang }}
              </div>
              <div class="systip-container-body" v-dompurify-html="currentMsg.noticeContent"></div>
              <div class="systip-container-btn">
                <el-button @click="index -= 1" v-if="index + 1 > 1">
                  上一条
                </el-button>
                <el-button type="primary" @click="index += 1" v-if="index + 1 < tipsMsg.length">
                  下一条
                </el-button>
                <el-button type="primary" @click="handleMarkReadClick" v-else>我知道了</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  data() {
    return {
      visible: true,
      hasMarkRead: false,
      index: 0,
      tipsMsg: [],
    };
  },

  computed: {
    currentMsg() {
      return this.tipsMsg[this.index] || {};
    },
  },

  methods: {
    handleClose() {
      this.visible = false;
    },

    handleMarkReadClick() {
      const oldMarkReadIds = (localStorage.getItem('SYSTIPMARKREAD') || '').split('#'); // 1#2#3
      const newMarkReadIds = this.tipsMsg.map((item) => item.noticeId);
      localStorage.setItem('SYSTIPMARKREAD', [...oldMarkReadIds, ...newMarkReadIds].join('#'));
      this.handleClose();
    },
  },

};
</script>

<style lang="scss"   scoped>
  @media screen and (min-width: 1600px){
    .systip-dialog {
      width: 760px;
    }
  }
  @media screen and (max-width: 1600px){
    .systip-dialog {
        width: 560px;
    }
  }
  .systip-dialog__wrapper{
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
  }
  .systip-dialog {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 760px;
    border-radius: 4px;
    height: 450px;
    background: #FFFFFF;
    margin: 0;
    .systip-dialog-header{
          background: #FBFBFB;
          box-shadow: inset 0px -1px 0px 0px #EFEFF1;
          height: 48px;
          line-height: 48px;
          display: flex;
          justify-content: space-between;
          padding: 0 16px;
          &>div{
            // flex: 1;
          }
          .systip-dialog-header_title{
            font-size: 16px;
            font-weight: bold;
            color: #666666;
          }
          .systip-dialog-header_count{
            font-size: 16px;
            color: #666666;
          }
          .systip-dialog-header_right{
            cursor: pointer;
          }
        }

    .systip-dialog__body {
      padding: 24px 30px 5px 30px;
      .systip-container {

        .systip-item {
          margin-bottom: 0px;
          border-bottom: 1px solid #ccc;
          padding-bottom: 8px;
          &:last-child{
            border-bottom: none;
          }
        }

        .systip-container-btn{
          text-align: center;
          margin-top: 24px;
          padding: 15px;
          border-top: 1px solid #EFEFF1;
          &::v-deep .el-button--primary {
            background: var(--color-project);
            color: #fff;
            border:none;
            font-size: 13px;
          }
        }
        .systip-container-title {
          text-align: center;
          font-size: 20px;
          font-weight: bold;
          color: #333333;
          line-height: 26px;
        }
        .systip-container-date{
          text-align: center;
          font-size: 14px;
          color: #999999;
          line-height: 19px;
          margin-top: 8px;
        }
        .systip-container-body {
          height: 234px;
          overflow-x: hidden;
          padding-top: 8px;
          color: #666666;
          font-size: 14px;
          line-height: 24px;
        }
        .systip-container-footer {
          text-align: right;
          padding-bottom: 10px;
        }
      }
    }
  }
</style>
