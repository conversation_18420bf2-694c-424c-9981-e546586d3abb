<template>
  <div>
    <ul
    class="clearfix"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @dblclick="handleDblClick">
      <li v-for="n in initedNumber" :class="getCustomClass(n)" :key="n">
        <div class="date">{{ n }}</div>
      </li>
    </ul>
  </div>
</template>

<script>

export default {
  props: {
    accountPeriod: {
      type: Date,
      required: true,
    },
    withData: {
      type: Array,
      default: () => [],
    },
    defaultNodeAjaxed: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      chosenCenter: 0,
      nodeChosen: [...this.defaultNodeAjaxed],
    };
  },
  computed: {
    initedNumber() {
      const date = new Date(this.accountPeriod);
      date.setDate(1);
      date.setMonth(date.getMonth() + 1);
      date.setDate(0);
      return date.getDate();
    },
    weekClasses() {
      let variable = new Date(this.accountPeriod);
      const classes = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
      variable.setDate(1);
      variable = variable.getDay();
      return classes.slice(variable).concat(classes.slice(0, variable));
    },
  },
  mounted() {},
  watch: {
    accountPeriod() {
      this.nodeChosen = [];
    },
    defaultNodeAjaxed(value) {
      this.nodeChosen = [...value];
    },
  },
  methods: {
    getCustomClass(value) {
      const { nodeChosen, withData, weekClasses } = this;
      return {
        chosen: nodeChosen.indexOf(value) > -1,
        hasData: withData.indexOf(value) > -1,
        [weekClasses[(value - 1) % 7]]: true,
      };
    },

    handleDblClick() {
      this.$emit('dblickAdd', this.chosenCenter);
    },

    handleMouseEnter(e) {
      const target = e.target || e.srcElement;
      target.addEventListener('mousedown', this.startMove); // 当鼠标指针移动到元素上方，并按下鼠标左键时
    },

    handleMouseLeave(e) {
      const target = e.target || e.srcElement;
      target.removeEventListener('mousedown', this.startMove);
    },

    startMove(e) {
      const { $el } = this;
      let target = e.target || e.srcElement;
      if (target.tagName === 'UL') { return; }
      while ($el.contains(target) && target.tagName !== 'LI') { target = target.parentNode; }

      const number = parseInt(target.textContent, 10);
      if (!number || number > this.initedNumber) { return; }
      this.nodeChosen = [number];
      this.chosenCenter = number;
      document.addEventListener('mousemove', this.handleMouseMoving); // 当鼠标指针在指定的元素中移动时
      document.addEventListener('mouseup', this.handleMouseUp); // 当鼠标指针移动到元素上方，并松开鼠标左键时
    },

    handleMouseMoving(e) {
      const { $el } = this;
      let target = e.target || e.srcElement;
      if (target.tagName === 'UL') { return; }
      while ($el.contains(target) && target.tagName !== 'LI') { target = target.parentNode; }

      const number = parseInt(target.textContent, 10);
      if (number && number <= this.initedNumber) {
        const result = [];
        const { chosenCenter } = this;
        let min = number;
        let max = chosenCenter;
        if (min > max) {
          const temp = min;
          min = max;
          max = temp;
        }
        for (let i = min; i <= max; i += 1) { result.push(i); }
        this.nodeChosen = result;
      }
    },
    handleMouseUp() {
      this.cancelMove();
      clearTimeout(this.mouseUpTimeout);
      this.mouseUpTimeout = setTimeout(() => this.$emit('getData', [...this.nodeChosen]), 300);
    },

    cancelMove() {
      document.removeEventListener('mousemove', this.handleMouseMoving);
      document.removeEventListener('mouseup', this.handleMouseUp);
    },
  },
};

</script>

<style lang="scss"   scoped>
  ul.clearfix {
    margin-bottom: 3px;
    & > li {
      float: left;
      padding: 3px 0;
      & + li {
        margin-left: 15px;
      }
      &:hover {
        opacity: .8;
      }
      &:before {
        content: "";
        display: block;
        height: 7px;
        background: url("/static/imgs/day.png");
        background-repeat: no-repeat;
        margin-bottom: 3px;
        overflow: hidden;
      }
      &.SUN:before {
        background-position: 1px 0;
      }
      &.MON:before {
        background-position: -36px 0;
      }
      &.TUE:before {
        background-position: -73px 0;
      }
      &.WED:before {
        background-position: -112px 0;
      }
      &.THU:before {
        background-position: -148px 0;
      }
      &.FRI:before {
        background-position: -187px 0;
      }
      &.SAT:before {
        background-position: -224px 0;
      }
      .date {
        width: 22px;
        height: 22px;
        line-height: 22px;
        color:  #a8a5a5;
        border-radius: 50%;
        font-size: 12px;
        font-weight: bold;
        text-align: center;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }
      &.chosen .date {
        color: #fff;
        background: #e58f00;
        border-color: #e58f00;
      }
      &.hasData .date {
        position: relative;
        &:after {
          content: "";
          position: absolute;
          top: 0;
          left: 100%;
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background: #C1182A;
        }
      }
    }
  }
  @media screen and (max-width: 1024px) {
    ul.clearfix {
      & > li {
        & + li {
          margin-left: 5px;
        }
      }
    }
  }
</style>
