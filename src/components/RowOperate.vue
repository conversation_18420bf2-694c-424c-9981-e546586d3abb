<!--
 * @Description: toolbar 表格选中行的操作
 * @Author: 肖泽涛
 * @LastEditors: 启旭
 * @Date: 2019-05-07 09:00:08
 * @LastEditTime: 2019-10-30 10:28:53
 -->
<template>
  <transition name="rowslide-fade">
    <div v-if="selectionIng.length || selectionIng > 0" class="row_operate">
      <p class="selectTitle">
        已选中
        <span>{{ selectionIng.length || selectionIng }}</span>
        <template v-if="localTotals && !hideToal">
          &nbsp;/&nbsp;
          <span>{{ localTotals }}</span>
        </template>
        &nbsp;项
      </p>
      <div class="others">
        <slot></slot>
        <el-button v-if="handleClear" type="text" @click="clearCheck"><i class="el-icon-close"></i>取消勾选</el-button>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  components: {},
  data() {
    return {};
  },
  computed: {
    localTotals() {
      const { total } = this;
      if (total) {
        return total;
      }
      const p = this.$parent;
      return p.total || p.pageTotal || 0;
    },
  },
  methods: {
    clearCheck() {
      const table = this.tableRef || this.$parent.$refs.table;
      if (table) {
        table.clearSelection();
      }
    },
    //  批量删除选中的单据
    removeBills() {
      this.$emit('removeBill');
    },
  },
  props: {
    selectionIng: {
      // 选中数
      type: [Number, Array],
      default: 0,
    },
    total: {
      type: Number,
      default: 0,
    },
    hideToal: {
      type: Boolean,
      default: false,
    },
    handleClear: {
      type: Boolean,
      default: true,
    },
    // 父组件的表格ref，用于清空表格的选中项，不传时会默认取父组件的 $refs.table
    tableRef: {
      type: Object,
    },
  },
};
</script>

<style lang="scss">
.row_operate {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;
  line-height: 54px;
  border-radius: 4px;
  background: $layout-backgroundcolor;
  // border-bottom: 2px solid #e4e4e5;
  box-sizing: border-box;
  text-align: left;
  z-index: 999;
  .selectTitle {
    line-height: 1;
    margin-left: 20px;
    span {
      color: #66ccff;
    }
  }
  .line {
    display: inline-block;
    width: 1px;
    height: 14px;
    margin: 0 15px;
    background: #999;
  }
  .others {
    height: 100%;
    margin-left: 16px;
    .el-button--text {
      padding: 0;
      position: relative;
      &::after{
        content: ' ';
        left: -6px;
        top: 0px;
        position: absolute;
        display: inline-block;
        width: 1px;
        height: var(--base-font-size);
        background: #999;
      }
      .iconfont{
        font-size: 12px;
      }
    }
  }
  & > *,
  .others > * {
    display: inline-block;
    vertical-align: middle;
  }
  &:after,
  .others:after {
    display: inline-block;
    content: '';
    width: 0;
    height: 100%;
    font-size: 0;
    margin: 0;
    vertical-align: middle;
    visibility: hidden;
  }
}
.rowslide-fade-enter-active {
  transition: all 0.3s ease;
}
.rowslide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}
.rowslide-fade-enter,
.rowslide-fade-leave-active {
  transform: translateX(-100%);
  opacity: 0;
}
</style>
