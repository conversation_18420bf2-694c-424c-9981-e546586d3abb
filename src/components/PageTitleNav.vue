<!--
 * @LastEditors: 启旭
 * @Author: 肖泽涛
 * @Description: 页面头部左侧导航标题
 -->
<template>
  <div v-if="!isTaxWorkspace">
    <!-- <el-popover popper-class="submenu-popper" ref="popover" placement="right-start" width="168" trigger="click">
      <ul>
        <template v-for="page in currentModule">
          <li
          v-if="!routerIsHidden(page)"
          class="list-item"
          :key="page.path"
          >
            <router-link :to="pageRoute(page)" class="list-item-content" >
              {{page.meta.title}}
            </router-link>
          </li>
        </template>
      </ul>
    </el-popover> -->
    <el-button class="select_menu" type="text">
      <span class="select_menu_title">{{ currentPageTitle }}</span>
      <!-- <span class="fa fa-angle-down"></span> -->
    </el-button>
  </div>
  <div v-else style="float:left;color:#266dd6;font-size:12px;font-weight:bold;line-height:36px;margin-right:12px;">{{$route.meta.title}}</div>
</template>

<script>

export default {
  data() {
    return {};
  },

  computed: {
    // 代账版本将财务的部分功能放在了实务模块。所以在显示菜单时候需要将这些模块对应于的展示出来
    documentModule() {
      const routes = this.$store.getters.permissionRouters;
      const documentModule = routes.find((item) => item.path.slice(1) === 'document');
      return documentModule || { children: [] };
    },
    isTaxWorkspace() {
      return this.$route.path.indexOf('taxWorkspace') !== -1;
    },
    routePath() {
      const path = this.$route.path.slice(1);
      if (path.split('/')[0] === 'finance' && this.$store.state.app.PROJECT_TYPE === 'agent') {
        const functionPath = path.split('/')[1];
        const isDocumentModule = this.documentModule.children
          .some((item) => item.path === functionPath);
        // 判断当前功能模块是不是代账实务的功能
        if (isDocumentModule) {
          return path.replace('finance', 'document');
        }
      }
      return path;
    },

    currentPageTitle() {
      return this.$route.meta.title;
    },

    currentModule() {
      const currentModule = this.findRouteParent();
      return currentModule?.children?.filter((page) => !!this.pageName(page)) || [];
    },
  },

  methods: {
    routerIsHidden(route) {
      if (route.hidden) return true;
      if (route.meta && route.meta.frontendSeqCode) {
        const { frontendSeqCode } = route.meta;
        const hasFontentPermission = this.$store.getters['user/hasFontentPermission'];
        const isBK = route.path.indexOf('/controlCenter') === 0;
        // console.log(route.meta.frontendSeqCode, isBK, !hasFontentPermission([route.meta.frontendSeqCode], isBK));
        return !hasFontentPermission([`${frontendSeqCode}-chaxun`], isBK);
      }
      return false;
    },
    // 获取路由目标地址
    pageRoute(page) {
      if (page.name) {
        return { name: page.name };
      }
      return page.path;
    },
    findRouteParent() {
      let routes = this.$store.getters.permissionRouters;
      const matchs = this.$router.match({ name: this.$route.name })?.matched || [];
      let parentRoute;
      matchs.forEach((match, index) => {
        if (index < matchs.length - 1) {
          parentRoute = routes.find((item) => item.name === match.name);
          routes = parentRoute?.children || [];
        }
      });
      return parentRoute;
    },

    pageName(page) {
      return (page && page.meta && page.meta.title) || '';
    },
  },
};
</script>

<style lang="scss" scoped>
.submenu-popper {padding:8px;}
.list-item {
  height: 36px;
  line-height: 36px;
  opacity: 0.85;
  font-size: 12px;
  color: #333;
  padding: 0 10px;
  list-style: none;
  cursor: pointer;
  // -webkit-transition: border-color .3s, background-color .3s, color .3s;
  // transition: border-color .3s, background-color .3s, color .3s;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  white-space: nowrap;
  a {
    color:#001020;
  }
  &:hover {
    background-color: #1481d5;
    a {
      color:#fff;
    }
  }
  .list-item-content{
    display: inline-block;
    width: 100%;
    height: 100%;
  }
}
.is-active {
  background: #1481d5;
}
</style>
