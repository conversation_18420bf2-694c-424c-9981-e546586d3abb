<template>
  <el-tooltip
    class="item"
    effect="dark"
    content="通知"
    :open-delay='300'
    :enterable='false'
    transition='el-fade-in-swing'
    placement="left"
  >
    <div
      class="notice_popover"
      :class="'notice_popover__' + size"
      v-popover:popover1
    >
      <el-popover
        ref="popover1"
        placement="bottom"
        :offset="100"
        width="300"
        trigger="hover"
        popper-class="notice_popover"
      >
        <notice
        :tabListData="tabListData"
        @setReadMessage="setReadMessage"
        @setAllRead="setAllRead"
        :targetTypes="messageTargetTypes"></notice>
      </el-popover>
      <a
        href="javascript:;"
        class="link_setting tooltipButton"
      >
        <slot>
          <i class="iconfont icon-xiaoxi1 icon" style='vertical-align: super;margin-top:16px;' ></i>
        </slot>

        <span
          v-if="unReadSysTips.length + unReadMessages.length"
          class="link_setting_badge"
        >
          {{ unReadSysTips.length + unReadMessages.length }}
        </span>
      </a>
    </div>
  </el-tooltip>
</template>
<script>
import Notice from '@/components/Notice.vue';
import { sortBy } from 'lodash-es';
import dayjs from 'dayjs';
import { objToUrl } from '@/assets/Utils';

const MessagesTypeMap = {
  USER: '用户',
  COMPANY: '账套',
  BK_COMPANY: '代账',
  SYS: '系统',
};

export default {
  name: 'Messages',
  props: {
    targetType: {
      type: String, // BK&USER or COMPANY  代账公司和用户消息 或者 账套消息
    },
    // 当查询目标为 COMPANY时， 需要指定公司id
    companyId: {
      type: [String, Number],
      default: '',
    },
    size: {
      type: String,
      default: 'default',
    },
    refresh: {
      type: Boolean,
    },
    // 自定义获取message的方法
    siderUnReadMessages: {
      type: Array,
    },
  },
  components: { Notice },
  computed: {
    sysTips() {
      return this.$store.state.user.sysTips;
    },
    unReadSysTips() {
      return this.targetType !== 'COMPANY' ? this.sysTips.filter((item) => !item.markRead) : [];
    },
    // 消息通知组件要整合系统提示和未读消息一起显示
    messages() {
      // 系统通知字段与消息不同在这里做兼容处理
      let messages;
      if (this.targetType !== 'COMPANY') {
        const sysTips = this.unReadSysTips.filter((item) => !item.markRead).map((item) => ({
          ...item,
          msgContent: item.noticeContent,
          msgTitle: item.noticeTitle,
          msgTime: item.noticePublicDate,
          targetType: 'SYS',
          msgId: item.noticeId,
        }));
        messages = [...sysTips, ...this.unReadMessages];
      } else {
        messages = [...this.unReadMessages];
      }
      sortBy(messages, (item) => new Date(item.msgTime));
      return messages;
    },
    tabListData() {
      let tabList = Object.entries(MessagesTypeMap).map(([id, name]) => ({ name, id, list: [] }));
      // 没传公司Id不需要查账套消息
      if (!this.companyId) {
        tabList = tabList.filter((item) => item.id !== 'COMPANY');
      }
      if (this.targetType) {
        tabList = tabList.filter((item) => item.id === this.targetType);
      }
      this.messages.forEach((item) => {
        const tabData = tabList.find((tab) => tab.id === item.targetType);
        if (!tabData) return;
        tabData.list.push(item);
      });

      return tabList;
    },
    bkCompanyCode() {
      return this.$store.state.user.userInfo.bkCompanyCode;
    },
    userId() {
      return this.$store.state.user.userInfo.userId;
    },
    messageTargetTypes() {
      // 没传公司Id不需要查账套消息
      if (!this.companyId) {
        return ['USER', 'BK_COMPANY'];
      } if (this.targetType === 'COMPANY') {
        return ['COMPANY'];
      }
      return ['USER', 'BK_COMPANY', 'COMPANY'];
    },
  },
  data() {
    return {
      unReadMessages: [],
      lastSearchTime: null,
    };
  },
  watch: {
    siderUnReadMessages() {
      this.unReadMessages = this.siderUnReadMessages;
    },
  },
  mounted() {
    // 由外部提供信息列表
    if (this.siderUnReadMessages) {
      this.unReadMessages = this.siderUnReadMessages;
    } else {
      this.getUnReadMessages();
    }

    if (this.refresh) {
      // 获取最新未读消息，每15分钟轮巡一次
      this.timer = setInterval(() => { this.getUnReadMessages(this.lastSearchTime); }, 5 * 60 * 1000);
    }
  },
  beforeDestroy() {
    if (this.refresh) {
      clearInterval(this.timer);
    }
  },
  methods: {
    getParams(lastSearchTime = false) {
      const targetsJson = this.messageTargetTypes.map((item) => {
        let targetIds = [];
        if (item === 'COMPANY') {
          targetIds = [this.companyId];
        }
        if (item === 'BK_COMPANY') {
          targetIds = [this.bkCompanyCode];
        }
        if (item === 'USER') {
          targetIds = [this.userId];
        }
        return {
          targetType: item,
          targetIds,
        };
      });

      return {
        readStatus: 0,
        pageSize: 99999,
        targetsJson: JSON.stringify(targetsJson),
        msgTime: lastSearchTime ? dayjs(lastSearchTime).format('YYYY-MM-DD HH:mm:ss') : '',
      };
    },
    // lastSearchTime 只查询传入时间之后创建的消息
    getUnReadMessages(lastSearchTime = false) {
      this.$http.get(`/auth/msg/v1.0?${objToUrl(this.getParams(lastSearchTime))}`)
        .then((res) => {
          if (lastSearchTime !== false && this.refresh) {
            this.unReadMessages.unshift(...res.data.data);
            this.showNotification(res.data.data);
          } else {
            this.unReadMessages = res.data.data;
          }
          // 本地时间不可靠， 将最近一个消息作为最后查询时间
          if (this.unReadMessages.length) {
            this.lastSearchTime = dayjs(this.unReadMessages[0].msgTime).toDate();
          }
        });
    },
    // 将消息设置为已读
    async setReadMessage(message) {
      if (message.targetType === 'SYS') {
        this.setReadSysMessage(message);
      } else {
        await this.setReadUserMessage(message);
      }
    },
    async setReadUserMessage(message) {
      await this.$http.put('/auth/msg/v1.0', { msgIds: [message.msgId] }).then((res) => {
        this.$message.success(res.data.returnMessage);
        this.unReadMessages = this.unReadMessages.filter((item) => item.msgId !== message.msgId);
      });
    },
    async setReadSysMessage(message) {
      const markReadIds = localStorage.getItem('SYSTIPMARKREAD'); // 1#2#3
      message.markRead = true;
      localStorage.setItem('SYSTIPMARKREAD', markReadIds ? `${markReadIds}#${message.msgId}` : (`${message.msgId}`));
      this.$store.commit('user/SET_SYSTIPS', this.sysTips);
    },
    // 将所有消息设置为已读
    setAllRead() {
      this.$http.put('/auth/msg/v1.0', { targetTypes: this.messageTargetTypes, targetIds: [this.companyId] }).then((res) => {
        this.$message.success(res.data.returnMessage);
        this.unReadMessages = [];
      });
    },
    // notifyVm 使用对象传递，保持引用组件实体
    createMessageNotifyNode(message, notifyVms) {
      const h = this.$createElement;
      const node = h('div', [
        h(
          'p',
          {
            attrs: {
              class: 'notificationText',
              title: message.msgContent,
            },
            title: message.msgContent,
          },
          message.msgContent,
        ),
        h(
          'div',
          {
            attrs: {
              class: 'btn_area',
            },
          },
          [
            h(
              'el-button',
              {
                type: 'primary',
                on: {
                  click: async () => {
                    await this.setReadMessage(message);
                    // notify 组件实体
                    notifyVms[message.msgId].close();
                  },
                },
              },
              '标记为已读',
            ),
            h(
              'el-button',
              {
                on: {
                  click: async () => {
                    this.$router.push({ name: 'messageCenter' });
                    // 关闭所有提示
                    Object.values(notifyVms).forEach((vm) => vm.close());
                  },
                },
              },
              '前往消息中心',
            ),
          ],
        ),
      ]);
      return node;
    },
    // 查看所有消息为跳转到消息中心
    showNotification(messages) {
      const notifyVms = {};
      messages.forEach((item, index) => {
        if (item.msgLv === 0) return;
        setTimeout(() => {
          notifyVms[item.msgId] = this.$notify({
            duration: 5000,
            title: item.msgTitle,
            message: this.createMessageNotifyNode(item, notifyVms),
          });
        }, index * 500);
      });
    },
  },
};
</script>
<style lang="scss">
.notice_popover {
  padding: 0 !important;
  .popper__arrow {
    &::after {
      border-bottom-color: #ebedf1 !important;
    }
  }
}

a.link_setting .link_setting_badge {
    display: block;
    position: absolute;
    top: 5px;
    right: 0;
    width: 20px;
    height: 15px;
    border-radius: 8px;
    background-color: #c0192a;
    font-size: 10px;
    line-height: 15px;
    color: #fff;
    font-weight: normal;
  }
.notice_popover__mini{
  a.link_setting .link_setting_badge {
    text-align: center;
    top: -5px;
    right: -10px;
  }
}
.notificationText {
  overflow : hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  cursor: pointer;
}
</style>
