import { chunk } from 'lodash-es';
/**
 * 计算文本在设定fontSize下的宽度
 *
 * @export
 * @param {*} text
 * @param {*} fontSize
 * @returns
 */
 export function computedTextWidth(text, fontSize, fontFamily = '宋体') {
  const span = document.createElement('span');
  span.style.fontSize = fontSize;
  span.style.position = 'fixed';
  span.style.top = '-10000px';
  span.style.fontFamily = fontFamily;
  span.innerText = text;
  document.body.appendChild(span);
  const width = span.offsetWidth;
  span.remove();
  return width;
}
/**
 * 计算文本在给定宽度下的合适字号
 *
 * @export
 * @param {*} text
 * @param {*} width
 * @param {*} minFontSize
 * @returns
 */
export function computedTextFontSize(text, width, minFontSize, fontFamily) {
  const span = document.createElement('span');
  span.style.width = `${width}mm`;
  span.style.display = 'inline-block';
  document.body.appendChild(span);
  const widthOfPx = span.offsetWidth;
  span.remove();
  console.log(widthOfPx, 'widthOfPx');
  let fontSize = 9;
  let relWidth = widthOfPx;
  while (fontSize > minFontSize) {
    relWidth = computedTextWidth(text, `${fontSize}pt`, fontFamily);
    console.log(widthOfPx, text, relWidth, fontSize, relWidth > widthOfPx);
    // 预留0.5mm， 防止换行
    if (relWidth > widthOfPx - 9) {
      fontSize -= 0.5;
    } else {
      break;
    }
  }
  return fontSize > minFontSize ? fontSize : minFontSize;
}
/**
*打印局部内容
*
* @export
* @param {string} [content=''] 需要打印的内容的innerHTML
* @param {boolean} [ifRender=false]
* @param {boolean} [ifPrint=true]
* @param {*} width
* @param {*} height
*/
export async function printContent(content = '', ifRender = false, ifPrint = true, width, height) {
 let printIframe = document.getElementById('printIframe');
 if (!printIframe) {
   printIframe = document.createElement('iframe');
   printIframe.setAttribute('id', 'printIframe');
   printIframe.setAttribute('class', 'print');

  //  printIframe.setAttribute('height', `${height}px`);

   document.body.appendChild(printIframe);
 }
//  printIframe.setAttribute('width', `${width}px`);
 if (ifRender) {
   const iframeHead = document.head.cloneNode(true);
   const iframeDoc = printIframe.contentWindow.document;
   Array.from(iframeHead.childNodes).forEach((node) => {
     if (node.tagName === 'SCRIPT') {
       iframeHead.removeChild(node);
     }
   });
   let styles = '';
   document.head.querySelectorAll('style').forEach((item) => { styles += item.innerHTML; });

   iframeDoc.open();
   iframeDoc.write(`<head>
            <style>${styles}</style>
            <style>
            @media print {
                 @page {
                   size: ${width}mm ${height}mm !important;
                   border: 0;
                   padding: 0cm  !important;
                   margin: 0cm !important;
                 }
              }
            </style>
             </head>`);
    const styleLinks = document.head.querySelectorAll('link[rel="stylesheet"]');
    styleLinks.forEach((item) => iframeDoc.head.insertBefore(item.cloneNode(true), iframeDoc.head.firstChild));
   iframeDoc.close();
 }
 if (content) printIframe.contentDocument.body.innerHTML = content;
 if (ifPrint) {
   // 🏡图片未加载完成
  setTimeout(() => printIframe.contentWindow.print(), 200);
 }
//  printIframe.remove();
}
export function transformMoneyUp(num) {
  if (/^[+-]?\d+(\.\d+)?$/.test(num)) {
    let tempNum = parseInt((100 * parseFloat(Math.abs(num), 10)).toFixed(0), 10);
    const chineseNum = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const measurementUnit = ['分', '角', '元', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿', '拾', '佰', '仟', '万'];

    if (tempNum === 0) {
      return '零元整';
    }

    if (String(tempNum).length <= measurementUnit.length) {
      let remainder;
      let unitIndex = 0;
      const transformNumArr = [];

      while (tempNum) {
        remainder = tempNum % 10;
        transformNumArr.push(measurementUnit[unitIndex]);
        unitIndex += 1;
        transformNumArr.push(chineseNum[remainder]);
        tempNum = parseInt(tempNum / 10, 10);
      }
      return `${num < 0 ? '负' : ''}${transformNumArr.reverse().join('').replace(/(零[拾佰仟角])+/g, '零')
        .replace(/零+([亿万元])/g, '$1')
        .replace(/零{1,2}分/, (word) => (word.length == 2 ? '' : '整'))}`;
    }
  }
  return num;
}

function paeseHtmlToStr(text) {
  const textNode = document.createTextNode(text);
  const div = document.createElement('div');
  div.append(textNode);
  return div.innerHTML;
}
export function breakText(text, size) {
  const breakTextArr = chunk(text.split(''), size);
  return breakTextArr.map((item) => paeseHtmlToStr(item.join('')));
}
// 清除换行符
export function clearBreak(text) {
  return text.replace('\n', '');
}
