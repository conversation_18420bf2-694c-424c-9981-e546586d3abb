<template>
  <div>
    <button @click="print">打印</button>
    <Invoice :orginData="data" ref="boxs" />
    <invoice-detail :orginData="data"  ref="box"  v-if="false"  />
  </div>
</template>
<script>
// import { printContent } from './helper';
import Invoice from './invoice.vue';
import InvoiceDetail from './invoiceDetail.vue';

const data = {
UDiskSn: '税控装置编号',
InvoiceType: '发票种类',
InvoiceCode: '**********',
InvoiceNum: '********',
MerchantTaxID: '839493744392823484',
MerchantName: '销方名称',
MerchantAddrAndPhone: '广东省广州市天河区林和街道体育东路122号羊城国际商贸大厦122号 ********',
MerchantBankAndAccount: '销方银行及账号平安银行广东省分行 ***************',
PurchaserTaxID: '839493744392823484',
PurchaserName: '海华税务师事务所',
PurchaserAddrAndPhone: '广东省广州市天河区林和街道体育东路122号羊城国际商贸大厦122号 ********',
PurchaserBankAndAccount: '销方银行及账号平安银行广东省分行 ***************',
TotalAmountTaxExcluded: '¥230301.23',
TotalTax: '¥2303.23',
TotalAmount: '¥230301.23',
TotalAmountUp: 230301.23,
Remarks: '备注',
Payee: '收款人',
Reviewer: '复核人',
Drawee: '开票人',
ListFlag: 0,
OriginInvoiceCode: '原发票代码',
OriginInvoiceNum: '原发票号码',
NegSpecialInvoicePermitNum: '专票红字通知单编号',
CatalogVer: '商品分类版本号',
InvoiceTime: '2021年05月22日',
VerifyCode: '校验码 882373',
Ciphertext: '03>/2085745-/6314*2/60-8>13+<**********-903712+-*6>59382*5/140->4*-<8479+02849>><428**331/<1+401+748190/70425<72',
QrCode: 'data:image/png;base64,iVBORw0dgdfgdfgfKGgo12234343434',
PreviewUrl: '发票预览地址',
OfdFileUrl: 'OFD 下载地址',
PdfFileUrl: 'PDF 下载地址',
State: '状态',
InvalidateOperator: '作废人',
InvalidateTime: '作废时间',
InvoiceItems: [
{
ItemName: '*非金属矿物*外墙aaaaaaaaaa',
UnitPrice: '129323.12222',
UnitPriceTaxExcluded: '129323.12222',
Amount: '129323.12222',
AmountTaxExcluded: '129323.12222',
Quantity: '12',
TaxRate: '12%',
Tax: '149323.124222',
Specifications: 'F-J2393',
Unit: '平方米',
DiscountAmountTaxExcluded: '12.32',
CatalogName: '商品分类名称',
CatalogCode: '商品分类编码',
HasPreferentialPolicy: '优惠政策标识',
PreferentialPolicy: '优惠政策类型',
TaxFreeType: '免税类型',
},
{
ItemName: '*非金属矿物*外墙',
UnitPrice: '129323.12222',
UnitPriceTaxExcluded: '129323.12222',
Amount: '129323.12222',
AmountTaxExcluded: '129323.12222',
Quantity: '12',
TaxRate: '12%',
Tax: '149323.124222',
Specifications: 'F-J2393',
Unit: '平方米',
DiscountAmountTaxExcluded: '12.32',
CatalogName: '商品分类名称',
CatalogCode: '商品分类编码',
HasPreferentialPolicy: '优惠政策标识',
PreferentialPolicy: '优惠政策类型',
TaxFreeType: '免税类型',
},
{
ItemName: '*非金属矿物*外墙砖',
UnitPrice: '129323.12222',
UnitPriceTaxExcluded: '129323.12222',
Amount: '129323.12222',
AmountTaxExcluded: '129323.12222',
Quantity: '12',
TaxRate: '12%',
Tax: '149323.124222',
Specifications: 'F-J2393',
Unit: '平方米',
DiscountAmountTaxExcluded: '12.32',
CatalogName: '商品分类名称',
CatalogCode: '商品分类编码',
HasPreferentialPolicy: '优惠政策标识',
PreferentialPolicy: '优惠政策类型',
TaxFreeType: '免税类型',
},
{
ItemName: '*非金属矿物*外墙砖',
UnitPrice: '129323.12222',
UnitPriceTaxExcluded: '129323.12222',
Amount: '129323.12222',
AmountTaxExcluded: '129323.12222',
Quantity: '12',
TaxRate: '12%',
Tax: '149323.124222',
Specifications: 'F-J2393',
Unit: '平方米',
DiscountAmountTaxExcluded: '12.32',
CatalogName: '商品分类名称',
CatalogCode: '商品分类编码',
HasPreferentialPolicy: '优惠政策标识',
PreferentialPolicy: '优惠政策类型',
TaxFreeType: '免税类型',
},
{
ItemName: '*非金属矿物*外墙砖',
UnitPrice: '129323.12222',
UnitPriceTaxExcluded: '129323.12222',
Amount: '129323.12222',
AmountTaxExcluded: '129323.12222',
Quantity: '12',
TaxRate: '12%',
Tax: '149323.124222',
Specifications: 'F-J2393',
Unit: '平方米',
DiscountAmountTaxExcluded: '12.32',
CatalogName: '商品分类名称',
CatalogCode: '商品分类编码',
HasPreferentialPolicy: '优惠政策标识',
PreferentialPolicy: '优惠政策类型',
TaxFreeType: '免税类型',
},
{
ItemName: '*非金属矿物*外墙砖',
UnitPrice: '129323.12222',
UnitPriceTaxExcluded: '129323.12222',
Amount: '129323.12222',
AmountTaxExcluded: '129323.12222',
Quantity: '12',
TaxRate: '12%',
Tax: '149323.124222',
Specifications: 'F-J2393',
Unit: '平方米',
DiscountAmountTaxExcluded: '12.32',
CatalogName: '商品分类名称',
CatalogCode: '商品分类编码',
HasPreferentialPolicy: '优惠政策标识',
PreferentialPolicy: '优惠政策类型',
TaxFreeType: '免税类型',
},
{
ItemName: '*非金属矿物*外墙砖',
UnitPrice: '129323.12222',
UnitPriceTaxExcluded: '129323.12222',
Amount: '129323.12222',
AmountTaxExcluded: '129323.12222',
Quantity: '12',
TaxRate: '12%',
Tax: '149323.124222',
Specifications: 'F-J2393',
Unit: '平方米',
DiscountAmountTaxExcluded: '12.32',
CatalogName: '商品分类名称',
CatalogCode: '商品分类编码',
HasPreferentialPolicy: '优惠政策标识',
PreferentialPolicy: '优惠政策类型',
TaxFreeType: '免税类型',
},
{
ItemName: '*非金属矿物*外墙砖',
UnitPrice: '129323.12222',
UnitPriceTaxExcluded: '129323.12222',
Amount: '129323.12222',
AmountTaxExcluded: '129323.12222',
Quantity: '12',
TaxRate: '12%',
Tax: '149323.124222',
Specifications: 'F-J2393',
Unit: '平方米',
DiscountAmountTaxExcluded: '12.32',
CatalogName: '商品分类名称',
CatalogCode: '商品分类编码',
HasPreferentialPolicy: '优惠政策标识',
PreferentialPolicy: '优惠政策类型',
TaxFreeType: '免税类型',
},
],
};
export default {
  components: { Invoice, InvoiceDetail },
  data() {
    return {
      data,
    };
  },
  methods: {
    print() {
      this.$refs.box.print();
      // const a = this.$refs.boxs.map((item) => item.toPrintContent()).join('');
      // printContent(
      //   a,
      //   true,
      //   true,
      //   240,
      //   140,
      // );
    },
  },
};
</script>
<style lang="scss">
*{
  padding:0;
  margin:0;
}
body{
  margin:0;
}
</style>
