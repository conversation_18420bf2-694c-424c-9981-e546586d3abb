<template>
  <div ref="printBox" class="printBox">
     <!--startprint-->
    <div class="invoice-detail" v-for="(page, pageIndex) in pageData" :key="pageIndex">
      <h4 class="invoice-detail-header">销售货物或者提供应税劳务、服务清单</h4>
      <div class="invoice-detail-PurchaserName">购买方名称：{{data.PurchaserName}}</div>
      <div class="invoice-detail-MerchantName">销售方名称：{{data.MerchantName}}</div>
      <div class="invoice-detail-InvoiceCode">所属{{InvoiceType}}代码：{{data.InvoiceCode}}</div>
      <div class="invoice-detail-InvoiceNum">号码：{{data.InvoiceNum}}</div>
      <div class="invoice-detail-total"><span>共</span><span>{{pageData.length}}</span><span>页</span></div>
      <div class="invoice-detail-page"><span>第</span><span>{{pageIndex + 1}}</span><span>页</span></div>
      <div class="invoice-detail-table">
        <div class="invoice-detail-table-row invoice-detail-table-header">
          <div class="invoice-detail-table-col">序号</div>
          <div class="invoice-detail-table-col">货物（劳务）名称</div>
          <div class="invoice-detail-table-col">规格型号</div>
          <div class="invoice-detail-table-col">单位</div>
          <div class="invoice-detail-table-col">数量</div>
          <div class="invoice-detail-table-col">单价</div>
          <div class="invoice-detail-table-col">金额</div>
          <div class="invoice-detail-table-col">税率</div>
          <div class="invoice-detail-table-col">税额</div>
        </div>
        <div class="invoice-detail-table-row invoice-detail-table-background">
          <div
            class="invoice-detail-table-col"
            v-for="i in 9"
            :key="i"
          ></div>
        </div>
        <div
          v-for="(item, index) in page"
          :key="index"
          class="invoice-detail-table-row"
        >
          <div
            v-for="col in columns"
            :key="col.field"
            class="invoice-detail-table-col"
            :style="getStyle(col)"
          >
            <span v-if="col.field === 'index'">{{index + pageIndex * 25 + 1}}</span>
            <span v-if="col.formatter">{{col.formatter(item[col.field])}}</span>
            <span v-else>{{item[col.field]}}</span>

          </div>
        </div>
        <div class="invoice-detail-table-sumBox">
          <div class="invoice-detail-table-row invoice-detail-table-sumBox-minSum">
            <div class="invoice-detail-table-col">小计</div>
            <div class="invoice-detail-table-col" v-for="i in 5" :key="i"></div>
            <div class="invoice-detail-table-col" style="text-align:right">{{computedTotalByFiled(page, 'AmountTaxExcluded')}}</div>
            <div class="invoice-detail-table-col"></div>
            <div class="invoice-detail-table-col" style="text-align:right">{{computedTotalByFiled(page, 'Tax')}}</div>
          </div>
          <div class="invoice-detail-table-row invoice-detail-table-sumBox-discount">
            <div class="invoice-detail-table-col">折扣</div>
            <div class="invoice-detail-table-col" v-for="i in 5" :key="i"></div>
            <div class="invoice-detail-table-col" style="text-align:right">{{computedTotalByFiled(page, 'AmountTaxExcluded', true)}}</div>
            <div class="invoice-detail-table-col"></div>
            <div class="invoice-detail-table-col"  style="text-align:right">{{computedTotalByFiled(page, 'Tax', true)}}</div>
          </div>
          <div class="invoice-detail-table-row invoice-detail-table-sumBox-sum" v-if="pageIndex === pageData.length - 1">
            <div class="invoice-detail-table-col">合计</div>
            <div class="invoice-detail-table-col" v-for="i in 5" :key="i"></div>
            <div class="invoice-detail-table-col"  style="text-align:right">{{data.TotalAmountTaxExcluded}}</div>
            <div class="invoice-detail-table-col"></div>
            <div class="invoice-detail-table-col"  style="text-align:right">{{data.TotalTax}}</div>
          </div>
        </div>
        <div class="invoice-detail-table-row invoice-detail-table-remark">
          <div class="invoice-detail-table-col">备注</div>
          <div class="invoice-detail-table-col">{{data.Remarks}}</div>
        </div>
      </div>
      <div class="invoice-detail-seller">销货单位（章）：</div>
      <div class="invoice-detail-invoiceTime">开票日期： {{data.InvoiceTime | date}}</div>
      <div class="invoice-detail-tip">注：本清单一式两联：第一联，销售方留存；第二联，销售方送交购买方</div>
    </div>
    <!--endprint-->
  </div>

</template>
<script>
import { chunk } from 'lodash-es';
import { printContent } from './helper';

export default {
  props: {
    orginData: {
      type: Object,
    },
  },
  data() {
    return {
      data: {},
      pageData: [],
      total: 100,
      page: 1,
      columns: [
        {
          title: '',
          field: 'index',
          width: 52,
          align: 'center',
          colspan: 1,
          rowspan: 1,
          checked: true,
          minFontSize: 5,
          fontFamily: 'STSong,SimSun',
          columnId: 'ItemName',
        },
        {
          title: '',
          field: 'ItemName',
          width: 52,
          align: 'left',
          colspan: 1,
          rowspan: 1,
          checked: true,
          minFontSize: 5,
          fontFamily: 'STSong,SimSun',
          columnId: 'ItemName',
        },
        {
          field: 'Specifications',
          width: 26,
          align: 'left',
          colspan: 1,
          rowspan: 1,
          checked: true,
          minFontSize: 5,
          fontFamily: 'STSong,SimSun',
          columnId: 'Specifications',
        },
        {
          field: 'Unit',
          width: 12,
          align: 'center',
          colspan: 1,
          rowspan: 1,
          checked: true,
          minFontSize: 5,
          fontFamily: 'STSong,SimSun',
          columnId: 'Unit',
        },
        {
          field: 'Quantity',
          width: 20,
          align: 'right',
          colspan: 1,
          rowspan: 1,
          checked: true,
          minFontSize: 5,
          fontFamily: 'STSong,SimSun',
          columnId: 'Quantity',
          formatter: (val) => (val === 0 ? '' : val),
        },
        {
          field: 'UnitPriceTaxExcluded',
          width: 20,
          align: 'right',
          colspan: 1,
          rowspan: 1,
          checked: true,
          minFontSize: 5,
          fontFamily: 'STSong,SimSun',
          columnId: 'UnitPriceTaxExcluded',
          formatter: (amount) => (!amount ? '' : `${Number(amount || 0).toFixed(2)}`),
        },
        {
          field: 'AmountTaxExcluded',
          width: 30,
          align: 'right',
          colspan: 1,
          rowspan: 1,
          checked: true,
          fontSize: 9,
          fontFamily: 'STSong,SimSun',
          columnId: 'AmountTaxExcluded',
          formatter: (amount) => `${Number(amount || 0).toFixed(2)}`,
        },
        {
          field: 'TaxRate',
          width: 11,
          align: 'right',
          colspan: 1,
          rowspan: 1,
          checked: true,
          fontSize: 9,
          fontFamily: 'STSong,SimSun',
          columnId: 'TaxRate',
          formatter: (val) => `${Number(val || 0) * 100 }%`,
        },
        {
          field: 'Tax',
          width: 29,
          align: 'right',
          colspan: 1,
          rowspan: 1,
          checked: true,
          fontSize: 9,
          fontFamily: 'STSong,SimSun',
          columnId: 'Tax',
          formatter: (amount) => `${Number(amount || 0).toFixed(2)}`,
        },
      ],
    };
  },
  computed: {
    InvoiceType() {
      return this.orginData.InvoiceType === 1 ? '增值税专用发票' : '增值税普通发票';
    },
  },
  mounted() {
    this.data = this.orginData;
    this.splitData(this.data);
  },
  methods: {
    getStyle(options) {
      const { align } = options;
      return {
        textAlign: align,
        fontSize: '8pt',
      };
    },
    splitData(data) {
      this.pageData = chunk(data.InvoiceItems, 25);
    },
    // 计算合计， isNegative 标识是否为折扣合计
    computedTotalByFiled(data, filed, isNegative = false) {
      // eslint-disable-next-line no-return-assign
      return data.filter((item) => !!item.isNegative === isNegative).reduce((a, b) => a += Number(b[filed] || 0), 0).toFixed(2);
    },
    print() {
      const dom = this.$refs.printBox;
      printContent(
        dom.outerHTML,
        true,
        true,
        210,
        297,
      );
    },
  },
};
</script>
<style lang="scss" scoped id="app">
.printBox{
  width: 210mm;
}
.invoice-detail {
  font-family: STKaiti, KaiTi;
  // color: #66ccff;
  // background: url('./bg/bill_image.png');
  width: 210mm;
  height: 297mm;
  position: relative;
  // border: 1px solid #ccc;
  page-break-before: auto;
	page-break-after: always;
  color: #000;
  > * {
    position: absolute;
  }
  &-header {
    top: 14mm;
    font-size: 18pt;
    font-weight: bold;
    text-align: center;
    width: 100%;
  }
  &-PurchaserName {
    top: 27mm;
    left: 10mm;
  }
  &-MerchantName {
    top: 37mm;
    left: 10mm;
  }
  &-InvoiceCode {
    top: 49mm;
    left: 10mm;
    width: 85mm;
  }
  &-InvoiceNum {
    top: 49mm;
    left: 95mm;
  }
  &-total {
    top: 49mm;
    left: 95mm + 70mm;
  }
  &-page {
    top: 49mm;
    left: 95mm + 70mm + 21mm;
  }
  &-total,
  &-page {
    display: flex;
    width: 18mm;
    justify-content: space-between;
  }
  &-table {
    top: 58mm;
    left: 4.5mm;
    border: 2px solid #000;
    height: 216mm;
    width: 201mm;
    &-row {
      display: flex;
      width: 100%;
      height: 7mm;
      // line-height: 7mm;
    }
    &-col {
      // border-right: 1px solid #000;
      box-sizing: border-box;
      font-size: 10pt;
      padding: 0 2px;
      &:nth-child(1) {
        flex-basis: 9mm;
      }
      &:nth-child(2) {
        flex-basis: 43mm;
      }
      &:nth-child(3) {
        flex-basis: 26mm;
      }
      &:nth-child(4) {
        flex-basis: 12mm;
      }
      &:nth-child(5) {
        flex-basis: 20mm;
      }
      &:nth-child(6) {
        flex-basis: 20mm;
      }
      &:nth-child(7) {
        flex-basis: 30mm;
      }
      &:nth-child(8) {
        flex-basis: 11mm;
      }
      &:nth-child(9) {
        flex-basis: 30mm;
      }
      // &:nth-child(9) {
      //   width: 12mm;
      // }
    }
    &-header {
      line-height: 7mm;
      border-bottom: 1px solid #000;
      margin-bottom: 2mm;
      & > div {
        text-align: center;
      }
    }
    &-background {
      position: absolute;
      top: 0;
      left: 0;
      height: calc(100% - 8mm);
      & > div:not(:last-child) {
        border-right: 1px solid #000;
      }
    }
    &-remark {
      border-top: 1px solid #000;
      height: 8mm;
      position: absolute;
      bottom: 0;
      line-height: 8mm;
      & > div {
        &:nth-child(1) {
          flex-basis: 9mm;
          text-align: center;
          border-right: 1px solid #000;
        }
        &:nth-child(2) {
          flex-basis: 192mm;
        }
      }
    }
    &-sumBox {
      position: absolute;
      bottom: 8mm;
      padding-bottom: 1px;
      width: 100%;
      & > div {
        height: 7mm;
        line-height: 7mm;
        .invoice-detail-table-col {
          text-align: center;
          font-size: 12px;
        }
      }
    }
  }
  &-seller {
    top: 277.5mm;
    left: 4.5mm;
  }
  &-invoiceTime {
    top: 277.5mm;
    left: 138mm;
  }
  &-tip {
    top: 287.5mm;
    left: 4.5mm;
  }
}

@media print {
  @page {
    size: 210mm 297mm !important;
    border: 0;
    padding: 0cm  !important;
    margin: 0cm !important;
    overflow: hidden;
  }
}
</style>
