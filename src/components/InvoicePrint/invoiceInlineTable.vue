<template>
  <div class="invoiceInlineTable clearfix">
    <div v-for="(item, index) in data" style="overflow: hidden;" :key="index" class="table-tr clearfix">
      <div class="table-td" style="float: left;
      box-sizing: border-box;
      color: #000;" v-for="col in columns" :key="col.field" :style="{...getStyle(col), top: `${57 + (index * 4 )}mm`}">
        {{col.formatter ? col.formatter(item[col.field]) : item[col.field]}}
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'invoiceInlineTable',
  inject: ['getStyle'],
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Array,
      default: () => [],
    },
  },
};
</script>
<style lang="scss" scoped>
.invoiceInlineTable {
  .table-tr {
    overflow: hidden;
    .table-td {
      position: absolute;
      float: left;
      box-sizing: border-box;
      padding: 1pt 1pt;
      color: #000;
      span{
        display: inline-block;
      }

    }
  }
}
</style>
