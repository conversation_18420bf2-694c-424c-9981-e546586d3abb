<template>
  <div class="invoice-box" ref="printBox">
    <img class="invoice-bg noPrint"   src="./bg/invoice_bg.png" style="" />
    <!-- <span class="invoice-title noPrint">
      {{invioiceTypeText}}
    </span> -->
    <!-- <div class="invoice-box-item" v-for="item in data" :key="item.id" :style="item.style">{{item.text}}</div> -->
    <div class="invoice-tempalte">
      <div v-for="(item, index) in data" :key="index"  :class="[
      item.printElementType.type,
      item.options.noPrint ? 'noPrint' : '',
      item.printElementType.type !== 'tableCustom' ? 'print-element' : '']"
      :style="getStyle(item.options)">
        <invoiceInlineTable ref="table" v-if="item.printElementType.type === 'tableCustom'" :columns="item.options.columns[0]" :data="item.data" />
        <!-- <VueQrcode v-else-if="item.options.field === 'QrCode'" :value="item.data" :options="{ size: 77 }" /> -->
        <img v-else-if="item.printElementType.type === 'image' && item.data" :src="item.data" class="invoice-qrcode noPrint" />
        <template v-else-if="item.options.field === 'Remarks'"><p style="white-space: pre-line;">{{item.data}}</p></template>
        <template v-else-if="item.printElementType.type === 'html'">
          <div v-html="item.options.formatter ? item.options.formatter(item.data) : item.data"></div>
        </template>
        <template v-else>{{item.options.formatter ? item.options.formatter(item.data) : item.data}}</template>
      </div>
    </div>
  </div>
</template>
<script>
import {
 cloneDeep, forEach, find, isEmpty,
} from 'lodash-es';
import QRCode from 'qrcode';
import options from './options';
import { computedTextFontSize } from './helper';
import invoiceInlineTable from './invoiceInlineTable.vue';
// import { transformMoney } from '../../assets/Utils';

const TotalAmountUpBefor = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAD6ADAAQAAAABAAAADwAAAAAHNtsJAAAA2UlEQVQoFaWTsQ3CMBBFHRiBAjpGoIRFQAiYIXOERSjoYIRIVEisQcUIRPxn2VF8SaSIfOkR3fm+8cWXzKWaKNyJg5iJr5iKj7iIq6hES0tlnqIQc7O6CHnWqUtE4iXWSbYdbEJdvQFHZcejuImV6BJ51qmjHp/bC46KYoHdwOapx+fuotmjLbQxHurxuQc/RtGwVb6vFe8rjTGGGN+CZ5dKmuYerfjnk8jDk9jK+0b1/M/bPusY/m2Pumd6GTphTCCTWE8YZkSib7a5VwYjme1MiaZoYfBX9QM7TTQ2a0LGQwAAAABJRU5ErkJggg==';
export default {
  components: { invoiceInlineTable },
  provide() {
    return {
      getStyle: this.getStyle,
    };
  },
  props: {
    orginData: {
      type: Object,
    },
  },
  data() {
    return {
      options: cloneDeep(options),
      data: [],
    };
  },
  mounted() {
    this.transformData(this.orginData);
  },
  watch: {
    orginData() {
      this.transformData(this.orginData);
    },
  },
  computed: {
    titile() {
      return `${this.invoiceTypeText}`;
    },
    kaiyingInvoiceTypeList() {
      return this.$store.state.staticJson.kaiyingInvoiceTypeList || [];
    },
    invioiceTypeText() {
      const item = find(this.kaiyingInvoiceTypeList, { keyValue: this.orginData.InvoiceType });
      let text;
      switch (item.keyValue) {
        case 1:
          text = '增值税专用发票';
          break;
        case 2:
          text = '增值税普通发票';
          break;
        case 3:
          text = '卷票';
          break;
        case 4:
          text = '增值税电子普通发票';
          break;
        case 8:
          text = '增值税电子专用发票';
          break;
        default:
          text = '';
      }
      return text;
    },
  },
  methods: {
    getStyle(option) {
      const {
        left, top, width, height, fontSize, letterSpacing, align, fontFamily, lineHeight,
        } = option;
      return {
        left: `${left}mm`,
        top: `${top}mm`,
        width: `${width}mm`,
        height: `${height}mm`,
        lineHeight: fontSize <= 7.5 ? `${fontSize / 2.8 - 0.5}mm` : (lineHeight ? `${lineHeight}mm` : ''),
        fontSize: `${fontSize}pt`,
        letterSpacing: letterSpacing ? `${letterSpacing / 2.8}mm` : '',
        textAlign: align || 'left',
        color: '#000',
        whiteSpace: 'normal',
        fontFamily,
        fontSizeForPx: fontSize * 72 / 75,
      };
    },
    async getValueForField(field, data) {
      if (field.indexOf('&') !== -1) {
        const fields = field.split('&');
        console.log(fields);
        const value = fields.reduce((prev, prop) => { console.log(data[prop], prev, prop); return `${prev} ${data[prop]}`; }, '');
        return value;
      }
      if (field === 'InvoiceItems' && data.ListFlag === 1) {
        return this.transformGoodsDataWithListFlag(data[field], data);
      }
      if (field === 'invoiceTypeText') {
        return this.invioiceTypeText;
      }
      if (field === 'QrCode' && data[field]) {
         const qrCodeBase64 = await QRCode.toDataURL(data[field], {
            errorCorrectionLevel: 'L',
            margin: 1,
            height: 64,
            width: 64,
            type: '10',
            scal: 177,
            color: {
                dark: '#000', // 二维码背景颜色
                // light: '#000' // 二维码前景颜色
            },
            rendererOpts: {
                quality: 0.9,
            },
        });
        return qrCodeBase64;
      }
      return data[field];
    },
    // 非普通发票移除 添加校验码
    addVerifyCodeWithPUPiao() {
      console.log(this.orginData.InvoiceType, '非普通发票移除 添加校验码');
      if (this.orginData.InvoiceType !== 2) {
        const Remarks = this.options.printElements.find((item) => item.options.field === 'Remarks');
        const VerifyCode = this.options.printElements.find((item) => item.options.field === 'VerifyCode');
        if (!VerifyCode) return;
        Remarks.options.top = VerifyCode.options.top;
        this.options.printElements.splice(this.options.printElements.indexOf(VerifyCode), 1);
      }
    },
    /**
     * 如果“清单标志”=1,商品区域打印的效果
     * 【货物或应税劳务、服务名称】打印为详见销货清单
     * 规格型号】空白
      【单位】空白
      【数量】空白
      【单价】空白
      【金额】取清单内所有商品的金额（不含税）合计
      【税率】取清单内第一个商品的税率
      【税额】取清单内所有商品的税额合计
     */
    transformGoodsDataWithListFlag(goodsData, invoiceData) {
      const ItemName = invoiceData.IssueType === 1 ? '详见对应正数发票及清单' : '详见销货清单';
      const AmountTaxExcluded = invoiceData.TotalAmountTaxExcluded;
      const TaxRate = isEmpty(goodsData) ? '' : goodsData[0].TaxRate;
      const Tax = invoiceData.TotalTax;
      return [{
        ItemName,
        AmountTaxExcluded,
        TaxRate,
        Tax,
        Unit: '',
        UnitPrice: '',
        Specifications: '',
        Quantity: '',
      }];
    },
    async transformData(data) {
      this.addVerifyCodeWithPUPiao();
      data.TotalAmountUpBefor = TotalAmountUpBefor; // 添加价税合计大写，前面的 圆叉 图标
      const result = await Promise.all(this.options.printElements.map(async (item) => {
        const { field } = item.options;
        const newItem = cloneDeep(item);
        if (field) {
          newItem.data = await this.getValueForField(field, data);
          if (newItem.printElementType.type !== 'tableCustom' && newItem.options.minFontSize) {
            newItem.options.fontSize = computedTextFontSize(newItem.data, newItem.options.width, newItem.options.minFontSize, newItem.options.fontFamily);
          }

          if (newItem.printElementType.type === 'tableCustom') {
            console.log(newItem.printElementType.type);
            this.computedTableOptionFontSize(newItem.options.columns[0], newItem.data);
          }
        }
        return newItem;
      })).then((res) => res);
      console.log(result);
      this.data = result;
    },
    computedTableOptionFontSize(columns, data) {
      // 计算每列所有数据字号， 取最小值
      forEach(columns, (col) => {
        if (col.minFontSize) {
          let fontSize = 9;
          forEach(data, (obj) => {
            const computedFontSize = computedTextFontSize(obj[col.field], col.width, col.minFontSize, col.fontFamily);
            // console.log(data, computedFontSize);
            fontSize = computedFontSize < fontSize ? computedFontSize : fontSize;
            console.log(obj[col.field], fontSize, col.width);
            // 如果计算出小于最小字号的fontSize 则可以提前退出循环
            if (fontSize <= col.minFontSize) {
              return false;
            }
            return true;
          });
          col.fontSize = fontSize;
        }
      });
    },
    print(LODOP) {
      const bgStr = 'data:image/png;base64,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';

      // 添加背景图
      LODOP.ADD_PRINT_SETUP_BKIMG(bgStr);
      LODOP.SET_SHOW_MODE('BKIMG_WIDTH', '240mm');
      LODOP.SET_SHOW_MODE('BKIMG_HEIGHT', '140mm');
      LODOP.SET_SHOW_MODE('BKIMG_IN_PREVIEW', true);
      Object.entries(this.data).forEach(([key, item]) => {
        const style = this.getStyle(item.options);

        if (item.printElementType.type === 'tableCustom') {
          const table = this.$refs.table[0].$el;
          const {
               top,
              } = item.options;
          // LODOP.ADD_PRINT_HTM(style.top, style.left, style.width, style.height, table.innerHTML);
          const columns = item.options.columns[0];
          item.data.forEach((row, index) => {
            columns.forEach((col) => {
              const colStyle = this.getStyle(col);
              colStyle.top = `${top + (4.4 * index)}mm`;
               this.setTextPrintItem(LODOP, row[col.field], colStyle, col.formatter);
               LODOP.SET_PRINT_STYLEA(0, 'LineSpacing', '-1mm');
            });
          });
        } else if (item.printElementType.type === 'image') {
          // 图片打印不清晰， 需要使用lodop 画图功能输出大写金额前的图片
          if (item.options.field === 'TotalAmountUpBefor') {
            LODOP.ADD_PRINT_ELLIPSE('99.32mm', '74.24mm', '4mm', '4mm', 0, 0.5);
            LODOP.SET_PRINT_STYLEA(0, 'ScalX', 0.8);
            LODOP.SET_PRINT_STYLEA(0, 'ScalY', 0.8);
            LODOP.ADD_PRINT_LINE('102.68mm', '74.77mm', '99.77mm', '77.68mm', 0, 0.5);
            LODOP.SET_PRINT_STYLEA(0, 'ScalX', 0.8);
            LODOP.SET_PRINT_STYLEA(0, 'ScalY', 0.8);
            LODOP.ADD_PRINT_LINE('99.72mm', '74.77mm', '102.92mm', '77.68mm', 0, 0.5);
            LODOP.SET_PRINT_STYLEA(0, 'ScalX', 0.8);
            LODOP.SET_PRINT_STYLEA(0, 'ScalY', 0.8);
          } else {
            LODOP.ADD_PRINT_IMAGE(style.top, style.left, style.width, style.height, item.data);
          }
          // LODOP.ADD_PRINT_IMAGE(style.top, style.left, style.width, style.height, item.data);
        } else {
          this.setTextPrintItem(LODOP, item.data, style, item.options.formatter, item.printElementType.type === 'html');
          if (item.printElementType.type === 'longText') {
            // console.log(style.lineHeight, 'longText')
            LODOP.SET_PRINT_STYLEA(0, 'LineSpacing', '-0.5mm');
          } else {
            LODOP.SET_PRINT_STYLEA(0, 'LineSpacing', '-1.1mm');
          }
        }

        if (item.options.noPrint) {
            LODOP.SET_PRINT_STYLEA(0, 'PreviewOnly', 1);
          }
       });
      //  LODOP.SET_PRINT_MODE("POS_BASEON_PAPER",true);
       LODOP.SET_PRINT_PAGESIZE(0, 2400, 1400, '发票');
    },
    setTextPrintItem(LODOP, data, style, formatter, isHTML) {
      if (!isHTML) {
        LODOP.ADD_PRINT_TEXT(style.top, style.left, style.width, style.height, formatter ? formatter(data) : data);
      } else {
        LODOP.ADD_PRINT_HTM(style.top, style.left, style.width, style.height, formatter ? formatter(data) : data);
      }

          if (style.textAlign === 'right') {
            LODOP.SET_PRINT_STYLEA(0, 'Alignment', 3);
          } else if (style.textAlign === 'center') {
            LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2);
          }
          if (style.fontFamily) {
            LODOP.SET_PRINT_STYLEA(0, 'FontName', style.fontFamily);
          }

          style.fontSizeForPx && LODOP.SET_PRINT_STYLEA(0, 'FontSize', style.fontSizeForPx);

          LODOP.SET_PRINT_STYLEA(0, 'TextNeatRow', true);
          style.letterSpacing && LODOP.SET_PRINT_STYLEA(0, 'LetterSpacing', style.letterSpacing);
    },
    // toPrintContent(top, left) {
    //   // const dom = this.$refs.printBox;
    //   // const style = `<style>
    //   //   .invoice-tempalte{
    //   //     left: ${left}mm;
    //   //     top: ${top}mm;
    //   //   }
    //   // </style>`;
    //   // return `${style}${dom.outerHTML}`;
    //   LODOP.PREVIEW()
    // },
  },
};
</script>
<style lang="scss" scoped>
.invoice-box{
  font-family:STSong,SimSun;
  // background: url('./bg/bill_image.png');
  width: 240mm;
  height: 140mm;
  position: relative;
  overflow: hidden;
  line-height: normal;
  // page-break-before: auto;
  // page-break-after: always;

  .invoice-box-item{
    position: absolute;
  }
  .invoice-bg{
    width: 240mm;
    position: absolute;
  }
  .invoice-title{
    font-size: 24px;
    position: absolute;
    text-align: center;
    width: 100%;
    top: 6.7mm;
  }
  .invoice-tempalte{
    margin: 0 auto;
    width: 240mm;
    height: 140mm;
    box-sizing: border-box;
    position: absolute;
    word-break: break-all;
    .print-element{
      position: absolute;
      // border: 1px dashed #ddd;
      // font-size: 12px;
      // background: #ccc;
      color: #000;
      .invoice-qrcode{
        width: 100%;
      }
    }
    .print-element.longText{
      word-break: break-all;
    }
  }
}
@media print {
  @page {
    size: 240mm 140mm !important;
    border:0  !important;
    padding:0cm  !important;
    margin:0cm  !important;
    overflow: hidden;
  }
  .noPrint{
    display: none;
  }
}
</style>
