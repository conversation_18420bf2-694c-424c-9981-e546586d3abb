import dayjs from 'dayjs';
import { chunk } from 'lodash-es';
import { breakText, transformMoneyUp, clearBreak } from './helper';

const options = {
  panels: [
    {
      index: 0,
      height: 140,
      width: 220,
      paperHeader: 0,
      paperFooter: 396.85039370078744,
      printElements: [
        {
          options: {
            left: 20.3,
            top: 7,
            height: 17,
            width: 17,
            field: 'QrCode',
          },
          printElementType: { type: 'image' },
        },
        {
          options: {
            left: 44.5,
            top: 20,
            height: 4,
            fontSize: 9,
            width: 100,
            field: '',
            formatter: () => '机器编号:',
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 97.5,
            top: 6.7,
            height: 4,
            fontSize: 18,
            width: '100',
            field: 'invoiceTypeText',
            noPrint: true,
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 44.5,
            top: 25,
            height: 4,
            width: 100,
            field: 'UDiskSn',
          },
          printElementType: { type: 'text' },
        },

        {
          options: {
            left: 170,
            top: 10.5,
            height: 9.75,
            width: 117,
            field: 'InvoiceNum',
            noPrint: true,
            fontFamily: 'Courier New',
            fontSize: 16.5,
            letterSpacing: 0,
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 43,
            top: 10.7,
            height: 9.75,
            width: 117,
            field: 'InvoiceCode',
            noPrint: true,
            fontFamily: 'Courier New',
            fontSize: 16.5,
            letterSpacing: 0,
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 201.5,
            top: 13,
            height: 4,
            width: 27,
            field: 'InvoiceCode',
            fontFamily: 'Courier New',
            fontSize: 8,
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 201.5,
            top: 17,
            height: 4,
            width: 27,
            field: 'InvoiceNum',
            fontFamily: 'Courier New',
            fontSize: 11,
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 188,
            top: 23.5,
            height: 4,
            width: 40,
            fontSize: 10,
            fontFamily: '宋体',
            lineHeight: 4,
            field: 'InvoiceTime',
            formatter: (date) => dayjs(date).format('YYYY年MM月DD日'),
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 143,
            top: 32,
            height: 20,
            width: 80,
            lineHeight: 4,
            fontSize: 12,
            fontFamily: 'Monospace',
            field: 'Ciphertext',
            formatter: (text) => {
              if (!text) return '';
              return `<div style=" font-family: 'Courier New'">${breakText(text, text.length / 4).join('<br />')}</div>`;
            },
          },
          printElementType: { type: 'html' },
        },
        {
          options: {
            left: 53.5,
            top: 31.6,
            height: 4,
            width: 77,
            fontSize: 12,
            minFontSize: 7.5,
            fontFamily: '宋体',
            field: 'PurchaserName',
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 53.5,
            top: 36.6,
            height: 4,
            width: 77,
            fontSize: 12,
            fontFamily: 'Courier New',
            field: 'PurchaserTaxID',
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 53.5,
            top: 42,
            height: 4,
            width: 77,
            fontSize: 12,
            minFontSize: 7.5,
            fontFamily: '宋体',
            formatter: clearBreak,
            field: 'PurchaserAddrAndPhone',
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 53.5,
            top: 47,
            height: 4,
            width: 77,
            minFontSize: 7.5,
            fontFamily: '宋体',
            formatter: clearBreak,
            field: 'PurchaserBankAndAccount',
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 23,
            top: 57,
            height: 38,
            width: 202,
            field: 'InvoiceItems',
            tableBorder: 'noBorder',
            tableBodyRowHeight: 12,
            tableBodyRowBorder: 'noBorder',
            columns: [
              [
                {
                  title: '',
                  field: 'ItemName',
                  width: 48,
                  height: 4.5,
                  left: 23,
                  align: 'left',
                  colspan: 1,
                  rowspan: 1,
                  checked: true,
                  minFontSize: 6,
                  fontFamily: '宋体',
                  columnId: 'ItemName',
                },
                {
                  field: 'Specifications',
                  width: 25,
                  align: 'left',
                  height: 4.5,
                  left: 73,
                  colspan: 1,
                  rowspan: 1,
                  checked: true,
                  minFontSize: 6,
                  fontFamily: '宋体',
                  columnId: 'Specifications',
                },
                {
                  field: 'Unit',
                  width: 12,
                  align: 'center',
                  height: 4.5,
                  left: 98.5,
                  colspan: 1,
                  rowspan: 1,
                  checked: true,
                  minFontSize: 5,
                  fontFamily: '宋体',
                  columnId: 'Unit',
                },
                {
                  field: 'Quantity',
                  width: 19,
                  height: 4.5,
                  left: 110.5,
                  align: 'right',
                  colspan: 1,
                  rowspan: 1,
                  checked: true,
                  minFontSize: 6,
                  fontFamily: '宋体',
                  columnId: 'Quantity',
                  formatter: (val) => (val === 0 ? '' : val),
                },
                {
                  field: 'UnitPriceTaxExcluded',
                  width: 21,
                  height: 4.5,
                  left: 128.5,
                  align: 'right',
                  colspan: 1,
                  rowspan: 1,
                  checked: true,
                  minFontSize: 6,
                  fontFamily: '宋体',
                  columnId: 'UnitPriceTaxExcluded',
                },
                {
                  field: 'AmountTaxExcluded',
                  width: 29.5,
                  height: 4.5,
                  left: 151,
                  align: 'right',
                  colspan: 1,
                  rowspan: 1,
                  checked: true,
                  fontSize: 9,
                  fontFamily: '宋体',
                  columnId: 'AmountTaxExcluded',
                  formatter: (val) => Number(val).toFixed(2),
                },
                {
                  field: 'TaxRate',
                  width: 10,
                  left: 181.5,
                  height: 4.5,
                  align: 'center',
                  colspan: 1,
                  rowspan: 1,
                  checked: true,
                  fontSize: 9,
                  fontFamily: '宋体',
                  formatter: (val) => `${Number(val) * 100 }%`,
                  columnId: 'TaxRate',
                },
                {
                  field: 'Tax',
                  width: 28,
                  left: 192.5,
                  height: 4.5,
                  align: 'right',
                  colspan: 1,
                  rowspan: 1,
                  checked: true,
                  fontSize: 9,
                  fontFamily: '宋体',
                  columnId: 'Tax',
                  formatter: (val) => Number(val).toFixed(2),
                },
              ],
            ],
          },
          printElementType: { title: '表格', type: 'tableCustom' },
        },
        {
          options: {
            left: 74.5,
            top: 99.3,
            height: 4,
            width: 4,
            field: 'TotalAmountUpBefor',
          },
          printElementType: { type: 'image' },
        },
        {
          options: {
            left: 80,
            top: 99.7,
            height: 5,
            width: 84,
            fontSize: 9,
            fontFamily: '宋体',
            field: 'TotalAmount',
            formatter: (amount) => transformMoneyUp(amount),
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 180,
            top: 99,
            height: 4,
            width: 39,
            fontSize: 9,
            fontFamily: 'Courier New',
            field: 'TotalAmount',
            formatter: (amount) => `¥${Number(amount).toFixed(2)}`,
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 53.5,
            top: 106.5,
            height: 4,
            lineHeight: 5,
            width: 77,
            minFontSize: 7.5,
            fontFamily: '宋体',
            field: 'MerchantName',
          },
          printElementType: { type: 'text' },
        },

        {
          options: {
            left: 53.5,
           	top: 110.6,
            height: 4,
            width: 77,
            fontSize: 12,
            fontFamily: 'Courier New',
            field: 'MerchantTaxID',
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 53.5,
            top: 115.6,
            height: 4,
            width: 77,
            minFontSize: 7.5,
            fontFamily: '宋体',
            formatter: clearBreak,
            field: 'MerchantAddrAndPhone',
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 53.5,
            top: 120.7,
            height: 4,
            width: 77,
            minFontSize: 7.5,
            fontFamily: '宋体',
            formatter: clearBreak,
            field: 'MerchantBankAndAccount',
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 142,
            top: 110.5,
            height: 18,
            width: 78,
            minFontSize: 7.5,
            fontFamily: '宋体',
            field: 'Remarks',
          },
          printElementType: { type: 'longText' },
        },
        {
          options: {
            left: 142,
            top: 106.5,
            height: 18,
            width: 78,
            minFontSize: 7.5,
            fontFamily: '宋体',
            field: 'VerifyCode',
            formatter: (data) => {
              if (data) {
                return `校验码 ${chunk(data.split(''), 5).map((item) => item.join('')).join(' ')}`;
              }
              return '';
            },
          },
          printElementType: { type: 'longText' },
        },
        {
          options: {
 left: 511.5, top: 327, height: 9.75, width: 60,
},
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 41,
            top: 126.7,
            height: 4,
            width: 40,
            fontSize: 9,
            lineHeight: 4,
            fontFamily: '宋体',
            field: 'Payee',
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 93,
            top: 126.7,
            height: 4,
            width: 30,
            fontSize: 9,
            lineHeight: 4,
            fontFamily: '宋体',
            field: 'Reviewer',
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 141,
            top: 126.7,
            height: 4,
            width: 30,
            fontSize: 9,
            lineHeight: 4,
            fontFamily: '宋体',
            field: 'Drawee',
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 151.5,
            top: 92,
            height: 4,
            width: 29,
            align: 'right',
            fontSize: 9,
            fontFamily: 'Courier New',
            field: 'TotalAmountTaxExcluded',
            formatter: (amount) => `¥${Number(amount).toFixed(2)}`,
          },
          printElementType: { type: 'text' },
        },
        {
          options: {
            left: 192,
            top: 92,
            height: 4,
            width: 28.5,
            align: 'right',
            fontSize: 9,
            fontFamily: 'Courier New',
            field: 'TotalTax',
            formatter: (amount) => `¥${Number(amount).toFixed(2)}`,
          },
          printElementType: { type: 'text' },
        },
      ],
      paperNumberLeft: 565.5,
      paperNumberTop: 809,
    },
  ],
};

export default options.panels[0];
