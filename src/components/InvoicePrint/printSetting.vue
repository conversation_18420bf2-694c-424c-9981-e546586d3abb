<template>
  <el-form inline>
    <el-form-item label="上边距">
      <el-input-number v-model="position.top" />
    </el-form-item>
    <el-form-item label="左边距">
      <el-input-number v-model="position.left" />
    </el-form-item>
    <p class="Danger">
      提示：边距每+-一个单位相当于边距的0.1cm。
    </p>
    <p class="Danger">
      点击确定后进入打印预览
    </p>
  </el-form>
</template>
<script>
export default {
  name: 'print-setting',
  data() {
    return {
      position: {
        top: 0,
        left: 0,
      },
    };
  },
  mounted() {
    const position = window.localStorage.getItem('invoice-print-position');
    if (position) {
      this.position = JSON.parse(position);
    }
  },
  watch: {
    position: {
      deep: true,
      handler() {
        window.localStorage.setItem('invoice-print-position', JSON.stringify(this.position));
      },
    },
  },
};
</script>
