import { ref, reactive } from 'vue';

export default function useDialogControl(initialData = {}) {
  const visible = ref(false);
  const data = reactive({ ...initialData });

  const open = () => {
    visible.value = true;
  };

  const close = () => {
    visible.value = false;
    // 重置数据
    Object.keys(data).forEach((key) => {
      if (typeof initialData[key] !== 'undefined') {
        data[key] = initialData[key];
      }
    });
  };

  return {
    visible,
    data,
    open,
    close,
  };
}
