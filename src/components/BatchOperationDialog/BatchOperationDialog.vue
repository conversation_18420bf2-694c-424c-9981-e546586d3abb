<template>
  <el-dialog
    append-to-body
    modal-append-to-body
    :class="dialogClass"
    :title="title"
    v-bind="$attrs"
    v-on="$listeners"
    class="BatchOperationDialog"
    @opened="onOpened"
  >
    <main>
      <section>
        <h2>{{ contentTitle }}</h2>
        <el-radio-group v-model="model.contentScope">
          <el-radio :label="1">所有{{targetText}}</el-radio>
          <el-radio v-if="!isHideSearch" :label="2">搜索范围内的{{targetText}}</el-radio>
          <el-radio
            :label="3"
            :disabled="!hasSelection"
          >选中的{{targetText}}</el-radio>
        </el-radio-group>
        <!-- 预留插槽，用于扩展额外的表单内容 -->
        <slot name="form"></slot>
      </section>
    </main>

    <footer slot="footer">
      <el-button @click="onClickCancel">{{ cancelText }}</el-button>
      <el-button
        type="primary"
        :loading="loading"
        @click="onClickConfirm"
      >{{ confirmText }}</el-button>
    </footer>
  </el-dialog>
</template>

<script>
import { defineComponent, ref } from 'vue';

export default defineComponent({
  name: 'BatchOperationDialog',

  props: {
    // 弹窗标题
    title: {
      type: String,
      required: true,
    },
    // 内容描述
    contentTitle: {
      type: String,
      required: true,
    },
    // 数据模型
    model: {
      type: Object,
      required: true,
    },
    // 确认按钮文字
    confirmText: {
      type: String,
      default: '确认',
    },
    // 取消按钮文字
    cancelText: {
      type: String,
      default: '取消',
    },
    // 是否禁用按搜索范围操作
    isHideSearch: {
      type: Boolean,
      default: false,
    },
    // 操作对象文字
    targetText: {
      type: String,
      default: '账套',
    },
    // 是否有选中项
    hasSelection: {
      type: Boolean,
      default: false,
    },
    // 确认方法
    confirmMethod: {
      type: Function,
      required: true,
    },
    // 自定义类名
    dialogClass: {
      type: String,
      default: 'BatchOperationDialog',
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false,
    },
  },

  setup(props, { emit }) {
    const hide = () => {
      emit('update:visible', false);
    };

    const onClickConfirm = async () => {
      try {
        await props.confirmMethod();
        hide();
        emit('confirmed');
      } catch (error) {
        console.error(error);
      }
    };

    const onClickCancel = () => {
      hide();
    };

    const onOpened = () => {
      props.model.contentScope = props.hasSelection ? 3 : 1;
    };

    return {
      hide,
      onClickCancel,
      onClickConfirm,
      onOpened,
    };
  },
});
</script>

<style lang="scss" scoped>
.BatchOperationDialog {
  // section {
  //   margin: 0 12px 12px;
  // }

  h2 {
    margin-bottom: 16px;
    font-size: 14px;
    // color: #606266;
  }
}
</style>
