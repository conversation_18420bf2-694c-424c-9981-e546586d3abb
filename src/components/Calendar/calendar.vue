<template>
  <section class="wh_container">
    <div class="wh_content_all">
      <ul class="wh_top_changge" v-if="showHeader">
        <li @click="PreMonth(myDate,false)">
          <div class="wh_jiantou1"></div>
        </li>
        <li class="wh_content_li">{{dateTop}}</li>
        <li @click="NextMonth(myDate,false)">
          <div class="wh_jiantou2"></div>
        </li>
      </ul>
      <div class="wh_content">
        <div class="wh_content_item" :key="tag" v-for="tag in textTop">
          <div class="wh_top_tag">
            {{tag}}
          </div>
        </div>
      </div>
      <div class="wh_content">
        <div
          class="wh_content_item"
          v-for="(item,index) in list"
          :key="index" @click="clickDay(item,index)">
          <div
            class="wh_item_date"
            v-bind:class="[
            { wh_isMark: item.isMark},
            {wh_other_dayhide:item.otherMonth!=='nowMonth'},
            {wh_want_dayhide:item.dayHide},
            {wh_isToday:item.isToday},
            {wh_chose_day:item.chooseDay},
            {wh_item_date_istip: item.textArr.length},setClass(item)]"
            >
            <div  class="wh_item_date_day">{{item.id}}</div>
            <div class="wh_item_date_tip"
              v-if="item.textArr.length === 1">
              {{item.textArr&&item.textArr[0]}}
            </div>
            <el-tooltip  v-else-if="item.textArr.length > 1" placement="top">
              <div slot="content">
                <div class="more_tip"
                v-for="(text, index) in item.textArr"
                :key="index">
                  {{text}}
                </div>
              </div>
              <div class="wh_item_date_tipMore">...</div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<script>
import timeUtil from './calendar';

export default {
  data() {
    return {
      myDate: [],
      list: [],
      historyChose: [],
      dateTop: '',
    };
  },
  props: {
    markDate: {
      type: Array,
      default: () => [],
    },
    markDateMore: {
      type: Array,
      default: () => [],
    },
    textTop: {
      type: Array,
      default: () => ['日', '一', '二', '三', '四', '五', '六'],
    },
    sundayStart: {
      type: Boolean,
      default: () => true,
    },
    agoDayHide: { type: String, default: '0' },
    futureDayHide: { type: String, default: '2554387200' },
    showHeader: { type: Boolean, default: true },
  },
  created() {
    this.intStart();
    this.myDate = new Date();
  },
  methods: {
    intStart() {
      timeUtil.sundayStart = this.sundayStart;
    },
    setClass(data) {
      const obj = {};
      obj[data.markClassName] = data.markClassName;
      return obj;
    },
    clickDay(item) {
      if (item.otherMonth === 'nowMonth' && !item.dayHide) {
        this.getList(this.myDate, item.date);
        this.$emit('day-click', item);
      }
      if (item.otherMonth !== 'nowMonth') {
        if (item.otherMonth === 'preMonth') {
          this.PreMonth(item.date);
        } else {
          this.NextMonth(item.date);
        }
      }
    },
    ChoseMonth(date, isChosedDay = true) {
      const formatDate = timeUtil.dateFormat(date);
      this.myDate = new Date(formatDate);
      this.$emit('changeMonth', timeUtil.dateFormat(this.myDate));
      if (isChosedDay) {
        this.getList(this.myDate, formatDate, isChosedDay);
      } else {
        this.getList(this.myDate);
      }
    },
    PreMonth(date, isChosedDay = true) {
      const formatDate = timeUtil.dateFormat(date);
      this.myDate = timeUtil.getOtherMonth(this.myDate, 'preMonth');
      this.$emit('changeMonth', timeUtil.dateFormat(this.myDate));
      if (isChosedDay) {
        this.getList(this.myDate, formatDate, isChosedDay);
      } else {
        this.getList(this.myDate);
      }
    },
    NextMonth(date, isChosedDay = true) {
      const formatDate = timeUtil.dateFormat(date);
      this.myDate = timeUtil.getOtherMonth(this.myDate, 'nextMonth');
      this.$emit('changeMonth', timeUtil.dateFormat(this.myDate));
      if (isChosedDay) {
        this.getList(this.myDate, formatDate, isChosedDay);
      } else {
        this.getList(this.myDate);
      }
    },
    forMatArgs() {
      let { markDate } = this;
      let { markDateMore } = this;
      markDate = markDate.map((k) => timeUtil.dateFormat(k));
      markDateMore = markDateMore.map((k) => {
        k.date = timeUtil.dateFormat(k.date);
        return k;
      });
      return [markDate, markDateMore];
    },
    getList(date, chooseDay) {
      const [markDate, markDateMore] = this.forMatArgs();
      this.dateTop = `${date.getFullYear()}年${date.getMonth() + 1}月`;
      const arr = timeUtil.getMonthList(this.myDate);
      for (let i = 0; i < arr.length; i += 1) {
        let markClassName = '';
        let textArr = [];
        const k = arr[i];
        k.chooseDay = false;
        const nowTime = k.date;
        const t = new Date(nowTime).getTime() / 1000;
        // 看每一天的class
        Object.values(markDateMore).map((c) => {
          if (c.date === nowTime) {
            markClassName = c.className || '';
            ({ textArr } = c);
          }
          return c;
        });
        // 标记选中某些天 设置class
        k.markClassName = markClassName;
        k.textArr = textArr;
        k.isMark = markDate.indexOf(nowTime) > -1;
        // 无法选中某天
        k.dayHide = t < this.agoDayHide || t > this.futureDayHide;
        if (k.isToday) {
          this.$emit('isToday', nowTime);
        }
        const flag = !k.dayHide && k.otherMonth === 'nowMonth';
        if (chooseDay && chooseDay === nowTime && flag) {
          this.$emit('choseDay', nowTime);
          this.historyChose.push(nowTime);
          k.chooseDay = true;
        } else if (
          this.historyChose[this.historyChose.length - 1] === nowTime && !chooseDay && flag
        ) {
          k.chooseDay = true;
        }
      }
      this.list = arr;
    },
  },
  mounted() {
    this.getList(this.myDate);
  },
  watch: {
    markDate: {
      handler() {
        this.getList(this.myDate);
      },
      deep: true,
    },
    markDateMore: {
      handler() {
        this.getList(this.myDate);
      },
      deep: true,
    },
    agoDayHide: {
      handler(val) {
        this.agoDayHide = parseInt(val, 10);
        this.getList(this.myDate);
      },
      deep: true,
    },
    futureDayHide: {
      handler(val) {
        this.futureDayHide = parseInt(val, 10);
        this.getList(this.myDate);
      },
      deep: true,
    },
    sundayStart: {
      handler() {
        this.intStart();
        this.getList(this.myDate);
      },
      deep: true,
    },
  },
};
</script>
<style scoped>
@media screen and (min-width: 460px) {
  .wh_item_date:hover {
    background: #fff;
    cursor: pointer;
  }
}
* {
  margin: 0;
  padding: 0;
}

.wh_container {
  /* max-width: 410px; */
  margin: auto;
}

li {
  list-style-type: none;
}
.wh_top_changge {
  display: flex;
}

.wh_top_changge li {
  cursor: pointer;
  display: flex;
  color: #333;
  font-size: 18px;
  flex: 1;
  justify-content: center;
  align-items: center;
  height: 47px;
}

.wh_top_changge .wh_content_li {
  cursor: auto;
  flex: 2.5;
}
.wh_content_all {
  font-family: -apple-system, BlinkMacSystemFont, "PingFang SC",
    "Helvetica Neue", STHeiti, "Microsoft Yahei", Tahoma, Simsun, sans-serif;
  background-color: #fff;
  width: 100%;
  overflow: hidden;
  padding-bottom: 8px;
}

.wh_content {
  display: flex;
  flex-wrap: wrap;
  padding: 0 3% 0 3%;
  width: 100%;
}

.wh_content:first-child .wh_content_item_tag,
.wh_content:first-child .wh_content_item {
  color: #ddd;
  font-size: 16px;
}

.wh_content_item,
wh_content_item_tag {
  font-size: 15px;
  width: 13.4%;
  text-align: center;
  color: #333;
  position: relative;
}
.wh_content_item {
  height: 40px;
}

.wh_top_tag {
  width: 40px;
  height: 40px;
  line-height: 40px;
  margin: auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.wh_item_date {
  width: 50px;
  height: 40px;
  /* line-height: 40px; */
  margin: auto;
  /* display: flex; */
  /* justify-content: center;
  align-items: center; */
}

.wh_jiantou1 {
  width: 12px;
  height: 12px;
  border-top: 2px solid #333;
  border-left: 2px solid #333;
  transform: rotate(-45deg);
}

.wh_jiantou1:active,
.wh_jiantou2:active {
  border-color: #ddd;
}

.wh_jiantou2 {
  width: 12px;
  height: 12px;
  border-top: 2px solid #333;
  border-right: 2px solid #333;
  transform: rotate(45deg);
}
.wh_content_item > .wh_isMark {
  margin: auto;
  border-radius: 100px;
  background: blue;
  z-index: 2;
}
.wh_content_item .wh_other_dayhide {
  color: #bfbfbf;
}
.wh_content_item .wh_want_dayhide {
  color: #bfbfbf;
}
.wh_content_item .wh_isToday {
  /* background: yellow; */
  /* border-radius: 100px; */
  color: blue;
}
.wh_content_item .wh_chose_day {
  /* background: green;
  border-radius: 100px; */
  color: #66ccff;
}
.wh_item_date_day{
  display: block;
  line-height: 22px;
  width: 100%;
}
.wh_item_date_tip, .wh_item_date_tipMore{
  display: block;
  line-height: 13px;
  width: 100%;

}
.wh_item_date_tip{
  font-size: 12px;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}
.wh_item_date_tipMore{
  display: block;
  width: 100%;
  line-height: 16px;
  font-size: 16px;
  font-weight: 600;
}
.wh_item_date_istip{
  color: red;
}
.more_tip{
  line-height: 16px;
}
</style>
