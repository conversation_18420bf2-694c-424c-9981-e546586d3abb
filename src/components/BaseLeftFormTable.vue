<template>
  <div>这是基础table</div>
</template>

<script>
const excludeKeyword = [
  '确定',
  '取消',
  '删除',
  '退出',
  '上一步',
  '下一步',
  '确认新增',
  '保存草稿',
  '保存',
  '关闭',
];
export default {
  props: {
    tableData: {
      type: Array,
      default: () => [],
      required: true,
    },

    canEdit: {
      type: Boolean,
      default: false,
      required: true,
    },

    // 一个 el-form 的 ref
    leftForm: {
      type: Object,
      default: null,
    },
  },

  data() {
    return {
      editingRow: null,
      oldRow: null,
      addRow: {},
      addRowCopy: {},
      isFirstEnter: true,
      throttleBodyClick: null,
      throttleLayoutRepealBtn: null,
    };
  },

  mounted() {
    this.throttleBodyClick = Lib.Utils.throttle(($e) => {
      if ($e.which === 1 && this.canEdit) {
        this.rowClickControl($e);
      }
    }).bind(this); // 初始化节流函数

    this.throttleLayoutRepealBtn = Lib.Utils.throttle(
      this.layoutRepealBtn,
    ).bind(this); // 初始化节流函数

    setTimeout(() => {
      window.addEventListener('keyup', this.keup);
      window.addEventListener('click', this.throttleBodyClick);

      $(this.$el)
        .find('.el-table__body-wrapper .el-scrollbar__wrap')
        .on('scroll', () => {
          $(this.$el)
            .find('.repeal-row-btn')
            .css('opacity', 0);
          this.throttleLayoutRepealBtn();
        });

      $(this.$el)
        .closest('.el-card__body')
        .on('scroll', () => {
          $(this.$el)
            .find('.repeal-row-btn')
            .css('opacity', 0);
          this.throttleLayoutRepealBtn();
        });
    }, 100);
  },

  beforeDestroy() {
    window.removeEventListener('keyup', this.keup);
    window.removeEventListener('click', this.throttleBodyClick);

    $(this.$el)
      .find('.el-table__body-wrapper .el-scrollbar__wrap')
      .unbind('scroll');

    $(this.$el)
      .closest('.el-card__body')
      .unbind('scroll');
  },

  watch: {
    editingRow() {
      this.$nextTick(() => {
        // 第一次进入需等动画结束再计算位置。
        if (this.isFirstEnter) {
          setTimeout(() => {
            this.layoutRepealBtn();
            this.isFirstEnter = false;
          }, 1000);
        } else {
          this.layoutRepealBtn();
        }
      });
    },

    // 检验会引起页面上下移动，需要重新计算位置
    validStateHash() {
      this.throttleLayoutRepealBtn();
    },
  },

  computed: {
    validStateHash() {
      return this.leftForm && this.leftForm.fields
        ? this.leftForm.fields
          .filter((item) => item.validateState === 'error')
          .map((item, index) => index)
          .join('')
        : '';
    },
  },

  methods: {
    keup(e) {
      if (!this.canEdit) return;
      if (e.keyCode === 13) {
        // 防止与各种下拉控件回车事件冲突， 判断下拉控件的展示状态
        if (
          $('.el-autocomplete-suggestion:not(.el-autocomplete-suggestion-auto):visible').length
          || $('.el-select-dropdown:visible').length
          || $('.el-picker-panel:visible').length
          || $('.el-popper:visible').length
        ) {
          return;
        }
        this.saveRow();
      } else if (e.keyCode === 27) {
        this.repealRow();
      }
    },

    /**
     * 获取下一个输入控件
     * 移除当前输入控件的焦点，将焦点转移到下一个输入控件上
     */
    nextInput() {
      const el = this.getNextElement($('input:focus')[0]);
      if (!el) return;

      $('input:focus').trigger('blur');
      el.focus();
    },

    /**
     * @param {HTMLElement} relativeInput 作为比较起点的 input
     * @param {number} key 按键
     */
    getNextElement(relativeInput, key) {
      const allInputElements = document.querySelectorAll('input');

      // 找到当前 input 在所有 input 中的序号
      // TODO, 可以考虑用 indexOf 重构
      let index;
      for (index = 0; index < allInputElements.length; index += 1) {
        if (relativeInput === allInputElements[index]) break;
      }

      if (key === 33) {
        return allInputElements[index - 1];
      }
      return allInputElements[index + 1];
    },

    rowClickControl($e) {
      // console.log('this.$el', $(this.$el).closest('.el-card.left-form')[0]);
      setTimeout(() => {
        const index = this.tableData.indexOf(this.editingRow);
        const $currentTable = this.$el.querySelector('.el-table .el-scrollbar__view .el-table__body') || this.$el;
        const $rows = $currentTable.querySelectorAll('.el-table__row');
        const $scrollbars = this.$el.querySelectorAll('.el-scrollbar__bar') || [];
        const $currentRow = $rows.item(index);
        const $currentFixedRow = this.$el
          .querySelectorAll('.el-table__fixed .el-table__row')
          .item(index);
        const $currentLeftPanel = $(this.$el).closest('.el-card.left-form')[0]
          || $(this.$el).closest('.el-dialog')[0];
        const $target = $e.target;
        let $parent = $target;
        let isContain = false;
        let isLeftPanelInner = false;
        let isScrollbar = false;
        let isTableInner = false;

        if (excludeKeyword.indexOf($target.innerText) !== -1) {
          return;
        }

        if ($currentRow) {
        // 遍历点击点的所有父节点，看是否有当前编辑行
          while ($parent) {
            if ($parent === $currentTable) {
              isTableInner = true;
            }
            if ($parent === $currentLeftPanel) {
              isLeftPanelInner = true;
            }
            if ($parent === $currentRow || $parent === $currentFixedRow) {
              isContain = true;
              isTableInner = true;
              break;
            }
            // eslint-disable-next-line no-loop-func
            if ([...$scrollbars].find((el) => el === $parent)) {
              isScrollbar = true;
            }
            $parent = $parent.parentNode;
          }
          // 点击处在右滑框内并且不在激活行才触发保存
          if (isLeftPanelInner && !isScrollbar && !isContain) this.saveRow();
          if (!isTableInner && this.$refs.table) this.$refs.table.setCurrentRow(null);
        }
      }, 100);
    },

    layoutRepealBtn() {
      const $el = $(this.$el);
      const index = this.tableData.indexOf(this.editingRow);
      const $currentTable = $el.find('.el-table') || $el;
      const $tableBody = $currentTable.find('.el-table__body-wrapper').eq(0);
      const $editingTr = $currentTable.find('tr.el-table__row').eq(index);
      let obj = $editingTr[0];
      let sum = 0;

      if ($el.css('position') === 'static') $el.css('position', 'relative');
      while (obj && obj !== $currentTable[0]) {
        sum += obj.offsetTop;
        obj = obj.offsetParent;
      }
      sum -= $el.find('.el-scrollbar__wrap')[0].scrollTop;
      // 当撤销按钮处于table可见区域时才显示
      if (sum > 0 && sum < $tableBody.height()) {
        $(this.$el)
          .find('.repeal-row-btn')
          .css({
            position: 'absolute',
            top: sum,
            transform: 'translate(-123%, 80%)',
            opacity: 1,
          });
      }
    },

    isEditing(row) {
      return this.editingRow === row;
    },

    addInsertRow() {
      this.addRow.isAddRow = true;
      this.addRowCopy = Lib.Utils.deepClone(this.addRow);
      this.oldRow = Lib.Utils.deepClone(this.addRow);
      this.tableData.unshift(this.addRow);
      this.editingRow = this.addRow;
    },

    async editRow(row, col, e) {
      // 阻止冒泡，防止触发window的click事件
      if (col.type === 'selection') return;
      e.stopPropagation();

      // 不能编辑或当前行已经处于编辑
      if (!this.canEdit || this.editingRow === row) return;

      // 执行保存，保证正在编辑的行合法保存再编辑下条
      if (await this.saveRow()) {
        this.oldRow = { ...row };
        this.editingRow = row;
      }
    },

    checkRowRemovable(props) {
      const { row, index } = props;
      return (
        this.canEdit
        && index > 0
        && (this.editingRow !== row || this.isEqualObject(row, this.oldRow))
      );
    },

    deleteRow(row) {
      const delRow = Lib.Utils.deepClone(row);
      const index = this.tableData.indexOf(row);
      if (index !== -1) {
        this.tableData.splice(index, 1);
        this.editingRow = this.addRow;
        this.oldRow = { ...this.addRow };
      }

      this.afterDeleteRow(delRow);
    },

    // 钩子，可被子类 override
    afterDeleteRow() {},

    // 钩子，可被子类 override
    beforeSaveRow() {},

    // 保存行数据方法
    async saveRow() {
      // console.log('doBaseSave');
      if (!this.canEdit || !this.editingRow) return false;

      // 不是新增行
      if (!this.editingRow.isAddRow) {
        if (!this.validRowFn()) return false;
        this.beforeSaveRow(Lib.Utils.deepClone(this.editingRow));
        this.editingRow = this.addRow;
        this.oldRow = Lib.Utils.deepClone(this.addRow);
        return true;
      }

      // 是新增行，并与新增行的备份不同（新增行有输入、更改）
      // console.log('isEqualObject', this.isEqualObject(this.editingRow, this.addRowCopy));
      if (!this.isEqualObject(this.editingRow, this.addRowCopy, ['taxRate', 'inputTaxAmount', 'deductionInputAmount'])) {
        if (!this.validRowFn()) return false;

        const newRow = Object.assign(
          Lib.Utils.deepClone(this.editingRow),
          { isAddRow: false },
          await this.getSpecialField(),
        );

        this.tableData.push(newRow);
        this.beforeSaveRow(newRow);
        this.initAddRow();
        this.foucsFirstInput();
      }
      return true;
    },

    // 聚焦到第一个框
    foucsFirstInput() {
      this.$nextTick(() => {
        const $table = this.$refs.table;
        let tds;
        let targetInput;
        if ($table) {
          $table.setCurrentRow(this.addRow);
          tds = $table.$el.querySelectorAll(
            '.el-table__body-wrapper table tbody tr:first-child td',
          );
          if (tds) {
            tds = [...tds].filter((item) => item.querySelector('.el-input'));
          }

          if (tds && tds.length !== 0) {
            targetInput = tds[0].querySelector('input');
            console.log('targetInput', targetInput);
            // 非输入框按回车聚焦
            if (tds[0].querySelector('.el-select')
              || tds[0].querySelector('.el-cascader')) {
              $(window).one('keyup', (event) => {
                if (event.which === 13 && targetInput) targetInput.focus();
              });
            } else {
              // 延迟聚焦 防止其它控件在聚焦后 夺取焦点
              setTimeout(() => {
                if (targetInput) targetInput.focus();
              }, 200);
            }
          }
        }
      });
    },

    validRowFn() {
      return true;
    },

    getSpecialField() {
      return {};
    },

    initAddRow() {
      Object.entries(this.addRowCopy).forEach(([key, value]) => {
        this.$set(this.addRow, key, value);
      });
    },

    repealRow() {
      if (!this.canEdit) return;
      Object.assign(this.editingRow, this.oldRow);
      this.editingRow = this.addRow;
      this.oldRow = Lib.Utils.deepClone(this.addRow);
      if (this.$refs.table) this.$refs.table.setCurrentRow(null);
    },

    isEqualObject(obj1, obj2, exceptionarr = []) {
      let key;
      if (obj1 === obj2) {
        // debugger;
        return true;
      }
      const obj1Keys = Object.keys(obj1);
      const obj2Keys = Object.keys(obj2);
      if (obj1Keys.length !== obj2Keys.length) return false;
      console.log(obj1, obj2);
      for (let i = 0; i < obj2Keys.length; i += 1) {
        key = obj2Keys[i];
        if (exceptionarr.includes(key)) continue;
        if (obj1[key] instanceof Date && obj2[key] instanceof Date) {
          if (obj1[key].getTime() !== obj2[key].getTime()) {
          //  debugger;
            return false;
          }
        } else if (obj1[key] instanceof Array && obj2[key] instanceof Array) {
          // eslint-disable-next-line no-loop-func
          if (!obj1[key].every((item, index) => item === obj2[key][index])) {
          //  debugger;
            return false;
          }
        } else if (obj1[key] !== obj2[key]) {
        //  debugger;
          return false;
        }
      }
      // debugger;
      return true;
    },
  },
};
</script>
