<template>
    <div class="Page flex-v">
    <div class="toolbar flex-fixed">
    <!-- 勾选行操作区 -->
    <slot name="row-operate" />
      <!-- 查询区域 -->
    <div v-if="!!$slots.query" ref="queryRef" class="Page__query qf_query">
        <slot name="query" />
    </div>
      <!-- 操作按钮区域 -->
      <div class="action-wrapper" v-if="!!$slots.actions">
        <slot name="actions"></slot>
      </div>
    </div>
    <!-- 内容区域，比如放表格 -->
    <div ref="mainRef" class="flex-fluid Page__main">
      <slot />
    </div>
    <!-- 页脚区域，比如放分页 -->
    <div v-if="!!$slots.footer" ref="footerRef" class="flex-fixed Page__footer">
      <slot name="footer" />
    </div>
  </div>
</template>
<script>
import { defineComponent } from 'vue'
export default defineComponent({
    name: 'Page',
    setup() {
        return {}
    }
})
</script>