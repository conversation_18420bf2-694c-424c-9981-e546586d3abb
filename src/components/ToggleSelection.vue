<template>
  <div class="toggle_area">
    <el-checkbox v-if="checkbox" class="checkbox" v-model="saveBeforeToggle">切换记录前先保存</el-checkbox>
    <el-button  :disabled="!choices.prev" icon="el-icon-d-arrow-left" @click="toggleData('prev');"></el-button>
    <el-button  :disabled="!choices.next" icon="el-icon-d-arrow-right" @click="toggleData('next');"></el-button>
  </div>
</template>

<script>
export default {
  props: {
    currentRow: null,
    sourceData: null,
    /* 列表内容 */
    tableData: {
      type: Array,
      default: () => [],
    },
    /* 列表项识别字段 */
    keyWord: {
      type: String,
    },
    /* 数据是否可以通过列表内容获取 */
    continuity: {
      type: Boolean,
      default: false,
    },
    /* 是否显示保存复选框 */
    checkbox: {
      type: Boolean,
      default: false,
    },
    /* 当前列表内容请求的页码 */
    page: {
      type: Number,
      default: 1,
    },
    /* 当前列表内容请求的分页大小 */
    pageSize: {
      type: Number,
      default: 10,
    },
    /* 当前列表内容是否请求完毕 */
    allLoaded: {
      type: [Boolean, Object],
      default: true,
    },
    /* 是否重新请求当前内容 */
    reload: {
      type: Boolean,
      default: true,
    },
    /* 刷新列表的函数 */
    loadData: {
      type: Function,
      default: () => Promise.resolve(),
    },
    /* 按钮可用性的自定义配置 */
    customChoices: {
      type: Object,
      default: () => ({ prev: true, next: true }),
    },
    /* 获取下一个数据的方法 */
    getSiblingData: null,
    /* 保存数据的方法 */
    saveRecord: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      saveBeforeToggle: true,
    };
  },
  computed: {
    parent() {
      console.log(this.$parent, 'parent');
      return this.$parent.$parent;
    },
    localAllLoaded() {
      const { allLoaded } = this;
      return $.type(allLoaded) === 'object'
        ? {
          up: allLoaded.up,
          down: allLoaded.down,
        }
        : {
          up: this.page <= 1,
          down: allLoaded,
        };
    },
    choices() {
      /* 数据不连续且有获取下一个数据的函数，返回按钮可用性的自定义配置 */
      if (!this.continuity && this.getSiblingData instanceof Function) {
        return this.customChoices;
      }

      const { tableData, currentRow, search } = this;
      const curIndex = currentRow
        ? tableData.indexOf(currentRow)
        : -1; /* 当前选中行 */
      if (curIndex > -1) {
        let pIndex = curIndex - 1;
        let nIndex = curIndex + 1;
        /* 列表所有数据有rowspan属性 */
        if (Reflect.has(currentRow, 'rowspan')) {
          pIndex = search(
            tableData,
            pIndex,
            0,
            (item) => item.rowspan > 0,
          ); /* 上一张记录在当前列表的序号 */
          nIndex = search(
            tableData,
            nIndex,
            tableData.length - 1,
            (item) => item.rowspan > 0,
          ); /* 下一张记录在当前列表的序号 */
        }
        return {
          /* 切换按钮的可用性：当前列表存在该数据或当前数据没加载完 */
          prev:
            tableData[pIndex] || (this.continuity && !this.localAllLoaded.up),
          next:
            tableData[nIndex] || (this.continuity && !this.localAllLoaded.down),
        };
      }
      return {};
    },
  },
  methods: {
    toggleData(name) {
      console.log(this.$parent, 'parent', this.parent);
      const promise = new Promise((resolve, reject) => {
        if (this.checkbox && this.saveBeforeToggle) {
          /* 切换前要保存数据 */
          const result = this.saveRecord();
          return result instanceof Promise ? resolve(result) : reject(result);
        }
        return resolve(-1);
      });
      const { getSiblingData } = this;
      /* 获得并激活要切换的数据 */
      console.log(getSiblingData, '=============');
      if (!(getSiblingData instanceof Function)) {
        promise.then((state) => this.setCurrentRow(name, state));
      } else {
        promise.then((state) => getSiblingData(name, this.sourceData,
          () => this.setCurrentRow(name, state)));
      }
    },
    /* 寻找符合条件的数据序号 */
    search(arr, bIndex, eIndex, condition) {
      console.log('=============================', arr, bIndex, eIndex, condition);
      if ([bIndex, eIndex].every((v) => v >= 0 && v + 1 <= arr.length)) {
        const increment = bIndex < eIndex ? 1 : -1;
        let cur = bIndex;
        const circleTimes = Math.abs(eIndex - bIndex);
        for (let i = 0; i <= circleTimes; i += 1) {
          if (condition(arr[cur])) {
            return cur;
          }
          cur += increment;
        }
      }
      return -1;
    },
    setCurrentRow(name, state) {
      const option = this.choices[name];
      const jumpPage = option === true;
      const {
        currentRow,
      } = this; /* option === true：当前列表没相应的数据，要请求接口 */
      const curIndex = this.tableData.indexOf(currentRow);
      let searchFn;
      // Reflect.has方法对应name in obj里面的in运算符。
      if (Reflect.has(currentRow, 'rowspan')) {
        /* 列表所有数据有rowspan属性 */
        searchFn = (bIndex, eIndex, condition) => {
          const { tableData } = this;
          const conditionFunc = condition instanceof Function
            ? (item) => item.rowspan > 0 && condition(item)
            : (item) => item.rowspan > 0;
          return tableData[
            this.search(tableData, bIndex, eIndex, conditionFunc)
          ];
        };
      } else {
        searchFn = (bIndex, eIndex, condition) => {
          const { tableData } = this;
          if (condition instanceof Function) {
            let result = -1;
            const increment = bIndex < eIndex ? 1 : -1;
            const times = Math.abs(eIndex - bIndex) + 1;
            for (let i = 0; i < times; i += 1) {
              if (condition(tableData[bIndex])) {
                result = bIndex;
                break;
              }
              bIndex += increment;
            }
            bIndex = result;
          }
          return tableData[bIndex];
        };
      }
      if (jumpPage || (state !== -1 && this.reload)) {
        /* 跳页或保存数据后重新刷新 */
        let params = {};
        const checkBack = name === 'prev';
        if (jumpPage) {
          const { pageSize } = this;
          const page = this.page + (checkBack ? -1 : 1);
          params = { checkBack, page, pageSize }; /* 传给刷新列表函数的实参 */
        }
        return this.loadData(params).then((data) => {
          let target = null;
          const { tableData } = this;
          const tLen = tableData.length;
          const jLen = data.length;
          if (jumpPage && jLen === tLen) {
            /* 跳页且加载方式不是滚动加载 */
            target = checkBack
              ? searchFn(tLen - 1, 0)
              : searchFn(0, tLen - 1); /* 获得相应的数据 */
          } else if ($.type(option) === 'object' && this.keyWord) {
            /* 应对后台保存后更改数据顺序的情况，option为下一个激活数据 */
            const { keyWord } = this;
            const value = option[keyWord];
            const conditionFunc = (item) => item[keyWord] === value;
            target = checkBack
              ? searchFn(curIndex - 1, 0, conditionFunc)
              : searchFn(curIndex, tableData.length - 1, conditionFunc);
          } else {
            /* 其它情况 */
            target = checkBack
              ? searchFn(jumpPage ? jLen - 1 : curIndex - 1, 0)
              : searchFn(curIndex + 1, tableData.length - 1);
          }
          console.log('======', target);
          /* target有值，则触发toggle事件，没值则触发hide事件 */
          if (target) {
            this.parent.$emit('toggle', target);
          } else {
            this.parent.$emit('hide');
          }
        });
      }
      console.log('----------', option);
      return option
        ? this.parent.$emit('toggle', option)
        : this.parent.$emit('hide');
    },
  },
};
</script>

<style scoped>
.toggle_area .checkbox {
  margin-right: 10px;
}
</style>
