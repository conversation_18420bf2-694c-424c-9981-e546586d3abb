import Vue from 'vue';
import Router from 'vue-router';
import {
  Form,
  FormItem,
  Input,
  Button,
  Table,
  TableColumn,
  Tabs,
  TabPane,
  Radio,
  DatePicker,
  Tooltip,
  Popover,
} from 'element-ui';
import App from './App.vue';
import router from './router';

Vue.component(Form.name, Form);
Vue.component(FormItem.name, FormItem);
Vue.component(Input.name, Input);
Vue.component(Button.name, Button);
Vue.component(Table.name, Table);
Vue.component(TableColumn.name, TableColumn);
Vue.component(Tabs.name, Tabs);
Vue.component(TabPane.name, TabPane);
Vue.component(Radio.name, Radio);
Vue.component(DatePicker.name, DatePicker);
Vue.component(Tooltip.name, Tooltip);
Vue.component(Popover.name, Popover);
Vue.use(Router);
console.log(router);
new Vue({
  router,
  render: (h) => h(App),
}).$mount('#app');
