<template>
  <div class="auth-warpper">
    <el-form>
      <div class="company-box">
        <p>选择账套</p>
        <el-table :data="companyList" border height="200px">
          <el-table-column label="选择" width="50" align="center">
            <template slot-scope="{row}">
              <el-radio :label="row.companyId" v-model="selectCompanyId"></el-radio>
            </template>

          </el-table-column>
          <el-table-column label="账套名称" prop="companyName" />
          <el-table-column label="增值税类型" prop="vatType" :width="110">
              <template slot-scope="scope">
                <span>{{ scope.row.vatType === 1 ? '一般纳税人' : '小规模纳税人' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="建账月份"
              prop="accountDate"
              :width="100"
              :formatter="accountDateFormatter">
            </el-table-column>
            <el-table-column
              label="创建时间"
              prop="createTime"
              :width="100"
              sortable>
              <template slot-scope="scope">
                <span>{{ scope.row.createTime | formatDate }}</span>
              </template>
            </el-table-column>
            <el-table-column label="到期时间" :width="100">
              <template slot-scope="scope">
                {{ methodsWay(scope.row.createTime, scope.row.companyTimeLimit)}}
              </template>
            </el-table-column>
        </el-table>
      </div>

      <el-form-item label="授权最近之前数据期数" label-width="140px">
        <el-input style="width: 350px" v-model.number="authCount"/>
      </el-form-item>
      <el-form-item label="授权账套起始时间" label-width="140px">
        <el-date-picker type="daterange" v-model="validRange" />
      </el-form-item>
    </el-form>

    <div class="btn-box">
      <el-button size="medium" type="primary" @click="authAccountData">授权确认并返回</el-button>
    </div>

  </div>
</template>
<script lang="js">
import { parse } from 'qs';
import request from 'axios';
import { Message } from 'element-ui';

import { formatDate } from '@/assets/Utils';

let params = window.location.search.substring(1);
params = parse(params);
// eventId由授权发起方传递
const eventId = params.eventId || '';
const eventCrc = params.eventCrc || '';
// 授权发起方的clientId
const authClientId = 'finance-enterprise-certificate-personal';
export default {
  data() {
    return {
      companyList: [],
      selectCompanyId: '',
      validRange: [],
      authCount: 6, // 授权账期数
      eventId,
    };
  },
  mounted() {
    request.get('/rest/proxy/stmt/merge/process/v1.2/all/cardList').then((res) => {
      this.companyList = res.data.data;
    });
    window.addEventListener('beforeUnload', () => {
      window.opener.postMessage({ close: true }, '*');
    });
  },
  filters: {
    formatDate(val) {
      if (val) {
        return formatDate(new Date(val), 'yyyy-MM-dd');
      }
      return '';
    },
  },
  methods: {
    // 格式化 建账月份
    accountDateFormatter(row) {
      if (row.accountDate) {
        const dateArray = row.accountDate.split('-');
        return dateArray[0] + dateArray[1];
      }
      return '';
    },
    // 账套 已到期时间
    methodsWay(rowTime, rowLimit) {
      let overYear; let
        overMonth;
      rowTime = new Date(rowTime);
      const expireYear = rowTime.getFullYear();
      const expireMonth = rowTime.getMonth() + 1;
      const expireDay = rowTime.getDate();
      const limitYear = parseInt(rowLimit / 12, 10) || 0;
      const limitMonth = rowLimit % 12 || 0;
      overMonth = expireMonth + limitMonth;
      overYear = expireYear + limitYear;

      if (overMonth > 12) {
        overYear += 1;
        overMonth -= 12;
      }
      if (overMonth < 10) {
        overMonth = `0${overMonth}`;
      }
      return `${overYear}-${overMonth}-${expireDay}`;
    },
    authAccountData() {
      if (this.selectCompanyId === '') {
        Message.error('请选择一个账套');
        return;
      }
      if (!Array.isArray(this.validRange) || this.validRange.length < 2) {
        Message.error('请选择授权帐套起止时间');
        return;
      }
      if (!this.authCount) {
        Message.error('请输入授权最近之前数据期数');
        return;
      }
      const validStartTime = formatDate(this.validRange[0], 'yyyy-MM-dd hh:mm:ss');
      console.log(validStartTime);
      const validEndTime = formatDate(this.validRange[1], 'yyyy-MM-dd hh:mm:ss');
      const company = this.companyList.find((item) => item.companyId === this.selectCompanyId);
      const postData = {
        authClientId,
        companyId: this.selectCompanyId,
        companyName: company.companyName,
        eventId,
        eventCrc,
        grantType: 1,
        validStartTime,
        validEndTime,
      };

      request.post('/auth/event/grant/v1.0/confirm/accountData', postData).then(() => {
        window.opener.postMessage({ success: true }, 'https://dev.enterprise.finance.cn');
        setTimeout(() => {
          window.close();
        }, 100);
        Message.success('操作成功');
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.auth-warpper{
  padding: 30px;

  &::v-deep .el-radio__label{
    display: none
  }
  .btn-box{
    margin-top: 40px;
    text-align: center;
  }
  .company-box{
    margin-bottom: 22px;
  }
}
</style>
