<template>
  <div id="app">
    <!-- <router-view></router-view> -->
    <button @click="a = !a">a</button>
    <Qnb v-if="a" v-model="aa" />
  </div>
</template>

<script>
import Qnb from '@/components/QNbInput.vue';

export default {
  components: { Qnb },
  name: 'app',
  data() {
    return {
      a: false,
      aa: 0,
    };
  },
};
</script>

<style>
#app{
  height: 100%;
  /*全局工作区的背景样式*/
  /* background:#e6e9ea; */
  font-size: 12px;
}
</style>
