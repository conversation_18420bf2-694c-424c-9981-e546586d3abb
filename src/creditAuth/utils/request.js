import axios from 'axios';
import {
  Message,
} from 'element-ui';
import { htmlDecodeByRegExp } from '@/assets/Utils';

axios.defaults.withCredentials = true;
// create an axios instance
const service = axios.create();
const pendings = []; // 声明一个数组用于存储每个ajax请求的取消函数和ajax标识
const { CancelToken } = axios;

/**
 *请求执行时查看是否已有正在请求中的相同请求，有的话取消当次请求， 没有则记录当次请求
 *
 * @param {*} ever 请求的config数据
 * @returns
 */
const checkPending = (ever) => {
  const pending = pendings.find((item) => item.ajaxKey === ever.ajaxKey);
  // 如果当前请求被记录则执行当次请求取消操作
  if (pending) {
    ever.executor('重复请求'); // 执行取消操作
    console.info('重复请求', ever.ajaxKey);
  } else {
    pendings.push(ever); // 反之把这条记录添加进数组
  }
};

/**
 *请求完成后从记录中移除
 *
 * @param {*} ever 请求的config数据
 * @returns
 */
const achievePending = (ever) => {
  const pendingIndex = pendings.findIndex((item) => item.ajaxKey === `${ever.url}&${ever.method}`);
  if (pendingIndex === -1) return; // 如果当前请求未被记录则不需要执行删除操作
  pendings.splice(pendingIndex, 1); // 把这条记录从数组中移除
};

// request interceptor
service.interceptors.request.use(
  (config) => {
    config.headers['content-type'] = 'application/json;charset=UTF-8';
    config.headers['x-requested-with'] = 'XMLHttpRequest';
    // 记录post 和 put 操作
    if (config.method !== 'get' && config.method !== 'delete') {
      // ------------------------------------------------------------------------------------
      config.cancelToken = new CancelToken((c) => {
        // 这里的ajax标识使用用请求地址&请求方式拼接的字符串
        checkPending({ ajaxKey: `${config.url}&${config.method}`, executor: c });
      });
      // -----------------------------------------------------------------------------------------
    }
    return config;
  },
  (error) => {
    console.log(error); // for debug
    Promise.reject(error);
  },
);

// response interceptor
service.interceptors.response.use(
  (response) => {
    achievePending(response.config);
    return response;
  },
  (error) => {
    console.log(`err${error}`, error); // for debug
    achievePending(error.config);
    const { response } = error;
    if (response) {
      try {
        if (
          !(response.status === 502 || response.status === 504)
        ) {
          if (response.status === 401) {
            throw new Error('您没有访问权限');
          }
          if (response.status === 401) {
            throw new Error('当前页面禁止访问，原因是没有正确的访问权限或服务器正在更新维护。');
          }
          if (response.status === 413) {
            throw new Error('文件大小超过上传限制');
          }
          if (response.status === 0) {
            throw new Error('网络异常，请检查您的网络状况');
          }

          if (response.status !== 200) {
            let errorMsg;

            if (response.data.errorMessage) {
              errorMsg = response.data.errorMessage;

              if (errorMsg === '其他异常' || errorMsg === '其它异常' || errorMsg === '操作失败') {
                errorMsg = `${errorMsg}-事件编号：${response.data.errorId}`;
              }

              throw new Error(errorMsg);
            } else if (response.data.error_description) {
              throw new Error(response.data.error_description);
            } else {
              errorMsg = `服务器异常！错误码：${response.status}`;
              // errorMsg = '服务器异常！错误码：' + response.data.errorId;
              throw new Error(errorMsg);
            }
          }
        }
      } catch (e) {
        console.log(e);
        const message = htmlDecodeByRegExp(e.message || '其它异常');
        Message.error(message);
      }
    }
    return Promise.reject(error);
  },
);

export default service;
