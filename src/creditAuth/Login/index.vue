<template>
  <div id="wrapper">
    <div class="header">
      <div class="title">{{PROJECT_NAME}}登录</div>
    </div>
    <div class="tab-box">
      <div class="tab-nav">
        <div
        class="tab-nav-item"
        @click="active=1"
        :class="{'tab-nav-active': active === 1}">账号密码登录</div>
        <div
        class="tab-nav-item"
        @click="active=2"
        :class="{'tab-nav-active': active === 2}">手机号登录</div>
      </div>
    </div>
    <PwdLogin @login-success="jumpLogin" v-show="active === 1" />
    <PhoneLogin @login-success="jumpLogin" v-show="active === 2" />
    <!-- <input placeholder="用户名" size="small" v-model="username"></input>
    <input placeholder="密码"  size="small"  v-model="password" type="password"></input>
    <button id="startBtn" type="primary" size="small" round>登录</button> -->
  </div>
</template>
<script lang="js">
import { stringify } from 'qs';
import {
  Message,
} from 'element-ui';
import { delCookie, getCookie } from '@/assets/Utils';
import {
  PROJECT_NAME, USERCENTER_URL, CLIENT_ID, WEB_DOMAIN_NAME,
} from '@/assets/constant';
import PhoneLogin from './PhoneLogin.vue';
import PwdLogin from './PwdLogin.vue';

export default {
  components: { PhoneLogin, PwdLogin },
  data() {
    return {
      active: 1,
      PROJECT_NAME,
    };
  },
  mounted() {
  },
  filters: {
  },
  methods: {
    // 用户中心登录完成之后，调用授权登录到对应的系统
    jumpLogin() {
      const params = {
        response_type: 'code', // 固定值， 使用用户中心返回的code进行登录
        client_id: CLIENT_ID,
        redirect_uri: `${WEB_DOMAIN_NAME}/auth/login/usercenterCszx`,
      };
      const authLoginUrl = `${USERCENTER_URL}/usercenter/oauth/authorize?${stringify(params)}`;
      this.authLogin(authLoginUrl);
      this.checkLoginStatus(() => {
        this.$router.push('/Auth');
      });
    },
    authLogin(src) {
      // 由于浏览器限制跨域情况下的重定向会导致， Orgin丢失变成null，
      // 所以无法使用ajax cros 调用， 使用iframe执行完所有重定向操作，浏览器自动设置token到cookie中
      const iframe = document.createElement('iframe');
      iframe.src = src;
      iframe.onload = function onload() {
        const errorMessage = iframe.contentWindow.getQueryVariable('errorMessage');
        if (errorMessage) {
          Message.error(errorMessage);
        }
      };
      document.body.appendChild(iframe);
      iframe.style.display = 'none';
    },
    // 轮询是否登录已完成
    checkLoginStatus(callback) {
      this.time = setInterval(() => {
        const isLoginSuccess = getCookie('usercenter_loginSuccess');
        console.log(isLoginSuccess, '======');
        if (isLoginSuccess) {
          delCookie('usercenter_loginSuccess');
          clearInterval(this.time);
          callback();
        }
      }, 500);
    },
  },
};
</script>
<style lang="scss">
.title{
    margin-top: 12px;
    margin-bottom: 20px;
    color: rgba(0,0,0,.45);
    font-size: 18px;
    text-align: center;
}
#wrapper input + input  {
  margin-top: 14px;
}
#wrapper{
    position: fixed;
    left: 50%;
    top: 50%;
    width: 368px;
    transform: translate(-50%, -50%);
}
#wrapper .submit-btn{
    width: 100%;
    font-size: 16px;
}
header{
    text-align: center;
    margin-top: 70px;
}
header span{
    position: relative;
    top: 2px;
    color: rgba(0,0,0,.85);
    font-weight: 600;
    font-size: 33px;
    font-family: Avenir,Helvetica Neue,Arial,Helvetica,sans-serif;
}
.footer{
    position: fixed;
    bottom: 20px;
    font-size: 12px;
    text-align: center;
    width: 100%;
    color: cadetblue
}
.footer .btn{
    color: #66ccff;
    cursor: pointer;
}
.error{
    padding: 10px 16px;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: #fff;
    display: inline-block;
    pointer-events: all;
    position: fixed;
    top: 10px;
    z-index: 10;
    left: 50%;
    transform: translateX(-50%);
    color: red
}
.fade-enter-active, .fade-leave-active {
  transition: opacity .5s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
.tab-box{
  color: rgba(0,0,0,.65);
  margin-bottom: 14px;
  text-align: center;
  .tab-nav{
    position: relative;
    box-sizing: border-box;
    display: inline-block;
    margin: 0;
    font-size:14px;
    padding-left: 0;
    list-style: none;
    transition: transform .3s cubic-bezier(.645,.045,.355,1);

    .tab-nav-item {
      position: relative;
      display: inline-block;
      box-sizing: border-box;
      height: 100%;
      margin: 0 32px 0 0;
      padding: 12px 16px;
      text-decoration: none;
      cursor: pointer;
      transition: color .3s cubic-bezier(.645,.045,.355,1);
      &:last-child{
        margin: 0;
      }
    }
    .tab-nav-active{
      color: #1890ff;
      font-weight: 500;
    }
  }
}

</style>
