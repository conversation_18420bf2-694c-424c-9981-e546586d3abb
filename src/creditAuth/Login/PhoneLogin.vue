<template>
  <el-form ref="form" :rules="rules" :model="formData">
    <el-form-item prop="username">
      <el-input placeholder="手机号"  size="medium" v-model.trim="formData.username"></el-input>
    </el-form-item>
    <el-form-item prop="smsCode">
      <div class="code-box">
        <el-input
        placeholder="验证码"
        size="medium"
        class="code-input"
        v-model.trim="formData.smsCode"></el-input>
        <el-button
        class="code-button"
        type="primary"
        :disabled="countDown !== 0"
        size="medium"
        @click="getSmsCode">
          <span v-if="countDown === 0">获取验证码</span>
          <span v-else>
            {{countDown}}秒
          </span>
        </el-button>
      </div>
    </el-form-item>
    <el-button class="submit-btn" type="primary" size="medium" @click="login" round>登录</el-button>
  </el-form>
</template>
<script lang="js">
import request from '../utils/request';
import { USERCENTER_URL, CLIENT_ID } from '../constant';

export default {
  components: {},
  data() {
    return {
      formData: {
        username: '',
        smsCode: '',
      },

      countDown: 0,
      rules: {
        username: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^(?:(?:\+|00)86)?1\d{10}$/, message: '目前只支持中国大陆的手机号码' },
        ],
        smsCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          { length: 6, message: '验证码长度为6' },
        ],
      },
    };
  },
  mounted() {
  },
  filters: {
  },
  methods: {
    login() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const postData = {
            phone: this.formData.username,
            smsCode: this.formData.smsCode,
            clientId: CLIENT_ID,
          };
          request.post(`${USERCENTER_URL}/usercenter/login/phone`, postData).then(() => {
            this.$emit('login-success');
          });
        }
      });
    },
    getSmsCode() {
      this.$refs.form.validateField('username', (valid) => {
        if (!valid) {
          request.get(
            `${USERCENTER_URL}/usercenter/common/captcha/sms/LOGIN/${this.formData.username}`,
          ).then((res) => {
            const code = res.data.returnMessage;
            this.formData.smsCode = code;
            this.countDown = 60;
            const time = window.setInterval(() => {
              if (this.countDown === 0) {
                window.clearInterval(time);
                return;
              }
              this.countDown -= 1;
            }, 1000);
          });
        }
      });
    },
  },
};
</script>
<style lang="scss">
.code-box{
  margin-top: 14px;
  .code-input{
    float: left;
    width: 60%;
  }
  .code-button{
    float: right;
    text-align: center;
    width: 35%
  }
}

</style>
