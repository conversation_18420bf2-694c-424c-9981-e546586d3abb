<template>
  <el-form :model="formData" ref="form" :rules="rules">
    <el-form-item prop="username">
      <el-input placeholder="手机号" v-model="formData.username" size="medium"></el-input>
    </el-form-item>
    <el-form-item  prop="password">
      <el-input placeholder="密码" v-model="formData.password" type="password" size="medium"></el-input>
    </el-form-item>
    <el-form-item  prop="verify" v-if="showCaptcha">
      <div class="login-verify">
        <div class="login-verify-input">
          <el-input
            name="verify"
            size="medium"
            placeholder="请输入验证码"
            v-model="formData.verify"
            :maxlength="4"
          ></el-input>
          <span>请按箭头方向依次输入4位验证码</span>
        </div>
        <img class="login-verify-img" :src="captcha" alt="验证码">
        <span class="login-verify-span" title="刷新验证码">
          <i
            class="el-icon-refresh"
            aria-hidden="true"
            @click="upnewCaptcha"
          ></i>
        </span>
      </div>
    </el-form-item>
    <el-form-item >
      <el-button @click="login" type="primary" class="submit-btn" style="width: 100%" size="medium" round>登录</el-button>
    </el-form-item>
  </el-form>
</template>
<script lang="js">
import { parse } from 'qs';
import { encryptByDES } from '@/assets/Utils';
import request from '../utils/request';
import { USERCENTER_URL, CLIENT_ID } from '../constant';

export default {
  components: {},
  data() {
    return {
      formData: {
        username: '13422488027',
        password: 'jd123456',
        verify: '',
      },
      showCaptcha: false,
      captcha: '',
      rules: {
        username: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        verify: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
      },
    };
  },
  mounted() {
  },
  filters: {
  },
  methods: {
    login() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const postData = {
            username: this.formData.username,
            password: encryptByDES(this.formData.password),
            clientId: CLIENT_ID,
          };
          request.post(
            `${USERCENTER_URL}/usercenter/login/pwd?captcha_code=${this.formData.verify}`,
            postData,
          ).then((res) => {
            this.$emit('loginSuccess');
          }, (err) => {
            const { data } = err.response;
            if (data) {
              // 判断是否携带验证码需求
              const errorMsgs = data.errorCode.split('|');

              console.log(errorMsgs);
              if (errorMsgs[1] === 'captcha') {
                this.showCaptcha = true;
              }
              if (this.showCaptcha) {
                this.upnewCaptcha();
              }
            }
          });
        }
      });
    },
    // 刷新二维码
    upnewCaptcha() {
      this.captcha = `${USERCENTER_URL}/usercenter/common/captcha/img/LOGIN?t=${Math.random()}`;
    },
  },
};
</script>
<style lang="scss">
.error{
    padding: 10px 16px;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: #fff;
    display: inline-block;
    pointer-events: all;
    position: fixed;
    top: 10px;
    z-index: 10;
    left: 50%;
    transform: translateX(-50%);
    color: red
}
.login-verify{
  display: flex;
  justify-content:space-between;
  .login-verify-input{
    width: 200px;
    span{
      color: rgba(0,0,0,.65);
    }
  }
  // .login-verify-input {

  // }
}
</style>
