<template>
  <div>
    <span @click="changeGrayTestStatus">{{this.isGrayTest ? '关闭' : '激活'}}灰度测试</span>
  </div>
</template>
<script>
export default {
  name: 'grayTest',
  computed: {
    isGrayTest() {
      return this.$store.state.user.isGrayTest;
    },
  },
  mounted() {

  },
  methods: {
    changeGrayTestStatus() {
      this.$store.dispatch('user/toggleGrayTestStatus');
    },
  },
};
</script>
