<!--
 * @Description:  编辑推广信息弹窗
 -->
<template>
  <div>
    <el-dialog
    title="编辑推广信息"
    :visible.sync="dialogVisible"
    :close-on-click-modal='false'
    :modal-append-to-body="true"
    append-to-body
    center
    top="8vh"
    width="95%"
    @close="$emit('hide')"
    destroy-on-close
    close-on-press-escape>
      <TinyEditor v-if="dialogVisible" v-model="content" @change="handleChange"/>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import get from 'lodash-es/get';
import TinyEditor from '@/components/TinyEditor/TinyEditor.vue';
import { updateDescribe, getDescribeDate } from '@/api/auth';

export default {
  components: { TinyEditor },
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
  },

  data() {
    return {
      content: '',
      describeText: '',
    };
  },

  watch: {
    visible(newValue) {
      if (newValue) {
        this.handleOpened();
      } else {
        this.content = '';
      }
    },
  },

  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(isVisible) {
        if (this.visible && !isVisible) {
          this.$emit('hide');
        }
      },
    },
    bkCompanyCode() {
      return this.$store.state.user.userInfo.bkCompanyCode;
    },
  },

  methods: {
    handleOpened() {
      getDescribeDate(this.bkCompanyCode).then((res) => {
        this.content = get(res, 'data.data[0]') || '';
        this.describeText = get(res, 'data.data[0]') || '';
      });
    },
    handleChange(val) {
      this.describeText = val;
    },

    handleSave() {
      const bl = new Blob([this.describeText]);
      const blSize = (bl.size || 0).div(1024).div(1024);
      console.log('bl', this.describeText, bl, blSize);
      if (blSize >= 5) {
        return this.$message.warning('内容过多，已超出5M大小，建议压缩图片大小或减少文字');
      }
      const params = {
        describeText: this.describeText,
      };
      updateDescribe(params).then((res) => {
        const msg = get(res, 'data.returnMessage');
        this.$message.success(msg);
        this.$emit('hide');
      });
    },
  },

};
</script>

<style lang="scss"  >
  .tox-tinymce-aux {
    z-index:98888
  }
</style>
