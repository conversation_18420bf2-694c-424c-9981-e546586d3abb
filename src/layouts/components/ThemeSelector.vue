<template>
  <el-dialog title="主题设置" :visible.sync="visible" append-to-body @close="hide" center width="440px">
    <div>
      <el-form>
        <!-- <el-form-item label="">
          <el-radio-group v-model="theme.color" class="theme-list">
            <div class="theme-item" :class="{ active: theme === item.id }" v-for="item in themeList" :key="item.id">
              <div @click="theme = item.id" class="theme-item__color" :style="{ background: item.color }"></div>
              <div class="theme-item__title">
                {{ item.name }}
              </div>
              <div class="theme-item__radio">
                <el-radio :label="item.id">
                  {{ '' }}
                </el-radio>
              </div>
            </div>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="字号">
          <el-radio-group v-model="theme.baseFontSize">
            <el-radio label="12px">小</el-radio>
            <el-radio label="14px">大</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

    </div>
    <div slot="footer">
      <el-button type="primary" @click="confirm">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import themeList from '@/theme/themeList';

export default {
  name: 'ThemeSelector',
  props: {
    visible: Boolean,
  },
  data() {
    return {
      themeList,
      theme: {
        color: '',
        baseFontSize: '',
      },
    };
  },
  watch: {
    visible: {
      immediate: true,
      handler: function handler(val) {
        if (val) {
          this.theme = { ...this.$store.state.app.theme };
        }
      },
    },
  },
  methods: {
    async confirm() {
      await this.$store.dispatch('app/toggleTheme', { ...this.theme });
      this.$message.success('操作成功');
      this.hide();
    },
    hide() {
      this.$emit('hide');
    },
  },
};
</script>
<style lang="scss" scoped>
.theme-list {
  display: flex;
  justify-content: space-around;

  .theme-item {
    width: 60px;
    padding: 30px 20px;
    text-align: center;

    .theme-item__color {
      width: 48px;
      height: 48px;
      border-radius: 4px;
      margin: 0 auto;
      cursor: pointer;
    }

    .theme-item__title {
      font-size: 14px;
      line-height: 40px;
    }

    &::v-deep .el-radio__label {
      padding-left: 0;
    }
  }
}
</style>
