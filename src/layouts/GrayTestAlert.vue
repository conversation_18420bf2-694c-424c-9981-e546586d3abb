<template>
  <div class="Danger" v-if="isGrayTest || backendGrayTest">
    <span v-if="isGrayTest && !backendGrayTest">只有前端处于灰度测试状态，请勿操作真实业务数据。</span>
    <span v-else>前后端同时处于灰度测试状态，请勿操作真实业务数据。</span>
    <el-button type="text" @click="changeGrayTestStatus">切换灰度测试</el-button>
  </div>
</template>
<script>
export default {
  name: 'grayTestAlert',
  computed: {
    isGrayTest() {
      return this.$store.state.user.isGrayTest;
    },
    backendGrayTest() {
      return this.$store.state.user.backendGrayTest;
    },
  },
  mounted() {

  },
  methods: {
    changeGrayTestStatus() {
      this.$store.dispatch('user/toggleGrayTestStatus');
    },
  },
};
</script>
