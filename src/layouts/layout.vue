<!--
 * @LastEditors: 启旭
 * @Author: 肖泽涛
 * @Description: 业务layout页面
 * @Date: 2019-03-07 11:56:28
 * @LastEditTime: 2020-04-15 16:12:50
 -->
<template>
  <el-container class="layout" :class="[this.$store.state.app.PROJECT_TYPE]" direction="vertical">
          <qf-header></qf-header>
    <qf-main>
      <router-view slot="content" class="body_content" :key="key"></router-view>
    </qf-main>
  </el-container>
</template>

<script>
import { mapActions } from 'vuex';
import store from '@/store/index';

import QfMain from './QMain.vue';
import QfHeader from './QHeader.vue';

// 标识该组件是否第一次加载
let isFirstLoad = true;

export default {
  data() {
    return {
      poll: {},
      timeHiden: true,

      dialogVisible: false,
      dialogForm: {
        companyName: '',
        accountDate: new Date(),
      },
      dialogRule: {
        companyName: [{ required: true, message: '请填写公司名称' }],
        accountDate: [{ required: true, message: '请填写建账月份' }],
      },
    };
  },
  components: {
    QfMain, QfHeader,
  },
  computed: {
    smallPattern() {
      return this.$store.state.smallPattern;
    },
    routePath() {
      return this.$route.path;
    },
    isTaxWorkspace() {
      return this.routePath.indexOf('taxWorkspace') !== -1;
    },
    userType() {
      return this.$store.state.user.userInfo.userType;
    },
    key() {
      const { $route } = this;
      const { name } = $route;
      return (name || 'route');
    },
    showLogin() {
      return this.$store.state.showLogin;
    },
  },
  async beforeRouteEnter(to, from, next) {
    // 在渲染该组件的对应路由被 confirm 前调用
    // 不！能！获取组件实例 `this`
    // 因为当守卫执行前，组件实例还没被创建
    // 此处代码在整个项目生命周期中只需要调用一次
    if (isFirstLoad) {
      await store.dispatch('app/loadAppInfo');
      await store.dispatch('user/getUserInfo');
      await store.dispatch('user/getFrontendSeqCodeWithBK');
      await store.dispatch('user/getBKCompanyInfo');
      await store.dispatch('app/loadDeclareEndDate');

      const { userInfo } = store.state.user;
      if (userInfo.selectCompanyId) {
        // 由于AccountPeriod需要从后端获取，所以需要在页面渲染之前获取到AccountPeriod。
        if (![
          'ROLE_HJ_HH_SPONSOR',
          'ROLE_HJ_HH_CHARGE',
          'ROLE_HJ_HH_CUSTOMER',
        ].hasMap(userInfo.roleNameStr)) {
          await store.dispatch('user/getCompanyInfo');
          await store.dispatch('user/getDefaultAccountPeriod', userInfo.selectCompanyId);
        }
        // 以下接口就算报错了，也允许进入页面
        try {
          await Promise.all([
            store.dispatch('staticJson/getUserManageTypeList'),
            store.dispatch('user/getTaxInfo'),
            store.dispatch('optionHistory/setALLOptionHistory'),
            store.dispatch('user/getBackendGrayTestStatus'),
          ]);
        } catch (e) {
          console.error(e);
        }
      }
      // 标识为以加载过
      isFirstLoad = false;
    }

    next();
  },
  watch: {
    showLogin(newVal) {
      if (newVal) {
        this.$alert('您尚未登录或者登录状态已过期,请重新登录', '系统提醒', {
          confirmButtonText: '前往登录',
          callback: () => {
            this.$router.push('/');
            this.$store.commit('LOGOUT');
          },
        });
      }
    },
  },
  async created() {
    this.$store.commit('app/SET_SCREEN_HEIGHT', document.body.clientHeight);
    
    window.addEventListener('resize', () => {
      this.$store.commit('app/SET_TABLE_HEIGHT');
      this.$store.commit('app/SET_SCREEN_HEIGHT', document.body.clientHeight);
    });
    this.timeHiden = false;

    const { userInfo } = this.$store.state.user;
    if (userInfo.selectCompanyId) {
      this.$store.dispatch('selectData/getSubjectList');
      this.$store.dispatch('selectData/getSubjectTag');
      this.$store.dispatch('user/getLockDataMonths');
      this.$store.dispatch('user/getUserManageList');
      this.$store.dispatch('user/getFrontendSeqCodeWithCompany');
      this.$store.dispatch('selectData/getTagOfSubjectList');
      this.$nextTick(() => {
        this.$store.commit('app/SET_TABLE_HEIGHT');
      });
    }
    // 获取所有静态选项数据
    this.loadAllStaticJson();
    this.poll = setInterval(() => {
      this.$http.get('/auth/usercenter/userinfo');
    }, 180000);
  },
  beforeDestroy() {
    clearInterval(this.poll);
  },
  methods: {
    ...mapActions('user', ['getUserInfo', 'getCompanyInfo']),
    // 打开创建公司
    addCompanyName() {
      this.dialogVisible = true;
    },
    // 创建公司
    createCompany() {
      if (this.dialogForm.companyName === '') {
        return this.$message.warning('请输入公司名称');
      }
      return this.$http.post('/auth/usercenter/create/company', {
        companyName: this.dialogForm.companyName,
        accountDateStr: this.dialogForm.accountDate.Format('yyyy-MM-01'),
      }).then(async () => {
        this.handleClose();
        this.$message.success('创建公司成功');
        this.getUserInfo();
        this.$router.push({ name: 'guidance' });
      });
    },
    loadAllStaticJson() {
      this.$store.dispatch('staticJson/getAllStaticJson');
    },
    // 取消
    handleClose() {
      this.$refs.dialog.resetFields();
      this.dialogVisible = false;
    },
  },
};
</script>
<style scoped>
  .layout {
    height: 100%;
  }
  .el-dialog.relogin_dialog {
    width: 530px;
  }
  .el-dialog.relogin_dialog .el-dialog__body {
    padding: 0;
    background: #fff;
    max-height: initial;
  }
  .hideHeader{
    opacity:0;
    height:0;
    position: fixed;
    top:0;
    left: 50%;
    border-radius:0  0  3px 3px;
    margin-left:-25px;
    width:60px;
    line-height:12px;
    background:#46484d;
    text-align:center;
    z-index: 1000;
    cursor: pointer;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    -ms-transition: all 0.5s;
    -o-transition: all 0.5s;
    transition: all 0.5s;
  }
  .showHeader{
    opacity: 1;
    height:12px;
    z-index: 1000;
  }

  .showHeader i{
    color:#fff;
  }
  .helpText{
    width:1000px;
    height:200px;
    position: absolute;
    text-align: center;
    left: 50%;
    top: 38%;
    /* background:red; */
    -webkit-transform:translate(-50%,-50%) ;
    -moz-transform:translate(-50%,-50%) ;
    -ms-transform:translate(-50%,-50%) ;
    -o-transform:translate(-50%,-50%) ;
    transform:translate(-50%,-50%) ;
  }
  .helpText.timeHiden{
    display: none;
  }
  .helpText img{
    float: left;
  }
  .helpText div{
    float: left;
  }
  .helpText P{
    font-size: 16px;
    color:#878787;
    line-height: 40px;
  }
  .helpText P a{
    color:#F19D17;
    font-size: 18px;
  }
</style>
