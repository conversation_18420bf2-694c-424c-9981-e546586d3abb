<template>
  <el-container :class="[
    isTaxWorkspace ? 'declarationworkspace' : '',
    sidebarCollapse ? 'sidebarCollapse_on' : 'sidebarCollapse_off',
    'workspace-backgroundcolor', 'workspace']">
    <el-aside
      class="sidebar flex-v"
      :width="sidebarWidth"
      :class="{ 'sidebar-small': sidebarCollapse, 'sidebar-normal': !sidebarCollapse }">
      <div v-if="!isTaxWorkspace" class="flex-fixed">
        <div @click="toggleSideBar" class="sidebar-toggle-btn  flex-fixed">
          <i
            class="iconfont"
            :class="{
            'icon-zhankai1': sidebarCollapse,
            'icon-shouqi1': !sidebarCollapse
          }"></i>
        </div>
      </div>

      <el-scrollbar ref="scrollContainer" :vertical="false" class="flex-fluid">
        <Sidebar />
      </el-scrollbar>
      <div v-if="isTaxWorkspace" class="morebtn flex-fixed" @click="goToGuidance">
        更多智能财税功能
      </div>
    </el-aside>
    <el-main :style="{ padding: '0 0 0 0px' }" :class="{hasTagsView:needTagsView}">
      <tags-view v-if="needTagsView"
        :style="{
          width: 'calc(100% - ' + sidebarWidth + ')'
        }"
      class="tagsView"/>
      <div class="content-wrapper">
        <keep-alive :include="[...cachedViews, 'RouteView']" :max="maxTagsView">
          <slot name="content"></slot>
        </keep-alive>
      </div>

      <div v-if="showQuickAccount">
        <quick-account v-show="showQuickAccount === 1" style="z-index: 2000;position: fixed;"></quick-account>
        <div v-show="showQuickAccount === 2" @click="showAccountBox" class="showAccount"></div>
      </div>
      <div class="line"></div>
    </el-main>
  </el-container>
</template>
<style lang="scss" >
@import '@/sass/layouts/qmain.scss';
</style>
<script>
import { mapActions } from 'vuex';
import { get } from 'lodash-es';
import { PROJECT_TYPE } from '@/assets/constant';
import { ROLE_MAP } from '@/assets/optionsData';
import QuickAccount from '@/components/QuickAccount/index.vue';
import Sidebar from './Sidebar/index.vue';
import TagsView from './TagsView/index.vue';

export default {
  props: {},
  components: { Sidebar, QuickAccount, TagsView },
  data() {
    return {
      breadcrumb: [],
      menuActive: '',
      childMenuShow: true,
      projectType: PROJECT_TYPE,
      latelyPath: [],
      latelyCommon: [],
      time: null,
      systipInstance: null,
      collectEmpty: false,
      latelyPathEmpty: false,
      prevNode: null,
      intro: null,
    };
  },
  computed: {
    sidebarCollapse() {
      // 税务工作区模式永远保持侧边栏展开状态
      return !this.isTaxWorkspace && this.$store.state.app.sidebarCollapse;
    },
    sidebarWidth() {
      const { width, smallWidth } = this.$store.state.app.appConfig.sidebar;
      return this.sidebarCollapse ? smallWidth : width;
    },
    // 当前是否属于账套外的页面
    isControlCenter() {
      return this.routePath.indexOf('controlCenter') !== -1;
    },
    needTagsView() {
      return !this.isControlCenter;
    },
    maxTagsView() {
      return get(this.$store.state.app.appConfig, 'tagsView.max');
    },
    cachedViews() {
      return this.$store.state.tagsView.cachedViews.map((item) => item.name);
    },
    showQuickAccount() {
      return this.$store.state.app.showQuickAccount;
    },
    routePath() {
      return this.$route.path;
    },
    isTaxWorkspace() {
      return this.routePath.indexOf('taxWorkspace') !== -1;
    },
    username() {
      return this.$store.state.user.userInfo.username;
    },
    roleNames() {
      return this.$store.state.user.userInfo.roleNames || [];
    },

    roleNamesText() {
      const roleName = this.$store.state.user.userInfo.roleNameStr;
      const roleNameStr = roleName.split(',') ? roleName.split(',')[0] : roleName;
      return ROLE_MAP[roleNameStr] || '未指定';
    },
    sysTips() {
      return this.$store.state.user.sysTips;
    },
    roleNameStr() {
      return this.$store.state.user.userInfo.roleNameStr;
    },
    isSuperManager() {
      return this.roleNameStr.indexOf('ROLE_OM_ADMIN') !== -1;
    },
    sidebarConfig() {
      return this.$store.state.app.appConfig.sidebar;
    },
  },
  mounted() {
    this.$router.afterEach(() => {
      console.log(this);
      if (this.showQuickAccount === 1) {
        this.$store.commit('app/SET_QUICK_ACCOUNT', 2);
      }
      this.$nextTick(() => {
        this.$store.commit('app/SET_TABLE_HEIGHT');
      });
    });
    this.getSysTips();
  },
  methods: {
    ...mapActions('user', ['getSysTips']),
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar');
    },
    showAccountBox() {
      this.$router.push({ name: 'guidance', query: { num: Math.random() } }, () => {
        this.$nextTick(() => {
          this.$store.commit('app/SET_QUICK_ACCOUNT', 1);
          this.$store.state.isShowAsideModal = true;
        });
      });
    },
    showChange(type) {
      this.childMenuShow = type;
    },
    loginOut() {
      this.$http.get('/auth/logout').then(() => {
        this.$router.push('/');
        this.$store.commit('LOGOUT');
      });
    },
    goToGuidance() {
      this.$router.push({ name: 'guidance' });
    },
  },
};
</script>
<style lang="scss" scoped>
.workspace {
  height: calc(100% - 57px);
  position: relative;
}
.hasTagsView{
  padding-top: 38px !important;

  .tagsView{
    position: fixed;
    top: 56px;
    // right: 0;
    z-index: 10;
  }
}
</style>
