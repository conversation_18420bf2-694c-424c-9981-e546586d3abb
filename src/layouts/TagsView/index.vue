<template>
  <div id="tags-view-container" class="tags-view-container">
    <scroll-pane ref="scrollPane" class="tags-view-wrapper" @scroll="handleScroll">
      <router-link
        v-for="tag in visitedViews"
        ref="tag"
        :key="tag.path"
        :class="{active: isActive(tag), affix: isAffix(tag)}"
        :to="{ name: tag.name, params: {useCache: true}, fullPath: tag.fullPath }"
        tag="span"
        class="tags-view-item"
        @click.middle.native="!isAffix(tag)?closeSelectedTag(tag):''"
        @contextmenu.prevent.native="openMenu(tag,$event)"
      >
        <span title="刷新" v-if="isActive(tag)" class="el-icon-refresh-right" @click.prevent.stop="refreshSelectedTag(tag)" />
        <span v-else-if="tag.name === 'guidance'" style="font-size: 13px" class="iconfont icon-home1"></span>
        {{ tag.title }}
        <span v-if="!isAffix(tag)" class="el-icon-close" @click.prevent.stop="closeSelectedTag(tag)" />
      </router-link>
    </scroll-pane>
    <ul v-show="visible" :style="{left:left+'px',top:top+'px'}" class="contextmenu">
      <li @click="refreshSelectedTag(selectedTag)">刷新</li>
      <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)">关闭</li>
      <li @click="closeOthersTags">关闭其他</li>
    </ul>
  </div>
</template>

<script>
import path from 'path';
import { get, find } from 'lodash-es';
import ScrollPane from './ScrollPane.vue';

export default {
  components: { ScrollPane },
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      storeUnsubscribe: null,
    };
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews;
    },
    cachedViews() {
      return this.$store.state.tagsView.cachedViews;
    },
    routes() {
      return this.$store.state.permission.routers;
    },
  },
  watch: {
    $route() {
      this.addTags();
      this.moveToCurrentTag();
      // this.resetAccountPeriod();
    },
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu);
      } else {
        document.body.removeEventListener('click', this.closeMenu);
      }
    },
    // accountPeriod(value, oldValue) {
    //   // 解决由于全局属期修改后，导致缓存的页面数据对不上的问题，在这里清空其他页面的缓存
    //   if (value !== oldValue) {
    //     this.updateAccountPeriodToCache();
    //   }
    // },
  },
  mounted() {
    this.init();
  },
  beforeDestroy() {
    // 当切换到账套外时，标签页会消失，同时需要清空所有缓存的页面
    this.$store.dispatch('tagsView/delAllVisitedViews');
    this.$store.dispatch('tagsView/delAllCachedViews');
  },
  methods: {
    init() {
      this.initTags();
      this.addTags();
    },
    isActive(route) {
      return route.name === this.$route.name;
    },
    isAffix(tag) {
      if (this.visitedViews.length === 1) return true;
      return tag.meta && tag.meta.affix;
    },
    filterAffixTags(routes, basePath = '/') {
      let tags = [];
      routes.forEach((route) => {
        if (route.meta && route.meta.affix) {
          const tagPath = path.resolve(basePath, route.path);
          tags.push({
            fullPath: tagPath,
            path: tagPath,
            name: route.name,
            meta: { ...route.meta },
          });
        }
        if (route.children) {
          const tempTags = this.filterAffixTags(route.children, route.path);
          if (tempTags.length >= 1) {
            tags = [...tags, ...tempTags];
          }
        }
      });
      return tags;
    },
    initTags() {
      const affixTags = this.affixTags = this.filterAffixTags(this.routes);
      for (const tag of affixTags) {
        // Must have tag name
        if (tag.name) {
          this.$store.dispatch('tagsView/addVisitedView', tag);
        }
      }
    },
    // 获取第一个非固定的标签页面
    getFirstTagWithoutAffix() {
      const tag = find(this.visitedViews, (item) => !find(this.affixTags, (affixTag) => affixTag.name === item.name));
      return tag;
    },
    async addTags() {
      // eslint-disable-next-line no-shadow
      const { name, path, meta } = this.$route;
      if (path.indexOf('controlCenter') !== -1) return false;
      if (meta?.noTag) return false;
      if (name) {
        this.$store.dispatch('tagsView/addView', this.$route);
         if (this.visitedViews.length > get(this.$store.state.app.appConfig, 'tagsView.max')) {
          const tag = this.getFirstTagWithoutAffix();
          await this.$store.dispatch('tagsView/delView', tag);
        }
      }
      return false;
    },
    moveToCurrentTag() {
      const tags = this.$refs.tag;
      this.$nextTick(() => {
        // eslint-disable-next-line no-restricted-syntax
        for (const tag of tags) {
          if (tag.to.name === this.$route.name) {
            this.$refs.scrollPane.moveToTarget(tag);
            // when query is different then update
            if (tag.to.fullPath !== this.$route.fullPath) {
              this.$store.dispatch('tagsView/updateVisitedView', this.$route);
            }
            break;
          }
        }
      });
    },
    refreshSelectedTag(view) {
      this.$store.dispatch('tagsView/delCachedView', view).then(() => {
        const { fullPath } = view;
        console.log(fullPath);
        this.$nextTick(() => {
          this.$router.replace({
            path: `/redirect${ fullPath}`,
          });
        });
      });
    },
    async closeSelectedTag(view) {
      try {
        if (this.isActive(view)) {
          await this.toLastView(this.visitedViews.filter((item) => item !== view), view);
        }
        this.$store.dispatch('tagsView/delView', view);
      } catch (e) {
        console.log('可能是页面中断了跳转', e);
      }
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag);
      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {
        this.moveToCurrentTag();
      });
    },
    closeAllTags(view) {
      this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {
        if (this.affixTags.some((tag) => tag.path === view.path)) {
          return;
        }
        this.toLastView(visitedViews, view);
      });
    },
    async toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0];
      if (latestView) {
        await this.$router.push(latestView.fullPath);
      } else {
        // now the default is to redirect to the home page if there is no tags-view,
        // you can adjust it according to your needs.
        if (view.name === 'guidance') {
          // to reload home page
          await this.$router.replace({ path: `/redirect${ view.fullPath}` });
        } else {
          await this.$router.push('/');
        }
      }
    },
    // updateAccountPeriodToCache() {
    //   const activeView = this.visitedViews.find((item) => this.isActive(item));
    //   this.$store.commit('tagsView/SET_CACHED_VIEW_PERIOD', { name: activeView.name, accountPeriod: this.accountPeriod });
    // },
    // resetAccountPeriod() {
    //   const activeView = this.cachedViews.find((item) => this.isActive(item));
    //   if (activeView && activeView.scopeAccountPeriod) {
    //     this.$store.commit('user/SET_GLOBAL_ACCOUNT_PERIOD', activeView.scopeAccountPeriod);
    //   }
    // },
    openMenu(tag, e) {
      const menuMinWidth = 105;
      const offsetLeft = this.$el.getBoundingClientRect().left; // container margin left
      const { offsetWidth } = this.$el; // container width
      const maxLeft = offsetWidth - menuMinWidth; // left boundary
      const left = e.clientX - offsetLeft + 35; // 15: margin right
      if (left > maxLeft) {
        this.left = maxLeft;
      } else {
        this.left = left;
      }
      this.top = e.clientY - 35;
      this.visible = true;
      this.selectedTag = tag;
    },
    closeMenu() {
      this.visible = false;
    },
    handleScroll() {
      this.closeMenu();
    },
  },
};
</script>

<style lang="scss" scoped>
.tags-view-container {
  height: 35px;
  width: 100%;
  background: #E0E5EC;
  padding-top: 5px;
  .tags-view-wrapper {
    .tags-view-item {
      display: inline-block;
      position: relative;
      cursor: pointer;
      border-top: 2px solid #E0E5EC;
      height: 32px;
      line-height: 32px;
      color: #666666;
      background: #E0E5EC;
      padding: 0 8px 0 9px;
      font-size: 13px;
      // margin-left: 4px;
      &:first-of-type {
        margin-left: 15px;
        &::before {
          background: transparent;
        }
      }
      &:last-of-type {
        margin-right: 15px;
      }
      &::before {
        content: '';
        background: #9CA1A7;
        display: inline-block;
        width: 1px;
        height: 18px;
        position: absolute;
        left: -2px;
        top: 6px;
      }
      &.affix{
        padding-right: 10px;
      }
      &.active {
        background: $layout-backgroundcolor;
        color: $color-theme-base;
        border-top: 2px solid $color-theme-base;
        font-weight: bold;
        height: 33px;
        &::before {
          background: transparent;
        }
      }
    }
     .active + .tags-view-item{
        &::before {
          background: transparent;
        }
      }
  }
  .contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);
    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;
      &:hover {
        background: #eee;
      }
    }
  }
}
</style>

<style lang="scss">
//reset element css of el-icon-close
.tags-view-wrapper {
  .tags-view-item {
    .el-icon-close {
      width: 16px;
      height: 16px;
      margin-left: 10px;
      vertical-align: 1px;
      border-radius: 50%;
      text-align: center;
      transition: all .3s cubic-bezier(.645, .045, .355, 1);
      transform-origin: 100% 50%;
      &:before {
        display: inline-block;
        vertical-align: -2px;
      }
      &:hover {
        background-color: $color-theme-base;
        color: #fff;
      }
    }
  }
}
</style>
