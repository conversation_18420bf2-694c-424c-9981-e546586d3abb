<template>
  <!-- eslint-disable vue/require-component-is -->
  <component v-bind="linkProps" @click.native="onClick">
    <slot/>
  </component>
</template>

<script>
import { isExternal } from '@/assets/Utils';
import dayjs from 'dayjs';

export default {
  props: {
    to: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    rolestr: {
      type: String,
      required: false,
    },
  },
  computed: {
    linkProps() {
      return this.getLinkProps(this.to);
    },
  },
  methods: {
    // 添加使用时间过长， 在切换菜单时，刷新页面的操作
    reload() {
      if (!(this.to.indexOf('EMPTYLINK') !== -1 || isExternal(this.to))) {
        if (this.to === this.$route.path || this.to === this.$route.redirectedFrom) return;
        if (dayjs().diff(dayjs(this.$store.state.app.startUseTime), 'hours') >= 2) {
          setTimeout(() => window.location.reload(), 300);
        }
      }
    },
    onClick() {
      this.reload();
      this.$emit('click');
    },
    getLinkProps(url) {
      if (url.indexOf('EMPTYLINK') !== -1) {
        return {
          is: 'router-link',
          to: '',
        };
      }

      if (isExternal(url)) {
        return {
          is: 'a',
          href: url,
          target: '_blank',
          rel: 'noopener',
        };
      }
      return {
        is: 'router-link',
        to: url,
      };
    },
  },
};
</script>
