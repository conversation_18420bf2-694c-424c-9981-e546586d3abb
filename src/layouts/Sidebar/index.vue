<!--
 * @Description:
 * @version:
 * @Company: 海闻软件
 * @Author: 启旭
 * @Date: 2020-05-25 09:47:37
 * @LastEditors: 启旭
 * @LastEditTime: 2020-05-25 10:12:07
-->
<template>
  <div class="scrollbar-wrapper sidebar-menu">
    <el-menu
      :collapse="allowPopup || isCollapse"
      :default-active="routePath"
    >
      <sidebar-item
        v-for="route in routers"
        :virtual="virtual"
        :isCollapse="isCollapse"
        :key="route.name"
        :item="route"
        :base-path="route.path"
      />
    </el-menu>
  </div>
</template>
<style lang="scss">
  @import '@/sass/layouts/sidebar.scss';
</style>
<script>
import { mapGetters } from 'vuex';
import { getInvoicingProfiles } from '@/pages/invoicing/api/index';
import { get } from 'lodash-es';
import SidebarItem from './SidebarItem.vue';

let virtual;
export default {
  components: { SidebarItem },
  data() {
    return {
      virtual: null,
    };
  },
  computed: {
    ...mapGetters([
      'permissionRouters',
      'sidebarCollapse',
    ]),
    allowPopup() {
      return get(this.$store.state.app.appConfig, 'sidebar.allowPopup');
    },
    isCollapse() {
      return !this.isTaxWorkspace && this.sidebarCollapse;
    },
    isControlCenter() {
      return this.routePath.indexOf('controlCenter') !== -1;
    },
    isTaxWorkspace() {
      return this.routePath.indexOf('taxWorkspace') !== -1;
    },
    routePath() {
      return this.$route.path;
    },
    showShareCompanyItem() {
      const { userInfo } = this.$store.state.user;
      const companyInfo = userInfo.companyInfo || {};
      const shareCompany = companyInfo.shareCompany || false;
      return shareCompany;
    },
    routers() {
      // 通过mate对象中的type属性获取当前菜单属于哪种状态， 从而做到根据路由切换菜单
      const { permissionRouters } = this;
      const { isControlCenter, isTaxWorkspace, showShareCompanyItem } = this;
      const routers = permissionRouters.filter((item) => {
        if (item.hidden) return false;
        const metaType = item?.meta?.type;
        const meteTitle = item?.meta?.title;
        // 外层
        if (isControlCenter) {
          return metaType === 'controlCenter';
        }
        // 申报平台
        if (isTaxWorkspace) {
          return metaType === 'taxWorkspace';
        }
        // 分享公司  费用界面隐藏
        if (showShareCompanyItem && meteTitle === '费用') {
          return false;
        }
        return metaType !== 'controlCenter' && metaType !== 'taxWorkspace';
      });
      return routers;
    },
  },
  mounted() {
    this.initInvoicingProfiles();
  },
  methods: {
    ...mapGetters('user', ['isXuRi']),
    async initInvoicingProfiles() {
      if (typeof virtual === 'boolean') return;
      const { userInfo } = this.$store.state.user;
      const vipCodeBkCom = userInfo.vipCodeBkCom || false;
      const selectCompanyId = userInfo.selectCompanyId || false;
      if (!vipCodeBkCom || !selectCompanyId) return;
      const isToll = vipCodeBkCom.indexOf('Paid-NewInvoive-001') !== -1;
      //  true  为虚拟
      //  false 为真实
      if (isToll) {
        await getInvoicingProfiles().then((res) => {
          [this.virtual] = res.data.data;
          [virtual] = res.data.data;
        });
      }
    },
  },
};
</script>
