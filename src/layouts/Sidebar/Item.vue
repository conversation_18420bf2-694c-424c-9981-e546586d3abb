<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    isCollapse: {
      type: Boolean,
    },
  },
  render(h, context) {
    const { icon, title, isCollapse } = context.props;
    const vnodes = [];
    if (icon) {
      vnodes.push(<i class={`icon-${icon} iconfont`} style="font-size:14px;text-align:center;padding:0 8px;"></i>);
    }
    if (title) {
      vnodes.push(<span>{(title)}</span>);
    }
    return vnodes;
  },
};
</script>
