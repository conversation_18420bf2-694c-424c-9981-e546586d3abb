<template>
  <div v-if="!isHidden" class="menu-wrapper">
    <template
      v-if="hasOneShowingChild(item.children,item) &&
        (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow"
      >
      <app-link v-if="!onlyOneChild.hidden" :name="onlyOneChild.name" :to="resolvePath(onlyOneChild.path)" :rolestr="onlyOneChild.meta.roleStr">
        <el-menu-item
          :index="resolvePath(onlyOneChild.path)"
          :class="{'submenu-title-noDropdown':!isNest}">
          <!-- <span v-if="isCollapse">{{onlyOneChild.meta.title}}</span> -->
          <item
            v-if="onlyOneChild.meta"
            :icon="onlyOneChild.meta.icon||(item.meta&&item.meta.icon)"
            :isCollapse="isCollapse"
            :title="isInvoicedMode && virtual ? `(虚拟)${onlyOneChild.meta.title}` : onlyOneChild.meta.title" />
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu v-else ref="subMenu"
      popper-append-to-body
      :show-timeout="200"
      :hide-timeout="200"
      :popper-class="!isNest ? 'menu-popup_first' : ''"
      :index="resolvePath(item.path)">
      <template slot="title">
        <item
          v-if="item.meta"
          :isCollapse="isCollapse"
          :icon="item.meta && item.meta.icon"
          :title="item.meta.title" />
      </template>
      <sidebar-item
        v-for="child in itemChildren"
        :is-nest="true"
        :item="child"
        :isCollapse="isCollapse"
        :key="child.name"
        :virtual="virtual"
        :base-path="resolvePath(child.path)"
        class="nest-menu" />
    </el-submenu>
  </div>
</template>

<script>

import path from 'path';
import { mapActions } from 'vuex';
import { isExternal } from '@/assets/Utils';
import { isEmpty } from '@hw/pl-table/lib/utils/util';
import Item from './Item.vue';
import AppLink from './Link.vue';
// import FixiOSBug from './FixiOSBug'

export default {
  name: 'SidebarItem',
  components: { Item, AppLink },
  props: {
    // route object
    item: {
      type: Object,
      required: true,
    },
    isNest: {
      type: Boolean,
      default: false,
    },
    basePath: {
      type: String,
      default: '',
    },
    isCollapse: {
      type: Boolean,
      default: false,
    },
    virtual: {
      type: Boolean,
    },
  },
  data() {
    // To fix https://github.com/PanJiaChen/vue-admin-template/issues/237
    // TODO: refactor with render function
    this.onlyOneChild = null;
    return {
    };
  },
  computed: {
    isNonprofit() {
      return this.$store.state.user.isNonprofit;
    },
    itemChildren() {
      const { userInfo } = this.$store.state.user;
      const companyInfo = userInfo.companyInfo || {};
      const shareCompany = companyInfo.shareCompany || false;
      if (shareCompany) {
        return this.item.children.filter((thing) => (thing.meta.title !== '项目设置' && thing.meta.title !== '报销内容'));
      }
      return this.item.children;
    },
    isHidden() {
      return this.routerIsHidden(this.item);
    },
    isInvoicedMode() {
      return this.basePath.indexOf('/invoicing') !== -1;
    },
  },

  methods: {
    routerIsHidden(route) {
      if (route.hidden) return true;
      if (route.children) {
        return isEmpty(route.children);
      }
      //  需求 3795 民间非营利组织会计准则不显示的报表及账本处理
          //  accountingId === 3 民间非营利组织会计准则
          //  route.meta 有 hideWithNonprofit 属性 则是隐藏
      if (this.isNonprofit && route.meta.hideWithNonprofit) {
        return true;
      }
      //  route.meta 有 showWithNonprofit 属性 则是只有 非盈利组织 才能显示
      if (!this.isNonprofit && route.meta.showWithNonprofit) {
        return true;
      }
      if (route.meta && route.meta.frontendSeqCode) {
        const { frontendSeqCode } = route.meta;
        const hasFontentPermission = this.$store.getters['user/hasFontentPermission'];
        const isBK = route.path.indexOf('/controlCenter') === 0;
        // console.log(route.meta.frontendSeqCode, isBK, !hasFontentPermission([route.meta.frontendSeqCode], isBK));
        // 当页面权限码只有模块和页面部分时，自动补充 chaxun 功能点， 如果带有功能点，则不处理
        const genPageFrontendSeqCode = ((code) => (code.split('-').length > 2 ? code : `${code}-chaxun`));
        if (Array.isArray(frontendSeqCode)) {
          return !hasFontentPermission(frontendSeqCode.map((item) => genPageFrontendSeqCode(item)), isBK);
        }
        return !hasFontentPermission([genPageFrontendSeqCode(frontendSeqCode)], isBK);
      }
      return false;
    },
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter((item) => {
        if (this.routerIsHidden(item)) {
          return false;
        }
        // Temp set(will be used if only has one showing child)
        this.onlyOneChild = item;
        return true;
      });
      const orginChildren = this.$store.state.app.appConfig.menus.find((item) => parent.name === item.name)?.children ?? children;
      // When there is only one child router, the child router is displayed by default
      // 只有设计上单个菜单的子菜单才需要提升到一级菜单
      if (showingChildren.length === 1 && orginChildren.filter((item) => !item.hidden).length === 1) {
        return true;
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = {
          ...parent, path: '', noShowingChildren: true, hidden: parent?.children?.length > 0,
        };
        return true;
      }

      return false;
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath;
      }
      if (isExternal(this.basePath)) {
        return this.basePath;
      }
      return path.resolve(this.basePath, routePath);
    },
  },
};
</script>
