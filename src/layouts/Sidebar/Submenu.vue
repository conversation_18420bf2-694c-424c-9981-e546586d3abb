<script>
import { Submenu } from 'element-ui';

const findElSubmenuParent = (vm) => {
  if (!vm.$parent) return null;
  if (vm.$parent.$options?.name !== 'ElSubmenu') {
    return findElSubmenuParent(vm.$parent);
  }
  return vm.$parent;
};
export default {
  name: 'ElSubmenu',
  componentName: 'ElSubmenu',
  mixins: [Submenu],
  render: Submenu.render,
  methods: {
    handleMouseleave(deepDispatch = false) {
      const { rootMenu } = this;
      if (
        (rootMenu.menuTrigger === 'click' && rootMenu.mode === 'horizontal')
        || (!rootMenu.collapse && rootMenu.mode === 'vertical')
      ) {
        return;
      }
      this.dispatch('ElSubmenu', 'mouse-leave-child');
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        if (!this.mouseInChild) this.rootMenu.closeMenu(this.index);
      }, this.hideTimeout);

      if (this.appendToBody && deepDispatch && !this.isFirstLevel) {
        const submenuParent = findElSubmenuParent(this);
        if (submenuParent) {
          submenuParent.handleMouseleave(true);
        }
      }
    },
  },
};
</script>
