<script>
import { mapActions } from 'vuex';
import { PROJECT_TYPE, VERSION } from '@/assets/constant';
import introConfig from '@/assets/introConfig';
import Messages from '@/components/Messages.vue';
import screenfull from 'screenfull';
import GlobalAccountPeriodPicker from '@/components/GlobalAccountPeriodPicker.vue';
import CompanyPicker from '@/components/CompanyPicker.vue';
import LogoSvg from '@/components/LogoSvg.vue';
import LogoSvgWhite from '@/components/LogoSvgWhite.vue';
import GrayTest from './GrayTest.vue';
import GrayTestAlert from './GrayTestAlert.vue';
import HelpCenterWindow from '../components/HelpCenterWindow.vue';
import PromotionDlg from './components/PromotionDlg.vue';
import ThemeSelector from './components/ThemeSelector.vue';
import DifyChat from '@/components/DifyChat.vue';

const isTest = process.env.NODE_ENV !== 'production';

export default {
  name: 'QHeader',

  components: {
    LogoSvg,
    LogoSvgWhite,
    Messages,
    GrayTest,
    GrayTestAlert,
    HelpCenterWindow,
    PromotionDlg,
    ThemeSelector,
    GlobalAccountPeriodPicker,
    CompanyPicker,
    DifyChat,
  },

  computed: {
    logoPath() {
      const logoName = this.isTaxWorkspace ? 'tax_logo' : this.$store.state.app.appConfig.logo;
      // 绿色主题下为深色header，logo需用浅色
      const logoState = this.$store.state.app.theme.color === 'green' ? 'white' : 'full';
      return `/static/imgs/logo/${logoName}_${logoState}.png`;
    },
    sidebarCollapse() {
      // 税务工作区模式永远保持侧边栏展开状态
      return !this.isTaxWorkspace && this.$store.state.app.sidebarCollapse;
    },
    sidebarWidth() {
      const { width } = this.$store.state.app.appConfig.sidebar;
      return width;
    },
    routePath() {
      return this.$route.path;
    },
    isTaxWorkspace() {
      return this.routePath.indexOf('taxWorkspace') !== -1;
    },
    username() {
      return this.$store.state.user.userInfo.username;
    },
    companyName() {
      return this.$store.state.user.userInfo.companyName;
    },
    roleNameStr() {
      return this.$store.state.user.userInfo.roleNameStr;
    },
    bkCompanyName() {
      return this.$store.state.user.userInfo.bkCompanyName;
    },
    bkCompanyCode() {
      return this.$store.state.user.userInfo.bkCompanyCode;
    },
    selectCompanyId() {
      return this.$store.state.user.userInfo.selectCompanyId;
    },
    chosenPath() {
      return this.$route.path.split('/')[1];
    },
    isControlCenterPage() {
      return this.chosenPath === 'controlCenter';
    },
    isSuperManager() {
      return this.roleNameStr.indexOf('ROLE_OM_ADMIN') !== -1;
    },
    isGrayTest() {
      return this.$store.state.user.isGrayTest;
    },
    // 标识该人员所处的代账公司是否为 有 记账公司管理权限 vipCode
    hasQuanXianVipCode() {
      const { userInfo } = this.$store.state.user;
      if (!userInfo.vipCodeBkCom) return false;
      return userInfo.vipCodeBkCom.indexOf('Paid-quanxian-001') !== -1;
    },
    hasCRMVipCode() {
      const { userInfo } = this.$store.state.user;
      if (!userInfo.vipCodeBkCom) return false;
      return userInfo.vipCodeBkCom.indexOf('paid-crm-001') !== -1;
    },
    hasCRMRule() {
      const { userInfo } = this.$store.state.user;
      const hasCRMRule = userInfo?.roleNames?.includes('ROLE_BASE_CRM');
      return hasCRMRule;
    },
    /** 是否拥有税易平台收费码 */
    hasHkTaxVipCode() {
      const { userInfo } = this.$store.state.user;
      if (!userInfo.vipCodeBkCom) return false;
      return userInfo.vipCodeBkCom.indexOf('paid-hk-001') !== -1;
    },
    hasHkTaxRule() {
      const { userInfo } = this.$store.state.user;
      const hasHkTaxRule = userInfo?.roleNames?.includes('ROLE_BASE_HK');
      return hasHkTaxRule;
    },
    isProd() {
      return process.env.NODE_ENV === 'production';
    },
  },

  data() {
    return {
      isTest,
      fullScreen: screenfull.isFullscreen,
      dragging: false,
      isCompact: false,
      projectType: PROJECT_TYPE,
      projectVersion: VERSION,
      time: null,
      leafPathData: [],
      searchText: '',
      logoDialogVisible: false,
      exit_popover: false,
      companyDialog: {
        bkCompanyName: this.$store.state.user.userInfo.bkCompanyName,
        createTime: '',
        bkCompanyLimit: '',
        companyTotal: '',
        enableBKAuthority: false,
      },
      imageUrl: '',
      intro: null,
      helpCenterWindowVisible: false,
      promotionVisible: false,
      themeSelectorVisible: false,
      aiAssistantTypes: [
        {
          type: 'financial',
          name: '财税专家',
          chatId: 'caishui-ai',
          token: 'bLJ9gDY0OZrCgSIg',
        },
        {
          type: 'accounting',
          name: '记账专家',
          chatId: 'shuiwu-ai',
          token: 'erajWq4wRxJEvZzJ',
        },
      ],
    };
  },

  mounted() {
    this.editBkCompany();
    // 只有财税智享才需要在线客服
    // if (this.$store.state.app.PROJECT_TYPE === 'enterprise') {
    //   this.createUdesk();
    // }
  },

  watch: {
    $route() {
      if (
        this.$route.path !== '/manager/guidance'
        && this.$route.path !== '/manager/guidanceSmall'
        && this.intro
      ) {
        this.intro.exit();
        this.intro = null;
      }
    },
  },

  methods: {
    goBookList() {
      this.$router.push({ name: 'booksSetList' });
    },
    showThemeSelector() {
      this.themeSelectorVisible = true;
    },
    // 操作指引
    showZhiyin() {
      const path = this.$route.path.split('/').slice(-1)[0];

      const intros = introConfig.filter((item) => {
        if (item.showPath) {
          return item.showPath.indexOf(path) !== -1;
        }
        if (item.element) {
          return $(item.element).length && !$(item.element).is(':hidden');
        }
        return false;
      });
      intros.sort((item1, item2) => item1.index - item2.index);
      if (intros.length !== 0) {
        this.$intro()
          .setOptions({
            steps: intros,
            nextLabel: '下一步',
            prevLabel: '上一步',
            doneLabel: '完成',
            skipLabel: '退出',
          })
          .start();
      } else {
        this.$message.info('当前页面无指引内容');
      }
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar');
    },
    upgrade() {
      this.$alert('请联系客服 020-87515610', '升级操作', {
        confirmButtonText: '确定',
      });
    },
    async editLogo() {
      const rsp = await this.$http.get('/auth/bk/company/get');
      this.companyDialog = rsp.data.data[0] || {};
      this.logoDialogVisible = true;
    },
    editBkCompany() {
      this.$nextTick(() => {
        this.$http.get('/auth/bk/company/logoFind').then((res) => {
          this.imageUrl = (res.data.data[0]
              && `data:image/png;base64,${res.data.data[0].bkCompanyLogo}`)
            || '';
        });
      });
    },
    async submitUpload() {
      if (!this.companyDialog.bkCompanyName) {
        return this.$message.warning('请输入代账公司名称');
      }
      if (this.companyDialog.bkCompanyName.length > 50) {
        return this.$message.warning('代账公司名称不能超过50个字符');
      }
      await this.$refs.avatarLoader.submit();
      return this.$http
        .put(
          `/auth/bk/company/${this.$store.state.user.userInfo.bkCompanyCode}`,
          {
            bkCompanyName: this.companyDialog.bkCompanyName,
            enableBKAuthority: this.hasQuanXianVipCode ? this.companyDialog.enableBKAuthority : undefined,
          },
        )
        .then(async () => {
          this.$message.success('成功');
          this.logoDialogVisible = false;
          this.getUserInfo();
          // 如果存在更新权限控制的行为，则进行刷新
          if (this.$store.state.user.BKCompanyInfo.enableBKAuthority !== this.companyDialog.enableBKAuthority) {
            await this.getBKCompanyInfo();
            this.getFrontendSeqCodeWithBK();
            this.$message.success('请重新登录，权限相关功能需重新登录才可生效');
          }
        });
    },
    ...mapActions('user', ['getUserInfo', 'getBKCompanyInfo', 'getFrontendSeqCodeWithBK']),
    toggleScreenFull() {
      this.fullScreen = !this.fullScreen;
      screenfull.toggle();
    },
    showNorat() {
      this.$emit('showNorat');
    },
    onAvatarChange(file) {
      this.imageUrl = file.url;
    },
    onAvatarChanges(files, fileList) {
      const rawFile = files[0];
      rawFile.uid = Date.now() + Math.random();
      const file = {
        status: 'ready',
        name: rawFile.name,
        size: rawFile.size,
        percentage: 0,
        uid: rawFile.uid,
        raw: rawFile,
      };

      try {
        file.url = URL.createObjectURL(rawFile);
      } catch (err) {
        console.error(err);
        return;
      }
      fileList.pop();
      fileList.push(file);
      this.imageUrl = file.url;
      this.$refs.avatarLoader.submit();
      console.log(files, fileList, file);
    },
    handleAvatarSuccess(res, file) {
      this.imageUrl = URL.createObjectURL(file.raw);
    },
    beforeAvatarUpload(file) {
      console.log(file);
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      return isLt2M;
    },
    createUdesk() {
      // 客服代码
      $(() => {
        // eslint-disable-next-line func-names
        (function (a, h, c, b, f, g) {
          a.UdeskApiObject = f;
          a[f] = a[f]
            // eslint-disable-next-line func-names
            || function () {
              // eslint-disable-next-line prefer-rest-params
              (a[f].d = a[f].d || []).push(arguments);
            };
          g = h.createElement(c);
          g.async = 1;
          g.charset = 'utf-8';
          g.src = b;
          // eslint-disable-next-line prefer-destructuring
          c = h.getElementsByTagName(c)[0];
          c.parentNode.insertBefore(g, c);
        }(
          window,
          document,
          'script',
          'https://assets-cli.udesk.cn/im_client/js/udeskApi.js',
          'ud',
        ));
        // eslint-disable-next-line no-undef
        ud({
          code: '340k27kk',
          link: 'https://cmb360.udesk.cn/im_client/?web_plugin_id=65915',
        });
      });
      $('#udesk_container').show();
      setTimeout(() => {
        $('#udesk_btn a').css('z-index', '1');
      }, 5000);
    },
    loginOut() {
      this.$http.get('/auth/logout').then(() => {
        if (this.isTaxWorkspace && this.$store.state.app.PROJECT_TYPE !== 'agent') {
          this.$router.push('/smarttax').then(() => {
            this.$store.commit('LOGOUT');
          });
        } else {
          this.$router.push('/').then(() => {
            this.$store.commit('LOGOUT');
          });
        }
      });
    },
    handlePromotion() {
      this.promotionVisible = true;
    },
    goCRM() {
      window.open(`${window.location.origin}/crm.html`);
    },
    goHkTax() {
      window.open(`${window.location.origin}/hktax.html`);
    },
    async onGlobalAccountPeriodChange() {
      await this.$store.dispatch('tagsView/delAllCachedViews');
      const { fullPath } = this.$route;
      this.$nextTick(() => {
        this.$router.replace({
          path: `/redirect${fullPath}`,
        });
      });
      this.$message.info('全局属期变更，标签页缓存已清除');
    },
    onAssistantSelected(assistant) {
      console.log('onAssistantSelected:', assistant);
    },
    onChatOpened() {
      console.log('onChatOpened');
    },
    onChatClosed() {
      console.log('onChatClosed');
    },
    onDifyLoaded() {
      console.log('onDifyLoaded');
    },
    openChat(type) {
      if (this.$refs.difyChat) {
        this.$refs.difyChat.openChat(type);
      }
    },
    closeChat() {
      if (this.$refs.difyChat) {
        this.$refs.difyChat.closeChat();
      }
    },
  },
};
</script>

<template>
  <el-header class="qf_header" :class="{
    'tax-workspace_header': isTaxWorkspace,
    'qf_header--light': isControlCenterPage,
    'qf_header--dark': !isControlCenterPage,
  }">
    <!-- 测试环境版本号 -->
    <span class="project_version" v-if="isTest && !isTaxWorkspace" >
      V{{projectVersion}}-id{{selectCompanyId}}
    </span>

    <div class="flex-h">
      <div class="header-left flex-fluid flex-h">
        <!-- LOGO -->
        <div class="logo flex-fixed">
          <LogoSvg v-if="isControlCenterPage" style="height:100%;" />
          <LogoSvgWhite v-else style="height:100%;" />
        </div>

        <!-- <div class="userlogo flex-fixed" v-if="imageUrl  !== '' && projectType==='agent'"><img :src="imageUrl" /></div> -->

        <!-- 代帐公司 -->
        <div
          v-if='isControlCenterPage'
          key="bkCompanyName"
          class="flex-fluid commodityNmae">
          <span
            v-permission.BK.disabled="['controlCenter-BKcompany-bianji']"
            title="点击修改代帐公司信息"
            style="cursor: pointer;"
            @click="editLogo">
            {{ bkCompanyName }}
          </span>
        </div>

        <!-- 跳转 -->
        <div v-else key="companyName" class="flex-fluid commodityNmae flex-h" style="align-items:center;">
          <CompanyPicker class="CompanyPicker" style="margin-right: 8px"/>
          <global-account-period-picker class="GlobalAccountPeriodPicker" @change="onGlobalAccountPeriodChange" />
          <!-- <span class="commodityNmae-text">{{ companyName }}</span> -->
          <!-- <router-link
            v-if="['ROLE_HJ_HH_SPONSOR','ROLE_HJ_HH_CHARGE','ROLE_HJ_HH_CUSTOMER'].hasMap(roleNameStr)"
            to="/controlCenter/cnoocBooksSetList"
            style="color:#E58F00;font-weight:normal;font-size:12px; margin-left:5px;">
            <span v-show='!(isControlCenterPage)' title="切换账套" class="iconfont icon-qiehuan1"></span>
          </router-link>
          <router-link
            v-else
            to="/controlCenter/booksSetList"
            style="color:#E58F00;font-weight:normal;font-size:12px;margin-left:5px;" >
            <span v-show='!(isControlCenterPage)' title="切换账套" class="iconfont icon-qiehuan1"></span>
          </router-link> -->
        </div>

        <div class="flex-fixed margin-left-10" style="display: flex"  v-if="isSuperManager" >
          <!-- 灰度 -->
          <GrayTestAlert />
        </div>
      </div>

      <div style="display: flex" class="flex-fixed top_right_fun header_right_wrapper">
        <div class="header_goCRM margin-right-10" >
          <el-button v-if='!isControlCenterPage' class="blackBtn" size="small" @click="goBookList">
            记账平台
          </el-button>

          <el-button v-if="hasCRMVipCode && hasCRMRule" class="blackBtn" size="small" @click="goCRM">
            CRM平台
          </el-button>

          <el-button v-if="hasHkTaxVipCode && hasHkTaxRule" class="blackBtn" size="small" @click="goHkTax">
            稅易平台
          </el-button>
        </div>
        <!-- AI助手 -->
        <div v-if="!isProd" class="help_btn">
          <DifyChat 
            ref="difyChat"
            :assistant-types="aiAssistantTypes"
            @assistant-selected="onAssistantSelected"
            @chat-closed="onChatClosed"
            @dify-loaded="onDifyLoaded"
          />
        </div>
        <div>
          <el-tooltip
            class="item"
            effect="dark"
            content="帮助中心"
            :open-delay='300'
            :enterable='false'
            transition='el-fade-in-swing'
            placement="left">
            <div class="help_btn" @click="helpCenterWindowVisible=!helpCenterWindowVisible">
              <i class="iconfont icon-wen"></i>
            </div>
          </el-tooltip>
        </div>

        <div>
          <Messages refresh key="COMPANY" v-if='!isControlCenterPage' :companyId="selectCompanyId"></Messages>
          <Messages  key="NO_COMPANY" v-else />
        </div>
        

        <!-- 全屏 -->
        <div>
          <el-tooltip
            class="item"
            effect="dark"
            content="全屏"
            :open-delay='300'
            :enterable='false'
            transition='el-fade-in-swing'
            placement="left"
            v-if="!fullScreen">
            <div @click="toggleScreenFull" class='tooltipButton fullScreen'>
              <i class="iconfont icon-quanping2"  style="font-size: 15px"></i>
            </div>
          </el-tooltip>
          <el-tooltip
            class="item"
            effect="dark"
            content="正常"
            :open-delay='300'
            :enterable='false'
            transition='el-fade-in-swing'
            placement="left"
            v-else>
            <div @click="toggleScreenFull" class='tooltipButton fullScreen'>
             <i class="iconfont icon-quanping2" style="font-size: 15px"></i>
            </div>
          </el-tooltip>
        </div>
        <!-- 个人信息 -->
        <div>
          <el-popover
            ref="popover4"
            placement="bottom"
            width="150"
            trigger="click"
            popper-class="user_popover">
            <ul class="userMneu">
              <li v-if="!['ROLE_HJ_HH_SPONSOR','ROLE_HJ_HH_CHARGE','ROLE_HJ_HH_CUSTOMER'].hasMap(roleNameStr)">
                <router-link to="/controlCenter/personalInformation">
                  <i class="iconfont icon-people"></i>个人信息
                </router-link>
              </li>
              <li @click="showThemeSelector">
                <a>
                  <i style="transform: scale(0.97);display: inline-block;" class="iconfont icon-pifu"></i>
                  <span>主题设置</span>
                </a>
              </li>
              <li v-if="isSuperManager">
                <GrayTest />
              </li>
              <li @click="loginOut"><a><i class="iconfont icon-close"></i>退出</a></li>
            </ul>
            <div slot="reference" class="user-box">
              <p class="portrait">
                <img src="/static/imgs/userlogo.png" alt="" />
              </p>
              <span class="username">{{username || '您尚未登录'}}</span>
              <span class="fa fa-angle-down"></span>
            </div>
          </el-popover>
        </div>
      </div>

      <!-- 套账完成后， 金鼎切换功能 去除 -->
      <div style="clear:both"></div>
    </div>

    <!-- 修改代账公司 -->
    <el-dialog
      title="修改公司"
      append-to-body
      :visible.sync="logoDialogVisible"
      class="companyDialogStyle"
      :modal-append-to-body="true"
    >
      <el-form
        ref="companyDialog"
        :model="companyDialog"
        label-width="160px"
      >
        <el-form-item label="公司LOGO：" style="position: relative;">
          <el-button type="text" class="promotionClass" @click="handlePromotion">点击编辑推广信息</el-button>
          <el-upload
            class="avatar-uploader"
            ref="avatarLoader"
            :show-file-list="false"
            :auto-upload="false"
            :limit="1"
            accept="image/jpg,image/png"
            :action="`/auth/bk/company/logoUpload/${$store.state.user.userInfo.bkCompanyCode} `"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
            list-type="picture"
            :on-exceed="onAvatarChanges"
            :on-change="onAvatarChange">
            <img v-if="imageUrl" :src="imageUrl" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="公司名称：">
          <el-input v-model="companyDialog.bkCompanyName" placeholder="请输入公司名称"></el-input>
        </el-form-item>
        <el-form-item label="创建时间：">{{ companyDialog.createTime | date}}</el-form-item>
        <el-form-item label="账套上限：">{{ companyDialog.bkCompanyLimit}}</el-form-item>
        <el-form-item label="剩余可创建账套：">
          {{ companyDialog.bkCompanyLimit - companyDialog.activeCompanyTotal}}
        </el-form-item>
        <el-form-item label="记账公司管理权限控制"  v-if="hasQuanXianVipCode">
          <el-radio-group v-model="companyDialog.enableBKAuthority">
            <el-radio :label="true">启用（每位员工均可授予不同管理权限）</el-radio>
            <el-radio :label="false">禁用（所有主管/财务人员均可使用所有管理权限）</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button style="float: left;" @click="upgrade">升 级</el-button>
        <el-button @click="logoDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitUpload" >确 定</el-button>
      </span>
    </el-dialog>

    <!-- 推广信息弹窗 -->
    <PromotionDlg
      :visible="promotionVisible"
      @hide="promotionVisible = false"
    ></PromotionDlg>
    <!-- 主题设置 -->
    <ThemeSelector
      :visible="themeSelectorVisible"
      @hide="themeSelectorVisible = false"
    />
     <!-- 帮助中心 -->
    <HelpCenterWindow :helpCenterWindowVisible.sync="helpCenterWindowVisible"></HelpCenterWindow>
  </el-header>
</template>

<style lang="scss">
@import "@/sass/layouts/qheader.scss";
</style>

<style lang="scss">
.promotionClass{
    position: absolute;
    left: 130px;
    top: 40px;
}
#udesk_btn a {
  margin: 6px 362px 3px 30px !important;
}

// .qf_header.tax-workspace_header {
//   .logo {
//     width: 160px;
//     height: 50px;
//     background: #3782f1;
//     text-align: center;
//     line-height: 50px;
//   }
// }
.userMneu li {
  height: 30px;
  line-height: 30px;
  padding: 0 10px 0 5px;
  cursor: pointer;
}
.userMneu li i {
  margin-right: 10px;
}
.userMneu li a {
  display: inline-block;
  color: #000;
  width: 100%;
  height: 100%;
}
.userMneu li:hover {
  background: #eee;
}
.GlobalAccountPeriodPicker{
  margin-right: 16px;
  width: 160px !important;
}
.el-select-dropdown.filterLeafPath .el-select-dropdown__item {
  padding: 0;
  a {
    display: inline-block;
    width: 100%;
    height: 100%;
    line-height: 28px;
    padding: 0 10px;
    color: inherit;
    box-sizing: border-box;
    i {
      margin-right: 5px;
    }
    &:hover {
      span {
        text-decoration: underline;
      }
    }
  }
}
.notice_popover {
  padding: 0 !important;
  .popper__arrow {
    &::after {
      border-bottom-color: #ebedf1 !important;
    }
  }
}
.companyDialogStyle {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }
}

.blackBtn {
  box-sizing: border-box;
  width: 92px;
  height: 32px;
  padding: 0;
  line-height: 32px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  border: none;
  border-radius: 4px;
  background-color: #EBF3FF;
  color: #5293F3;
}
</style>
