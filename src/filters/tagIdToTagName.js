/*
 * @Description:
 * @version: 过验证eslint
 * @Company: 海闻软件
 * @Author: 晓荣
 * @Date: 2019-08-05 10:41:12
 * @LastEditors: 晓荣
 * @LastEditTime: 2019-08-13 10:21:33
 */
import store from '@/store';

/**
 * 获取tag的全路径名称
 *
 * @export
 * @param {*} tag
 * @returns
 */
export function getTagFullName(tag) {
  const { tagCodeMap } = store.state.selectData;
  if (!tag) return '';
  const { tagTypeName } = tag;
  let AllTagNamePath = tag.tagName;
  const levelLen = String(tag.tagCode).length / 4;
  // 开启全路径查询和层级超过一级的标签才需要查找中间级标签名称
  if (levelLen > 2) {
    let index = levelLen - 1;
    // 获取当前标签的上一级内容，拼接标签路径
    while (index >= 2) {
      const currentLevelTag = tagCodeMap.get(tag.tagCode.slice(0, index * 4));
      if (!currentLevelTag) {
        break;
      }
      index -= 1;
      AllTagNamePath = `${currentLevelTag.tagName}-${AllTagNamePath}`;
    }
  }
  return `${tagTypeName}[${AllTagNamePath}]`;
}
/**
 * 将标签id 的转为 标签名显示 默认格式 `标签类别[最后一级标签名]`
 *
 * @export
 * @param {Array | string} [tagIds=[]] // 如果是字符串则使用逗号分隔
 * @param {boolean} [fullPath=false] 显示完整的标签名层级 `标签类别[父级标签名-最后一级标签名]`
 * @returns {string}
 */

export default function tagIdToTagName(tagIds = [], fullPath = true) {
  if (typeof tagIds === 'string' && tagIds !== '') {
    tagIds = tagIds.split(',');
  }
  if (!Array.isArray(tagIds) || tagIds.length === 0) return '';
  const result = [];
  const { tagMap } = store.state.selectData;
  if (tagMap.size === 0) return '';
  try {
    tagIds.forEach((tagId) => {
      const tag = tagMap.get(tagId);
      if (!tag) return;
      let tagFullName;
      if (fullPath) {
        tagFullName = getTagFullName(tag);
      } else {
        const { tagTypeName } = tag;
        const { tagName } = tag;
        tagFullName = `${tagTypeName}[${tagName}]`;
      }
      result.push(tagFullName);
    });
  } catch (e) {
    console.log(e);
  }
  return result.length > 0 ? result.join('；') : '';
}
