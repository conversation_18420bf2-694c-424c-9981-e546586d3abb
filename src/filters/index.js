import Vue from 'vue';
import {
  formatMoney, zeroMoney, formatDecimalNum, transformMoney, filterArray, toNonExponential, warpFormatDecimalNum,
} from '@/assets/Utils';
import tagIdToTagName from '@/filters/tagIdToTagName';

Vue.filter('moneyFilter', formatMoney);
Vue.filter('changeMoney', zeroMoney);
Vue.filter('decimalFilter', formatDecimalNum);
Vue.filter('transformMoney', transformMoney);
Vue.filter('warpFormatDecimalNum', warpFormatDecimalNum);
Vue.filter('tagIdToTagName', tagIdToTagName);
Vue.filter('date', (value) => {
  if (!value) {
    return '';
  }
  if (value instanceof Date) {
    return value.Format('yyyy-MM-dd');
  }
  if (typeof value === 'number') {
    return new Date(value).Format('yyyy-MM-dd');
  }
  return value.substring(0, 10);
  // return value.toLocaleDateString()
});
// 科学计数法转数值字符串显示
Vue.filter('toNonExponential', (value) => toNonExponential(value));
Vue.filter('percent', (value) => {
  if (!value) {
    return '';
  }
  const strData = parseFloat(value) * 100;
  const ret = `${strData.toFixed(2)}%`;
  return ret;
  // return value.toLocaleDateString()
});
Vue.filter('timestamp2Natlang', (dateTimeStamp) => {
  let
    result = '';
  const minute = 1000 * 60;
  const hour = minute * 60;
  const day = hour * 24;
  const month = day * 30;
  const now = new Date().getTime();
  dateTimeStamp = new Date(dateTimeStamp);
  const diffValue = now - dateTimeStamp;

  if (diffValue < 0) {
    return new Date(dateTimeStamp).Format('yyyy-MM-dd hh:mm');
  }

  const
    monthC = diffValue / month;
  const weekC = diffValue / (7 * day);
  const dayC = diffValue / day;
  const hourC = diffValue / hour;
  const minC = diffValue / minute;

  if (monthC >= 1) {
    result += `${parseInt(monthC, 10)}月前`;
  } else if (weekC >= 1) {
    result += `${parseInt(weekC, 10)}周前`;
  } else if (dayC >= 1) {
    result += `${parseInt(dayC, 10)}天前`;
  } else if (hourC >= 1) {
    result += `${parseInt(hourC, 10)}小时前`;
  } else if (minC >= 1) {
    result += `${parseInt(minC, 10)}分钟前`;
  } else {
    result = '刚刚';
  }
  return result;
});
Vue.filter('dateMonth', (value) => {
  if (typeof value === 'object') {
    return value.Format('yyyy-MM-dd').substring(0, 7);
  } if (typeof value === 'string') {
    return value.substring(0, 7);
  }
  return '';
  // return value.toLocaleDateString()
});
// 取小数后两位
Vue.filter('toFixed2', (value) => {
  if (value === 0) return 0;
  if (!value) return '';

  if (/^[+-]?\d+(\.\d+)?$/.test(value) || /^[+-]?[1-9](\.\d+)?[Ee][+-]?\d+$/.test(value)) {
    // console.log(value)
    return String(parseFloat(Number(value).toFixed(2))).replace(/(\d{3})+(?=\.)/, (str) => str.replace(/\d{3}/g, ',$&')).replace(/^([+-]?),/, '$1');
  }
  return value;
  // return value.toFixed(2);
  // return value.toLocaleDateString()
});
// 改变销售单状态
Vue.filter('getBillStatus', (value) => {
  switch (value) {
    case 1:
      return '草稿';
    case 2:
      return '已售出';
    case 3:
      return '撤销';
    default:
      return value;
  }
});
// 改变出货单状态
Vue.filter('getDeliveryBillStatus', (value) => {
  switch (value) {
    case 1:
      return '未出货';
    case 2:
      return '部分出货';
    case 3:
      return '已出货';
    default:
      return value;
  }
});
// 改变账目类型
Vue.filter('getAccountsType', (value) => {
  switch (value) {
    case 1001:
      return '销售销货';
    case 1002:
      return '销售收款';
    case 1003:
      return '销售退货';
    case 1004:
      return '销售退款';
    default:
      return value;
  }
});
// 科目显示银行账号后四位
Vue.filter('formatSubject', (value = '') => {
  const arr = (value || '').split('_');
  if (arr.length === 2 && !Number.isNaN(Number(arr[1]))) {
    arr[1] = arr[1].slice(-4);
    return arr.join('_');
  }
  return value;
});
/**
 * 通用数组过滤器
 * @param  {Object} value      传入的值
 * @param  {Array}  Arr        含有所有值的数组
 * @param  {[type]} keyName    数组对象key的名字
 * @param  {[type]} valueName) 数组对象value的名字
 * @return {String}            过滤后的新值
 */
Vue.filter('commonArrayFilter', (value, Arr = [], keyName, valueName) => {
  let r = ''; let
    o = null;
  if (value !== undefined) {
    o = filterArray(Arr, keyName, value);
    if (o !== undefined && o !== null) {
      if (typeof o[valueName] === 'string') {
        o[valueName] = o[valueName].trim();
      }
      r = o ? o[valueName] : '';
    }
  }
  return r;
});
