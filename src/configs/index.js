import { appConfig as agentConfig } from './configs/agent_default';
import { appConfig as enterpriseConfig } from './configs/enterprise_default';
import { findChannelConfigWithChannelCode } from './channelMap';

const PROJECT_TYPE = process.env.VUE_APP_PROJECT_TYPE;
// 根据渠道获取对应的项目配置
// 后端渠道码设计上可以有多个，暂时想象不出多个渠道码的场景，暂时只处理一种的情况
// eslint-disable-next-line import/prefer-default-export
export const getConfigWihtChannel = (channelCodes = []) => {
  const configs = channelCodes.map((channelCode) => findChannelConfigWithChannelCode(channelCode))
    .filter((item) => !!item);
  if (configs.length) {
    // 暂时不处理获取到多个配置的场景，默认获取到的第一个
    return configs[0];
  }
  // 从渠道码获取不到配置的情况则根据处于财税智享或者莲融获取默认的配置
  if (PROJECT_TYPE === 'enterprise' || localStorage.getItem('XuRiRouter') === 'XuRi') {
    return enterpriseConfig;
  }
  return agentConfig;
};
