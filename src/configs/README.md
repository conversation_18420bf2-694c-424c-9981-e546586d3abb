### 项目运行时配置文件夹

##### 目录结构

configs
  -configs // 项目各项配文件
  -menuSettings // 各种模式下的菜单配置
  -channelMap.js // 渠道配置文件，记录每种渠道的相关配置文件
  -index.js // 抛出跟配置相关的方法， getConfigWihtChannel

#### 路由配置结构

{
  // 当设置 ture 时， 路由不会在侧边菜单出现
  hidden: true, // 默认 false
  noRouter: true, // 默认false, 用于多级嵌套菜单时，该级菜单不需要存在于路由表中，此时将子级路由提升到当前等级
  name: 'router-name', // 路由的名称，必填,且不允许重复
  pageName: 'page-name' // 页面的名称与pages.js中注册的页面列表中name一致
  meta: {
    show: ['admin'], // 设置该路由进入所需的系统角色权限码
    title: 'title',  // 设置该路由在侧边和面包屑中显示的文字
    icon: 'icon', // 图标名称
    showWithVipCode: ['vip-code'] // 在特定收费码下显示， 此字段后续考虑放到pages中配置
    hideWithVipCode: ['vip-code'] // 在特定收费码下隐藏
  }
}