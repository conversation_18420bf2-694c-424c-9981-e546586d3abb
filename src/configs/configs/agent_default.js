// 项目配置

import jindingMenus from '../menuSettings/jinding';
// eslint-disable-next-line import/prefer-default-export
export const appConfig = {
  menus: jindingMenus,
  baseFontSize: '12px',
  // 侧边栏配置
  sidebar: {
    defaultCollapse: true,
    allowPopup: false, // 是否在展开模式下，也用popup弹出子级菜单
    width: '160px',
    smallWidth: '76px',
  },
  logo: 'jd_logo',
  tagsView: {
    enabled: true,
    max: 10,
  },
  // 首页配置
  home: {
    // 切换旧版页面按钮
    oldToggle: 'show',
    // 快捷功能入口
    quickJump: 'show',
    // 快捷操作
    quickAction: 'show',
    // 是否启用小规模企业专属首页
    useSmallHome: true,
  },
  input: {
    voucher: 'show', // 生成凭证功能
  },
  output: {
    voucher: 'show', // 生成凭证功能
  },
  trade: {
    voucher: 'show', // 生成凭证功能
    linkInvoice: 'show', // 关联发票
  },
};
