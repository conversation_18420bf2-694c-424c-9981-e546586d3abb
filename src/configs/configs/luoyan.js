// 罗燕渠道项目配置
import { merge, omit } from 'lodash-es';
import { appConfig as baseConfig } from './agent_default';
import luoyanMenus from '../menuSettings/luoyan';
// eslint-disable-next-line import/prefer-default-export
export const appConfig = merge({}, omit(baseConfig, ['menus']), {
  // 菜单
  menus: luoyanMenus,
  // 侧边栏配置
  sidebar: {
    defaultCollapse: false,
    allowPopup: false,
    width: '160px',
    smallWidth: '76px',
  },
  logo: 'jingyingzhang_logo',
  // 首页配置
  home: {
    // 快捷功能入口
    quickJump: 'hide',
    oldToggle: 'hide',
    useSmallHome: false,
  },
  input: {
    voucher: 'hide', // 生成凭证功能
  },
  output: {
    voucher: 'hide', // 生成凭证功能
  },
  trade: {
    voucher: 'hide', // 生成凭证功能
    linkInvoice: 'hide', // 关联发票
  },
});
