const menus = [
  {
    path: '/manager',
    children: [
      {
        name: 'guidanceSmall',
        path: 'guidanceSmall',
        meta: { title: '首页', noTag: true, show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'] },
        pageName: 'guidanceSmall',
      },
    ],
    hidden: true,
  },
  {
    path: '/manager/guidance',
    meta: {
      title: '首页',
      icon: 'shouye',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
    },
    children: [
      {
        name: 'guidance',
        path: '/manager/guidance',
        meta: { title: '首页', affix: true },
        pageName: 'guidance',
      },
    ],
  },
  {
    name: 'navigation',
    path: '/invoicing',
    meta: {
      title: '开票',
      icon: 'kaipiao',
      showWithVipCode: ['Paid-NewInvoive-001'],
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
    },
    children: [
      {
        name: 'navigation',
        path: 'navigation',
        meta: { title: '业务导航' },
        pageName: 'navigation',
      },
      {
        name: 'invoiceCommodityCode',
        path: 'invoiceCommodityCode',
        meta: { title: '商品编码设置' },
        pageName: 'invoiceCommodityCode',
      },
      {
        path: 'notInvoiced',
        name: 'notInvoiced',
        meta: { title: '销售单待开票流水', showWithVipCode: ['xuri-supported-2021'] },
        pageName: 'notInvoiced',
      },
      {
        path: 'corporateInformation',
        name: 'corporateInformation',
        meta: { title: '企业基本信息' },
        pageName: 'corporateInformation',
      },
      {
        path: 'invoiced',
        name: 'invoiced',
        meta: { title: '已开票列表' },
        pageName: 'invoiced',
      },
      {
        path: 'voidInvoice',
        name: 'voidInvoice',
        meta: { title: '未开票作废' },
        pageName: 'voidInvoice',
      },
      {
        path: 'copyNewspaper',
        name: 'copyNewspaper',
        meta: { title: '网上抄报' },
        pageName: 'copyNewspaper',
      },
      {
        path: 'invoicingRedPunch',
        name: 'invoicingRedPunch',
        meta: { title: '红字信息表' },
        pageName: 'invoicingRedPunch',
      },
    ],
  },
  {
    path: '/customerInteractions',
    name: 'customerInteractions',
    meta: {
      icon: 'shezhi1',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      title: '交互',
    },
    children: [
      {
        name: 'taxForecast',
        path: 'taxForecast',
        meta: { title: '预估税款' },
        pageName: 'taxForecast',
      },
      {
        name: 'deliveryConfirm',
        path: 'deliveryConfirm',
        meta: { title: '交付确认' },
        pageName: 'deliveryConfirm',
      },
      {
        name: 'businessFileManage',
        path: 'businessFileManage',
        meta: { title: '上传做账文件' },
        pageName: 'businessFileManage',
      },
      {
        name: 'initFileManage',
        path: 'initFileManage',
        meta: { title: '上传初始化文件' },
        pageName: 'initFileManage',
      },

    ],
  },
  {
    path: '/document',
    name: 'documentModule',
    meta: {
      icon: 'shiwu',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      title: '实务',
    },
    children: [
      {
 name: 'outputTax', path: 'outputTax', meta: { title: '销项' }, pageName: 'outputTax',
},
      {
 name: 'inputTax', path: 'inputTax', meta: { title: '进项' }, pageName: 'inputTax',
},
      {
 name: 'salary', path: 'salary', meta: { title: '工资' }, pageName: 'salary',
},
      {
 name: 'trade', path: 'trade', meta: { title: '资金' }, pageName: 'trade',
},
      {
 name: 'documentManagement', path: 'documentManagement', meta: { title: '单据' }, pageName: 'documentManagement',
},
      {
 name: 'createVoucher', path: 'createVoucher', meta: { title: '新增凭证' }, pageName: 'createVoucher',
},

    ],
  },
  {
    path: '/finance',
    name: 'financeModule',
    meta: { icon: 'caiwu1', show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'], title: '财务' },
    children: [
      {
        name: 'endCheckout',
        path: 'endCheckout',
        meta: { title: '期末结转' },
        pageName: 'endCheckout',
      },
      {
        name: 'simpleCostAccount',
        path: 'simpleCostAccount',
        meta: { title: '成本核算', hideWithNonprofit: true },
        pageName: 'simpleCostAccount',
      },
      {
        name: 'voucherDetails',
        path: 'voucherDetails',
        meta: { title: '凭证明细表' },
        pageName: 'voucherDetails',
      },
    ],
  },

  {
    path: '/assets',
    name: 'assets',
    meta: {
      icon: 'zichan',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      title: '资产',
    },
    children: [
      {
        name: 'card',
        path: 'card',
        meta: { title: '资产卡片' },
        pageName: 'card',
      },
      {
        name: 'maintain',
        path: 'maintain',
        meta: { title: '资产类别维护' },
        pageName: 'maintain',
      },
      {
        name: 'depreciationSchedule',
        path: 'depreciationSchedule',
        meta: { title: '资产折旧明细表' },
        pageName: 'depreciationSchedule',
      },
      {
        name: 'depreciationAccount',
        path: 'depreciationAccount',
        meta: { title: '资产折旧明细账' },
        pageName: 'depreciationAccount',
      },
      {
        name: 'disposalSchedule',
        path: 'disposalSchedule',
        meta: { title: '资产处置明细表' },
        pageName: 'disposalSchedule',
      },
      {
        name: 'assetsDepartment',
        path: 'assetsDepartment',
        meta: { title: '资产部门管理' },
        pageName: 'assetsDepartment',
      },
      {
        name: 'buildingEngineering',
        path: 'buildingEngineering',
        meta: { title: '在建工程' },
        pageName: 'buildingEngineering',
      },
      {
        name: 'changeAssetsRecord',
        path: 'changeAssetsRecord',
        meta: { title: '资产变更记录' },
        pageName: 'changeAssetsRecord',
      },
    ],
  },
  {
    path: '/report',
    name: 'report',
    meta: {
      icon: 'baobiao',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      title: '报表',
    },
    children: [
      {
        path: 'balanceSheet',
        name: 'balanceSheet',
        meta: { title: '资产负债表' },
        pageName: 'balanceSheet',
      },
      {
        path: 'profitStatement',
        name: 'profitStatement',
        meta: { title: '利润表', hideWithNonprofit: true },
        pageName: 'profitStatement',
      },
      {
        path: 'businessStatement',
        name: 'businessStatement',
        meta: { title: '业务活动表', showWithNonprofit: true },
        pageName: 'businessStatement',
      },
      {
        name: 'flowMeter',
        path: 'flowMeter',
        meta: { title: '现金流量表' },
        pageName: 'flowMeter',
      },
      {
        name: 'tradeBook',
        path: 'tradeBook',
        meta: { title: '资金流水一览表' },
        pageName: 'tradeBook',
      },
      {
        name: 'agingScheOfAccRec',
        path: 'agingScheOfAccRec',
        meta: { title: '应收账款账龄分析表' },
        props: { subjectCode: 1122 },
        pageName: 'agingScheOfAccRec',
      },
      {
        name: 'agingScheOfAccPay',
        path: 'agingScheOfAccPay',
        meta: { title: '应付账款账龄分析表' },
        props: { subjectCode: 2202 },
        pageName: 'agingScheOfAccPay',
      },
      {
        name: 'subjectTagSheet',
        path: 'subjectTagSheet',
        meta: { title: '科目标签汇总表' },
        pageName: 'subjectTagSheet',
      },
      {
        name: 'profitAndLossTable',
        path: 'profitAndLossTable',
        meta: { title: '经营盈亏表', hideWithNonprofit: true },
        pageName: 'profitAndLossTable',
      },
      {
        name: 'risk',
        path: 'risk',
        meta: { title: '财务监控' },
        pageName: 'risk',
      },
    ],
  },
  {
    path: '/books',
    name: 'books',
    meta: { icon: 'zhangben', show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'], title: '账本' },
    children: [
      {
        name: 'cashTally',
        path: 'cashTally',
        meta: { title: '库存现金日记账' },
        pageName: 'cashTally',
      },
      {
        name: 'bankDeposit',
        path: 'bankDeposit',
        meta: { title: '银行存款日记账' },
        pageName: 'bankDeposit',
      },
      {
        name: 'subAccount',
        path: 'subAccount',
        meta: { title: '明细账' },
        pageName: 'subAccount',
      },
      {
        path: 'generalLedger',
        name: 'generalLedger',
        meta: { title: '总账' },
        pageName: 'generalLedger',
      },
      {
        name: 'amountGeneralSubsidiary',
        path: 'amountGeneralSubsidiary',
        meta: { title: '数量金额明细账', hideWithNonprofit: true },
        pageName: 'amountGeneralSubsidiary',
      },
      {
        name: 'amountGeneralLedger',
        path: 'amountGeneralLedger',
        meta: { title: '数量金额总账', hideWithNonprofit: true },
        pageName: 'amountGeneralLedger',
      },
      {
        name: 'accountBalanceSheet',
        path: 'accountBalanceSheet',
        meta: { title: '科目余额表' },
        pageName: 'accountBalanceSheet',
      },
      {
        name: 'multiColumnBreakdown',
        path: 'multiColumnBreakdown',
        meta: { title: '多栏明细账' },
        pageName: 'multiColumnBreakdown',
      },
      {
        name: 'addedTaxPayableStatement',
        path: 'addedTaxPayableStatement',
        meta: { title: '应交增值税多栏明细账' },
        pageName: 'addedTaxPayableStatement',
      },
      {
        name: 'accumulatedDepreciation',
        path: 'accumulatedDepreciation',
        meta: { title: '固定资产及累计折旧明细账' },
        pageName: 'accumulatedDepreciation',
      },
      {
        name: 'subjectTagDetailSheet',
        path: 'subjectTagDetailSheet',
        meta: { title: '科目标签明细账' },
        pageName: 'subjectTagDetailSheet',
      },
      {
        name: 'commodityWater',
        path: 'commodityWater',
        meta: { title: '仓帐', hideWithNonprofit: true },
        pageName: 'commodityWater',
      },
      {
        name: 'commodityWaterSummary',
        path: 'commodityWaterSummary',
        meta: { title: '仓帐汇总表', hideWithNonprofit: true },
        pageName: 'commodityWaterSummary',
      },
      {
        name: 'salesSummary',
        path: 'salesSummary',
        meta: { title: '销售汇总表', hideWithNonprofit: true },
        pageName: 'salesSummary',
      },
      {
        name: 'incomeStatistics',
        path: 'incomeStatistics',
        meta: { title: '收入统计表', hideWithNonprofit: true },
        pageName: 'incomeStatistics',
      },
      {
        name: 'adjustExchangeRecordSheet',
        path: 'adjustExchangeRecordSheet',
        meta: { title: '调汇记录表', hideWithNonprofit: true },
        pageName: 'adjustExchangeRecordSheet',
      },
      {
        name: 'purchasingStatistics',
        path: 'purchasingStatistics',
        meta: { title: '采购统计表', hideWithNonprofit: true },
        pageName: 'purchasingStatistics',
      },
    ],
  },
  {
    path: '/tax',
    name: 'tax',
    meta: {
      icon: 'shuiwu1',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      title: '税务',
    },
    children: [
      // {
      //   name: 'aiTaxLink',
      //   redirect: { name: 'declarationChecklist' },
      //   meta: { title: '智能税务' },
      //   path: '/taxWorkspace/aiTaxLink',
      // },
      {
        name: 'declarationChecklistLink',
        redirect: { name: 'declarationChecklist' },
        meta: { title: '申报清册' },
        path: '/taxWorkspace/declarationChecklistLink',
      },
      {
        name: 'taxHomeLink',
        redirect: { name: 'taxHome' },
        meta: { title: '税务信息' },
        path: '/taxWorkspace/taxHomeLink',
      },
      {
        name: 'taxPayLink',
        redirect: { name: 'taxPay' },
        path: '/taxWorkspace/taxPayLink',
        meta: { title: '税款缴纳' },
      },
      {
        name: 'valueAddedTaxLink',
        redirect: { name: 'newValueAddedTax' },
        path: '/taxWorkspace/valueAddedTaxLink',
        meta: { title: '增值税申报' },
      },
      {
        name: 'businessIncomeTaxLink',
        redirect: { name: 'newBusinessIncomeTax' },
        path: '/taxWorkspace/businessIncomeTaxLink',
        meta: { title: '企业所得税月（季）申报' },
      },
      {
        name: 'settlementPaymentLink',
        redirect: { name: 'newSettlementPayment' },
        path: '/taxWorkspace/settlementPaymentLink',
        meta: { title: '企业所得税年报' },
      },

      {
        name: 'financialStatementListPageLink',
        redirect: { name: 'financialStatementListPage' },
        path: '/taxWorkspace/financialStatementListPage',
        meta: { title: '财务会计报表申报' },
      },
      {
        name: 'stampTaxListPageLink',
        redirect: { name: 'stampTaxListPage' },
        path: '/taxWorkspace/stampTaxListPage',
        meta: { title: '印花税' },
      },
      {
        name: 'taxCollection',
        path: 'taxCollection',
        meta: { title: '税金计提表' },
        pageName: 'taxCollection',
      },
      {
        name: 'personTaxLink',
        redirect: { name: 'personTax' },
        path: '/taxWorkspace/personTaxLink',
        meta: { title: '个人所得税' },
      },
      {
        name: 'behaviorTips',
        path: 'behaviorTips',
        meta: { title: '风险提醒' },
        pageName: 'behaviorTips',
      },

    ],
  },
  {
    path: '/bailian',
    name: 'bailian',
    meta: {
      icon: 'bailian',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      title: '白莲',
    },
    children: [
      {
        name: 'bailianincome',
        path: 'income',
        meta: { paymentType: 2, title: '手工单据（收入）（小规模专用）' },
        pageName: 'bailianincome',
      },
      {
        name: 'bailianpurchase',
        path: 'purchase',
        meta: { paymentType: 1, title: '手工单据（采购）（小规模专用）' },
        pageName: 'bailianpurchase',
      },
      {
        name: 'bailianbank',
        path: 'bank',
        meta: { paymentType: 1, title: '银行' },
        pageName: 'bailianbank',
      },
      {
        name: 'bailiancash',
        path: 'cash',
        meta: { paymentType: 2, title: '现金' },
        pageName: 'bailiancash',
      },
    ],
  },

  {
    path: '/Regulations',
    name: 'Regulations',
    meta: {
      icon: 'fagui',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      title: '法规',
    },
    children: [
      {
        name: 'Regulations',
        path: '/regulations/regulations',
        meta: { title: '法规' },
        pageName: 'Regulations',
      },
    ],
  },
  {
    path: '/options',
    name: 'options',
    meta: {
      icon: 'shezhi1',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      title: '设置',
    },
    children: [
      {
        name: 'customerManagement',
        path: 'customerManagement',
        meta: { title: '客户管理' },
        pageName: 'customerManagement',
      },
      {
        name: 'supplierInfo',
        path: 'supplierInfo',
        meta: { title: '供应商管理' },
        pageName: 'supplierInfo',
      },
      {
        name: 'bankAccount',
        path: 'bankAccount',
        meta: { title: '银行账户' },
        pageName: 'bankAccount',
      },
      {
        name: 'subCurrency',
        path: 'subCurrency',
        meta: { title: '币别' },
        pageName: 'subCurrency',
      },
      {
        name: 'companyInformation',
        path: 'companyInformation',
        meta: { title: '公司信息' },
        pageName: 'companyInformation',
      },
      {
        name: 'subjectInfo',
        path: 'subjectInfo',
        meta: { title: '科目管理' },
        pageName: 'subjectInfo',
      },
      {
        path: 'subjectConversion',
        name: 'subjectConversion',
        meta: { title: '科目转换' },
        pageName: 'subjectConversion',
      },
      {
        name: 'openingBalance',
        path: 'openingBalance',
        pageName: 'openingBalance',
        meta: { title: '期初余额' },
      },
      {
        name: 'unitOpt',
        path: 'unitOpt',
        meta: { title: '单位设置' },
        pageName: 'unitOpt',
      },
      {
        name: 'commoditySetting',
        path: 'commoditySetting',
        meta: { title: '商品/服务管理' },
        pageName: 'commoditySetting',
      },
      {
        name: 'costType',
        path: 'type',
        meta: { title: '费用类型管理' },
        pageName: 'costType',
      },
      {
        name: 'abstract',
        path: 'abstract',
        meta: { title: '摘要设置' },
        pageName: 'abstract',
      },
      {
        name: 'businessSetting',
        path: 'businessSetting',
        meta: { title: '业务设置' },
        pageName: 'businessSetting',
      },
      {
        name: 'personnelManagement',
        path: 'personnelManagement',
        meta: { title: '人员管理' },
        pageName: 'personnelManagement',
      },
      {
        name: 'subjectTag',
        path: 'subjectTag',
        meta: { title: '标签管理' },
        pageName: 'subjectTag',
      },
      {
        name: 'subjectTagOpeningBalance',
        path: 'subjectTagOpeningBalance',
        meta: { title: '科目标签期初余额' },
        pageName: 'subjectTagOpeningBalance',
      },
      {
        name: 'ledgerCover',
        path: 'ledgerCover',
        meta: { title: '封面打印' },
        pageName: 'ledgerCover',
      },
    ],
  },
  {
    path: '/taxWorkspace',
    children: [
      {
        name: 'enterpriseIncomeTax',
        path: 'enterpriseIncomeTax',
        hidden: true,
        pageName: 'enterpriseIncomeTax',
      },
      {
        name: 'VATDeclaration',
        path: 'VATDeclaration',
        hidden: true,
        pageName: 'VATDeclaration',
      },
      {
        name: 'BITDeclaration',
        path: 'BITDeclaration',
        hidden: true,
        pageName: 'BITDeclaration',
      },
      {
        name: 'financialStatementDeclare',
        path: 'financialStatementDeclare',
        hidden: true,
        pageName: 'financialStatementDeclare',
      },
      {
        name: 'stampTaxDeclare',
        path: 'stampTaxDeclare',
        hidden: true,
        pageName: 'stampTaxDeclare',
      },
    ],
    hidden: true,
  },
  // {
  //   path: '/taxWorkspace/aiTax',
  //   meta: {
  //     title: '智能税务',
  //     show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
  //     type: 'taxWorkspace',
  //     icon: 'wodeshouye',
  //   },
  //   children: [
  //     {
  //       name: 'aiTax',
  //       path: '/taxWorkspace/aiTax',
  //       meta: { title: '智能税务' },
  //       pageName: 'aiTax',
  //     },
  //   ],
  // },
  {
    path: '/taxWorkspace/declarationChecklist',
    meta: {
      title: '申报清册',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'taxWorkspace',
      icon: 'qingce',
    },
    children: [
      {
        name: 'declarationChecklist',
        path: '/taxWorkspace/declarationChecklist',
        meta: { title: '申报清册' },
        pageName: 'declarationChecklist',
      },
    ],
  },
  {
    path: '/taxWorkspace/taxHome',
    meta: {
      title: '税务信息',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'taxWorkspace',
      icon: 'shuiwu1',
    },
    children: [
      {
        name: 'taxHome',
        path: '/taxWorkspace/taxHome',
        meta: { title: '税务信息' },
        pageName: 'taxHome',
      },
    ],
  },
  {
    path: '/taxWorkspace/taxPay',
    meta: {
      title: '税款缴纳',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'taxWorkspace',
      icon: 'jiaokuan',
    },
    children: [
      {
        name: 'taxPay',
        path: '/taxWorkspace/taxPay',
        meta: { title: '税款缴纳' },
        pageName: 'taxPay',
      },
    ],
  },
  {
    path: '/taxWorkspace/newValueAddedTax',
    meta: {
      title: '增值税申报',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'taxWorkspace',
      icon: 'zengzhishui',
    },
    children: [
      {
        name: 'newValueAddedTax',
        path: '/taxWorkspace/newValueAddedTax',
        meta: { title: '增值税申报' },
        pageName: 'newValueAddedTax',
      },
    ],
  },
  {
    path: '/taxWorkspace/newBusinessIncomeTax',
    meta: {
      title: '企业所得税月（季）申报',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'taxWorkspace',
      icon: 'yuedushenbao',
    },
    children: [
      {
        name: 'newBusinessIncomeTax',
        path: '/taxWorkspace/newBusinessIncomeTax',
        meta: { title: '企业所得税月（季）申报' },
        pageName: 'newBusinessIncomeTax',
      },
    ],
  },
  {
    path: '/taxWorkspace/newSettlementPayment',
    meta: {
      title: '企业所得税年报',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'taxWorkspace',
      icon: 'niandushenbao',
    },
    children: [
      {
        name: 'newSettlementPayment',
        path: '/taxWorkspace/newSettlementPayment',
        meta: { title: '企业所得税年报' },
        pageName: 'newSettlementPayment',
      },
    ],
  },
  {
    path: '/taxWorkspace/financialStatementListPage',
    meta: {
      title: '财务会计报表申报',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'taxWorkspace',
      icon: 'XDZshenbao-lingshenbao',
    },
    children: [
      {
        name: 'financialStatementListPage',
        path: '/taxWorkspace/financialStatementListPage',
        meta: { title: '财务会计报表申报' },
        pageName: 'financialStatementListPage',
      },
    ],
  },
  {
    path: '/taxWorkspace/personTax',
    meta: {
      title: '个人所得税',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'taxWorkspace',
      icon: 'geshuishezhi',
    },
    children: [
      {
        name: 'personTax',
        path: '/taxWorkspace/personTax',
        meta: { title: '个人所得税' },
        pageName: 'personTax',
      },
      {
        name: 'comprehensiveIncome',
        path: '/taxWorkspace/personTax/comprehensiveIncome',
        meta: { title: '综合所得申报' },
        hidden: true,
        pageName: 'comprehensiveIncome',
      },
      {
        name: 'specialDeduction',
        path: '/taxWorkspace/personTax/specialDeduction',
        meta: { title: '专项附加扣除信息' },
        hidden: true,
        pageName: 'specialDeduction',
      },
      {
        name: 'individualPayment',
        path: '/taxWorkspace/personTax/individualPayment',
        meta: { title: '三方协议管理' },
        hidden: true,
        pageName: 'individualPayment',
      },
      {
        name: 'individualIncomeTax',
        path: '/taxWorkspace/personTax/individualIncomeTax',
        meta: { title: '经营所得申报' },
        hidden: true,
        pageName: 'individualIncomeTax',
      },
      {
        name: 'individualIncomeTaxPayment',
        path: '/taxWorkspace/personTax/individualIncomeTaxPayment',
        meta: { title: '经营所得缴款' },
        hidden: true,
        pageName: 'individualIncomeTaxPayment',
      },
      {
        name: 'unitDeclare',
        path: '/taxWorkspace/personTax/unitDeclare',
        meta: { title: '单位申报记录' },
        hidden: true,
        pageName: 'unitDeclare',
      },
      {
        name: 'taxStatus',
        path: '/taxWorkspace/personTax/taxStatus',
        meta: { title: '税款缴纳' },
        hidden: true,
        pageName: 'taxStatus',
      },
      {
        name: 'paymentRecord',
        path: '/taxWorkspace/personTax/paymentRecord',
        meta: { title: '扣纳记录' },
        hidden: true,
        pageName: 'paymentRecord',
      },
      {
        name: 'personInfoCollection',
        path: '/taxWorkspace/personTax/personInfoCollection',
        meta: { title: '个人信息采集' },
        hidden: true,
        pageName: 'personInfoCollection',
      },
    ],
  },
  {
    path: '/taxWorkspace/stampTaxListPage',
    meta: {
      title: '印花税',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'taxWorkspace',
      icon: 'yinshuashui',
    },
    children: [
      {
        name: 'stampTaxListPage',
        path: '/taxWorkspace/stampTaxListPage',
        meta: { title: '印花税' },
        pageName: 'stampTaxListPage',
      },
    ],
  },
  {
    path: '/maintenance',
    meta: {
      icon: 'yunwei',
      show: ['ROLE_OM_ADMIN', 'ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      title: '运维',
    },
    alwaysShow: true,
    children: [
      {
        name: 'ledgerDataMigration',
        path: 'ledgerDataMigration',
        meta: { title: '旧账迁移（系统版）', businessType: 1 },
        pageName: 'ledgerDataMigration',
      },
      // {
      //   name: 'initialiseModel',
      //   path: 'initialiseModel',
      //   meta: { title: '旧账迁移（模板版）', businessType: 2 },
      //   pageName: 'initialiseModel',
      // },

      {
        name: 'oldAccountsMigration',
        path: 'oldAccountsMigration',
        meta: { title: '旧账迁移（新版）' },
        pageName: 'oldAccountsMigration',
      },
    ],
  },
  {
    path: '/controlCenter/booksSetList',
    meta: {
      title: '账套',
      icon: 'zhangtao',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'booksSetList',
        path: '/controlCenter/booksSetList',
        meta: { title: '账套' },
        pageName: 'booksSetList',
      },
    ],
  },
  {
    path: '/controlCenter/companyProgress',
    meta: {
      title: '进度',
      icon: 'zhangtao',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'companyProgress',
        path: '/controlCenter/companyProgress',
        meta: { title: '进度' },
        pageName: 'companyProgress',
      },
    ],
  },
  {
    path: '/controlCenter/batch',
    meta: {
      title: '批量',
      icon: 'shuiwu1',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'invoiceCollectionList',
        path: 'invoiceCollectionList',
        meta: { title: '发票采集' },
        pageName: 'invoiceCollectionList',
      },
      {
        name: 'taxDeclarationList',
        path: 'taxDeclarationList',
        meta: { title: '企业税申报' },
        pageName: 'taxDeclarationList',
      },
      {
        name: 'personalIncomeTax',
        path: 'personalIncomeTax',
        meta: { title: '代扣代缴申报' },
        pageName: 'personalIncomeTax',
      },
      {
        name: 'bussinessIncomeTax',
        path: 'bussinessIncomeTax',
        meta: { title: '经营所得申报' },
        pageName: 'bussinessIncomeTax',
      },
      {
        name: 'declarationLedger',
        path: 'declarationLedger',
        meta: { title: '申报台账' },
        pageName: 'declarationLedger',
      },
      {
        name: 'missDeclaredCheck',
        path: 'missDeclaredCheck',
        meta: { title: '漏报检查' },
        pageName: 'missDeclaredCheck',
      },
    ],
  },
  {
    path: '/controlCenter/riskStatistics',
    meta: {
      title: '风险',
      icon: 'fengxian',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'riskStatistics',
        path: '/controlCenter/riskStatistics',
        meta: { title: '风险' },
        pageName: 'riskStatistics',
      },
    ],
  },
  {
    name: 'staffManagementModule',
    path: '/controlCenter/staffManagement',
    meta: {
      title: '员工',
      icon: 'yuangong',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'staffManagement',
        path: '/controlCenter/staffManagement',
        meta: { title: '员工' },
        pageName: 'staffManagement',
      },
    ],
  },

  {
    name: 'wechatManage',
    path: '/controlCenter/WechatManage',
    meta: {
      title: '运营',
      icon: 'yunying1',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'wechatManage',
        path: '/controlCenter/wechatManage',
        meta: { title: '运营' },
        pageName: 'wechatManage',
      },
    ],
  },
  {
    name: 'statisticsModule',
    path: '/controlCenter/statistics',
    meta: {
      title: '统计',
      icon: 'tongji',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'statistics',
        path: '/controlCenter/statistics',
        meta: { title: '自定义报表' },
        pageName: 'statistics',
      },
      {
        name: 'accountingLedger',
        path: '/controlCenter/accountingLedger',
        meta: { title: '记账台账' },
        pageName: 'accountingLedger',
      },
      {
        name: 'statisticsReport',
        path: '/controlCenter/statistics/:reportId',
        hidden: true,
        props: true,
        pageName: 'statisticsReport',
      },
      // {
      //   name: 'bookSetStatistics',
      //   path: '/controlCenter/bookSetStatistics',
      //   meta: { title: '账套统计' },
      //   pageName: 'bookSetStatistics',
      // },
    ],
  },
  {
    name: 'personalInformationModule',
    path: '/controlCenter/personalInformation',
    meta: {
      title: '个人',
      icon: 'geren',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'controlCenter',
    },
    children: [{
      name: 'personalInformation', pageName: 'personalInformation', path: '/controlCenter/personalInformation', meta: { title: '个人' },
    }],
  },
  {
    name: 'operationLogModule',
    path: '/controlCenter/operationLog',
    meta: {
      title: '日志',
      icon: 'rizhi',
      show: ['ROLE_CHARGE', 'ROLE_OM_ADMIN'],
      type: 'controlCenter',
    },
    children: [{
      name: 'operationLog', pageName: 'operationLog', path: '/controlCenter/operationLog', meta: { title: '日志' },
    }],
  },
//   {
//     name: 'firstInitializationModule',
//     path: '/controlCenter/firstInitialization',
//     meta: {
//       title: '导入',
//       icon: 'daoru',
//       show: ['ROLE_OM_ADMIN'],
//       type: 'controlCenter',
//     },
//     children: [{
//  name: 'firstInitialization', pageName: 'firstInitialization', path: '/controlCenter/firstInitialization', meta: { title: '导入' },
// }],
//   },
  {
    path: '/controlCenter/messageCenter',
    meta: {
      title: '消息',
      icon: 'xiaoxi2',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'messageCenter',
        path: '/controlCenter/messageCenter',
        meta: { title: '消息' },
        pageName: 'messageCenter',
      },
    ],
  },

  {
    path: '/controlCenter/service',
    meta: {
      title: '服务',
      icon: 'fuwu',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT', 'ROLE_CUSTOMER_WECHAT'],
      type: 'controlCenter',
    },
    children: [
      
      {
        name: 'invoiceVerification',
        path: 'invoiceVerification',
        meta: { title: '发票查验账单' },
        pageName: 'invoiceVerification',
      },
      {
        name: 'basicOrder',
        path: 'basicOrder',
        meta: { title: '基础功能订单' },
        pageName: 'basicOrder',
      },
      {
        name: 'featureOrder',
        path: 'featureOrder',
        meta: { title: '发票查验订单' },
        pageName: 'featureOrder',
      },
    ],
  },
  {
    path: '/controlCenter/roleCenter',
    meta: {
      title: '角色',
      icon: 'jiaose',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      showWithVipCode: ['Paid-role-001'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'roleCenter',
        path: '/controlCenter/roleCenter',
        meta: { title: '角色' },
        pageName: 'roleCenter',
      },
    ],
  },

  {
    path: '/controlCenter/backups',
    meta: {
      title: '备份',
      icon: 'beifen',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'backups',
        path: '/controlCenter/backups',
        meta: { title: '备份' },
        pageName: 'backups',
      },
    ],
  },
  {
    path: '/controlCenter/recycleBin',
    meta: {
      title: '回收站',
      icon: 'navicon-ywpz',
      show: ['ROLE_CHARGE'],
      type: 'controlCenter',
    },
    hidden: true,
    children: [
      {
        name: 'recycleBin',
        path: '/controlCenter/recycleBin',
        meta: { title: '回收站' },
        pageName: 'recycleBin',
      },
    ],
  },

  {
    name: 'openAccountingBook',
    pageName: 'openAccountingBook',
    path: '/controlCenter/openAccountingBook/:companyId',
    meta: {
      title: '打开账套',
      type: 'controlCenter',
    },
    hidden: true,
  },
];
// 用来生成菜单树的方法, 整理文档用，放着不删
// const getTree = (menu, level = 0, result = []) => {
//   menu.forEach((item) => {
//     if (!item.hidden) {
//       result.push({
//         一级菜单: level === 0 ? item.meta.title : '',
//         二级菜单: level === 1 ? item.meta.title : '',
//         三级菜单: level === 2 ? item.meta.title : '',
//       });
//       if (item.children && item.children.length > 1) {
//         getTree(item.children, level + 1, result);
//       }
//     }
//   });
//   return result;
// };
// console.log(JSON.stringify(getTree(menus)));
export default menus;
