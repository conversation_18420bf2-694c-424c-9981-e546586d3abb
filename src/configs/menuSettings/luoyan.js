const menus = [
  {
    path: '/manager',
    children: [
      {
        name: 'guidanceSmall',
        path: 'guidanceSmall',
        meta: { title: '首页', show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'] },
        pageName: 'guidanceSmall',
      },
    ],
    hidden: true,
  },
  {
    path: '/manager/guidance',
    meta: {
      title: '首页',
      icon: 'shouye',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
    },
    children: [
      {
        name: 'guidance',
        path: '/manager/guidance',
        meta: { title: '首页', affix: true },
        pageName: 'guidance',
      },
    ],
  },
  {
    path: '/document',
    name: 'document',
    meta: {
      icon: 'yewu',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      title: '业务',
    },
    children: [
      {
        name: 'outputTax',
        path: 'outputTax',
        meta: { title: '销售发票' },
        pageName: 'outputTax',
      },
      {
        name: 'inputTax',
        path: 'inputTax',
        meta: { title: '购进发票' },
        pageName: 'inputTax',
      },
      {
        name: 'salary',
        path: 'salary',
        meta: { title: '工资' },
        pageName: 'salary',
      },
      {
        name: 'trade',
        path: 'trade',
        meta: { title: '资金' },
        pageName: 'trade',
      },
      {
        name: 'documentManagement',
        path: 'documentManagement',
        meta: { title: '库存单据' },
        pageName: 'documentManagement',
      },
      {
        path: 'othersIn',
        name: 'othersIn',
        meta: { title: '手工单据（收入）' },
        pageName: 'othersIn',
      },
      {
        path: 'othersOut',
        name: 'othersOut',
        meta: { title: '手工单据（支出）' },
        pageName: 'othersOut',
      },
    ],
  },
  {
    path: '/finance',
    name: 'financeModule',
    meta: { icon: 'caiwu1', show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'], title: '财务' },
    children: [
      {
        name: 'enterpriseJournal',
        path: 'enterpriseJournal',
        meta: { title: '单据匹配及审核' },
        pageName: 'enterpriseJournal',
      },
      {
        name: 'voucherDetails',
        path: 'voucherDetails',
        meta: { title: '凭证明细表' },
        pageName: 'voucherDetails',
      },
      {
        name: 'simpleCostAccount',
        path: 'simpleCostAccount',
        meta: { title: '成本核算' },
        pageName: 'simpleCostAccount',
      },
      {
        name: 'endCheckout',
        path: 'endCheckout',
        meta: { title: '期末结转' },
        pageName: 'endCheckout',
      },
      {
        path: '/books',
        name: 'booksModule',
        noRouter: true,
        meta: {
          show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
          title: '账本',
        },
        children: [
          {
            name: 'cashTally',
            path: 'cashTally',
            meta: { title: '库存现金日记账' },
            pageName: 'cashTally',
          },
          {
            name: 'bankDeposit',
            path: 'bankDeposit',
            meta: { title: '银行存款日记账' },
            pageName: 'bankDeposit',
          },
          {
            name: 'subAccount',
            path: 'subAccount',
            meta: { title: '明细账' },
            pageName: 'subAccount',
          },
          {
            path: 'generalLedger',
            name: 'generalLedger',
            meta: { title: '总账' },
            pageName: 'generalLedger',
          },
          {
            name: 'amountGeneralSubsidiary',
            path: 'amountGeneralSubsidiary',
            meta: { title: '数量金额明细账' },
            pageName: 'amountGeneralSubsidiary',
          },
          {
            name: 'amountGeneralLedger',
            path: 'amountGeneralLedger',
            meta: { title: '数量金额总账' },
            pageName: 'amountGeneralLedger',
          },
          {
            name: 'accountBalanceSheet',
            path: 'accountBalanceSheet',
            meta: { title: '科目余额表' },
            pageName: 'accountBalanceSheet',
          },
          {
            name: 'multiColumnBreakdown',
            path: 'multiColumnBreakdown',
            meta: { title: '多栏明细账' },
            pageName: 'multiColumnBreakdown',
          },
          {
            name: 'addedTaxPayableStatement',
            path: 'addedTaxPayableStatement',
            meta: { title: '应交增值税多栏明细账' },
            pageName: 'addedTaxPayableStatement',
          },
          {
            name: 'accumulatedDepreciation',
            path: 'accumulatedDepreciation',
            meta: { title: '固定资产及累计折旧明细账' },
            pageName: 'accumulatedDepreciation',
          },
          {
            name: 'subjectTagDetailSheet',
            path: 'subjectTagDetailSheet',
            meta: { title: '科目标签明细账' },
            pageName: 'subjectTagDetailSheet',
          },
          {
            name: 'commodityWater',
            path: 'commodityWater',
            meta: { title: '仓帐' },
            pageName: 'commodityWater',
          },
        ],
      },
      {
        path: '/assets',
        name: 'assetsModule',
        noRouter: true,
        meta: { show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'], title: '资产' },
        children: [
          {
            name: 'card',
            path: 'card',
            meta: { title: '资产卡片' },
            pageName: 'card',
          },
          {
            name: 'maintain',
            path: 'maintain',
            meta: { title: '资产类别维护' },
            pageName: 'maintain',
          },
          {
            name: 'depreciationSchedule',
            path: 'depreciationSchedule',
            meta: { title: '资产折旧明细表' },
            pageName: 'depreciationSchedule',
          },
          {
            name: 'depreciationAccount',
            path: 'depreciationAccount',
            meta: { title: '资产折旧明细账' },
            pageName: 'depreciationAccount',
          },
          {
            name: 'disposalSchedule',
            path: 'disposalSchedule',
            meta: { title: '资产处置明细表' },
            pageName: 'disposalSchedule',
          },
          {
            name: 'assetsDepartment',
            path: 'assetsDepartment',
            meta: { title: '资产部门管理' },
            pageName: 'assetsDepartment',
          },
          {
            name: 'buildingEngineering',
            path: 'buildingEngineering',
            meta: { title: '在建工程' },
            pageName: 'buildingEngineering',
          },
          {
            name: 'changeAssetsRecord',
            path: 'changeAssetsRecord',
            meta: { title: '资产变更记录' },
            pageName: 'changeAssetsRecord',
          },
        ],
      },
    ],
  },
  {
    path: '/customerInteractions',
    name: 'customerInteractions',
    meta: {
      icon: 'shezhi1',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      title: '交互',
    },
    children: [
      {
        name: 'taxForecast',
        path: 'taxForecast',
        meta: { title: '预估税款' },
        pageName: 'taxForecast',
      },
      {
        name: 'businessFileManage',
        path: 'businessFileManage',
        meta: { title: '上传做账文件' },
        pageName: 'businessFileManage',
      },
      {
        name: 'initFileManage',
        path: 'initFileManage',
        meta: { title: '上传初始化文件' },
        pageName: 'initFileManage',
      },
      {
        name: 'deliveryConfirm',
        path: 'deliveryConfirm',
        meta: { title: '交付确认' },
        pageName: 'deliveryConfirm',
      },
    ]
  },
  {
    path: '/report',
    name: 'reportModule',
    meta: {
      icon: 'baobiao',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      title: '报表',
    },
    children: [
      {
        path: 'managerReportModule',
        name: 'managerReportModule',
        noRouter: true,
        meta: {
          show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
          title: '管理报表',
        },
        children: [
          {
            name: 'tradeBook',
            path: 'tradeBook',
            meta: { title: '资金流水一览表' },
            pageName: 'tradeBook',
          },
          {
            name: 'agingScheOfAccRec',
            path: 'agingScheOfAccRec',
            meta: { title: '应收账款账龄分析表' },
            props: { subjectCode: 1122 },
            pageName: 'agingScheOfAccRec',
          },
          {
            name: 'agingScheOfAccPay',
            path: 'agingScheOfAccPay',
            meta: { title: '应付账款账龄分析表' },
            props: { subjectCode: 2202 },
            pageName: 'agingScheOfAccPay',
          },
          {
            name: 'subjectTagSheet',
            path: 'subjectTagSheet',
            meta: { title: '科目标签汇总表' },
            pageName: 'subjectTagSheet',
          },
          {
            name: 'commodityWaterSummary',
            path: 'commodityWaterSummary',
            meta: { title: '仓帐汇总表' },
            pageName: 'commodityWaterSummary',
          },
          {
            name: 'salesSummary',
            path: 'salesSummary',
            meta: { title: '销售汇总表' },
            pageName: 'salesSummary',
          },
          {
            name: 'incomeStatistics',
            path: 'incomeStatistics',
            meta: { title: '收入统计表' },
            pageName: 'incomeStatistics',
          },
          {
            name: 'adjustExchangeRecordSheet',
            path: 'adjustExchangeRecordSheet',
            meta: { title: '调汇记录表' },
            pageName: 'adjustExchangeRecordSheet',
          },
          {
            name: 'purchasingStatistics',
            path: 'purchasingStatistics',
            meta: { title: '采购统计表' },
            pageName: 'purchasingStatistics',
          },
          {
            name: 'risk',
            path: 'risk',
            meta: { title: '财务监控' },
            pageName: 'risk',
          },
          {
            name: 'riskIndicators',
            path: 'riskIndicators',
            meta: { title: '经营指标' },
            pageName: 'riskIndicators',
          },
          {
            name: 'profitAndLossTable',
            path: 'profitAndLossTable',
            meta: { title: '经营盈亏表' },
            pageName: 'profitAndLossTable',
          },
        ],
      },
      {
        path: 'financeReportModule',
        name: 'financeReportModule',
        noRouter: true,
        meta: {
          show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
          title: '财务报表',
        },
        children: [
          {
            path: 'balanceSheet',
            name: 'balanceSheet',
            meta: { title: '资产负债表' },
            pageName: 'balanceSheet',
          },
          {
            path: 'profitStatement',
            name: 'profitStatement',
            meta: { title: '利润表' },
            pageName: 'profitStatement',
          },
          {
            name: 'flowMeter',
            path: 'flowMeter',
            meta: { title: '现金流量表' },
            pageName: 'flowMeter',
          },
        ],
      },
      {
        name: 'behaviorTips',
        path: 'behaviorTips',
        meta: { title: '风险提醒' },
        pageName: 'behaviorTips',
      },
    ],
  },
  {
    path: '/tax',
    name: 'taxModule',
    meta: {
      icon: 'shuiwu1',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      title: '税务',
    },
    children: [
      {
        name: 'declarationChecklistLink',
        redirect: { name: 'declarationChecklist' },
        meta: { title: '申报清册' },
        path: '/taxWorkspace/declarationChecklistLink',
      },
      {
        name: 'taxHomeLink',
        redirect: { name: 'taxHome' },
        meta: { title: '税务信息' },
        path: '/taxWorkspace/taxHomeLink',
      },
      {
        name: 'taxPayLink',
        redirect: { name: 'taxPay' },
        path: '/taxWorkspace/taxPayLink',
        meta: { title: '税款缴纳' },
      },
      {
        name: 'valueAddedTaxLink',
        redirect: { name: 'newValueAddedTax' },
        path: '/taxWorkspace/valueAddedTaxLink',
        meta: { title: '增值税申报' },
      },
      {
        name: 'businessIncomeTaxLink',
        redirect: { name: 'newBusinessIncomeTax' },
        path: '/taxWorkspace/businessIncomeTaxLink',
        meta: { title: '企业所得税月（季）申报' },
      },
      {
        name: 'settlementPaymentLink',
        redirect: { name: 'newSettlementPayment' },
        path: '/taxWorkspace/settlementPaymentLink',
        meta: { title: '企业所得税年报' },
      },
      {
        name: 'taxCollection',
        path: 'taxCollection',
        meta: { title: '税金计提表' },
        pageName: 'taxCollection',
      },
      // {
      //   name: 'personTaxLink',
      //   path: '/taxWorkspace/personTax',
      //   meta: { title: '个人所得税' },
      //   pageName: 'personTax',
      // },

    ],
  },
  {
    path: '/options',
    name: 'options',
    meta: {
      icon: 'shezhi1',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      title: '设置',
    },
    children: [
      {
        name: 'companyInformation',
        path: 'companyInformation',
        meta: { title: '公司信息' },
        pageName: 'companyInformation',
      },
      {
        name: 'customerManagement',
        path: 'customerManagement',
        meta: { title: '客户管理' },
        pageName: 'customerManagement',
      },
      {
        name: 'supplierInfo',
        path: 'supplierInfo',
        meta: { title: '供应商管理' },
        pageName: 'supplierInfo',
      },
      {
        name: 'commoditySetting',
        path: 'commoditySetting',
        meta: { title: '商品/服务管理' },
        pageName: 'commoditySetting',
      },
      {
        name: 'costType',
        path: 'costType',
        meta: { title: '费用类型管理' },
        pageName: 'costType',
      },
      {
        name: 'bankAccount',
        path: 'bankAccount',
        meta: { title: '银行账户' },
        pageName: 'bankAccount',
      },
      {
        name: 'subCurrency',
        path: 'subCurrency',
        meta: { title: '币别' },
        pageName: 'subCurrency',
      },
      {
        name: 'unitOpt',
        path: 'unitOpt',
        meta: { title: '单位设置' },
        pageName: 'unitOpt',
      },
      {
        name: 'subjectInfo',
        path: 'subjectInfo',
        meta: { title: '科目管理' },
        pageName: 'subjectInfo',
      },
      {
        name: 'openingBalance',
        path: 'openingBalance',
        meta: { title: '科目期初' },
        pageName: 'openingBalance',
      },
      {
        name: 'subjectTag',
        path: 'subjectTag',
        meta: { title: '标签管理' },
        pageName: 'subjectTag',
      },
      {
        name: 'subjectTagOpeningBalance',
        path: 'subjectTagOpeningBalance',
        meta: { title: '科目标签期初余额' },
        pageName: 'subjectTagOpeningBalance',
      },
      {
        name: 'abstract',
        path: 'abstract',
        meta: { title: '摘要设置' },
        pageName: 'abstract',
      },
      {
        name: 'ledgerCover',
        path: 'ledgerCover',
        meta: { title: '账本封面打印' },
        pageName: 'ledgerCover',
      },
      {
        name: 'personnelManagement',
        path: 'personnelManagement',
        meta: { title: '账本打印人员管理' },
        pageName: 'personnelManagement',
      },
      {
        path: 'subjectConversion',
        name: 'subjectConversion',
        meta: { title: '科目转换' },
        pageName: 'subjectConversion',
      },
    ],
  },
  {
    path: '/taxWorkspace',
    children: [
      {
        name: 'enterpriseIncomeTax',
        path: 'enterpriseIncomeTax',
        hidden: true,
        pageName: 'enterpriseIncomeTax',
      },
      {
        name: 'VATDeclaration',
        path: 'VATDeclaration',
        hidden: true,
        pageName: 'VATDeclaration',
      },
      {
        name: 'BITDeclaration',
        path: 'BITDeclaration',
        hidden: true,
        pageName: 'BITDeclaration',
      },
    ],
    hidden: true,
  },
  {
    path: '/taxWorkspace/declarationChecklist',
    meta: {
      title: '申报清册',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      type: 'taxWorkspace',
      icon: 'qingce',
    },
    children: [
      {
        name: 'declarationChecklist',
        path: '/taxWorkspace/declarationChecklist',
        meta: { title: '申报清册' },
        pageName: 'declarationChecklist',
      },
    ],
  },
  {
    path: '/taxWorkspace/taxHome',
    meta: {
      title: '税务信息',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      type: 'taxWorkspace',
      icon: 'shuiwu1',
    },
    children: [
      {
        name: 'taxHome',
        path: '/taxWorkspace/taxHome',
        meta: { title: '税务信息' },
        pageName: 'taxHome',
      },
    ],
  },
  {
    path: '/taxWorkspace/taxPay',
    meta: {
      title: '税款缴纳',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      type: 'taxWorkspace',
      icon: 'jiaokuan',
    },
    children: [
      {
        name: 'taxPay',
        path: '/taxWorkspace/taxPay',
        meta: { title: '税款缴纳' },
        pageName: 'taxPay',
      },
    ],
  },
  {
    path: '/taxWorkspace/newValueAddedTax',
    meta: {
      title: '增值税申报',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      type: 'taxWorkspace',
      icon: 'zengzhishui',
    },
    children: [
      {
        name: 'newValueAddedTax',
        path: '/taxWorkspace/newValueAddedTax',
        meta: { title: '增值税申报' },
        pageName: 'newValueAddedTax',
      },
    ],
  },
  {
    path: '/taxWorkspace/newBusinessIncomeTax',
    meta: {
      title: '企业所得税月（季）申报',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      type: 'taxWorkspace',
      icon: 'yuedushenbao',
    },
    children: [
      {
        name: 'newBusinessIncomeTax',
        path: '/taxWorkspace/newBusinessIncomeTax',
        meta: { title: '企业所得税月（季）申报' },
        pageName: 'newBusinessIncomeTax',
      },
    ],
  },
  {
    path: '/taxWorkspace/newSettlementPayment',
    meta: {
      title: '企业所得税年报',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      type: 'taxWorkspace',
      icon: 'niandushenbao',
    },
    children: [
      {
        name: 'newSettlementPayment',
        path: '/taxWorkspace/newSettlementPayment',
        meta: { title: '企业所得税年报' },
        pageName: 'newSettlementPayment',
      },
    ],
  },
  // {
  //   path: '/taxWorkspace/personTax',
  //   name: 'taxWorkspacePersonTax',
  //   meta: {
  //     title: '个人所得税',
  //     show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
  //     type: 'taxWorkspace',
  //     icon: 'geshuishezhi',
  //   },
  //   children: [
  //     {
  //       name: 'personTax',
  //       path: '/taxWorkspace/personTax',
  //       meta: { title: '个人所得税' },
  //       pageName: 'personTax',
  //     },
  //     {
  //       name: 'comprehensiveIncome',
  //       path: '/taxWorkspace/personTax/comprehensiveIncome',
  //       meta: { title: '综合所得申报' },
  //       hidden: true,
  //       pageName: 'comprehensiveIncome',
  //     },
  //     {
  //       name: 'unitDeclare',
  //       path: '/taxWorkspace/personTax/unitDeclare',
  //       meta: { title: '单位申报记录' },
  //       hidden: true,
  //       pageName: 'unitDeclare',
  //     },
  //     {
  //       name: 'taxStatus',
  //       path: '/taxWorkspace/personTax/taxStatus',
  //       meta: { title: '税款缴纳' },
  //       hidden: true,
  //       pageName: 'taxStatus',
  //     },
  //     {
  //       name: 'paymentRecord',
  //       path: '/taxWorkspace/personTax/paymentRecord',
  //       meta: { title: '扣纳记录' },
  //       hidden: true,
  //       pageName: 'paymentRecord',
  //     },
  //     {
  //       name: 'personInfoCollection',
  //       path: '/taxWorkspace/personTax/personInfo',
  //       meta: { title: '个人信息采集' },
  //       hidden: true,
  //       pageName: 'personInfoCollection',
  //     },
  //   ],
  // },
  {
    path: '/maintenance',
    name: 'maintenance',
    meta: {
      icon: 'yunwei',
      show: ['ROLE_OM_ADMIN', 'ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      title: '运维',
    },
    alwaysShow: true,
    children: [
      {
        name: 'ledgerDataMigration',
        path: 'ledgerDataMigration',
        meta: { title: '旧账迁移（系统版）', businessType: 1 },
        pageName: 'ledgerDataMigration',
      },
      // {
      //   name: 'initialiseModel',
      //   path: 'initialiseModel',
      //   meta: { title: '旧账迁移（模板版）', businessType: 2 },
      //   pageName: 'initialiseModel',
      // },
    ],
  },
  {
    path: '/manager/promotion',
    name: 'promotionModule',
    meta: {
      icon: 'tuiguang',
      show: ['ROLE_CHARGE'],
      title: '推广',
    },
    children: [
      {
 path: '/manager/promotion', name: 'promotion', meta: { title: '推广' }, pageName: 'promotion',
},
    ],
  },
  {
    path: '/controlCenter/booksSetList',
    meta: {
      title: '账套',
      icon: 'zhangtao',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'booksSetList',
        path: '/controlCenter/booksSetList',
        meta: { title: '账套' },
        pageName: 'booksSetList',
      },
    ],
  },
  {
    path: '/controlCenter/riskStatistics',
    name: 'riskStatisticsModule',
    meta: {
      title: '风险',
      icon: 'fengxian',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'riskStatistics',
        path: '/controlCenter/riskStatistics',
        meta: { title: '风险' },
        pageName: 'riskStatistics',
      },
    ],
  },
  {
    name: 'staffManagementModule',
    path: '/controlCenter/staffManagement',
    meta: {
      title: '员工',
      icon: 'yuangong',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'staffManagement',
        path: '/controlCenter/staffManagement',
        meta: { title: '员工' },
        pageName: 'staffManagement',
      },
    ],
  },
  {
    name: 'statisticsModule',
    path: '/controlCenter/statistics',
    meta: {
      title: '统计',
      XuRi: true,
      icon: 'tongji',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'statistics',
        path: '/controlCenter/statistics',
        meta: { title: '统计' },
        pageName: 'statistics',
      },
      {
        name: 'statisticsReport',
        path: '/controlCenter/statistics/:reportId',
        hidden: true,
        props: true,
        pageName: 'statisticsReport',
      },
    ],
  },
  {
    name: 'personalInformationModule',
    path: '/controlCenter/personalInformation',
    meta: {
      title: '个人',
      icon: 'geren',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'personalInformation',
        path: '/controlCenter/personalInformation',
        meta: { title: '个人' },
        pageName: 'personalInformation',
      },
    ],
  },
  {
    name: 'operationLogModule',
    path: '/controlCenter/operationLog',
    meta: {
      title: '日志',
      icon: 'rizhi',
      show: ['ROLE_CHARGE', 'ROLE_OM_ADMIN'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'operationLog',
        path: '/controlCenter/operationLog',
        meta: { title: '日志' },
        pageName: 'operationLog',
      },
    ],
  },
  {
    name: 'firstInitializationModule',
    path: '/controlCenter/firstInitialization',
    meta: {
      title: '导入',
      icon: 'daoru',
      show: ['ROLE_OM_ADMIN'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'firstInitialization',
        path: '/controlCenter/firstInitialization',
        meta: { title: '导入' },
        pageName: 'firstInitialization',
      },
    ],
  },
  {
    path: '/controlCenter/messageCenter',
    meta: {
      title: '消息',
      icon: 'xiaoxi2',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'messageCenter',
        path: '/controlCenter/messageCenter',
        meta: { title: '消息' },
        pageName: 'messageCenter',
      },
    ],
  },
  {
    path: '/controlCenter/service',
    meta: {
      title: '服务',
      icon: 'fuwu',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'invoiceVerification',
        path: 'invoiceVerification',
        meta: { title: '发票查验账单' },
        pageName: 'invoiceVerification',
      },
      {
        name: 'basicOrder',
        path: 'basicOrder',
        meta: { title: '基础功能订单' },
        pageName: 'basicOrder',
      },
      {
        name: 'featureOrder',
        path: 'featureOrder',
        meta: { title: '发票查验订单' },
        pageName: 'featureOrder',
      },
    ],
  },
  {
    path: '/controlCenter/backups',
    meta: {
      title: '备份',
      icon: 'beifen',
      show: ['ROLE_CHARGE'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'backups',
        path: '/controlCenter/backups',
        meta: { title: '备份' },
        pageName: 'backups',
      },
    ],
  },
  {
    path: '/controlCenter/recycleBin',
    meta: {
      title: '回收站',
      icon: 'youjian',
      show: ['ROLE_CHARGE'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'recycleBin',
        path: '/controlCenter/recycleBin',
        meta: { title: '回收站' },
        hidden: true,
        pageName: 'recycleBin',
      },
    ],
  },
  {
    path: '/controlCenter/roleCenter',
    meta: {
      title: '角色',
      icon: 'jiaose',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      showWithVipCode: ['Paid-role-001'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'roleCenter',
        path: '/controlCenter/roleCenter',
        meta: { title: '角色' },
        pageName: 'roleCenter',
      },
    ],
  },
  {
    path: '/controlCenter/taxDeclarationList',
    meta: {
      title: '申报',
      icon: 'shuiwu1',
      show: ['ROLE_CHARGE', 'ROLE_SPONSOR', 'ROLE_ASSISTANT'],
      type: 'controlCenter',
    },
    children: [
      {
        name: 'taxDeclarationList',
        path: '/controlCenter/taxDeclarationList',
        meta: { title: '申报' },
        pageName: 'taxDeclarationList',
      },
    ],
  },
];
export default menus;
