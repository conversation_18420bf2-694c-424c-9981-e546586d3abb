import { defineComponent } from 'vue';

type EnumSelectComponent = ReturnType<typeof defineComponent<{
  value: string,
  clearable: boolean,
  placeholder: string,
  autoSelect: boolean,
  multiple: boolean,
  filterMethod: (item: Option | Readonly<Option>) => boolean
}>>

type Option = { label: string, value: string | number | undefined };

export function makeEnumSelectComponent(data: Option[], options: {
  name?: string;
  // 是否自动选择第一条记录
  autoSelect?: boolean;
  // 传递给 el-select 的 props
  props?: Record<string, any>;
}): EnumSelectComponent

export function stringsToEnumOptions (enumNames: Array<string>): Option[]

export function makeBooleanEnumOptions(trueText: string, falseText: string, useOneAndZero?: boolean): Option[]
