/// <reference path = "makeEnumSelectComponent.d.ts" />

import assign from 'lodash-es/assign';
import {
 defineComponent, h, computed, watch, onMounted,
} from 'vue';

export function makeEnumSelectComponent(data, options = {}) {
  return defineComponent({
    name: options.name ?? 'EnumSelectComponent',

    props: {
      /** 值 */
      value: {
        type: [String, Number, Array],
        default: '',
      },

      /** 是否可以清空选中项 */
      clearable: {
        type: Boolean,
        default: false,
      },

      /** 空值占位显示 */
      placeholder: {
        type: String,
        default: '全部',
      },

      /** 是否自动选中第一项 */
      autoSelect: {
        type: Boolean,
        default: options.autoSelect ?? false,
      },

      /** 选项过滤方法 */
      filterMethod: {
        type: Function,
        default: () => true,
      },

      multiple: {
        type: Boolean,
        default: false,
      },
    },

    setup(props, { emit }) {
      const localValue = computed({
        get() {
          return props.value;
        },

        set(value) {
          emit('input', value);
        },
      });

      const autoSelect = () => {
        if (props.autoSelect && !localValue.value && data.length) {
          localValue.value = props.multiple ? [data[0].value] : data[0].value;
        }
      };
      watch(() => props.autoSelect, autoSelect);
      onMounted(autoSelect);

      return {
        optionList: data,
        localValue,
      };
    },

    render() {
      return h(
        'el-select',
        {
          class: 'EnumSelectComponent',
          props: {
            value: this.localValue,
            clearable: this.clearable,
            placeholder: this.placeholder,
            multiple: this.multiple,
            ...assign({}, (options.props ?? {}), this.$attrs),
          },
          on: {
            ...this.$listeners,
          },
        },

        data.filter(this.filterMethod).map((option) => h('el-option', {
            key: option.value,
            props: {
              label: option.label,
              value: option.value,
            },
          })),
      );
    },
  });
}

export function stringsToEnumOptions(enumNames) {
  return Object.freeze(enumNames.map((label, index) => Object.freeze({ label, value: index + 1 })));
}

export function makeBooleanEnumOptions(trueLabel, falseLabel, useOneAndZero) {
  return Object.freeze([
    { label: trueLabel, value: useOneAndZero ? 1 : true },
    { label: falseLabel, value: useOneAndZero ? 0 : false },
  ]);
}
