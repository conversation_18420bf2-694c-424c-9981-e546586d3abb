/* eslint-disable import/prefer-default-export */
import { MessageBox } from 'element-ui';

import request from '@/utils/request';
import router from 'RouteConfig/route';

// 判断父级科目是否有发生额或余额
export async function hasHappenMoney(rsp) {
  const { returnCode, returnMessage } = rsp.data;
  if (returnCode === 250) {
    const message = JSON.parse(returnMessage);
    const {
 subjectCode, subjectFullName, subjectParentCode,
} = message[0];
    const text = `检查到本次新增科目的上级科目 ${subjectFullName} 有余额，为保证科目余额表金额正确，请将余额转到新增科目下`;
    await MessageBox.alert(text, '提示', {
      confirmButtonText: '前往科目转换',
      center: true,
    }).then(() => {
      router.push({ name: 'subjectConversion', params: { origin: 'hasHappenMoney', subjectCode, subjectParentCode } });
    });
  }
}

export async function insertSubject(list) {
  const rsp = await request.post('/rest/companyConfig/companyBasis/subjectinfo/v1.1/insert?ignoreParent=1', list);
  await hasHappenMoney(rsp);
  return rsp;
}
