import { cloneDeep } from 'lodash-es';

function filterTree(treeData, filterFn, childrenKey = 'children') {
  const filteredTree = cloneDeep(treeData);
  function traverse(node) {
    if (node[childrenKey]) {
      node[childrenKey] = node[childrenKey].filter(traverse);
    }
    return filterFn(node);
  }

  return filteredTree.filter(traverse);
}
export default function combinUserWithGroup(departments, employees) {
  // 创建一个以groupId为键的对象，用于快速查找部门和员工
  const dataMap = new Map();
  departments.forEach((department) => {
    // 此处在用户组id拼接 group_ 用于区分部门和员工
    dataMap.set(department.groupId, {
      ...department,
      children: [],
      id: `group_${department.groupId}`,
      isGroup: true,
      name: department.groupName,
      parentId: department.parentGroupId ? `group_${department.parentGroupId}` : null,
    });
  });
  dataMap.set('group_noGroup', {
      children: [],
      id: 'group_noGroup',
      isGroup: true,
      name: '无部门',
      parentId: null,
  });
  employees.forEach((employee) => {
    const department = dataMap.get(employee.groupId) ?? dataMap.get('group_noGroup');
    if (department) {
      if (!department.children) department.children = [];
      department.children.push({
        ...employee,
        id: employee.userId,
        name: employee.userName,
      });
    }
  });

  // 构建树形数据结构
  const treeData = [
    dataMap.get('group_noGroup'),
  ];
  departments.forEach((department) => {
    if (!department.parentGroupId) {
      treeData.push(dataMap.get(department.groupId));
    } else if (dataMap.get(department.parentGroupId)) {
      dataMap.get(department.parentGroupId).children.push(dataMap.get(department.groupId));
    }
  });
  return filterTree(treeData, (item) => {
    if (item.isGroup) {
      return item.children.length > 0;
    }
    return true;
  });
}
