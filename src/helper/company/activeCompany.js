import { MessageBox } from 'element-ui';
import dayjs from 'dayjs';
import { pick } from 'lodash-es';
import request from '@/utils/request';
import store from '@/store';

// 查询代账公司的订单信息
export const loadOrderInfo = async (companyId) => {
  const rsp = await request.get(`/auth/company/comTimeDeadlineInfo/${companyId}`);
  const data = rsp.data.data[0];
  return pick(data, [
    'companyTotal',
    'activeCompanyTotal',
    'currOrderComLimit',
    'currOrderEndDate',
    'currOrderBeginDate']);
};
export async function activeCompany(companyId) {
  const {
  currOrderComLimit, currOrderEndDate, activeCompanyTotal, currOrderBeginDate,
  } = await loadOrderInfo(companyId);
  // 无生效中的订单
  if (!currOrderEndDate || dayjs().isAfter(dayjs(currOrderEndDate), 'day')) {
    MessageBox.alert('您无生效中订单，请尽快续费，可联系客服热线：020-87515610', '续费激活');
  } else if (currOrderComLimit - activeCompanyTotal <= 0) {
    MessageBox.alert('您的生效中订单剩余账套数为0，无法激活该账套，请联系客服热线：020-87515610', '续费激活');
  } else {
    MessageBox.confirm(`
      您有生效中订单，生效时间为${dayjs(currOrderBeginDate).format('YYYY-MM-DD')}至${dayjs(currOrderEndDate).format('YYYY-MM-DD')}，请确定是否激活,
      账套总数为${currOrderComLimit}个，剩余账套数为${currOrderComLimit - activeCompanyTotal}个，请确定是否激活
    `, '续费激活', {
      cancelButtonText: '关闭',
      confirmButtonText: '激活',
      showClose: true,
      duration: 0,
    }).then(async () => {
      await request.put(`/auth/company/updateComDeadlineFromOrder/${companyId}`);
      // 激活成功后调选择公司接口刷新会话
      await request.get(`/auth/select/company/${companyId}`);
      MessageBox.alert('当前账套已成功激活，刷新页面后即可使用', '提示', {
        confirmButtonText: '刷新',
      }).then(() => {
        window.location.reload();
      });
    });
  }
}
