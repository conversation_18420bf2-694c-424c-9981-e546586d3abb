import { MessageBox, Message } from 'element-ui';
import dayjs from 'dayjs';
import { activeCompany } from '@/helper/company/activeCompany';
import store from '@/store';
import { isEmpty } from 'lodash-es';

let messageVm;
/**
 * 检查公司的过期时间，并且触发相应的提示内容
 * @param {number} companyId
 */
export default async function validCompanyState(companyId) {
  // 每次切换公司都需要关闭原来的提示框
  if (messageVm) {
    messageVm.close();
  }
  let { companyInfo } = store.state.user;
  const { shareCompany } = store.state.user.userInfo.companyInfo;
  if (companyId !== companyInfo.companyId) {
    await store.dispatch('user/getCompanyInfo');
    companyInfo = store.state.user.companyInfo;
  }

  const companyTimeDeadline = dayjs(companyInfo.companyTimeDeadline);
  const remainDay = companyTimeDeadline.diff(dayjs().endOf('date'), 'day');
  const isDisabled = companyInfo.companyStatus === 2; // 账套处于停用状态
  const isOverdue = remainDay < 0;
  // 停用时的提示
  if (isDisabled) {
    messageVm = Message({
      message: isOverdue
        ? '账套已停用，如需继续使用，请先启用后，激活账套，才能继续使用'
        : `账套已被停用。亲，您的使用期还有${remainDay}天`,
      type: 'warning',
      showClose: true,
      duration: 0,
    });
    return;
  }
  if (isOverdue) {
    let { BKCompanyInfo } = store.state.user;
    const roles = store.state.user.userInfo.roleNames || [];
    if (isEmpty(BKCompanyInfo)) {
      await store.dispatch('user/getBKCompanyInfo');
      BKCompanyInfo = store.state.user.BKCompanyInfo;
    }
    // 账套计费模式 为记账公司账套时
    if (BKCompanyInfo.companyCostMode === 2 && !shareCompany) {
      MessageBox.confirm('亲，您的当前账套订购已到期，只能查询不能使用其他操作，如需接着正常使用，请尽快购买/续费！如有疑问，请联系客服热线：020-87515610', '续费激活', {
        cancelButtonText: '关闭',
        confirmButtonText: '续费激活',
        showClose: true,
        duration: 0,
      }).then(async () => {
        // 主管账号才可以续费激活
        if (roles.includes('ROLE_CHARGE')) {
          activeCompany(companyInfo.companyId);
        } else {
          Message.warning('您没有权限进行续费激活，请联系主管进行续费');
        }
      });
    } else {
      messageVm = Message.error({
        message: '亲，您的订购已到期，只能查询不能使用其他操作，如需接着正常使用，请尽快购买/续费！如有疑问，请联系客服热线：020-87515610',
        type: 'error',
        showClose: true,
        duration: 0,
      });
    }
  } else if (remainDay < 30 && remainDay >= 0) {
    messageVm = Message({
      message: `亲，您的使用期还有${remainDay}天，如果使用还不错，现在就购买/续费吧。客服热线：020-87515610`,
      type: 'warning',
      showClose: true,
      duration: 0,
    });
  }
}
