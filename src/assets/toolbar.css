  .toolbar {
    position: relative;
    padding: 8px 0px;
    /* text-align: right; */
    /* background: #fff; */
    /* min-height: 40px; */
    box-sizing: content-box !important;
    /* margin-top:4px; */
    /* border: 1px solid #cfdbe2; */
    border-top-width: 1px;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
  }
  .action-wrapper{
    padding-top: 6px;
  }

  .toolbar .select_menu {
    display: none;
    
  }

  .toolbar > .fa.fa-question-circle {
    float: left;
    line-height: 40px;
    margin-right:14px;
  }

  .toolbar > .fa.fa-question-circle.float_none {
    float: none;
  }

  .toolbar .select_menu:hover,
  .toolbar .select_menu:focus {
    color:#FF5629;
  }

  .toolbar .select_menu_title {
    font-size: 12px;
    font-weight: bold;
    color:#188ae2;
    display: inline-block;
    margin-right: 12px;
  }
  .toolbar .select_menu_title + span {color:#188ae2;}

  .toolbar .el-tabs {
    float: left;
  }

  .toolbar .el-tabs .el-tabs__header {
    border-bottom: none;
  }

  .toolbar .el-tabs .el-tabs__nav-wrap,
  .toolbar .el-tabs .el-tabs__nav-scroll {
    margin: 0;
    overflow: visible;
  }

  .toolbar .el-tabs .el-tabs__nav-scroll .el-tabs__item {
    height: 40px;
    line-height: 40px;
    color: #8391a5;
    font-size: 12px;
  }

  .toolbar .el-tabs .el-tabs__nav-scroll .el-tabs__item.is-active {
    color: #188ae2;
  }

  .toolbar .el-tabs .el-tabs__nav-scroll .el-tabs__active-bar {
    top: 100%;
    background-color: #FF5629;
  }

  .toolbar .qf_query {
    vertical-align: middle;
    height: 40px;
    line-height: 40px;
    /* margin-top:-2px; */
  }

  .toolbar .qf_query .el-form-item {
    margin-top: 0;
    margin-bottom: 0;
  }

  .toolbar .el-form--inline .el-form-item__content{
    vertical-align: middle;
  }

  .toolbar .qf_query .el-form-item__error {
    padding: 0;
    margin: 0px;
  }

  .toolbar .qf_query .el-form-item .el-input-group__prepend {
    width: 60px;
  }

  .toolbar .qf_query .el-form-item .el-input-group__prepend + input,
  .toolbar .qf_query .el-form-item .el-form-item__content>.el-input,
  .toolbar .qf_query .el-form-item .el-select {
    width: 124px;
  }

  .toolbar .qf_query .el-form-item  .el-date-editor {
    width: 200px;
  }
  .toolbar .qf_query .el-form-item .el-date-editor--daterange{
    width: 236px;
  }
  .toolbar .qf_query .el-form-item .el-date-editor--month {
    width: 110px;
  }

  .toolbar .qf_query .el-form-item .el-date-editor--year {
    width: 95px;
  }
  .toolbar .qf_query .el-form-item .el-date-editor--monthrange{
    width: 176px;
  }
  .toolbar .el-tabs__nav-wrap::after{
    height: 0px;
  }
  /* .el-date-picker {
      min-width: 198px;
  }
  .el-date-picker .el-picker-panel__content{
    min-width: 160px;
    margin: 0px;
  }
  .el-date-picker .el-month-table tr{
    display: block;
    text-align: center;
  }
  .el-date-picker .el-month-table tr td, .el-date-picker .el-month-table tr th{
    display: inline-block;
    padding-top: 8px;
    padding-bottom: 8px;
  }
  .el-date-picker .el-month-table tr td a {
    width: 36px;
  } */

  .toolbar .qf_query .el-form-item .el-button {
    height: 30px;
    padding: 0 16px;
    margin-top: 6px;
    margin-bottom: 6px;
  }
  .toolbar .el-button  span .iconfont{
    vertical-align: text-bottom;
  }

  .toolbar .qf_query .el-form-item .line {
    display: block;
    width: 1px;
    height: 14px;
    line-height: 1;
    margin: 13px 2px;
    background: rgb(225,225,225);
  }

  .toolbar .el_popover_area {
    display: inline;
  }

  .toolbar .el-form-item__content {
    line-height: 1;
    vertical-align: middle;
  }

  .toolbar .el-cascader .el-input:after,.toolbar .el-form-item__content .el-radio-group:after,.toolbar  .el-form-item__content .el-checkbox-group:after,.toolbar  .el-form-item__content .el-input:after,.toolbar .el-form-item__content .el-autocomplete:after,.toolbar .el-form-item__content  .el-date-editor:after{
    
    display: inline-block;
    content: "";
    width: 0;
    font-size: 0;
    line-height: 1;
    vertical-align: middle;
    visibility: hidden;
    height: 40px;
  }

  .toolbar .qf_query .el-form-item:last-child {
    margin-right: 0;
  }

  .toolbar .el-button-group {
    vertical-align: baseline;
  }

  .toolbar .el-button-group .el-button {
    float: none;
  }
  .toolbar .el-tabs__nav-wrap::after{
    height: 0px;
  }

  /* .toolbar .el-button-group .el-button:last-child[style] {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  } */

  .toolbar .select_menu ~ *:not(.el-tabs) {
    display: inline-block;
    vertical-align: middle;
  }

  .toolbar .qf_query .reset, .toolbar > .el-button:not(.select_menu):not(.export):not(.import):not(.el-button--text) {
    height: 26px;
    padding: 0 16px;
    font-size: 12px;
    /* background: #44b549;
    border-color: #44b549; */
  }

  /* .toolbar .qf_query .reset:hover, .toolbar .qf_query .reset:focus,
  .toolbar > .el-button:not(.select_menu):not(.export):not(.import):not(.el-button--text):hover,
  .toolbar > .el-button:not(.select_menu):not(.export):not(.import):not(.el-button--text):focus {
    color: #fff;
    background: #56C059;
    border-color: #56C059;
  } */
  /* 
  .toolbar > .el-button i {
    font-size: 14px;
    margin-right: 5px;
    vertical-align: initial;
  } */

  .toolbar .select_menu ~ *:not(:empty) + *:not(:empty) {
    margin-left: 10px;
  }

  .toolbar .row_operate + * {
    margin-left: 0 !important;
  }

  /* .toolbar .row_operate .el-button--text {
    font-size: 12px;
  } */

  .toolbar .row_operate .el-button--text:focus,
  .toolbar .row_operate .el-button--text:hover {
    color: #ff5629;
  }

  .toolbar .qf_query + * {
    margin-left: 20px !important;
  }

  /* .toolbar:after {
    display: inline-block;
    content: "";
    width: 0;
    height: 40px;
    clear: both;
    font-size: 0;
    margin: 0;
    vertical-align: middle;
    visibility: hidden;
  } */
  .toolbar .export,
  .toolbar .import {
    padding: 0;
  }

  .toolbar .export:before,
  .toolbar .import:before {
    display: inline-block;
    content: "";
    width: 19px;
    height: 24px;
    margin-right: 2px;
    margin-bottom: 4px;
    background: url(/static/imgs/excel_export.png) no-repeat;
    vertical-align: middle;
  }

  .toolbar .export:hover:before {
    background-position: -19px 0;
  }

  .toolbar .import:before {
    background-position: -38px 0;
  }

  .toolbar .import:hover:before {
    background-position: -57px 0;
  }

  .toolbar .export > span,
  .toolbar .import > span {
    display: inline-block;
    font-size: 12px;
    line-height: 28px;
    color: rgb(31, 40, 61);
    vertical-align: middle;
  }

  .toolbar .export:hover > span,
  .toolbar .import:hover > span {
    color: #FF5629;
  }
  /* .toolbar::before{
    content: '';
    width: 2px;
    height: 28px;
    background: #c0192a;
    position: absolute;
    left: -2px;
    top: 10px;
  } */
  .toolbar .el-form-item__label{
  line-height: 40px;
  }