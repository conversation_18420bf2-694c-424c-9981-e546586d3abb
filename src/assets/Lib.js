/* eslint-disable no-extend-native */
// import 'element-ui/lib/theme-chalk/index.css';

import '@/assets/base.css';

import '@/assets/animate.css';
import '@/assets/font-awesome-4.7.0/css/font-awesome.min.css';
import '@/assets/aliIcon/iconfont.css';
import { Decimal } from 'decimal.js';
import * as Utils from '@/assets/Utils';
import dayjs from 'dayjs';
Date.prototype.Format = function Format(fmt) { // author: meizz
  return Utils.formatDate(this, fmt);
};

Date.prototype.toJSON = function DatetoJSON() {
  return this.Format("yyyy-MM-dd hh:mm:ss")
}

if (!Array.prototype.find) {
  Array.prototype.find = function find(searchFunc) {
    for (let i = 0; i < this.length; i += 1) {
      if (searchFunc(this[i], i, this)) {
        return this[i];
      }
    }
    return undefined;
  };
}

if (!Array.prototype.findIndex) {
  Array.prototype.findIndex = function findIndex(searchFunc) {
    for (let i = 0; i < this.length; i += 1) {
      if (searchFunc(this[i], i, this)) {
        return i;
      }
    }
    return -1;
  };
}

Array.prototype.hasMap = function hasMap(strArr) {
  if (!strArr) {
    return false;
  }
  const arr = strArr.split(',');
  for (let i = 0; i < arr.length; i += 1) {
    if (this.indexOf(arr[i]) > -1) {
      return true;
    }
  }
  return false;
};
// 加法函数
function accAdd(arg1, arg2) {
  return new Decimal(arg1).add(arg2).toNumber();
}
// 给Number类型增加一个add方法，，使用时直接用 .add 即可完成计算。
Number.prototype.add = function add(arg) {
  return accAdd(arg, this);
};


// 减法函数
function Subtr(arg1, arg2) {
  return new Decimal(arg1).minus(arg2).toNumber();
}

// 给Number类型增加一个add方法，，使用时直接用 .sub 即可完成计算。
Number.prototype.sub = function sub(arg) {
  return Subtr(this, arg);
};


// 乘法函数
function accMul(arg1, arg2) {
  return new Decimal(arg1).mul(arg2).toNumber();
}
// 给Number类型增加一个mul方法，使用时直接用 .mul 即可完成计算。
Number.prototype.mul = function mul(arg) {
  return accMul(arg, this);
};


// 除法函数
function accDiv(arg1, arg2) {
  return new Decimal(arg1).div(arg2).toNumber();
}
// 给Number类型增加一个div方法，，使用时直接用 .div 即可完成计算。
Number.prototype.div = function div(arg) {
  return accDiv(this, arg);
};
Number.prototype.toFixed = function toFixed(s) {
  return new Decimal(this).toFixed(s);
};

const Rxports = {
  Utils,
};
export default Rxports;
