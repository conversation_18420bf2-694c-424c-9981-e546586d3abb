/*侧滑表单*/
.slide-fade-enter-active {
  transition: all 0.5s !important;
}
.slide-fade-leave-active {
  transition: all 0.5s !important;
}
.slide-fade-enter, .slide-fade-leave-active {
  transform: translateX(1360px) !important;
} 
.v-modal[style]:empty {
  transform: translateZ(0);
}
.el-card.left-form {
  position: fixed;
  top: 0;
  right: 0;
  height: 100%;
  border: none;
  overflow-x: auto;
  box-sizing: border-box;
  -moz-box-shadow: -10px 0 20px rgba(30, 30, 30, 0.1);
  /* 老的 Firefox */
  box-shadow: -10px 0 20px rgba(30, 30, 30, 0.1);
  z-index: 1024;
}
.el-card.left-form .el-cascader__label {
 line-height: 36px;
}
.content:not(.sidebarSmall) .el-card.left-form {
  max-width: calc(100% - 126px);
}
.content.sidebarSmall .el-card.left-form {
  max-width: calc(100% - 50px);
}
@media screen and (min-width:1366px) {
  .el-card.left-form {
    width: 84%;
  }
}
@media screen and (max-width:1366px) {
  .el-card.left-form {
    width: 94%;
  }
}
.el-card.left-form .el-card__header {
  position: relative;
  padding: 0 20px;
  height: 50px;
}
.el-card.left-form .el-card__header:before {
  display: inline-block;
  content: "";
  width: 0;
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 0;
  vertical-align: middle;
}
.el-card.left-form .el-card__header > .header {
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}
.el-card.left-form .el-card__header > .header > .content {
  display: flex;
  display: -ms-flexbox;
  display: -webkit-flex;
  flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  align-items: center;
  -ms-flex-align: center;
  margin-right: 24px;
  white-space: nowrap;
  overflow: hidden;
  *zoom: 1;
}
.el-card.left-form .el-card__header > .header > .content > .title:first-of-type {
  display: block;
  flex-grow: 1;
  -ms-flex: 1 1 100%;
  font-size: 15px;
  padding-left: 20px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  background: url(/static/imgs/dot1.png) no-repeat;
}
.el-card.left-form .el-card__header > .header > .content > .title:first-of-type + * {
  margin-left: 10px;
}
.el-card.left-form .el-card__header > .header > .content > .el-button.close:last-of-type {
  position: absolute;
  top: 50%;
  right: 10px;
  margin-left: 0;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  -o-transform: translateY(-50%);
}
.el-card.left-form .el-card__header > .header > .content > .el-button.close:last-of-type .el-icon-close {
  padding: 10px;
  font-size: 14px;
  color: rgb(114, 130, 153);
}
.el-card.left-form .el-card__header > .header > .content > .el-button.close:last-of-type .el-icon-close:hover {
  color: rgb(144, 160, 183);
}
.el-card.left-form .el-card__body {
  height: calc(100% - 50px);
  overflow-y: auto;
  box-sizing: border-box;
}
.el-card.left-form .el-card__body .action_bar {
  margin-top: 20px; 
  text-align: center;
}
.el-card.left-form .el-card__body .action_bar > .el-checkbox,
.el-card.left-form .el-card__body .action_bar > .toggle_area > .el-checkbox {
  margin-right: 10px;
}
.el-card.left-form .el-card__body .action_bar > .toggle_area {
  display: inline-block;
  margin-right: 10px;
  margin-bottom: 10px;
}
.el-card.left-form .el-card__body .action_bar > .el-button {
  margin-bottom: 10px;
}
.el-card.left-form .el-card__body>.el-row .el-col {
  min-width: 90px;
}
.el-card.left-form .el-card__body .el-table {
  /* margin-top: 20px; */
  /* margin-bottom: 20px; */
}
.el-card.left-form .el-table .cell .el-cascader,
.el-card.left-form .el-card__body .el-col .el-select,
.el-card.left-form .el-card__body .el-col .el-cascader,
.el-card.left-form .el-card__body .el-col .el-autocomplete,
.el-card.left-form .el-card__body .el-col .el-date-editor.el-input,
.el-table .cell .el-select,
.el-table .cell  .el-cascader,
.el-table .cell .el-autocomplete,
.el-table .cell .el-date-editor.el-input {
  width: 100%;
  min-width: 50px;
  max-width: 100%;
}
.el-card.left-form .el-card__body .el-row .el-col .el-radio-group {
  line-height: 36px;
}
.el-card.left-form .el-card__body .el-row .el-col .el-radio-group > .el-radio + .el-radio {
  margin-left: 0;
}
.el-card.left-form .el-card__body .el-row .el-col .el-radio-group > .el-radio:not(:last-child) {
  margin-right: 15px;
}

.el-card.left-form .form-body h2 {
  font-size: 14px;
  padding-top: 18px;
}
.el-card.left-form .form-body .el-row .sale-detaile {
  margin: 10px 0;
}
/* 左滑框样式结束 */