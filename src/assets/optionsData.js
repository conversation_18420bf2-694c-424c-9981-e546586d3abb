
// 付款状态
export const paymentStatus = [
  {
    value: 1,
    label: '未付款',
  },
  {
    value: 2,
    label: '银行已付',
  },
  {
    value: 3,
    label: '现金已付',
  },
  {
    value: 4,
    label: '冲借支',
  },
];

// 审批状态
export const approverStatus = [
  {
    value: 2,
    label: '审批中',
  },
  {
    value: 3,
    label: '已审批',
  },
];

// 细表审批状态
export const itemApproverStatus = [
  {
    value: -2,
    label: '发起申请',
  },
  {
    value: -1,
    label: '已拒绝',
  },
  {
    value: 0,
    label: '未审批',
  },
  {
    value: 1,
    label: '审批通过',
  },
];

// 交易方式
export const payType = [
  {
    value: 1,
    label: '到付',
  },
  {
    value: 2,
    label: '赊付',
  },
];

// 付款方式
export const paymentModeList = [
  {
    value: 1,
    label: '现金',
  },
  {
    value: 2,
    label: '银行',
  },
  {
    value: 4,
    label: '赊账',
  },
  {
    value: 5,
    label: '其他货币资金',
  },
];

// 申报表状态
export const accountStatusOpts = [
  {
    value: 1,
    label: '已核算',
  },
  {
    value: 0,
    label: '未核算',
  },
];

// 模版编号
export const moduleId = [
  {
    value: 1,
    label: '材料到库存商品',
  },
  {
    value: 2,
    label: '材料到半成品',
  },
  {
    value: 3,
    label: '材料到半成品到库存商品',
  },
  {
    value: 4,
    label: '材料到半成品加材料到库存商品',
  },
  {
    value: 5,
    label: '半成品到库存商品',
  },
  {
    value: 6,
    label: '半成品加材料到库存商品',
  },
];

// 开票方式
export const invoiceMethod = [
  {
    value: 1,
    label: '专票',
  },
  {
    value: 2,
    label: '普票',
  },
  {
    value: 3,
    label: '不开票',
  },
];

// 送货方式
export const shipMethod = [
  {
    value: 1,
    label: '自提',
  },
  {
    value: 2,
    label: '送货',
  },
];

// 是否
export const noOk = [
  {
    value: false,
    label: '否',
  },
  {
    value: true,
    label: '是',
  },
];

// 是否
export const noOk1 = [
  {
    value: 0,
    label: '否',
  },
  {
    value: 1,
    label: '是',
  },
];

// 银行账号类型
export const bankAccountType = [
  {
    value: 1,
    label: '银行账户',
  },
  {
    value: 2,
    label: '现金账户',
  },
  {
    value: 3,
    label: '其他资金账户',
  },
];

// 银行启停状态
export const bankAccountStatus = [
  {
    value: true,
    label: '启用',
  },
  {
    value: false,
    label: '停用',
  },
];

// 费用类型
export const costType = [
  {
    value: 1,
    label: '财务费用',
  },
  {
    value: 2,
    label: '管理费用',
  },
  {
    value: 3,
    label: '销售费用',
  },
  {
    value: 4,
    label: '采购运输费',
  },
];

// 费用状态
export const costBillState = [
  {
    value: 1,
    label: '草稿',
  },
  {
    value: 2,
    label: '已对账',
  },
];

// 摊销状态
export const apportionStatus = [
  {
    value: 1,
    label: '未启动',
  },
  {
    value: 2,
    label: '摊销中',
  },
  {
    value: 3,
    label: '摊完',
  },
];

// 账目类型 收款
export const paymentsReveiceAccountType = [
  {
    value: 1002,
    label: '销售收款',
  },
  {
    value: 2007,
    label: '采购收款',
  },
  {
    value: 4002,
    label: '固定资产清理',
  },
  {
    value: 4003,
    label: '固定资产盘亏',
  },
];

// 账目类型 付款
export const paymentsPaymentAccountType = [
  {
    value: 1004,
    label: '销售退款',
  },
  {
    value: 2004,
    label: '采购付款',
  },
  {
    value: 3002,
    label: '费用付款',
  },
  {
    value: 4005,
    label: '固定资产购入',
  },
];

// 原材料性质
export const goodsNatures = [
  {
    value: 0,
    label: '主要材料',
  },
  {
    value: 1,
    label: '关联材料',
  },
  {
    value: 2,
    label: '其他材料',
  },
];

// 证照类型
export const certificateList = [
  {
    value: 1,
    label: '居民身份证',
  },
  {
    value: 2,
    label: '中国护照',
  },
  {
    value: 3,
    label: '港澳居民来往内地通行证',
  },
  {
    value: 4,
    label: '台湾居民来往大陆通行证',
  },
  {
    value: 5,
    label: '外国护照',
  },
  {
    value: 6,
    label: '其他',
  },
];
// 日期控件配置参数
export const pickerOptions2 = {
  disabledDate(time) {
    return time.getTime() > Date.now();
  },
  shortcuts: [
    {
      text: '最近一周',
      onClick(picker) {
        const end = new Date();
        const start = new Date(end.getTime());
        start.setDate(start.getDate() - 6);
        picker.$emit('pick', [start, end]);
      },
    },
    {
      text: '最近一个月',
      onClick(picker) {
        const end = new Date();
        const start = new Date(end.getTime());
        start.setDate(start.getDate() + 1);
        start.setMonth(start.getMonth() - 1);
        picker.$emit('pick', [start, end]);
      },
    },
    {
      text: '最近三个月',
      onClick(picker) {
        const end = new Date();
        const start = new Date(end.getTime());
        start.setDate(start.getDate() + 1);
        start.setMonth(start.getMonth() - 3);
        picker.$emit('pick', [start, end]);
      },
    },
  ],
};
export const ROLE_MAP = {
  ROLE_ADMIN: '管理员',
  ROLE_SALES: '销售',
  ROLE_WAREHOUSE: '仓管',
  ROLE_FINANCE: '财务',
  ROLE_OTHER: '其他',
  ROLE_CHARGE: '主管',
  ROLE_SPONSOR: '主办',
  ROLE_ASSISTANT: '助理',
  ROLE_OM_ADMIN: '运维管理员(内部使用)',
  ROLE_HJ_HH_SPONSOR: '海华汇缴业务主办',
  ROLE_HJ_HH_CHARGE: '海华汇缴业务主管',
  ROLE_HJ_HH_CUSTOMER: '海华汇缴业务客户',
  ROLE_CUSTOMER_WECHAT: '委托企业客户',
};
export const MESSAGE_TYPE_MAP = {
  M001: '自动生成科目类消息',
  M002: '销项发票采集（手工采集）',
  M003: '销项发票采集（定时采集）',
  M004: '销项发票明细采集',
  M005: '进项发票采集（手工采集）',
  M006: '进项发票采集（定时采集）',
  M007: '风险统计',
}
