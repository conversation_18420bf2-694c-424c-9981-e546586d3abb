/*! pace 1.0.0 */
(function () {
  let a; let b; let c; let d; let e; let f; let g; let h; let i; let j; let k; let l; let m; let n; let o; let p; let q; let r; let s; let t; let u; let v; let w; let x; let y; let z; let A; let B; let C; let D; let E; let F; let G; let H; let I; let J; let K; let L; let M; let N; let O; let P; let Q; let R; let S; let T; let U; let V; let W; const X = [].slice; const Y = {}.hasOwnProperty; const Z = function (a, b) { function c() { this.constructor = a; } for (const d in b)Y.call(b, d) && (a[d] = b[d]); return c.prototype = b.prototype, a.prototype = new c(), a.__super__ = b.prototype, a; }; const $ = [].indexOf || function (a) { for (let b = 0, c = this.length; c > b; b+= 1) if (b in this && this[b] === a) return b; return -1; }; for (u = {
    catchupTime: 100, initialRate: 0.03, minTime: 250, ghostTime: 100, maxProgressPerFrame: 20, easeFactor: 1.25, startOnPageLoad: !0, restartOnPushState: !0, restartOnRequestAfter: 500, target: 'body', elements: { checkInterval: 100, selectors: ['body'] }, eventLag: { minSamples: 10, sampleCount: 3, lagThreshold: 3 }, ajax: { trackMethods: ['GET'], trackWebSockets: !0, ignoreURLs: [] },
  }, C = function () { let a; return (a = typeof performance !== 'undefined' && performance !== null && typeof performance.now === 'function' ? performance.now() : void 0) != null ? a : +new Date(); }, E = window.requestAnimationFrame || window.mozRequestAnimationFrame || window.webkitRequestAnimationFrame || window.msRequestAnimationFrame, t = window.cancelAnimationFrame || window.mozCancelAnimationFrame, E == null && (E = function (a) { return setTimeout(a, 50); }, t = function (a) { return clearTimeout(a); }), G = function (a) { let b; let c; return b = C(), (c = function () { let d; return d = C() - b, d >= 33 ? (b = C(), a(d, () => E(c))) : setTimeout(c, 33 - d); })(); }, F = function () { let a; let b; let c; return c = arguments[0], b = arguments[1], a = arguments.length >= 3 ? X.call(arguments, 2) : [], typeof c[b] === 'function' ? c[b].apply(c, a) : c[b]; }, v = function () { let a; let b; let c; let d; let e; let f; let g; for (b = arguments[0], d = arguments.length >= 2 ? X.call(arguments, 1) : [], f = 0, g = d.length; g > f; f+= 1) if (c = d[f]) for (a in c)Y.call(c, a) && (e = c[a], b[a] != null && typeof b[a] === 'object' && e != null && typeof e === 'object' ? v(b[a], e) : b[a] = e); return b; }, q = function (a) { let b; let c; let d; let e; let f; for (c = b = 0, e = 0, f = a.length; f > e; e+= 1)d = a[e], c += Math.abs(d), b++; return c / b; }, x = function (a, b) { let c; let d; let e; if (a == null && (a = 'options'), b == null && (b = !0), e = document.querySelector(`[data-pace-${a}]`)) { if (c = e.getAttribute(`data-pace-${a}`), !b) return c; try { return JSON.parse(c); } catch (f) { return d = f, typeof console !== 'undefined' && console !== null ? console.error('Error parsing inline pace options', d) : void 0; } } }, g = (function () { function a() {} return a.prototype.on = function (a, b, c, d) { let e; return d == null && (d = !1), this.bindings == null && (this.bindings = {}), (e = this.bindings)[a] == null && (e[a] = []), this.bindings[a].push({ handler: b, ctx: c, once: d }); }, a.prototype.once = function (a, b, c) { return this.on(a, b, c, !0); }, a.prototype.off = function (a, b) { let c; let d; let e; if (((d = this.bindings) != null ? d[a] : void 0) != null) { if (b == null) return delete this.bindings[a]; for (c = 0, e = []; c < this.bindings[a].length;)e.push(this.bindings[a][c].handler === b ? this.bindings[a].splice(c, 1) : c+= 1); return e; } }, a.prototype.trigger = function () { let a; let b; let c; let d; let e; let f; let g; let h; let i; if (c = arguments[0], a = arguments.length >= 2 ? X.call(arguments, 1) : [], (g = this.bindings) != null ? g[c] : void 0) { for (e = 0, i = []; e < this.bindings[c].length;)h = this.bindings[c][e], d = h.handler, b = h.ctx, f = h.once, d.apply(b != null ? b : this, a), i.push(f ? this.bindings[c].splice(e, 1) : e+= 1); return i; } }, a; }()), j = window.Pace || {}, window.Pace = j, v(j, g.prototype), D = j.options = v({}, u, window.paceOptions, x()), U = ['ajax', 'document', 'eventLag', 'elements'], Q = 0, S = U.length; S > Q; Q+= 1)K = U[Q], D[K] === !0 && (D[K] = u[K]); i = (function (a) { function b() { return V = b.__super__.constructor.apply(this, arguments); } return Z(b, a), b; }(Error)), b = (function () { function a() { this.progress = 0; } return a.prototype.getElement = function () { let a; if (this.el == null) { if (a = document.querySelector(D.target), !a) throw new i(); this.el = document.createElement('div'), this.el.className = 'pace pace-active', document.body.className = document.body.className.replace(/pace-done/g, ''), document.body.className += ' pace-running', this.el.innerHTML = '<div class="pace-progress">\n  <div class="pace-progress-inner"></div>\n</div>\n<div class="pace-activity"></div>', a.firstChild != null ? a.insertBefore(this.el, a.firstChild) : a.appendChild(this.el); } return this.el; }, a.prototype.finish = function () { let a; return a = this.getElement(), a.className = a.className.replace('pace-active', ''), a.className += ' pace-inactive', document.body.className = document.body.className.replace('pace-running', ''), document.body.className += ' pace-done'; }, a.prototype.update = function (a) { return this.progress = a, this.render(); }, a.prototype.destroy = function () { try { this.getElement().parentNode.removeChild(this.getElement()); } catch (a) { i = a; } return this.el = void 0; }, a.prototype.render = function () { let a; let b; let c; let d; let e; let f; let g; if (document.querySelector(D.target) == null) return !1; for (a = this.getElement(), d = `translate3d(${this.progress}%, 0, 0)`, g = ['webkitTransform', 'msTransform', 'transform'], e = 0, f = g.length; f > e; e+= 1)b = g[e], a.children[0].style[b] = d; return (!this.lastRenderedProgress || this.lastRenderedProgress | this.progress !== 0 | 0) && (a.children[0].setAttribute('data-progress-text', `${0 | this.progress}%`), this.progress >= 100 ? c = '99' : (c = this.progress < 10 ? '0' : '', c += 0 | this.progress), a.children[0].setAttribute('data-progress', `${c}`)), this.lastRenderedProgress = this.progress; }, a.prototype.done = function () { return this.progress >= 100; }, a; }()), h = (function () { function a() { this.bindings = {}; } return a.prototype.trigger = function (a, b) { let c; let d; let e; let f; let g; if (this.bindings[a] != null) { for (f = this.bindings[a], g = [], d = 0, e = f.length; e > d; d+= 1)c = f[d], g.push(c.call(this, b)); return g; } }, a.prototype.on = function (a, b) { let c; return (c = this.bindings)[a] == null && (c[a] = []), this.bindings[a].push(b); }, a; }()), P = window.XMLHttpRequest, O = window.XDomainRequest, N = window.WebSocket, w = function (a, b) { let c; let d; let e; let f; f = []; for (d in b.prototype) try { e = b.prototype[d], f.push(a[d] == null && typeof e !== 'function' ? a[d] = e : void 0); } catch (g) { c = g; } return f; }, A = [], j.ignore = function () { let a; let b; let c; return b = arguments[0], a = arguments.length >= 2 ? X.call(arguments, 1) : [], A.unshift('ignore'), c = b.apply(null, a), A.shift(), c; }, j.track = function () { let a; let b; let c; return b = arguments[0], a = arguments.length >= 2 ? X.call(arguments, 1) : [], A.unshift('track'), c = b.apply(null, a), A.shift(), c; }, J = function (a) { let b; if (a == null && (a = 'GET'), A[0] === 'track') return 'force'; if (!A.length && D.ajax) { if (a === 'socket' && D.ajax.trackWebSockets) return !0; if (b = a.toUpperCase(), $.call(D.ajax.trackMethods, b) >= 0) return !0; } return !1; }, k = (function (a) {
    function b() {
      let a; const c = this; b.__super__.constructor.apply(this, arguments), a = function (a) { let b; return b = a.open, a.open = function (d, e) { return J(d) && c.trigger('request', { type: d, url: e, request: a }), b.apply(a, arguments); }; }, window.XMLHttpRequest = function (b) { let c; return c = new P(b), a(c), c; }; try { w(window.XMLHttpRequest, P); } catch (d) {} if (O != null) { window.XDomainRequest = function () { let b; return b = new O(), a(b), b; }; try { w(window.XDomainRequest, O); } catch (d) {} } if (N != null && D.ajax.trackWebSockets) {
        window.WebSocket = function (a, b) {
          let d; return d = b != null ? new N(a, b) : new N(a), J('socket') && c.trigger('request', {
            type: 'socket', url: a, protocols: b, request: d,
          }), d;
        }; try { w(window.WebSocket, N); } catch (d) {}
      }
    } return Z(b, a), b;
  }(h)), R = null, y = function () { return R == null && (R = new k()), R; }, I = function (a) { let b; let c; let d; let e; for (e = D.ajax.ignoreURLs, c = 0, d = e.length; d > c; c+= 1) if (b = e[c], typeof b === 'string') { if (a.indexOf(b) !== -1) return !0; } else if (b.test(a)) return !0; return !1; }, y().on('request', function (b) { let c; let d; let e; let f; let g; return f = b.type, e = b.request, g = b.url, I(g) ? void 0 : j.running || D.restartOnRequestAfter === !1 && J(f) !== 'force' ? void 0 : (d = arguments, c = D.restartOnRequestAfter || 0, typeof c === 'boolean' && (c = 0), setTimeout(() => { let b; let c; let g; let h; let i; let k; if (b = f === 'socket' ? e.readyState < 2 : (h = e.readyState) > 0 && h < 4) { for (j.restart(), i = j.sources, k = [], c = 0, g = i.length; g > c; c+= 1) { if (K = i[c], K instanceof a) { K.watch.apply(K, d); break; }k.push(void 0); } return k; } }, c)); }), a = (function () { function a() { const a = this; this.elements = [], y().on('request', function () { return a.watch.apply(a, arguments); }); } return a.prototype.watch = function (a) { let b; let c; let d; let e; return d = a.type, b = a.request, e = a.url, I(e) ? void 0 : (c = d === 'socket' ? new n(b) : new o(b), this.elements.push(c)); }, a; }()), o = (function () { function a(a) { let b; let c; let d; let e; let f; let g; const h = this; if (this.progress = 0, window.ProgressEvent != null) for (c = null, a.addEventListener('progress', a => h.progress = a.lengthComputable ? 100 * a.loaded / a.total : h.progress + (100 - h.progress) / 2, !1), g = ['load', 'abort', 'timeout', 'error'], d = 0, e = g.length; e > d; d+= 1)b = g[d], a.addEventListener(b, () => h.progress = 100, !1); else f = a.onreadystatechange, a.onreadystatechange = function () { let b; return (b = a.readyState) === 0 || b === 4 ? h.progress = 100 : a.readyState === 3 && (h.progress = 50), typeof f === 'function' ? f.apply(null, arguments) : void 0; }; } return a; }()), n = (function () { function a(a) { let b; let c; let d; let e; const f = this; for (this.progress = 0, e = ['error', 'open'], c = 0, d = e.length; d > c; c+= 1)b = e[c], a.addEventListener(b, () => f.progress = 100, !1); } return a; }()), d = (function () { function a(a) { let b; let c; let d; let f; for (a == null && (a = {}), this.elements = [], a.selectors == null && (a.selectors = []), f = a.selectors, c = 0, d = f.length; d > c; c+= 1)b = f[c], this.elements.push(new e(b)); } return a; }()), e = (function () { function a(a) { this.selector = a, this.progress = 0, this.check(); } return a.prototype.check = function () { const a = this; return document.querySelector(this.selector) ? this.done() : setTimeout(() => a.check(), D.elements.checkInterval); }, a.prototype.done = function () { return this.progress = 100; }, a; }()), c = (function () { function a() { let a; let b; const c = this; this.progress = (b = this.states[document.readyState]) != null ? b : 100, a = document.onreadystatechange, document.onreadystatechange = function () { return c.states[document.readyState] != null && (c.progress = c.states[document.readyState]), typeof a === 'function' ? a.apply(null, arguments) : void 0; }; } return a.prototype.states = { loading: 0, interactive: 50, complete: 100 }, a; }()), f = (function () { function a() { let a; let b; let c; let d; let e; const f = this; this.progress = 0, a = 0, e = [], d = 0, c = C(), b = setInterval(() => { let g; return g = C() - c - 50, c = C(), e.push(g), e.length > D.eventLag.sampleCount && e.shift(), a = q(e), ++d >= D.eventLag.minSamples && a < D.eventLag.lagThreshold ? (f.progress = 100, clearInterval(b)) : f.progress = 100 * (3 / (a + 3)); }, 50); } return a; }()), m = (function () { function a(a) { this.source = a, this.last = this.sinceLastUpdate = 0, this.rate = D.initialRate, this.catchup = 0, this.progress = this.lastProgress = 0, this.source != null && (this.progress = F(this.source, 'progress')); } return a.prototype.tick = function (a, b) { let c; return b == null && (b = F(this.source, 'progress')), b >= 100 && (this.done = !0), b === this.last ? this.sinceLastUpdate += a : (this.sinceLastUpdate && (this.rate = (b - this.last) / this.sinceLastUpdate), this.catchup = (b - this.progress) / D.catchupTime, this.sinceLastUpdate = 0, this.last = b), b > this.progress && (this.progress += this.catchup * a), c = 1 - Math.pow(this.progress / 100, D.easeFactor), this.progress += c * this.rate * a, this.progress = Math.min(this.lastProgress + D.maxProgressPerFrame, this.progress), this.progress = Math.max(0, this.progress), this.progress = Math.min(100, this.progress), this.lastProgress = this.progress, this.progress; }, a; }()), L = null, H = null, r = null, M = null, p = null, s = null, j.running = !1, z = function () { return D.restartOnPushState ? j.restart() : void 0; }, window.history.pushState != null && (T = window.history.pushState, window.history.pushState = function () { return z(), T.apply(window.history, arguments); }), window.history.replaceState != null && (W = window.history.replaceState, window.history.replaceState = function () { return z(), W.apply(window.history, arguments); }), l = {
    ajax: a, elements: d, document: c, eventLag: f,
  }, (B = function () { let a; let c; let d; let e; let f; let g; let h; let i; for (j.sources = L = [], g = ['ajax', 'elements', 'document', 'eventLag'], c = 0, e = g.length; e > c; c+= 1)a = g[c], D[a] !== !1 && L.push(new l[a](D[a])); for (i = (h = D.extraSources) != null ? h : [], d = 0, f = i.length; f > d; d+= 1)K = i[d], L.push(new K(D)); return j.bar = r = new b(), H = [], M = new m(); })(), j.stop = function () { return j.trigger('stop'), j.running = !1, r.destroy(), s = !0, p != null && (typeof t === 'function' && t(p), p = null), B(); }, j.restart = function () { return j.trigger('restart'), j.stop(), j.start(); }, j.go = function () { let a; return j.running = !0, r.render(), a = C(), s = !1, p = G((b, c) => { let d; let e; let f; let g; let h; let i; let k; let l; let n; let o; let p; let q; let t; let u; let v; let w; for (l = 100 - r.progress, e = p = 0, f = !0, i = q = 0, u = L.length; u > q; i = ++q) for (K = L[i], o = H[i] != null ? H[i] : H[i] = [], h = (w = K.elements) != null ? w : [K], k = t = 0, v = h.length; v > t; k = ++t)g = h[k], n = o[k] != null ? o[k] : o[k] = new m(g), f &= n.done, n.done || (e++, p += n.tick(b)); return d = p / e, r.update(M.tick(b, d)), r.done() || f || s ? (r.update(100), j.trigger('done'), setTimeout(() => r.finish(), j.running = !1, j.trigger('hide'), Math.max(D.ghostTime, Math.max(D.minTime - (C() - a), 0)))) : c(); }); }, j.start = function (a) { v(D, a), j.running = !0; try { r.render(); } catch (b) { i = b; } return document.querySelector('.pace') ? (j.trigger('start'), j.go()) : setTimeout(j.start, 50); }, typeof define === 'function' && define.amd ? define(() => j) : typeof exports === 'object' ? module.exports = j : D.startOnPageLoad && j.start();
}).call(this);
