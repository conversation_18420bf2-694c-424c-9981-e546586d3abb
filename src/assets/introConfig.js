// const introConfig = [
//   {
//     hint: '登记电子税务局用户名和密码，实现发票自动采集',
//     element: '#FA_PIAO_CAI_JI'
//   },
//   {
//     hint: '上传航信、百望开票文件，将数据导入系统',
//     element: '#FA_PIAO_DAO_RU'
//   },
//   {
//     hint: '手工录入发票信息',
//     element: '#SHOU_LU_FA_PIAO'
//   },
//   {
//     hint: '选择入账内容（入账内容=入账科目），相同开票内容的发票一次性完成对接一次对接，系统自动记忆',
//     element: '#PI_LIANG_DUI_JIE'
//   },
//   {
//     hint: '选择会计周期、发票类型、号码、代码、金额等，多维度查找发票',
//     element: '#GENG_DUO_SHOU_SUO'
//   },
// ]
const introConfig = [
  {
    intro: '登记电子税务局用户名和密码，实现发票自动采集',
    element: '#FA_PIAO_CAI_JI',
    index: 1,
  },
  {
    intro: '手工录入发票信息',
    element: '#SHOU_LU_FA_PIAO',
    index: 3,
  },
  {
    intro: '选择入账内容（入账内容=入账科目），相同开票内容的发票一次性完成对接一次对接，系统自动记忆',
    element: '#PI_LIANG_DUI_JIE',
    index: 4,
  },
  {
    intro: '上传航信、百望开票文件，将数据导入系统',
    element: '#FA_PIAO_DAO_RU',
    index: 2,
  },
  {
    intro: '选择会计周期、发票类型、号码、代码、金额等，多维度查找发票',
    element: '#GENG_DUO_SHOU_SUO',
    index: 5,
  },
  {
    intro: '<div style="height:300px;"><div>生成凭证</div><img height="300" width="580" src="/static/imgs/CMB/发票生成凭证指引.png"></div>',
    showPath: ['inputTax', 'outputTax'],
    index: 6,
  },

  // 资金 Start
  {
    intro: '选择银行账号，没有银行账号的可以新增银行账号',
    element: '#XUAN_ZE_YING_HANG',
    index: 1,
  },
  {
    intro: '选择导入方式，上传流水文件，数据导入系统',
    element: '#ZI_JIN_DAO_RU',
    index: 2,
  },
  {
    intro: '<div style="height:300px;"><div>手录流水</div><img width="580" src="/static/imgs/CMB/手录流水.png"></div>',
    showPath: ['trade'],
    index: 3,
  },
  {
    intro: '<div style="height:300px;"><div>补充入账科目</div><img width="580" src="/static/imgs/CMB/补充入账科目.png"></div>',
    showPath: ['trade'],
    index: 4,
  },
  {
    intro: '<div style="height:300px;"><div>点击科目旁边的图标</div><img width="580" src="/static/imgs/CMB/资金-点击科目旁边的图标.png"></div>',
    showPath: ['trade'],
    index: 5,
  },
  {
    intro: '<div style="height:300px;"><div>选择分拆科目</div><img width="580" src="/static/imgs/CMB/资金-选择分拆科目.png"></div>',
    showPath: ['trade'],
    index: 6,
  },
  {
    intro: '<div style="height:300px;"><div>录入科目金额</div><img width="580" src="/static/imgs/CMB/资金-录入科目金额.png"></div>',
    showPath: ['trade'],
    index: 7,
  },
  {
    intro: '<div style="height:300px;"><div>搜索员工报销单</div><img width="580" src="/static/imgs/CMB/资金-搜索员工报销单.png"></div>',
    showPath: ['trade'],
    index: 8,
  },
  {
    intro: '<div style="height:300px;"><div>勾选报销单</div><img width="580" src="/static/imgs/CMB/资金-勾选报销单.png"></div>',
    showPath: ['trade'],
    index: 9,
  },
  {
    intro: '<div style="height:300px;"><div>确定关联报销单</div><img width="580" src="/static/imgs/CMB/资金-确定关联报销单.png"></div>',
    showPath: ['trade'],
    index: 10,
  },
  {
    intro: '<div style="height:300px;"><div>查看拆分的流水或报销单生成的科目</div><img width="580" src="/static/imgs/CMB/资金-查看拆分的流水或报销单生成的科目.png"></div>',
    showPath: ['trade'],
    index: 11,
  },
  {
    intro: '<div style="height:300px;"><div>资金生成凭证</div><img width="580" src="/static/imgs/CMB/资金生成凭证.png"></div>',
    showPath: ['trade'],
    index: 12,
  },
  {
    intro: '选择会计属期、收付类别、凭证状态、金额等，多维度查找银行流水',
    element: '#ZI_JIN_GENG_DUO_SHOU_SUO',
    index: 13,
  },
  {
    intro: '点击“更多”，选择导出/打印出纳账、结算表',
    element: '#ZI_JIN_GENG_DUO',
    index: 14,
  },
  // 资金 End

  {
    intro: '<div style="height:300px;"><div>设置附加税率</div><img width="580" src="/static/imgs/CMB/设置附加税率.png"></div>',
    showPath: ['endCheckout'],
    index: 1,
  },
  {
    intro: '<div style="height:300px;"><div>设置发出商品数量</div><img width="580" src="/static/imgs/CMB/设置发出商品数量.png"></div>',
    showPath: ['endCheckout'],
    index: 2,
  },
  {
    intro: '<div style="height:300px;"><div>一键生成结转凭证</div><img width="580" src="/static/imgs/CMB/一键生成结转凭证.png"></div>',
    showPath: ['endCheckout'],
    index: 3,
  },
  {
    intro: '<div style="height:370px;"><div>导入/录入数据</div><img width="580" src="/static/imgs/CMB/导入及录入数据.png"></div>',
    showPath: ['quickAccount'],
    index: 1,
  },
  {
    intro: '<div style="height:370px;"><div>凭证规则设置</div><img width="580" src="/static/imgs/CMB/凭证规则设置.png"></div>',
    showPath: ['quickAccount'],
    index: 2,
  },
  {
    intro: '<div style="height:370px;"><div>一键生成凭证</div><img width="580" src="/static/imgs/CMB/一键生成凭证.png"></div>',
    showPath: ['quickAccount'],
    index: 3,
  },
  {
    intro: '<div style="height:370px;"><div>提示处理</div><img width="580" src="/static/imgs/CMB/提示处理.png"></div>',
    showPath: ['quickAccount'],
    index: 4,
  },
  {
    intro: '<div style="height:370px;"><div>结果浏览</div><img width="580" src="/static/imgs/CMB/结果浏览.png"></div>',
    showPath: ['quickAccount'],
    index: 5,
  },
];

export default introConfig;
