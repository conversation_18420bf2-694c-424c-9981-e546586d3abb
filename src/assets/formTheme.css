
  .body .el-tabs__nav {
    width: 50%;
    margin-top: 40px;
  }

  .body .el-tabs__nav .el-tabs__item {
    width: 450px;
    height: auto;
    line-height: 1;
    font-size: 20px;
    font-family: 'Microsoft YaHei';
    padding: 20px 0;
    text-align: center;
  }

  .body .el-form-item.is-error {
    margin-bottom: 8px;
  }

  .body .el-form-item__error {
    color: #444; 
    font-size: 12px;
    line-height: 42px;
    padding: 0 30px 0 36px;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 40%;
    width: 30%;
    margin-left: 20px;
    border: 1px solid #c0192a;
    background: #FFEDED;
  }

  .body .el-form-item__error:before {
    content: '';
    position: absolute;
    top: 6px;
    left: -6px;
    width: 0;
    height: 0;
    border-right: 6px solid #c0192a;
    border-bottom: 6px solid transparent;
    border-top: 6px solid transparent; 
  }

  .body .el-form-item__error:after {
    content: '';
    position: absolute;
    top: 7px;
    left: -5px;
    width: 0;
    height: 0;
    border-bottom: 5px solid transparent;
    border-right: 5px solid #FFEDED;
    border-top: 5px solid transparent; 
  }

  .validSign {
    position: absolute;
    left: 40%;
    top: 50%;
    margin-top: -9px;
    font-size: 20px;
    z-index: 99999;
    margin-left: 28px;
  }

  .validSign:before {
    vertical-align: middle;
  }

  .el-icon-circle-close.validSign {
    color: red;
  }

  .el-icon-circle-check.validSign {
    color: green;
  }

  .form_QRCode {
    text-align: center;;
  } 

  .form_QRCode_title {
    font-size: 20px; 
    font-weight: bold;
  } 

  .form_QRCode_img {
    margin: 20px 0;
    width: 270px;
    height: 270px;
  }

  .verticalAlign_middle {
    vertical-align: middle;
  }

  .form_row_padding {
    padding-top: 30px; 
    padding-left: 25px;
  }

  .form_row_withOutPaddingLeft {
    padding-left: 0px;
  }

  .form_col_border {
    border-right: 1px solid #ccc;
  }

  /* .form_widget_width {
    width: 40% !important;
  } */

  /* .form_subWidget {
    float: left;
    width: 50% !important;
  } */

  .form_subWidget .form_vertifyBtnArea {
    padding-left: 10px;
  }

  .form_subWidget .form_vertifyBtnArea .form_btn_getVertifyCode {
    width: 100%; 
    margin-top: 1px !important;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .form_div_degreeSignArea {
    line-height: normal;
    position: relative;
  }

  .form_div_degreeSignArea > div {
    position: relative; 
    padding-left: 10px; 
    padding-right: 25px;
  }

  .form_div_degreeSign {
    width: 33.3%; 
    height: 20px;
    display: inline-block; 
    background: #ccc; 
    vertical-align: middle;
  }

  .form_div_degreeSign:first-child {
    margin-left: -10px;
  }

  .form_div_degreeSign:last-child {
    margin-right: -10px;
  }

  .form_div_degreeSign_true {
    background: #f39800;
  }

  .form_span_tip {
    position: absolute;
    top: 50%; 
    right: 0;  
    margin-top: -8px;
    color: #f39800;
    vertical-align: middle;
  }

  .form_link {
    color: #2d4d7b;
  }

  .bodyRight_col {
    padding-left: 80px;
  }



  .regHelpArea{
    margin-top: 120px;
    margin-left: 20px;
  }