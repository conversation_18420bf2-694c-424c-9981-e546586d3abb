iframe.print {
  position: fixed;
  top: 0;
	left: 0;
	border: none !important;
  visibility: hidden;
	z-index: -99999;
	
}
body{
	background: none !important;
	background: transparent !important;
}
@media print {
	@page {
		size: A4 landscape;
		margin:2cm;
	}
	body {
		background: none !important;
		background: transparent !important;
	}
	.A4{
		page-break-before: auto;        
		page-break-after: always;
	}
	.el-table__body-wrapper .el-scrollbar__view{
		min-height: 0 !important;
	}
	/*.el-table th,
	.el-table__header-wrapper thead div {
		background: none;
	}*/

	.el-table .el-table__header-wrapper table[style],
	.el-table .el-table__body-wrapper table[style] {
		/* width: 100% !important; */
	}

	.el-table .el-table__header-wrapper table col[name^="el-table"][width],
	.el-table .el-table__body-wrapper table col[name^="el-table"][width],
	.el-table .el-table__body-wrapper .el-table__empty-block[style] {
	  /* width: auto !important; */
	}
	.el-table{
		border: none;
		border-top: 1px solid #000;
		border-left: 1px solid #000;
	}
	
	.el-table > div[style], .el-table .el-table__body-wrapper .el-table__empty-block[style] {
		height: auto !important;
	}

	/*  */
	
}
/* 资产凭证组件 */
.voucherWithAssetsItem-wrapper{
	display: flex;
}
.voucherWithAssetsItem-itemBox{
	flex: 1;
	line-height: 28px;
}
.voucherWithAssetsItem-item{
	display: flex;
}
.voucher-code{
	flex: 1;
	white-space: pre-line;
}
.operate{
	width: 30px;
}

