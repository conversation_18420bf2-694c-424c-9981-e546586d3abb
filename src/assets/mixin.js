export const bAHandleChange = {
  methods: {
    // 银行账户处理
    bAHandleChange(value) {
    	console.info('mixin');
      const q = this.formData;
      switch (value.length) {
        case 2:
          [q.settlementMode, q.bankAccount] = value;
          break;
        case 1:
          q.settlementMode = value[0];
          this.options.settlementModeOpt.map((item, index) => {
            if (value[0] === item.keyValue) {
              q.bankAccount = item.keyNotes;
              return false;
            }
            q.bankAccount = '';
          });
          break;
        default:
          [q.settlementMode, q.bankAccount] = ['', ''];
      }
    },
  },
};
