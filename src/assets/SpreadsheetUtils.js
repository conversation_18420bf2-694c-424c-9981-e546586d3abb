/**
 * Mark
 * 2018-3-22 14:49:10
 * 解析的标签格式{ fill: { colname : "quantity", source : "types" }, merge: { colname : "quantity", ref: "name" }}
 */

export default class SpreadsheetUtils {
  constructor($spreadsheet) {
    this.$spreadsheet = $spreadsheet;
    this.allRules = null;
    this.groupByRowRules = null;
  }

  // 数值下标转EXCEL列序号
  convertColIndex(i) {
    const convert_tmp = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ'];
    return convert_tmp[i];
  }

  getMergeRefRange({ startRowNum, sheetName }, dataSource = []) {
    if (!startRowNum || !sheetName) { throw new Error('rule is not empty!'); }
    if (dataSource.length === 0) { return null; }
    const rangeStr = `A${startRowNum}:AZ${startRowNum + dataSource.length - 1}`;
    const kendoSpreadsheet = this.$spreadsheet.data('kendoSpreadsheet');
    const sheet = kendoSpreadsheet.sheetByName(sheetName);

    console.log('需要检测合并的区域', rangeStr);
    return sheet.range(rangeStr);
  }

  doRenderMergeRange({ startRowNum, sheetName }, dataSource) {
    const ref = this.getMergeRefRange({ startRowNum, sheetName }, dataSource);
    this.renderMergeRange(ref);
  }

  renderMergeRange(ref) {
    // console.log('ref', ref);
    const MERGETAG = 'merge'; const COLUMNKEY = 'colname'; const
      REFKEY = 'ref';
    if (!ref) { throw new Error('ref is not empty!'); }
    let preRow = null;
    const needSelfColumnList = [];
    const needOtherColumnList = [];
    const rememberMergedColumn = {};

    const getMergeRangeStr = (preRow) => {
      let rangeStr = '';
      if (preRow.row !== preRow.startRow.row) {
        const startRangStr = `${this.convertColIndex(preRow.startRow.column)}${preRow.startRow.row + 1}`;
        const endRangStr = `${this.convertColIndex(preRow.column)}${preRow.row + 1}`;
        rangeStr = `${startRangStr}:${endRangStr}`;
      }
      return rangeStr;
    };

    // 该方法首行行号为0, 用于取range范围时需加1
    ref.forEachCell((row, column, cell) => {
      const { tmsCellFlag } = cell;

      // 按列遍历
      if (tmsCellFlag && tmsCellFlag.hasOwnProperty(MERGETAG)) {
        // 参照自己合并的
        if (tmsCellFlag[MERGETAG][COLUMNKEY] === tmsCellFlag[MERGETAG][REFKEY]) {
          needSelfColumnList.push({
            row, column, cell, ref: tmsCellFlag[MERGETAG][REFKEY],
          });
        } else {
          needOtherColumnList.push({
            row, column, cell, ref: tmsCellFlag[MERGETAG][REFKEY],
          });
        }
      }

      // 参照自己合并的列进行合并
      const firstCellItem = needSelfColumnList.find(item => item.column === column);
      if (firstCellItem) {
        console.log('row', row, cell.value);
        // 第一行
        if (firstCellItem.row === row) {
          preRow = { row, column, cell };
          preRow.startRow = firstCellItem;
        } else {
          // 非第一行
          if (preRow.cell.value === cell.value) {
            preRow = {
              row, column, cell, startRow: preRow.startRow,
            };
            // 已经到最后一格了，看看要不要合并。
            if (ref._ref.bottomRight && ref._ref.bottomRight.row === row) {
              const rangeStr = getMergeRangeStr(preRow);
              if (rangeStr) {
                console.log('rangeStr', rangeStr);
                ref._sheet.range(rangeStr).merge();
                rememberMergedColumn.hasOwnProperty(firstCellItem.ref) ? rememberMergedColumn[firstCellItem.ref].push(preRow) : (rememberMergedColumn[firstCellItem.ref] = [preRow]);
              }
            }
          } else {
            const rangeStr = getMergeRangeStr(preRow);
            if (rangeStr) {
              console.log('rangeStr', rangeStr);
              ref._sheet.range(rangeStr).merge();
              // 记录，当参考合并时使用
              rememberMergedColumn.hasOwnProperty(firstCellItem.ref) ? rememberMergedColumn[firstCellItem.ref].push(preRow) : (rememberMergedColumn[firstCellItem.ref] = [preRow]);
            }
            preRow = {
              row, column, cell, startRow: { row, column, cell },
            };
          }
        }
      }
    });

    // 在范围内参照自己的合并完后，开始解析参照它列的进行合并
    needOtherColumnList.forEach((item) => {
      rememberMergedColumn.hasOwnProperty(item.ref) && rememberMergedColumn[item.ref].forEach((preRow) => {
        let rangeStr = '';
        preRow.column = preRow.startRow.column = item.column;
        rangeStr = getMergeRangeStr(preRow);
        console.log(`参考合并的范围：${rangeStr}参考${item.ref}`);
        rangeStr && ref._sheet.range(rangeStr).merge();
      });
    });
  }

  getAllRules(cache = true) {
    return !cache || this.allRules === null ? this.resolveRules() : this.allRules;
  }

  getGroupByRowRules(cache = true) {
    return !cache || this.groupByRowRules === null ? this.groupByRow(this.getAllRules(cache)) : this.groupByRowRules;
  }

  resolveRules() {
    if (!this.$spreadsheet) { throw new Error('spreadsheet instance is not empty!'); }

    const kendoSpreadsheet = this.$spreadsheet.data('kendoSpreadsheet');
    const tmsCellFlagResult = [];
    kendoSpreadsheet.sheets().forEach((sheet) => {
      const sheetName = sheet._sheetName;
      const tmsCellFlagList = [];
      sheet.range(sheet._sheetRef).forEachCell((row, column, cell) => {
        const { tmsCellFlag } = cell;
        tmsCellFlag && tmsCellFlagList.push({
          row, column, tmsCellFlag, cell,
        });
    	});
    	tmsCellFlagList.length && tmsCellFlagResult.push({ sheetName, tmsCellFlagList });
    });

    return (this.allRules = tmsCellFlagResult);
  }

  groupByRow(tmsCellFlagResult) {
    if (!tmsCellFlagResult) { throw new Error('tmsCellFlagResult is not empty!'); }
    this.groupByRowRules = tmsCellFlagResult.map((item) => {
      item.groupByRow = {};
      item.tmsCellFlagList.forEach((cellItem) => {
        item.groupByRow.hasOwnProperty(cellItem.row) ? item.groupByRow[cellItem.row].push(cellItem) : (item.groupByRow[cellItem.row] = [cellItem]);
      });
      return item;
    });

    return this.groupByRowRules;
  }

  resolveRowFillRules(cache = true) {
  	const FILLTAG = 'fill'; const FILLSOURCEKEY = 'source'; const
      FILLCOLKEY = 'colname';

  	// 第一步： 解析出表格所有的标记，按表格分组。 [{ sheetName: '表名', tmsCellFlagList:[所有的标记] }]
  	// 第二步： 将第一步的数据，在每个表里面按行再次分组。[{ sheetName: '表名', tmsCellFlagList:[所有的标记], groupByRow: { 行号: [该行的所有标记]} }]
  	// 第三步： 解析第二步每个sheet的groupByRow成SpreadSheetFilter所需要的数据形式。

  	// 第一步 第二步
    const groupByRowRules = this.getGroupByRowRules(cache);

    // 第三步
    const rowFillRules = [];

    // 遍历每个sheet
    groupByRowRules.forEach((item) => {
      // 每个sheet里面已经有按行分好了组的标记。{ 行号: [该行的所有标记]}
      Object.entries(item.groupByRow).forEach(([row, tmsCellFlagList]) => {
        // 拿到了一行里面所有的标记tmsCellFlagList
        const rulesObj = {
          sheetName: item.sheetName, dataSource: '', type: 'row', range: {},
        };
        let fillCol = null;
        tmsCellFlagList.forEach((cellItem) => {
          // 只对标记值key为FILLTAG的才进行rule规则构建。
          if (!cellItem.tmsCellFlag.hasOwnProperty(FILLTAG) || cellItem.tmsCellFlag[FILLTAG] === null) { return; }

          // 构建出一个 { 数据属性名: 列号, ... } 的fillCol对象
          Object.entries(cellItem.tmsCellFlag[FILLTAG]).forEach(([key, value]) => {
	         	if (key === FILLCOLKEY) {
	         		if (fillCol === null) {
	         			fillCol = { [value]: String(cellItem.column) };
	         		} else {
	         			// goodsName : "2,3"
	         			fillCol[value] = typeof fillCol[value] === 'string' ? [fillCol[value], cellItem.column].join(',') : String(cellItem.column);
	         		}
            }

            if (key === FILLSOURCEKEY) {
              rulesObj.dataSource = value;
            }
	        });
        });

        if (fillCol === null) { return; }

        rulesObj.range.row = Number(row) + 1; // 第一行为1
        rulesObj.range.col = 1;
        rulesObj.range.columnCout = 42;
        rulesObj.range.fillCol = fillCol;

        const firstRow = rowFillRules.find(item => item.sheetName === rulesObj.sheetName);
        firstRow ? rowFillRules.splice(rowFillRules.indexOf(firstRow), 0, rulesObj) : rowFillRules.push(rulesObj);
      });
    });

    return rowFillRules;
  }
}
