/*信息框样式*/
.el-message-box {
  border-bottom-left-radius: 2px;
  border-bottom-right-radius: 2px;
}
.el-message-box .el-message-box__header {
  position: relative;
  padding: 11px 20px;
  background: #2a3553;
}
.el-message-box .el-message-box__header > .el-message-box__title {
  color: white;
  font-size: 15px;
}
.el-message-box .el-message-box__header .el-message-box__headerbtn[aria-label=Close] {
  position: absolute;
  top: 50%;
  right: 10px;
  padding: 10px;
  font-size: 12px;
  float: none;
  border: none;
  outline: none;
  cursor: pointer;
  background: transparent;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  -o-transform: translateY(-50%);
}
.el-message-box .el-message-box__header > .el-message-box__headerbtn[aria-label=Close] > .el-message-box__close {
  position: static;
}
.el-message-box .el-message-box__header > .el-message-box__headerbtn[aria-label=Close] > .el-message-box__close:hover {
  color: white;
}
.el-message-box .el-message-box__btns {
  padding: 11px 20px;
}
.el-message-box .el-message-box__btns > .el-button {
  padding: 6px 20px;
  border-radius: 2px;
}
.el-message-box .el-message-box__btns > .el-button.el-button--primary:hover {
  color: white;
}
/*弹窗样式*/
.el-dialog {
  width: 750px;
  border-radius: 2px;
  box-shadow: 0 20px 20px rgba(42,53,83,.2);
  overflow: hidden;
}
.el-dialog.width80{
  width: 80%;
  max-width: 1000px;
}
.el-dialog.width90{
  width: 90%;
  max-width: 1200px;
}
.el-dialog.widthverybig {
  width:95%;
  margin-top:25px !important;
}

.el-dialog .el-dialog__header {
  position: relative;
  padding: 13px 20px;
  background: #fff;
  border-bottom: 1px solid #f2f2f2;
  cursor: move;
  text-align: center;
}
.el-dialog .el-dialog__header > .el-dialog__title {
  color: #333;
  font-size: 14px;
  text-align: center;
}
.el-dialog .el-dialog__header .el-dialog__headerbtn[aria-label=Close] {
  position: absolute;
  top: 50%;
  right: 10px;
  padding: 10px;
  font-size: 12px;
  float: none;
  border: none;
  outline: none;
  cursor: pointer;
  background: transparent;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  -o-transform: translateY(-50%);
}
.el-dialog .el-dialog__header .el-dialog__close:hover {
  color: white;
}
.el-dialog .el-dialog__body {
  min-height: 0px;
  padding: 20px 20px;
  /*padding: 48px 48px 48px 36px;
   box-sizing: border-box; */
  background: #fff;
  overflow: auto;
}
.el-dialog .el-dialog__body .el-form.qf_query {
  background: transparent !important;
}
.el-dialog .el-dialog__body .el-table .el-table__header-wrapper .showBar {
  display: none;
}
.el-dialog .el-dialog__footer {
  padding: 11px 20px;
  border-top: 1px solid #D0CFCF;
  background: #F8F8F8;
}
/* .el-dialog .el-dialog__footer .el-button {
  padding: 6px 20px;
  border-radius: 2px;
} */
@media screen and (max-width: 1920px) {
  .el-dialog .el-dialog__body {
    max-height: 420px;
  }
}
@media screen and (max-width: 1366px) {
  .el-dialog .el-dialog__body {
    /* max-height: 360px; 暂时去除 */
  }
}

@media screen and (max-width: 1920px) {
  .widthverybig .el-dialog__body {
    max-height:none;
  }
}
@media screen and (max-width: 1366px) {
  .widthverybig .el-dialog__body {
    max-height:none;
  }
}

/* 限制浏览器弹出的窗口样式 */
.browser_limit {
  width: 600px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  box-sizing: border-box;
}

.browser_limit .el-message-box__header {
  padding: 12px 20px ;
  text-align: left;
  background: #EFEFEF;
  border-bottom: 1px solid #D0CFCF;
}

.browser_limit button[aria-label=Close] {
  display: none;
}
