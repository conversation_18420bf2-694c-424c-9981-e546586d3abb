(function ($, window, document) {
  function SheetConfig(option) {
    if ($.isEmptyObject(option.spreadSheet)) {
      throw new Error('Sheet配置组件创建失败，未设置SpreadSheet对象！');
    }
    const optiondef = {};
    this.op = $.extend(true, {}, optiondef, option);
  }

  SheetConfig.prototype.initTab = function () {
    const { op } = this;
    if (op.spreadSheetDOM) {
      const toolBar = [{
        tabName: '底稿设置',
        button: [{
          type: 'button',
          text: '设置选中单元格的标记',
          className: 'sheetConfigSetBtn',
          icoClass: 'k-i-exception',
          click: op.sheetConfigSetBtnClick,
        }, {
          type: 'button',
          text: '清除多余内容',
          className: 'deleteRowBtnClick',
          icoClass: 'k-i-exception',
          click: op.deleteRowBtnClick,
        }, {
          type: 'button',
          text: '设置只读',
          className: 'setReadonlyBtn',
          icoClass: 'k-i-exception',
          click: this._setReadonly.bind(this),
        }, {
          type: 'button',
          text: '设置可写',
          className: 'setWriteBtn',
          icoClass: 'k-i-exception',
          click: this._setWrite.bind(this),
        }],
      }];

      $(toolBar).each(function () {
        $('.k-last', $(op.spreadSheetDOM)).removeClass('k-last');
        const controlId = Math.random().toString().substring(2, 12);
        const $tab = $('.k-spreadsheet-tabstrip', $(op.spreadSheetDOM)).data('kendoTabStrip');
        let bottonHtml = ''; let html = '';

        $(this.button).each(function () { // DOM
          switch (this.type) {
            case 'button':
              bottonHtml += `<a href="javascript:;" tabindex="0" title="${this.text}" id="${this.id}" class="k-button k-button-icontext ${this.className}" data-overflow="never" style="visibility: visible;"><span class="k-icon k-font-icon ${this.icoClass}"></span>${this.text}</a>`;
              break;
            case 'separator':
              bottonHtml += '<span class="k-separator"></span>';
              break;
            default: break;
          }
        });

        html = `${'<div data-role="spreadsheettoolbar" class="k-toolbar k-widget k-toolbar-resizable k-spreadsheet-toolbar" tabindex="0">'
					+ '<div tabindex="0" class="k-overflow-anchor k-button" style="visibility: hidden; width: 1px;"><span class="k-icon k-i-arrow-s"></span></div>'}${
          bottonHtml
        }</div>`;

        $tab.insertAfter(
          [{
            text: this.tabName,
            content: html,
          }], $tab.tabGroup.children().last(),
        );

        $(this.button).each(function () { // 点击事件
          switch (this.type) {
            case 'button':
              if (this.click) {
                $(`.${this.className}`, $($tab.element)).bind('click', this.click);
              }
              break;
            case 'separator':
              break;
            default: break;
          }
        });
      });
    }
  };

  // 设置只读
  SheetConfig.prototype._setReadonly = function () {
    const sheet = this.op.spreadSheet.activeSheet();

    sheet.range(sheet.selection()).enable(false);
  };

  // 设置可写
  SheetConfig.prototype._setWrite = function () {
    const sheet = this.op.spreadSheet.activeSheet();

    sheet.range(sheet.selection()).enable(true);
  };

  $.extend({
    sheetConfig(option) {
      const sc = new SheetConfig(option);
      sc.initTab();
    },
  });
}(jQuery, window, document));
