/**
 * 项目常量
 * by Mark
 * 2017-3-22 10:21:22
 */

export const USERCENTER_DOMAIN_NAME = process.env.VUE_APP_USERCENTER_DOMAIN_NAME;

export const POWERTAX_URL = process.env.VUE_APP_POWERTAX_URL;
export const PROJECT_TYPE = process.env.VUE_APP_PROJECT_TYPE;
export const VERSION = process.env.VUE_APP_VERSION;
export const PROJECT_NAME = process.env.VUE_APP_PROJECT_TYPE === "agent" ? "票靓·服务版" : "票靓·企业版";
export const USERCENTER_DOMAIN_NAME_HTTPS = process.env.VUE_APP_USERCENTER_DOMAIN_NAME_HTTPS;
export const { NODE_ENV } = process.env;

const isXuRi = window.location.host.indexOf("xuri") !== -1;
let USERCENTER_URL; // 由新窗口打开的用户中心的url， 里面包含了 CLIENT_ID 和 REDIRECT_URI 参数
let CLIENT_ID; // 当前应用在用户中心的id
let REDIRECT_URI; // 用户中心登录完成后的重定向地址
let WEB_DOMAIN_NAME; // 当前的域名
// 根据当前域名选择对应的用户中心配置
if (isXuRi) {
  USERCENTER_URL = process.env.VUE_APP_XURI_USERCENTER_URL;
  CLIENT_ID = process.env.VUE_APP_XURI_CLIENT_ID;
  REDIRECT_URI = process.env.VUE_APP_XURI_REDIRECT_URI;
  WEB_DOMAIN_NAME = process.env.VUE_APP_XURI_WEB_DOMAIN_NAME;
} else {
  USERCENTER_URL = process.env.VUE_APP_CSZX_USERCENTER_URL;
  CLIENT_ID = process.env.VUE_APP_CSZX_CLIENT_ID;
  REDIRECT_URI = process.env.VUE_APP_CSZX_REDIRECT_URI;
  WEB_DOMAIN_NAME = process.env.VUE_APP_CSZX_WEB_DOMAIN_NAME;
}

export {
  USERCENTER_URL,
  CLIENT_ID,
  REDIRECT_URI,
  WEB_DOMAIN_NAME,
}
