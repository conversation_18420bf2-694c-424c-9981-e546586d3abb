import Vue from 'vue';

// 注册一个全局自定义指令 v-focus
Vue.directive('focus', {
  // 当绑定元素插入到 DOM 中。
  inserted(el) {
    // 聚焦元素
    el.querySelector('input').focus();
  },
});

// 注册一个全局滚动指令 v-scroll
Vue.directive('scroll', {
  bind(el, binding) {
    const $wrap = $('.el-scrollbar__wrap', el);
    const always = function always() {
      this.scrollTop = this.dataset.scrollTop || 0;
      this.dataset.loadDisabled = 'false';
    }.bind($wrap[0]);
    $wrap.scroll(function scroll() {
      if (
        el.dataset.allLoaded === 'false'
        && this.dataset.loadDisabled !== 'true'
        && Math.ceil(this.scrollTop + this.clientHeight) >= this.scrollHeight
      ) {
        this.dataset.loadDisabled = 'true';
        binding.value().then(always, always);
        this.dataset.scrollTop = this.scrollTop;
      }
    });
  },
});

Vue.directive('lrselchange', {
  // 当绑定元素修改到 DOM 中。
  componentUpdated(el, binding) {
    binding.value[binding.arg] = 'xxx';
  },
});

Vue.directive('rightClick', {
  inserted(el) {
    // 聚焦元素
    el.oncontextmenu = function (event) {
      if (document.all) window.event.returnValue = false;
      else event.preventDefault();
    };
  },
});
function upDownFocus(e) {
  console.log(e);
  const { keyCode } = e;
  if (keyCode !== 40 && keyCode !== 38) {
    return;
  }
  const el = e.target;
  const parentRow = $(el).parents('tr');
  const foucsRow = keyCode === 40 ? parentRow.next() : parentRow.prev();
  if (foucsRow.length !== 0) {
    const inputIndex = $(el)
      .parents('td')
      .index();
    const foucsTd = foucsRow.children('td')[inputIndex];
    $(foucsTd)
      .find('input')
      .focus();
  }
  e.preventDefault();
}
// 表格上下键切换聚焦
Vue.directive('upDownFocus', {
  // 当绑定元素插入到 DOM 中。
  inserted(el) {
    // 聚焦元素
    console.log(el.querySelector('input'), el);
    el.querySelector('input').addEventListener('keydown', upDownFocus);
  },
  unbind(el) {
    el.querySelector('input').removeEventListener('keydown', upDownFocus);
  },
});
// 将负数文本内容变为红色字体
Vue.directive('minus-style', {
  inserted(el, binding) {
    // 聚焦元素
    console.log(el, binding);
    let amount;
    if (typeof binding.value === 'number') {
      amount = binding.value;
    } else {
      const { innerText } = el;
      innerText.replace(/,/g, '');
      amount = Number(innerText);
    }
    if (!Number.isNaN(amount) && amount < 0) {
      el.style.color = 'red';
    }
  },
});
