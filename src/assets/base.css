﻿@import url(./popups.css);
@import url(./sliders.css);
@import url(./toolbar.css);
@import url(./commonPrintStyle.css);
body {
  color: #222222;
  font-size: 14px;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  overflow: hidden;
  background: #f6f6f6;
  vertical-align: baseline;
}

html {
  width: 100%;
}

html,
body,
div,
span,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
abbr,
address,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
samp,
small,
strong,
sub,
sup,
var,
b,
i,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

nav,
ul {
  list-style: none;
}

table {
  /* border-collapse: collapse; */
  border-spacing: 0;
}

input,
textarea {
  font-family: 'Microsoft Yahei';
}

a {
  color: #2d4b7b;
}

a.linkhei,
.linkhei {
  color: #000;
}

html {
  -ms-text-size-adjust: 100%;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 1 */
}

.fl {
  float: left;
}

.top2 {
  top: 2px;
}

.width-80 {
  width: 80px !important;
}

.width-100 {
  width: 100px !important;
}
.width-120 {
  width: 120px !important;
}

.width-200 {
  width: 200px !important;
}

.width-220 {
  width: 220px !important;
}

.padding-top-10 {
  padding-top: 10px !important;
}

.padding-bottom-10 {
  padding-bottom: 10px !important;
}

.padding-right-10 {
  padding-right: 10px !important;
}

.padding-left-10 {
  padding-left: 10px !important;
}

.margin-top-10 {
  margin-top: 10px !important;
}

.margin-bottom-10 {
  margin-bottom: 10px !important;
}

.margin-right-10 {
  margin-right: 10px !important;
}

.margin-left-10 {
  margin-left: 10px !important;
}

.content {
  height: 100%;
}

.text_center {
  text-align: center;
}

.test input {
  border: none;
}

.fr {
  float: right;
}

.cl-fff {
  color: #fff;
}

.cl-333 {
  color: #333;
}

.cl-666 {
  color: #666;
}

.cl-999 {
  color: #999;
}

.cl-ccc {
  color: #ccc;
}

.cl-eee {
  color: #eee;
}

.cl-red {
  color: #f55136;
}

.cl-green {
  color: #3ab28c;
}

.fz12 {
  font-size: 12px;
}

.fz16 {
  font-size: 16px;
}

.fz18 {
  font-size: 18px;
}

.fz20 {
  font-size: 20px;
}

.fz22 {
  font-size: 22px;
}

.fz24 {
  font-size: 24px;
}

.fz40 {
  font-size: 40px;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.auto-width {
  width: auto;
}

/* 账本，特殊行变色 */

.el-table__body .booksAccumulativeColor td {
  background: #fffadd !important;
}

.el-table tr td .cell .highlight {
  font-weight: bold;
}

.el-table__body .reportAccumulativeColor td {
  background: #e7f7fa85 !important;
}

.clearfix:after {
  content: '';
  display: block;
  height: 0;
  visibility: hidden;
  clear: both;
}

.clearfix {
  *zoom: 1;
}

a {
  text-decoration: none;
  outline: none !important;
  color: rgb(32, 160, 255);
  /* font-size: 14px; */
}

.pace {
  -webkit-pointer-events: none;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.pace-inactive {
  display: none;
}

.pace .pace-progress {
  background: #f39800;
  position: fixed;
  z-index: 2000;
  top: 0;
  right: 100%;
  width: 100%;
  height: 2px;
}

.pace .pace-progress-inner {
  display: block;
  position: absolute;
  right: 0px;
  width: 100px;
  height: 100%;
  box-shadow: 0 0 10px #f39800, 0 0 5px #f39800;
  opacity: 1;
  -webkit-transform: rotate(3deg) translate(0px, -4px);
  -moz-transform: rotate(3deg) translate(0px, -4px);
  -ms-transform: rotate(3deg) translate(0px, -4px);
  -o-transform: rotate(3deg) translate(0px, -4px);
  transform: rotate(3deg) translate(0px, -4px);
}

.pace .pace-activity {
  display: block;
  position: fixed;
  z-index: 2000;
  top: 15px;
  right: 15px;
  width: 14px;
  height: 14px;
  border: solid 2px transparent;
  border-top-color: #f39800;
  border-left-color: #f39800;
  border-radius: 10px;
  -webkit-animation: pace-spinner 400ms linear infinite;
  -moz-animation: pace-spinner 400ms linear infinite;
  -ms-animation: pace-spinner 400ms linear infinite;
  -o-animation: pace-spinner 400ms linear infinite;
  animation: pace-spinner 400ms linear infinite;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  margin: 0;
}

input[type='number'] {
  -moz-appearance: textfield;
}

@-webkit-keyframes pace-spinner {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-moz-keyframes pace-spinner {
  0% {
    -moz-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -moz-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-o-keyframes pace-spinner {
  0% {
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-ms-keyframes pace-spinner {
  0% {
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes pace-spinner {
  0% {
    transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-webkit-keyframes pace-spinner {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-moz-keyframes pace-spinner {
  0% {
    -moz-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -moz-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-o-keyframes pace-spinner {
  0% {
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-ms-keyframes pace-spinner {
  0% {
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes pace-spinner {
  0% {
    transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.pace {
  -webkit-pointer-events: none;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.pace-inactive {
  display: none;
}

.pace .pace-progress {
  background: #f39800;
  position: fixed;
  z-index: 2000;
  top: 0;
  right: 100%;
  width: 100%;
  height: 2px;
}

.pace .pace-progress-inner {
  display: block;
  position: absolute;
  right: 0px;
  width: 100px;
  height: 100%;
  box-shadow: 0 0 10px #f39800, 0 0 5px #f39800;
  opacity: 1;
  -webkit-transform: rotate(3deg) translate(0px, -4px);
  -moz-transform: rotate(3deg) translate(0px, -4px);
  -ms-transform: rotate(3deg) translate(0px, -4px);
  -o-transform: rotate(3deg) translate(0px, -4px);
  transform: rotate(3deg) translate(0px, -4px);
}

.pace .pace-activity {
  display: block;
  position: fixed;
  z-index: 2000;
  top: 15px;
  right: 15px;
  width: 14px;
  height: 14px;
  border: solid 2px transparent;
  border-top-color: #f39800;
  border-left-color: #f39800;
  border-radius: 10px;
  -webkit-animation: pace-spinner 400ms linear infinite;
  -moz-animation: pace-spinner 400ms linear infinite;
  -ms-animation: pace-spinner 400ms linear infinite;
  -o-animation: pace-spinner 400ms linear infinite;
  animation: pace-spinner 400ms linear infinite;
}

@-webkit-keyframes pace-spinner {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-moz-keyframes pace-spinner {
  0% {
    -moz-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -moz-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-o-keyframes pace-spinner {
  0% {
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-ms-keyframes pace-spinner {
  0% {
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes pace-spinner {
  0% {
    transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

html,
body {
  height: 100%;
}

.body
/*bootstrap .clearfix*/

.clearfix:before,
.clearfix:after {
  content: ' ';
  display: table;
}

.clearfix:after {
  clear: both;
}

/**
 * For IE 6/7 only
 */

.clearfix {
  *zoom: 1;
}

/*.el-select-dropdown__item {
  height: 36px !important;
}*/
/* 不知道用来干嘛 */
/* .workspace {
    padding-right: 20px;
} */

.el-tabs__item {
  color: #333;
  font-size: 14px;
}

.lr_left_from_mask {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  opacity: 0.5;
  background-color: black;
}

/* 覆盖element-ui样式 start */

/* 用于显示超长提示文本 */

.el-message__group {
  min-height: 20px;
  height: auto;
}

.el-message__img {
  height: 100%;
  background: rgb(19, 206, 102);
}
/* 保证message一定处于最上层 */
.el-message {
  z-index: 9999 !important;
}

/* 页面表格尺寸变化时， 实现平滑过渡 */

.el-table {
  z-index: auto;
  overflow: initial;
}
.el-table {
  z-index: auto;
  overflow: initial;
}

/* 限制表格内编辑框的高度， 实现统一 */

.el-table__row .el-input__inner {
  height: 26px;
  line-height: 26px;
}
.el-table__row .el-input__icon {
  line-height: 26px;
}

/* 限制表格单元格不允许换行 */

.el-table td .cell,
.el-table td .cell > span:not(.el-cascader):not(.el-tooltip),
.el-table td .cell > p {
  word-break: keep-all;
  /* 不换行 */
  white-space: nowrap;
  /* 不换行 */
  /* overflow:hidden;内容超出宽度时隐藏超出部分的内容 */
  text-overflow: ellipsis;
  /* 当对象内文本溢出时显示省略标记(...) ；需与overflow:hidden;一起使用。*/
}
.el-table td.canWrapTd .cell {
  white-space: normal;
  word-break: break-all;
}

.el-table__body tr > td.col-hidden {
  display: none;
}

/* .el-table__footer-wrapper tbody td{
  font-weight: bold;
  background: #fff;
  border-top: 1px solid #ecdfdf;
  border-bottom: 1px solid #ecdfdf;
} */

.el-table__empty-block {
  position: static;
}

.el-table__empty-block .el-table__empty-text {
  white-space: nowrap;
}

.el-select-dropdown__item span,
.el-picker-panel__body-wrapper div,
.el-picker-panel__body-wrapper td,
.el-picker-panel__body-wrapper th,
.el-picker-panel__shortcut {
  font-size: 12px;
}

.el-dirty {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 3px;
  border-color: red transparent transparent red;
  padding: 0;
  overflow: hidden;
  vertical-align: top;
  left: 0;
  top: 0;
}

.el-pagination {
  margin-top: 10px;
  overflow: hidden;
}

/* 缩小时间选择器单元格的高度 */

.el-date-table td {
  line-height: 32px;
}

/* 覆盖element-UI样式 end */

/* 滚动条样式 start */

::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.15);
  border: 2px solid transparent;
  outline-offset: -2px;
  outline: 2px solid transparent;
  border-radius: 6px;
  -webkit-border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.35);
}

::-webkit-scrollbar-track-piece {
  background-color: transparent;
  border-radius: 6px;
}

::-webkit-scrollbar-corner {
  display: none;
}

/* 滚动条样式 end */

.font-weight-bold {
  font-weight: bold;
}

.addrow-text {
  color: #ccc;
  cursor: pointer;
}

/* .el-checkbox__inner{
  position: relative;
  display: inline-block;
  width: 18px;
  height: 18px;
  border-radius: 3px;
  border: 1px solid #d9d9d9;
  background-color: #fff;
  transition: border-color .1s cubic-bezier(.71,-.46,.29,1.46),background-color .1s cubic-bezier(.71,-.46,.29,1.46);
  z-index:0;
} */

/* .el-checkbox__inner::after {
  box-sizing: content-box;
  content: "";
  border: 2px solid #fff;
  border-left: 0;
  border-top: 0;
  height: 12px;
  left: 5px;
  position: absolute;
  top: 0;
  transform: rotate(45deg) scaleY(0);
  width: 4px;
  transition: transform .15s cubic-bezier(.71,-.46,.88,.6) .05s;
  transform-origin: center;
} */

/* .el-checkbox__input.is-checked .el-checkbox__inner {
  border-color: #2db7f5;
  background-color: #2db7f5;
} */

.cell,
.cell span {
  font-size: 12px;
  /*font-family: 宋体;*/
}

.el-pager li,
.el-pager li:last-child,
.el-select-dropdown {
  border-color: #aaa;
}

/* .el-pagination .el-select .el-input input{
  height: 24px;
  border-radius: 0px;
  border-color: #aaa;
}
.el-pagination ul.el-pager > li, .el-pagination span, .el-pagination button {
  font-size: 12px;
  height: 24px;
  line-height: 24px;
} */

/* .el-pagination ul.el-pager > li.active {
  border-color: #c0192a;
  background-color: #c0192a;
} */

/* .el-input__inner {
  padding: 3px 6px;
  height: 24px;
  display: inline-block;
  border-radius: 2px;
  border-color: #bbb;
} */

/* .el-select .el-input__inner[disabled] {
  height: 24px !important;
} */

/* .el-form-item__label {
  font-size: 12px;
  padding-top: 12px;
  padding-bottom: 12px;
  color:#333;
} */

/* .el-input__inner,
.el-radio__label,
.el-checkbox__label,
.el-textarea__inner {
  font-size: 12px;
  line-height: normal;
} */

.el-transfer-panel__list .el-checkbox__label {
  line-height: 32px;
}

.el-form-item.is-error {
  margin-bottom: 12px;
}

.el-form-item:not(.is-error) {
  margin-bottom: 2px;
}

.el-tree-node__label,
.el-icon-caret-bottom {
  font-size: 12px;
}

/* .el-cascader__label {
    width: 90%;
    font-size: 12px;
    line-height: 40px;
} */

.el-select-dropdown__item {
  font-size: 12px;
  padding: 8px 10px;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: rgb(72, 81, 106);
  height: 28px;
  line-height: 1;
  box-sizing: border-box;
  cursor: pointer;
}

/* .el-select-dropdown__item span {
    line-height: 1 !important;
} */
/* 6164 测试环境-莲融&财税智享，账套界面创建新公司界面问题 */
/* .el-cascader-menu{
    overflow: auto;
} */

/* .el-cascader-menu__item {
  line-height: 1;
  font-size: 12px;
} */

.el-form-item__label {
  line-height: 36px;
}

.el-form-item.is-required .el-form-item__label:before {
  content: '*';
  color: #c00;
  margin-right: 4px;
  font-size: 12px;
  font-family: Tahoma;
}

.borderHover {
  height: 32px !important;
  line-height: 30px !important;
  border-radius: 4px;
  padding-left: 6px;
  max-width: 100% !important;
  cursor: pointer;
  padding-top: 1px;
  font-size: 12px;
  box-sizing: border-box;
}

.borderHover:hover {
  background-color: #f5fbff;
  height: 32px !important;
  line-height: 30px !important;
  border-radius: 4px;
  padding-left: 5px;
  padding-top: 0px;
  border: 1px solid #b2e0ff;
  max-width: 100% !important;
  cursor: pointer;
}

/*字体颜色*/

.Blue {
  color: #188ae2;
}

.Success {
  color: #13ce66;
}

.Warning {
  color: #f7ba2a;
}

.Danger {
  color: #ff4949;
}
.Info {
  color: #909399;
}

/*单元格编辑按钮样式*/

/* .hoverRow{
  cursor: pointer;
} */

.editCol {
  /* width: 100%; */
  height: 100%;
  min-height: 13px;
  position: relative;
  cursor: pointer;
  /* display: inline-block;  6301 测试环境和生产环境，科目余额表部分科目名字太长显示不出 */
}
.editCol span:hover {
  text-decoration: underline;
}

.editCol i {
  line-height: 20px;
  width: 20px;
  height: 20px;
  font-size: 14px;
  text-underline-position: under;
}

/* .el-table .el-table__row i:hover {
    color: #66ccff;
} */

.editCol i:first-of-type {
  margin-left: 10px;
}

tr:hover .editCol {
  text-decoration: underline;
}

.hoverRow:hover i {
  display: inline-block;
}

.no-padding-top {
  padding-top: 0px !important;
}

/* .showBar{
  position: absolute;
  background: url('./imgs/view.png') no-repeat top right;
  top:0;
  right: -15px;
  width: 20px;
  height: 28px;
  z-index: 10;
  opacity: 0.6;
  cursor: pointer;
} */

/* .el-main{
  position: relative;
  translate: all 0.5s;
} */

/* .el-main>.line{
  width: 2px;
  height: 28px;
  background: #c0192a;
  position: absolute;
  left: 7px;
  top: 12px;
} */

.smallTable {
  display: none;
}

.fullTable {
  position: fixed;
  left: 0;
  top: 0;
  width: 100% !important;
  height: 100% !important;
  max-height: 100% !important;
  margin: 0 !important;
  z-index: 1023;
  box-sizing: border-box;
}

.fullTable.withScrollBarY {
  padding-right: 10px;
}

.fullTable.withScrollBarX .el-scrollbar__bar.is-horizontal:before {
  content: '';
  position: absolute;
  top: 0;
  left: -10px;
  right: -10px;
  display: block;
  height: 10px;
  background: white;
}

/* .fullTable .showBar{
  position: absolute;
  background: url('./imgs/reduce_btn.png') no-repeat center;
  top:50%;
  margin-top: -30px;
  right: 0px;
  width: 60px;
  height: 60px;
  z-index: 10;
  opacity: 0.6;
  cursor: pointer;
} */

.fullTable.withScrollBarY .showBar {
  right: 12px;
}

.showBar:hover {
  opacity: 0.8 !important;
}

.el-message[style] {
  z-index: 9060 !important;
}

.el-table > .tableWrapper {
  border-color: rgb(200, 200, 200);
}

.el-table > .tableWrapper::before,
.el-table > .tableWrapper::after {
  background: rgb(200, 200, 200);
}

/*紧凑模式 start*/

.smallPattern .el-table td {
  height: 24px;
  border: none;
}

.smallPattern .el-table .el-table__footer tr:last-child td {
  border-bottom: 1px solid rgb(200, 200, 200);
}

/* .el-table th{
  height: 30px;
  border-bottom: 1px solid rgb(223, 223, 223);
  border-right: 1px solid rgb(223, 223, 223);
} */

.smallPattern .qMain_div {
  height: 100%;
}

.smallPattern .qf_header {
  height: 0;
}

.smallPattern .qf_header .logo {
  visibility: hidden;
}

/*.smallPattern .workspace{
   padding: 0; 
}*/

.smallPattern .showLogo {
  animation: showLogo 0.5s;
  -webkit-animation: showLogo 0.5s;
}

/*紧凑模式下显示搜索框*/

.smallShowSearch .qf_header {
  height: 50px;
}

.smallShowSearch .qMain_div {
  height: calc(100% - 50px);
}

/*.smallShowSearch .body_content{
  margin-top: 10px;
}*/

.smallShowSearch .qf_header .logo {
  visibility: visible;
}

/*.smallShowSearch .showLogo{
  display: none;
}*/

/* 右对齐  有右边距的数字列 */

.el-table .is-right.has-right-padding-lr .cell {
  padding-right: 25px;
}

/*表格排序样式*/

.el-table .sortable_th:hover,
.el-table .sortable_th:hover div {
  /* background: #66ccff */
  background: #d1e7f9;
  cursor: pointer;
}

/*联想 下拉的字体 行高 */

.el-autocomplete-suggestion
  .el-scrollbar
  .el-autocomplete-suggestion__wrap
  .el-autocomplete-suggestion__list
  li {
  font-size: 12px;
  line-height: 28px;
  text-align: left;
}

.el-autocomplete .el-input__inner {
  padding-right: 20px;
}

.el-autocomplete .el-input__icon.el-icon-circle-close.el-input__clear {
  right: -6px;
}

.padding20 {
  padding: 20 !important;
}

/* .el-form-item__content > *:not(:only-child) { float: left; } */

/*  明细账导出 弹窗受影响
/* .el-form-item__content>*+* {
    margin-left: 5px;
} */

.el-form-item__content > .el-col > span {
  line-height: 36px;
}

.el-cascader {
  display: block;
  float: none;
}

.el-message-box .el-form-item__content > * {
  float: none;
}

/* .el-cascader,
.el-form-item__content>*[class^="el-"]:not(label) {
    line-height: 1;
} */
/* 此处强行涨高form-item高度， 不知道何用意。 导致其他元素行高对不齐，故注释掉 */
/* .el-cascader .el-input:after,
.el-form-item__content .el-radio-group:after,
.el-form-item__content .el-checkbox-group:after,
.el-form-item__content .el-input:after,
.el-form-item__content .el-autocomplete:after {
    display: inline-block;
    content: "";
    width: 0;
    height: 36px;
    font-size: 0;
    line-height: 1;
    vertical-align: middle;
    visibility: hidden;
} */

/* .el-table .cell .el-cascader .el-input:after {
    height: 24px;
} */

.el-form-item__content .el-autocomplete .el-input:after {
  display: none;
}

.el-cascader .el-input input,
.el-form-item__content .el-input input,
.el-form-item__content .el-radio-group .el-radio,
.el-form-item__content .el-checkbox-group .el-checkbox {
  vertical-align: middle;
}

.el-input.is-disabled .el-input__inner {
  background: #f2f2f2;
  color: #666666;
}

.el-cascader.is-disabled .el-cascader__label {
  color: #727272;
  line-height: 36px;
}

.el-table .cell .el-cascader .el-cascader__label {
  line-height: 24px;
}

.el-cascader.is-disabled .el-input__icon,
.el-cascader.is-disabled .el-cascader__clearIcon {
  visibility: hidden;
}

/* 禅道bug3457 测试环境-增值税申报表：字体颜色需优化 */

.spreadsheet.jxs .k-state-disabled {
  color: inherit !important;
  opacity: 1;
}

/* 禅道bug3457 */

.spreadsheet.jxs .k-spreadsheet-sheets-bar .k-spreadsheet-sheets-remove,
.spreadsheet.jxs .k-spreadsheet-sheets-bar .k-spreadsheet-sheets-bar-add {
  display: none !important;
}

.spreadsheet.jxs .k-spreadsheet-pane .k-spreadsheet-haxis,
.spreadsheet.jxs .k-spreadsheet-pane .k-spreadsheet-vaxis {
  border: 0;
}

/* .k-animation-container[style] {
  display: none !important;
} */

.k-spreadsheet-cell-context-menu,
.k-spreadsheet-row-header-context-menu,
.k-spreadsheet-col-header-context-menu {
  display: none !important;
  height: 0;
}

.spreadsheet.jxs .k-spreadsheet-sheets-items {
  font-size: 12px;
}

/*
.spreadsheet.jxs .k-spreadsheet-pane[style] {
  left: -20px !important;
  top: -20px !important;
}
*/

.el-card.spreadsheet-dialog {
  width: 90%;
  height: 90%;
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 1024;
  background: #fff;
  transform: translate(-50%, -50%);
}

.el-card.spreadsheet-dialog .el-card__body {
  padding: 0;
  margin-left: -11px;
}

.spreadsheet-dialog-cover {
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1023;
}

.el-card.spreadsheet-dialog .el-card__header {
  position: relative;
  padding: 0 20px;
  height: 51px;
}

.el-card.spreadsheet-dialog .el-card__header:before {
  display: inline-block;
  content: '';
  width: 0;
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 0;
  vertical-align: middle;
}

.el-card.spreadsheet-dialog .el-card__header > .header {
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.el-card.spreadsheet-dialog .el-card__header > .header > .content {
  display: flex;
  display: -ms-flexbox;
  display: -webkit-flex;
  flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  align-items: center;
  -ms-flex-align: center;
  white-space: nowrap;
  overflow: hidden;
  *zoom: 1;
}

.el-card.spreadsheet-dialog
  .el-card__header
  > .header
  > .content
  > .title:first-of-type {
  display: block;
  flex-grow: 1;
  flex: 1 1 100%;
  font-size: 15px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.el-card.spreadsheet-dialog
  .el-card__header
  > .header
  > .content
  > .title:first-of-type
  + * {
  margin-left: 10px;
}

.el-card.spreadsheet-dialog
  .el-card__header
  > .header
  > .content
  > .el-button.close:last-of-type {
  position: absolute;
  top: 50%;
  right: 10px;
  margin-left: 0;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  -o-transform: translateY(-50%);
}

.el-card.spreadsheet-dialog
  .el-card__header
  > .header
  > .content
  > .el-button.close:last-of-type
  .el-icon-close {
  padding: 10px;
  font-size: 14px;
  color: rgb(114, 130, 153) !important;
}

.el-card.spreadsheet-dialog
  .el-card__header
  > .header
  > .content
  > .el-button.close:last-of-type
  .el-icon-close:hover {
  color: rgb(144, 160, 183) !important;
}

.table-button {
  padding-right: 3px;
}

.table-button i {
  display: block;
  cursor: pointer;
}

/* 增加 */

.table-button .fa-plus-circle {
  color: #000;
}

/* 删除 */

.table-button .fa-trash {
  color: #66ccff;
}

/* 撤销 */

.repeal-row-btn {
  position: fixed;
  cursor: pointer;
  color: #c0192a;
}

.table-button i:nth-child(2) {
  margin-top: 2px;
}

/*滚动加载底部提示*/

.loading_info {
  font-size: 12px;
  position: relative;
  color: #000;
  margin-top: -1px;
  line-height: 30px;
  text-align: center;
  background: white;
  border: 1px solid rgb(223, 230, 236);
}

.loading_info > i {
  margin-right: 5px;
}

.loading_info > .el-button {
  padding: 0;
}

.el-icon-caret-top {
  font-size: 14px;
  width: 30px;
}

.el-pagination .el-select .el-input {
  width: 120px;
}

.el-icon-caret-top:before,
.el-icon-caret-bottom:before {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  transform: translateY(13%);
  content: '\F0DE';
}

.el-icon-caret-bottom:before {
  transform: translateY(-13%) rotateZ(180deg);
}

/* 表格边框特别加粗*/
.cellborderClass {
  border-right-width: 3px !important;
  border-right-color: #797979 !important;
}

/* 表格内字体颜色配置　*/

.table_font_black {
  color: #181818;
}

.table_font_red {
  color: red;
}

.table_font_warning {
  color: #9a691d;
}

.table_font_success {
  color: #44b549;
}

.table_font_notification {
  color: #909399;
}

.table_font_bold {
  font-weight: bold;
}
.table_text_center {
  text-align: center !important;
}

/* 微调工具栏 */

.el-switch__input:checked + .el-switch__core {
  background: #44b549;
  border-color: #44b549;
}

/* 修改月，年，日期下拉框大小 */

.el-month-table td,
.el-year-table td {
  padding-top: 12px;
  padding-bottom: 12px;
}

/* 下拉新增框样式 */

/* .el-cascader-menus .add-btn,
.el-autocomplete-suggestion,
.el-select-dropdown .add-btn {
    background: #57c5f7;
    color: #ffffff;
    text-align: center;
    cursor: pointer;
    line-height: 22px;
    font-size: 12px;
} */

.el-cascader-menus .add-btn {
  width: 100%;
  bottom: -22px;
  border-bottom: solid 1px rgb(209, 215, 229);
  border-left: solid 1px rgb(209, 215, 229);
  border-right: solid 1px rgb(209, 215, 229);
  left: -1px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.el-checkbox__input.is-checked .el-checkbox__inner.el-checkbox__minus::after {
  -ms-transform: rotate(90deg) scaleY(1);
  transform: rotate(90deg) scaleY(1);
}

.el-checkbox__inner.el-checkbox__minus::after {
  width: 0px;
  left: 7px;
  top: 1px;
}

.display-none,
.hide {
  display: none !important;
}

.pointer {
  cursor: pointer;
}

.el-table tr.table-row-highlight td {
  background: #efbc66 !important;
}

.el-table tr.row-warning td,
.el-table tr.row-warning td .editCol,
.el-table tr.row-warning td .el-input__inner {
  color: red !important;
}

.el-scrollbar__bar {
  /* opacity: 1 !important; */
  /* z-index: 8; */
}

/* .el-scrollbar__bar.is-vertical {
    left: 100%;
    width: 10px;
} */

/* .el-scrollbar__bar.is-horizontal {
  top: 100%;
  height: 10px;
} */

/* .el-scrollbar__thumb {
  background-color: rgba(150, 150, 150, 0.3);
}

.el-scrollbar__thumb:hover {
  background-color: rgba(150, 150, 150, 0.7);
} */

.modifiedSign:after {
  display: inline-block;
  content: '';
  position: absolute;
  top: -3px;
  right: 0;
  width: 0;
  height: 0;
  border-right: 5px solid red;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  transform: rotate(-225deg);
}
td.row-warning {
  color: red !important;
}
td.total-row {
  font-weight: bold;
  background: #fffadd !important;
  border-top: 1px solid #f5e29d !important;
  border-bottom: 1px solid #f5e29d !important;
}
.el-table tr.row-bold td,
td.row-bold {
  font-weight: bold;
}

td.leftText:not(.el-table-column--selection) {
  text-align: left !important;
}

.no-header.el-tabs .el-tabs__header {
  display: none;
}

.el-notification.voucherNotification {
  right: 20px;
  box-shadow: none;
  border: 1px solid #666;
}

/* .el-notification__title {
    margin-top: 10px;
} */

.notificationText {
  color: rgb(31, 40, 61);
}

.autoCloseTip {
  color: red;
}

.autoCloseTip,
.btn_area {
  margin-top: 10px;
  text-align: right;
}

.el-icon-browse,
.el-icon-noBrowse {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.el-icon-browse:before {
  content: '\E6E1';
}

.el-icon-noBrowse:before {
  content: '\E6AB';
}

.button_bubble {
  font: 15px 微软雅黑, Arial, sans-serif;
  /* 半透明的文本阴影 */
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.4);
  /* 重置默认的下划线样式 */
  text-decoration: none !important;
  white-space: nowrap;
  /* display: inline-block; */
  vertical-align: baseline;
  position: relative;
  cursor: pointer;
  padding: 10px 20px;
  background-repeat: no-repeat;
  /* 当浏览器不支持多背景时 */
  background-position: bottom left;
  background-image: url('./imgs/button_bg.png');
  /* 具有多个属性值的 CSS3 背景定位 */
  background-position: bottom left, top right, 0 0, 0 0;
  background-clip: border-box;
  /* 应用默认边框 8px */
  /* -moz-border-radius: 8px;
  -webkit-border-radius: 8px; */
  /* border-radius: 8px; */
  /* 按钮内部 */
  -moz-box-shadow: 0 0 1px #fff inset;
  -webkit-box-shadow: 0 0 1px #fff inset;
  box-shadow: 0 0 1px #fff inset;
  /* 使用CSS3对背景位置进行动画处理 （并不是所有浏览器都支持）*/
  -webkit-transition: background-position 2s;
  -moz-transition: background-position 2s;
  -o-transition: background-position 2s;
  transition: background-position 2s;
}

.blue.button_bubble {
  color: #0f4b6d !important;
  /* border: 1px solid #84acc3 !important; */
  /* 回退时的颜色 */
  background-color: #48b5f2;
  /* 不同浏览器支持的规则 */
  background-image: url('./imgs/button_bg.png'),
    -moz-radial-gradient(center bottom, circle, rgba(89, 208, 244, 1) 0, rgba(
            89,
            208,
            244,
            0
          )
          100px),
    -moz-linear-gradient(#4fbbf7, #3faeeb);
  background-image: url('./imgs/button_bg.png'),
    -webkit-gradient(radial, 50% 100%, 0, 50% 100%, 100, from(rgba(89, 208, 244, 1)), to(rgba(89, 208, 244, 0))),
    -webkit-gradient(linear, 0% 0%, 0% 100%, from(#4fbbf7), to(#3faeeb));
}

[class*='_ul_style']:hover .blue.button_bubble {
  background-color: #63c7fe;
  background-image: url('./imgs/button_bg.png'),
    -moz-radial-gradient(center bottom, circle, rgba(109, 217, 250, 1) 0, rgba(
            109,
            217,
            250,
            0
          )
          100px),
    -moz-linear-gradient(#63c7fe, #58bef7);
  background-image: url('./imgs/button_bg.png'),
    -webkit-gradient(radial, 50% 100%, 0, 50% 100%, 100, from(rgba(109, 217, 250, 1)), to(rgba(109, 217, 250, 0))),
    -webkit-gradient(linear, 0% 0%, 0% 100%, from(#63c7fe), to(#58bef7));
}

.green.button_bubble {
  color: #0f4b6d !important;
  /* border: 1px solid #84acc3 !important; */
  /* 回退时的颜色 */
  background-color: #48b5f2;
  /* 不同浏览器支持的规则 */
  background-image: url('./imgs/button_bg.png'),
    -webkit-gradient(radial, 50% 100%, 0, 50% 100%, 100, from(rgba(162, 211, 30, 1)), to(rgba(162, 211, 30, 0))),
    -webkit-gradient(linear, 0% 0%, 0% 100%, from(#82cc27), to(#74b317));
}

[class*='_ul_style']:hover .green.button_bubble {
  background-color: #63c7fe;
  background-image: url('./imgs/button_bg.png'),
    -webkit-gradient(radial, 50% 100%, 0, 50% 100%, 100, from(rgba(162, 211, 30, 1)), to(rgba(162, 211, 30, 0))),
    -webkit-gradient(linear, 0% 0%, 0% 100%, from(#82cc27), to(#74b317));
}

.red.button_bubble {
  color: #0f4b6d !important;
  /* border: 1px solid #84acc3 !important; */
  /* 回退时的颜色 */
  background-color: #48b5f2;
  /* 不同浏览器支持的规则 */
  background-image: url('./imgs/button_bg.png'),
    -webkit-gradient(radial, 50% 100%, 0, 50% 100%, 100, from(rgba(255, 0, 0, 1)), to(rgba(255, 0, 0, 0))),
    -webkit-gradient(linear, 0% 0%, 0% 100%, from(#ff3003), to(#ff2030));
}

[class*='_ul_style']:hover .red.button_bubble {
  background-color: #63c7fe;
  background-image: url('./imgs/button_bg.png'),
    -webkit-gradient(radial, 50% 100%, 0, 50% 100%, 100, from(rgba(255, 0, 0, 1)), to(rgba(255, 0, 0, 0))),
    -webkit-gradient(linear, 0% 0%, 0% 100%, from(#ff3003), to(#ff2030));
}

.orange.button_bubble {
  color: #693e0a !important;
  background-color: #e38d27;
  background-image: url('./imgs/button_bg.png'),
    -webkit-gradient(radial, 50% 100%, 0, 50% 100%, 100, from(rgba(232, 189, 45, 1)), to(rgba(232, 189, 45, 0))),
    -webkit-gradient(linear, 0% 0%, 0% 100%, from(#f1982f), to(#d4821f));
}

[class*='_ul_style']:hover .orange.button_bubble {
  background-color: #ec9732;
  background-image: url('./imgs/button_bg.png'),
    -webkit-gradient(radial, 50% 100%, 0, 50% 100%, 100, from(rgba(241, 192, 52, 1)), to(rgba(241, 192, 52, 0))),
    -webkit-gradient(linear, 0% 0%, 0% 100%, from(#f9a746), to(#e18f2b));
}

.el-loading-mask.is-fullscreen .el-loading-spinner {
  box-shadow: 0px 0px 30px 5px rgba(43, 43, 43, 0.2);
  width: 220px;
  height: 100px;
  background: rgba(63, 65, 70, 0.7);
  position: absolute;
  top: 50%;
  margin-top: -50px;
  left: 50%;
  margin-left: -150px;
  border-radius: 6px;
}

.is-fullscreen.is-fullscreen .el-loading-spinner svg.circular {
  height: 30px;
  width: 30px;
  margin-top: 15px;
}

.is-fullscreen.is-fullscreen .el-loading-spinner svg.circular .path {
  animation: loading-dash 1s ease-in-out infinite;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-width: 6;
  stroke: #fff;
  stroke-linecap: round;
}

.is-fullscreen .el-loading-spinner p {
  height: 35px;
  line-height: 35px;
  color: #fff;
}

/* JDBG165 涉及报表和账本所有的表头都要居中 */

/* .el-table .el-table__header-wrapper thead tr th .cell,
.el-table__fixed .el-table__fixed-header-wrapper thead tr th .cell {
  text-align: center;
} */

/* 固定列 阴影效果去除 */

.el-table__fixed {
  box-shadow: none;
}

/* .el-pagination .el-pagination__total:after {
  content: "记录";
} */

/* .el-pagination__sizes .el-input .el-input__inner {
  font-size: 12px;
  vertical-align: top;
} */

.iconfont.icon-bofang {
  font-size: 22px !important;
  vertical-align: middle !important;
  color: #728299;
}

.iconfont.icon-bofang + span {
  font-size: 12px;
  vertical-align: middle;
  color: #728299;
}

.videohelp:hover .iconfont.icon-bofang,
.videohelp:hover .iconfont.icon-bofang + span {
  color: #ff5629;
}

/* 列表切换 */

.list-toggle {
  padding: 0 0 5px 0;
}

.list-toggle .el-button .iconfont {
  font-size: 14px;
}

.el-button.el-button--expect {
  color: #fff;
  background-image: linear-gradient(to right, #00b9f5, #0080ee);
  border-color: rgb(32, 160, 255);
}

.el-button.icon-button {
  width: 26px;
  height: 26px;
  line-height: 26px;
  background: #88cc48;
  color: #fff;
  font-size: 16px;
  padding: 0;
  text-align: center;
}

.el-icon-arrow-down {
  display: inline-block;
  font-family: FontAwesome !important;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}

.el-icon-arrow-down:before {
  content: '\F107';
}

.el-icon-arrow-up {
  display: inline-block;
  font-family: FontAwesome !important;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}

.el-icon-arrow-up:before {
  content: '\F106';
}

/* 针对新版的CSS开始　*/

.el-date-editor .el-range-separator {
  line-height: 22px;
}

.el-table:not(.pingzheng) {
  border-color: #cfdbe2;
  border-top-width: 1px;
}

.el-table:not(.pingzheng):before,
.el-table:not(.pingzheng):after {
  background-color: #ddd;
}

/* .el-table:not(.pingzheng) th {
  height: 30px;
  background-color: #eeeeee;
  border-color: #ddd;
} */

.el-table:not(.pingzheng) .caret-wrapper {
  height: 24px;
  vertical-align: middle;
}

.el-table:not(.pingzheng) .caret-wrapper .sort-caret.ascending {
  top: 1px;
}

.el-table:not(.pingzheng) .caret-wrapper .sort-caret.descending {
  bottom: 1px;
}

/*.el-table:not(.pingzheng) th>.cell, .el-table th>div {
  line-height: 24px;
} 
.el-table:not(.pingzheng) td {
  border-color: #d4d4d4;
  border-color: #dedede;
}
.el-table:not(.pingzheng) td {
  border-color: #d4d4d4;
  border-color: #dedede;
  vertical-align: bottom;
} */

.el-table:not(.pingzheng) td[rowspan]:not([rowspan='1']) {
  vertical-align: middle;
}

/* .el-table:not(.pingzheng) td>.cell {
  min-height: 22px;
  line-height: 1;
  box-sizing: border-box;
}
.el-table:not(.pingzheng) td:not(.el-table-column--selection)>.cell {
  padding: 6px 10px 3px 3px;
}
.el-table:not(.pingzheng) td.el-table-column--selection>.cell {
  line-height: 22px;
}
.el-table:not(.pingzheng) th>.cell .el-checkbox__inner,
.el-table:not(.pingzheng) td>.cell .el-checkbox__inner {
  width: 16px;
  height: 16px;
}
.el-table:not(.pingzheng) th>.cell .el-checkbox__inner:after,
.el-table:not(.pingzheng) td>.cell .el-checkbox__inner:after {
  top: 1px;
  height: 10px;
  left: 4px;
} */

.el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: #edf6fd !important;
}

.el-table:not(.pingzheng) .el-table__body-wrapper tbody tr:first-child td {
  border-top: none;
}

.toolbar .el-tabs {
  margin-right: 12px;
}
td.money-col {
  color: #b07635;
}
td.minus-money-col {
  color: #f56c6c;
}
/* .toolbar ~ .el-table .cell,
.toolbar ~ .el-table .cell > span {
  font-family: 'MicrosoftYaHei';
} */

/* .el-scrollbar__bar.is-horizontal {
    left: 0;
}

.el-scrollbar__bar.is-vertical {
    top: 0;
} */

.editCol + .editCol {
  margin-left: 5px;
}

.el-card.left-form .el-card__header {
  height: 50px;
}

.el-card {
  border-radius: 0px;
}

/* .el-scrollbar__thumb {
    background-color: rgba(170, 170, 170);
    border-radius: 0;
}

.el-scrollbar__thumb:hover,
.el-scrollbar__thumb:active {
    background-color: rgba(200, 200, 200);
    border-radius: 0;
} */

/* .el-dropdown-menu .el-dropdown-menu__item.is-disabled {
    color: #767676;
    cursor: not-allowed;
    pointer-events: auto;
} */

.el-radio + .el-radio {
  margin-left: 16px;
}

/* .el-table tr { background-color:#f1f3f4 } */

.el-table__row--striped td {
  background-color: #f1f3f4;
}

/* 账套列表表格要求采用白色底 */

.el-table.white-table .el-table__row--striped td {
  background-color: #fff;
}

.el-table.white-table tr {
  background-color: #fff;
}

a.hover {
  text-decoration: underline !important;
}

.el-pager li {
  font-size: 12px;
}

.el-pager li.active {
  color: #89010e;
}

/* 针对新版的CSS结束　*/

/* 弹出框的导入步骤标题 */

.dialog-body .step-title {
  font-weight: bold;
  background-color: #ff5629;
  color: white;
  height: 40px;
  line-height: 40px;
  font-size: 18px;
  padding-left: 10px;
}

/* 让文字一行显示，出现省略号，注意还需设置宽度和高度 */

.ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

/* 进销项列表为空时的显示样式 */

.el-table .el-table__empty-text.style-1 {
  font-size: 14px;
  line-height: 26px;
  color: #888;
}

.introjs-fixParent {
  z-index: 100000000 !important;
  opacity: 1 !important;
  -webkit-transform: none !important;
  transform: none !important;
  /* position: relative !important; */
}

.introjs-relativePosition,
tr.introjs-showElement > td,
tr.introjs-showElement > th {
  position: relative;
  box-shadow: 0px 0px 18px yellow;
}

.introjs-helperLayer {
  display: none;
}

.introjs-tooltip {
  /* border: 2px dashed #fff; */
  padding: 20px;
  width: 600px;
  max-width: 600px;
  background: #fff;
  color: #1285dd;
}

.introjs-button {
  color: #1285dd !important;
}

.introjs-nextbutton {
  color: #1285dd !important;
}

.introjs-arrow.left,
.introjs-arrow.left-bottom {
  left: -20px;
  border-color: transparent #fff transparent transparent;
}

.introjs-button:hover {
  color: #66ccff !important;
}

.introjs-tooltiptext {
  font-size: 24px;
}

.introjs-disabled {
  display: none;
}

/* 调整表单中的行高 */

/* .el-form-item__label {
    line-height: 32px;
} */

.el-tabs--border-card .el-tabs__item.is-top.is-active {
  position: relative;
}

.el-tabs--border-card .el-tabs__item.is-top.is-active::before {
  content: '';
  width: 100%;
  height: 5px;
  background: #188ae2;
  position: absolute;
  top: -3px;
  left: 0;
  z-index: 10;
}

.el-tabs--border-card {
  border-top-color: transparent;
}

.el-tabs__item {
  height: 34px;
  line-height: 34px;
}

div.el-dropdown > button.el-dropdown-selfdefine {
  margin-left: 10px;
}
.el-button + div.el-dropdown > button {
  margin-left: 10px;
}

.lockDataDisabled {
  position: relative;
}

.lockDataDisabled > .fa-ban {
  position: absolute;
  left: 50%;
  top: 50%;
  font-size: 16px;
  z-index: 10000;
  transform: translate(-50%, -50%);
  color: red;
  margin: 0 !important;
}

#GENG_DUO_SHOU_SUO .el-date-editor--month input,
#GENG_DUO_SHOU_SUO .el-date-editor--monthrange {
  border-radius: 4px 0px 0px 4px;
}
.server_message_box {
  width: auto;
  max-height: 450px;
}
.server_message_box .el-notification__content {
  max-height: 400px;
  overflow-y: scroll;
}
#serverStatus {
  width: 500px;
  height: 500px;
  position: fixed;
  right: 40px;
  top: 40px;
  background: #13ce66;
}
.el-dialog .el-dialog__header .el-dialog__close {
  font-size: 16px;
}
.el-dialog .el-dialog__header .el-dialog__close:hover {
  color: #188ae2;
}
.showselectscroll-y .el-select-dropdown__wrap {
  margin-right: 0px !important;
}
.printBox {
  width: 900px;
}
.custom-input {
  display: inline;
}
.custom-input-number {
  width: 90px;
}
.custom-el-form h6 {
  display: inline;
  padding: 6px;
  font-weight: normal;
}
.hw-errormsg-dialog {
  word-break: break-all;
}
.hw-errormsg-dialog .el-icon-close::before {
  content: none;
}
.hw-errormsg-dialog .el-icon-close::after {
  content: '复制';
  font-size: 12px;
  color: #f39800;
  left: 20px;
}
.hw-errormsg-dialog .el-message__closeBtn {
  right: 7px;
}

/* 恢复原来的虚拟滚动条位置 */
/* .el-scrollbar__bar.is-vertical {
    left: unset;
    width: 5px;
} */

.color-block_yellow {
  cursor: pointer;
  width: 100%;
  height: 100%;
  border-radius: 6px;
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    from(#f66b09),
    to(#ffd80d)
  );
  background: linear-gradient(to right, #f66b09, #ffd80d);
  position: relative;
  overflow: hidden;
  text-align: center;
  line-height: 72px;
  font-size: 14px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  color: white;
  -webkit-box-shadow: 0px 2px 12px rgba(246, 107, 9, 0.5);
  box-shadow: 0px 2px 12px rgba(246, 107, 9, 0.5);
}

.flex-h {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: flex;
  flex-flow: nowrap row;
}

.flex-v {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: flex;
  flex-flow: nowrap column;
}

.flex-fixed {
  flex: 0 0 auto;
  box-sizing: border-box;
}

.flex-fluid {
  flex: 1 1 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.pointer {
  cursor: pointer;
}
.breakCell .cell{
  white-space: normal !important;
}
*[draggable-state] {
  user-select: text;
  -webkit-user-drag: auto;
}
.collapse-transition {
  transition: unset;
}

/* 虚拟滚动的Tree组件样式覆盖 */
.VTree .node-item .node-check input:checked + label {
  border-color: var(--color-theme-base);
  background-color: var(--color-theme-base);
}
.VTree .node-item.node-item-active {
  color: #222222;
}
.VTree .node-item .node-check input.indeterminate[type='checkbox'] + label,
.VTree .node-item .node-check input[type='checkbox']:indeterminate + label {
  border-color: var(--color-theme-base);
  background-color: var(--color-theme-base);
}
