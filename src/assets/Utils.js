/*
 * @Description:
 * @version:
 * @Company: 海闻软件
 * @Author: 启旭
 * @Date: 2019-10-22 15:10:32
 * @LastEditors: 启旭
 * @LastEditTime: 2020-05-08 15:47:32
 */

import { get, isArray, isObject, isString } from 'lodash-es';
import cloneDeep from 'lodash-es/cloneDeep';
import chunk from 'lodash-es/chunk';
import isEqual from 'lodash-es/isEqual';
export {default as md5} from '@/utils/md5';
export {default as encryptByDES} from '@/utils/des';
/**
 *检查提交数据是否更改
 *
 * @param {*} judge 需要检查的字段
 * @param {*} source 原数据
 * @param {*} target 需要提交的数据
 * @returns
 */
export function checkIfAjaxDataModified(judge, source, target) {
  function isModified(obj1, obj2, keys = []) {
    const {
      toString,
    } = Object.prototype;
    if (toString.call(obj1) !== toString.call(obj2)) {
      return true;
    }
    if (obj1 instanceof Object) {
      if (obj1 === obj2) {
        return false;
      }
      if (obj1 instanceof Date) {
        return obj1.Format('yyyy-MM-dd') !== obj2.Format('yyyy-MM-dd');
      }
      if (typeof keys === 'string') { keys = [keys]; }
      if (obj1 instanceof Array) {
        if (obj1.length !== obj2.length) {
          return true;
        }
        if (keys.length === 0) {
          return !isEqual(obj1, obj2);
        }
        for (let i = 0; i < obj1.length; i += 1) {
          if (keys.some((key) => isModified(obj1[i][key], obj2[i][key]))) {
            return true;
          }
        }
        return false;
      }
      return keys.some((key) => isModified(obj1[key], obj2[key]));
    }
    return obj1 !== obj2;
  }

  const {
    impact,
    others,
  } = judge;
  const checkArr = [impact, others];
  const {
    toString,
  } = Object.prototype;
  for (let i = 0; i < checkArr.length; i += 1) {
    const checkItem = checkArr[i];
    if (toString.call(checkItem).indexOf('Object') > -1) {
      const modifyState = checkIfAjaxDataModified(checkItem, source, target);
      if (modifyState) {
        return modifyState;
      }
    }
    if (checkItem instanceof Array) {
      const strArr = [];
      const objArr = [];
      for (let j = 0; j < checkItem.length; j += 1) {
        const childItem = checkItem[j];
        if (typeof childItem === 'string') {
          strArr.push(childItem);
        } else {
          objArr.push(childItem);
        }
      }
      if (strArr.length && isModified(source, target, strArr)) {
        return 1;
      }
      for (let z = 0; z < objArr.length; z += 1) {
        const modifyState = Object.entries(objArr[z])
          .some(
            ([subKey, subValue]) => checkIfAjaxDataModified(
              subValue,
              source[subKey],
              target[subKey],
            ),
          );
          if (Number(modifyState)) {
            return Number(modifyState);
          }
      }
    }
  }
  return 0;
}
/**
 *对象数组根据对象的某个属性去重。
 *
 * @param {Array} [array=[]]
 * @param {string} attribute
 * @returns
 */
export function arrayDuplicateByAttr(array = [], attribute) {
  return array.filter((item, index, arr) => {
    const isSame = arr.findIndex((innerItem) => innerItem[attribute] === item[attribute]) === index;
    return isSame;
  });
}
/**
 *科学计数法转数值字符串显示
 *
 * @export
 * @param {Number | String} num
 * @returns {String}
 */
export function toNonExponential(num = '') {
  num = Number(num);
  const m = num.toExponential().match(/\d(?:\.(\d*))?e([+-]\d+)/);
  return num.toFixed(Math.max(0, (m[1] || '').length - m[2]));
}
/**
 * 修复 0.355.toFixed(2) 四舍五入错误
 *
 * @param {Number} num
 * @param {Number} s -保留多少位小数
 * @returns
 */
export function toFixed(num, s) {
  const times = 10 ** s;
  let des = num * times + 0.5;
  des = parseInt(des, 10) / times;
  return `${des}`;
}
/**
 *  加载spreadsheet的样式文件
 *
 * @returns
 */
export function setSpreadSheetCss() {
  if (document.getElementById('kendoCss')) {
    return;
  }
  const head = document.getElementsByTagName('HEAD').item(0);
  const style = document.createElement('link');
  style.href = '/static/lib/kendoUI/kendo.common.min.css';
  style.rel = 'stylesheet';
  style.type = 'text/css';
  style.id = 'kendoCss';
  head.appendChild(style);
  const style1 = document.createElement('link');
  style1.href = '/static/lib/kendoUI/kendo.default.min.css';
  style1.rel = 'stylesheet';
  style1.type = 'text/css';
  head.appendChild(style1);
}

/**
 * 给数组分块
 *
 * @param {Array} array
 * @param {number} [size=100]
 * @returns
 */
export function makeChunk(array, size = 100) {
  return chunk(array, size);
}

/**
 * 分时函数
 * @param {Array} ary
 * @param {Function} fn
 * @param {Number} count
 */
export function timeChunk(ary, fn, count) {
  let t;
  const start = () => {
    for (let i = 0; i < Math.min(count || 1, ary.length); i += 1) {
      const obj = ary.shift();
      fn(obj);
    }
  };
  return () => {
    t = setInterval(() => {
      if (ary.length === 0) { // 如果全部节点都已经被创建好
        clearInterval(t);
      }
      start();
    }, 200); // 分批执行的时间间隔，也可以用参数的形式传入
  };
}
/**
 *获取vue最高级root父组件
 *
 * @export
 * @param {*} t
 * @returns
 */
export function getRootInstance(t) {
  let $root = t;
  while ($root.$parent) {
    $root = $root.$parent;
  }
  return $root;
}
/**
 *对象数组根据某个属性值去重
 *
 * @export
 * @param {*} [array=[]]
 * @param {string} [keyName='']
 * @returns
 */
export function getUnequallyObjArray(array = [], keyName = '') {
  const keyValueArray = [];
  const r = [];
  array.forEach((item) => {
    if (keyValueArray.indexOf(item[keyName]) === -1) {
      r.push(item);
    }
    keyValueArray.push(item[keyName]);
  });
  return r;
}
/**
 *数字金额转中文大写金额
 *
 * @export
 * @param {Number} num
 * @returns
 */
export function transformMoney(num) {
  if (/^[+-]?\d+(\.\d+)?$/.test(num)) {
    let tempNum = parseInt((100 * parseFloat(Math.abs(num), 10)).toFixed(0), 10);
    const chineseNum = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const measurementUnit = ['分', '角', '元', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿', '拾', '佰', '仟', '万'];

    if (tempNum === 0) {
      return '人民币零元整';
    }

    if (String(tempNum).length <= measurementUnit.length) {
      let remainder;
      let unitIndex = 0;
      const transformNumArr = [];

      while (tempNum) {
        remainder = tempNum % 10;
        transformNumArr.push(measurementUnit[unitIndex]);
        unitIndex += 1;
        transformNumArr.push(chineseNum[remainder]);
        tempNum = parseInt(tempNum / 10, 10);
      }
      return `人民币${num < 0 ? '负' : ''}${transformNumArr.reverse().join('').replace(/(零[拾佰仟角])+/g, '零')
        .replace(/零+([亿万元])/g, '$1')
        .replace(/零{1,2}分/, (word) => (word.length == 2 ? '' : '整'))}`;
    }
  }
  return num;
}
/**
 * 搜狗不支持打印预览
 *
 * @export
 * @returns Boolean
 */
export function printWarning() {
  if (navigator.userAgent.toLowerCase().indexOf('se 2.x') > -1) {
    return true;
  }
  return false;
}
/**
 *打印局部内容
 *
 * @export
 * @param {string} [content=''] 需要打印的内容的innerHTML
 * @param {boolean} [ifRender=false]
 * @param {boolean} [ifPrint=true]
 * @param {*} width
 * @param {*} height
 */
export function printContent(content = '', ifRender = false, ifPrint = true, width, height) {
  let printIframe = document.getElementById('printIframe');
  if (!printIframe) {
    printIframe = document.createElement('iframe');
    printIframe.setAttribute('id', 'printIframe');
    printIframe.setAttribute('class', 'print');

    printIframe.setAttribute('height', `${height}px`);

    document.body.appendChild(printIframe);
  }
  printIframe.setAttribute('width', `${810}px`);
  if (ifRender) {
    const iframeHead = document.head.cloneNode(true);
    const iframeDoc = printIframe.contentWindow.document;
    Array.from(iframeHead.childNodes).forEach((node) => {
      if (node.tagName === 'SCRIPT') {
        iframeHead.removeChild(node);
      }
    });
    iframeDoc.open();
    iframeDoc.write(`<head>
              <link href="/static/commonPrintStyle.css" rel="stylesheet">
              <style>
              .box[style] {
                  width: 100% !important;
                  font-size: 14px;
                  text-align: center;
              }
              .text-center {
                text-align: center;
              }
              .highlight {
                font-weight: bold;
              }
              .fr{
                  float:right
              }
              .fl{
                  float:left
              }
              .el-col-8 {
                  width: 33.33333%;
              }
              .el-col-1, .el-col-2, .el-col-3, .el-col-4, .el-col-5, .el-col-6, .el-col-7, .el-col-8, .el-col-9, .el-col-10, .el-col-11, .el-col-12, .el-col-13, .el-col-14, .el-col-15, .el-col-16, .el-col-17, .el-col-18, .el-col-19, .el-col-20, .el-col-21, .el-col-22, .el-col-23, .el-col-24 {
                  float: left;
                  box-sizing: border-box;
              }
              .clearfix:after {
                content: "";
                display: block;
                height: 0;
                visibility: hidden;
                clear: both
              }
              .el-table{
                  width:100%  !important;
                  border-top: 0 !important;
              }
              .clearfix {
                *zoom: 1
              }
              .el-row {
                  position: relative;
                  box-sizing: border-box;
              }
              .expand-trigger>div {
                  height: 0 !important;
              }
              .printFlooter {
                  padding-top: 10px;
                  text-align: left;
                }
              .pageNumberClass {
                position:absolute;
                right:0px;
              }
                  @page :pseudo-class {
                      size: A4 landscape;
                      margin:2cm;
                  }
                  .box .el-table td, .box .el-table th {
                      font-family:"宋体";
                      height: 22px;
                      border-right: 1px solid #000 !important;
                      border-bottom: 1px solid #000 !important;
                      background: transparent;
                      word-break: break-all;
                  }
                  .box .el-table .printStyle td, .box .el-table .printStyle th {
                      font-weight: 800 !important;
                  }
                  .box .el-table.table_tr_height17 td, .box .el-table.table_tr_height17 th {
                    height: 17px;
                    border-right: 1px solid #000 !important;
                    border-bottom: 1px solid #000 !important;
                    background: transparent;
                    word-break: break-all;
                  }
                  .box .el-table.table_tr_height21 td, .box .el-table.table_tr_height21 th {
                    height: 21px;
                    border-right: 1px solid #000 !important;
                    border-bottom: 1px solid #000 !important;
                    background: transparent;
                    word-break: break-all;
                  }
                  .box .el-table.table_tr_height22 td, .box .el-table.table_tr_height22 th {
                    height: 22px;
                    border-right: 1px solid #000 !important;
                    border-bottom: 1px solid #000 !important;
                    background: transparent;
                    word-break: break-all;
                  }
                  .box .el-table.table_tr_height24 td, .box .el-table.table_tr_height24 th {
                    height: 24px;
                    border-right: 1px solid #000 !important;
                    border-bottom: 1px solid #000 !important;
                    background: transparent;
                    word-break: break-all;
                  }
                  .box .el-table.table_tr_height26 td, .box .el-table.table_tr_height26 th {
                    height: 26px;
                    border-right: 1px solid #000 !important;
                    border-bottom: 1px solid #000 !important;
                    background: transparent;
                    word-break: break-all;
                  }
                  .box .el-table.table_tr_height27 td, .box .el-table.table_tr_height27 th {
                    height: 27px;
                    border-right: 1px solid #000 !important;
                    border-bottom: 1px solid #000 !important;
                    background: transparent;
                    word-break: break-all;
                  }
                  .box .el-table.table_tr_height29 td, .box .el-table.table_tr_height29 th {
                    height: 29px;
                    border-right: 1px solid #000 !important;
                    border-bottom: 1px solid #000 !important;
                    background: transparent;
                    word-break: break-all;
                  }
                  .box .el-table.table_tr_height30 td, .box .el-table.table_tr_height30 th {
                    height: 30px;
                    border-right: 1px solid #000 !important;
                    border-bottom: 1px solid #000 !important;
                    background: transparent;
                    word-break: break-all;
                  }
                  .box .el-table.table_tr_height31 td, .box .el-table.table_tr_height31 th {
                    height: 31px;
                    border-right: 1px solid #000 !important;
                    border-bottom: 1px solid #000 !important;
                    background: transparent;
                    word-break: break-all;
                  }
                  .box .el-table.table_tr_height34 td, .box .el-table.table_tr_height34 th {
                    height: 34px;
                    border-right: 1px solid #000 !important;
                    border-bottom: 1px solid #000 !important;
                    background: transparent;
                    word-break: break-all;
                  }
                  .box .el-table.table_tr_height37 td, .box .el-table.table_tr_height37 th {
                    height: 37px;
                    border-right: 1px solid #000 !important;
                    border-bottom: 1px solid #000 !important;
                    background: transparent;
                    word-break: break-all;
                  }

                  .highlightStyle {
                    font-weight: 800;
                  }
                  .textCenter {
                    text-align: center;
                  }
                  .el-table th.is-center, .el-table td.is-center {
                      text-align: center;
                  }
                  .el-table th.is-right, .el-table td.is-right {
                      text-align: right;
                  }
                  .el-table .el-table__header-wrapper th.is-right, .el-table .el-table__header-wrapper td.is-right{
                    text-align: center;
                  }
                  .box .el-table.table_tr_height35 .cell, .box .el-table.table_tr_height35 .cell span{
                    font-size: 16px;
                  }
                  .box .el-table.table_tr_height37 .cell, .box .el-table.table_tr_height37 .cell span{
                    font-size: 16px;
                  }
                  .box .el-table.table_tr_height31 .cell, .box .el-table.table_tr_height31 .cell span{
                    font-size: 16px;
                  }
                  .cell, .cell span {
                      font-size: 12px;
                  }
                  .cell {
                      width: 100% !important;
                      padding-left: 2px;
                      padding-right: 2px;
                      box-sizing: border-box;
                  }
                  body {
                      background: none !important;
                      background: transparent !important;
                  }
                  .A4{
                      page-break-before: auto;
                      page-break-after: always;
                      position: relative;
                  }
                  .el-table__body-wrapper .el-scrollbar__view{
                      min-height: 0 !important;
                  }
                  /*.el-table th,
                  .el-table__header-wrapper thead div {
                      background: none;
                  }*/

                  .el-table__header-wrapper .el-table__header {
                      border-top: 1px solid;
                  }
                  .el-table .el-table__header-wrapper table[style],
                  .el-table .el-table__body-wrapper table[style] {
                      /* width: 100% !important; */
                  }
                  .el-table--enable-row-hover .el-table__body tr>td.col-hidden {
                      display: none;
                  }

                  .el-table .el-table__header-wrapper table col[name^="el-table"][width],
                  .el-table .el-table__body-wrapper table col[name^="el-table"][width],
                  .el-table .el-table__body-wrapper .el-table__empty-block[style] {
                    /* width: auto !important; */
                  }
                  .el-table{
                      border: none;
                      border-left: 1px solid #000;
                  }

                  .el-table > div[style], .el-table .el-table__body-wrapper .el-table__empty-block[style] {
                      height: auto !important;
                      overflow: visible !important;
                      margin-bottom: 7px;
                  }

                  .subAccountTable{ margin-top: 15px;  margin-bottom: 15px; }
                  .subAccountTable .noBorder{
                      border:none;
                      box-shadow:0;
                      height:100%;
                      width:100%;
                      display:block;
                      box-sizing: border-box;
                  }
                  .subAccountTable td{ border-right: 1px solid rgb(236, 223, 223); border-bottom: 1px solid rgb(236, 223, 223); }
                  .subAccountTable td .el-row.xiahuaxian .el-col{  min-width:0 !important; }
                  .subAccountTable td .el-row.xiahuaxian .el-col span{ text-decoration:underline; }
                  .subAccountTable td .el-row.xiahuaxian .el-input{ width: 60%; }
                  .subAccountTable td .el-row.xiahuaxian .el-input input{
                      width: 100%;
                      height: auto;
                      border:none;
                      border-bottom: 1px solid #000;
                      background: #fff;
                      padding: 0;
                      box-shadow:none;
                      outline: none;
                  }
                  .subAccountTable td .el-row.xiahuaxian .el-input input[disabled] { background: transparent; }
                  .subAccountTable tr td{ height:30px; padding:0; }
                  .subAccountTable tr td .cell{ height:30px; }
                  .subAccountTable .unChangableCell { background-color: rgb(238, 240, 246) !important;  }
                  .subAccountTable .thTop{ height:50%; padding: 6px 0; border-bottom: 1px solid rgb(236, 223, 223); }
                  .subAccountTable .unitTd, .subAccountTable  .unitTd .cell{  padding:0; }
                  .subAccountTable .moneyUint{ height:50%; width: 199px; background-image: url(/static/imgs/1410MoneyUint.png) !important;}
                  .subAccountTable .moneyUintTd{ height: 30px; overflow: hidden; background-image: url(/static/imgs/1410MoneyUint.png) !important; background-repeat-y: repeat;}
                  .subAccountTable .editInput{ display:none; height:100%; }
                  .subAccountTable .moneyUint span{
                      float: left;
                      display: inline-block;
                      width: 17px;
                      height: 100%;
                      margin-right: 1px;
                      background-color: #fff;
                      text-align: center;
                      font-size: 12px;
                  }
                  .subAccountTable .moneyUint span.first{width: 16px !important;margin-left:2px}
                  .subAccountTable .moneyUint span.last{width: 16px !important;}
                  .subAccountTable .currency .currency_content { height: 26px; margin-top: -1px;}
                  .subAccountTable .currency .currency_content .el-select .el-input { width: 70px; }
                  .subAccountTable .currency .currency_content .el-select .el-input .el-input__inner { cursor: pointer; }
                  .subAccountTable .currency .currency_content label { float: left; width: 71px; display: inline-block; white-space: nowrap; text-align: right;}
                  .subAccountTable .currency .currency_content label + * { float: left; width: calc("100% - 71px"); }
                  .subAccountTable .noTooltip .cell{  white-space: inherit; }
                  .subAccountTable .moneyUintTd span{
                      font-family: 'tahoma';
                      position: relative;
                      display: block;
                      height:30px;
                      line-height: 30px;
                      font-weight: bold;
                      right:-5px;
                      font-size: 14px;
                      letter-spacing: 11px;
                      overflow: hidden;
                      text-align: right;
                      /* border-right: 1px solid yellow; */
                      }
                  .subAccountTable .moneyUintTd span.redColor{
                      color:red;
                      }
                  /*  */

              </style>
          </head>`);
    iframeDoc.close();
  }
  if (content) printIframe.contentDocument.body.innerHTML = content;
  if (printIframe.contentDocument.body.getElementsByTagName('th')[0]) {
    printIframe.contentDocument.body.getElementsByTagName('th')[0].width = 40;
  }
  if (ifPrint) printIframe.contentWindow.print();
  // $(printIframe).remove();
}
/**
 *兼容性click下载事件
 *
 * @export
 * @param {*} element
 */
export function invokeClick(element, encodeFlag = true) {
  const href = element.getAttribute('href')
  // 打印导出的url方便查看问题
  console.log(href, '======导出url')
  if(href && encodeFlag) {
    element.setAttribute('href', encodeURI(href));
  }
  element.setAttribute('target', '_blank');
  if (element.fireEvent) {
    element.fireEvent('onclick');
  } else if (document.createEvent) {
    const event = document.createEvent('MouseEvents');
    event.initEvent('click', true, true);
    element.dispatchEvent(event);
  } else if (element.click) {
    element.click();
  }
}

/**
 * 将指定文本添加到剪贴板
 *
 * @export
 * @param {string} message
 */
export function copyToClipBoard(message) {
  const oInput = document.createElement('input');
  oInput.value = message;
  document.body.appendChild(oInput);
  oInput.select();
  document.execCommand('Copy');
  oInput.style.display = 'none';
  oInput.remove();
}
/**
 *选中输入框的文本
 *
 * @param {*} target
 * @param {*} aFParam
 * @param {*} aSParam
 * @returns
 */
export function selectText(target, aFParam, aSParam) {
  const fParam = +aFParam;
  const sParam = +aSParam;
  if (target.createTextRange) {
    const range = target.createTextRange();
    range.moveStart('character', fParam || 0);
    range.moveEnd('character', Number.isNaN(sParam) ? 1 : sParam);
    return range.select();
  }
  // 谷歌 type为number时不支持这个方法
  if (target.setSelectionRange && target.type !== 'number') {
    target.focus();
    try {
      return target.setSelectionRange(fParam || 0, Number.isNaN(sParam)
        ? target.value.length : sParam);
    } catch (e) {
      console.error(e);
    }
  }
  return target.select && target.select();
}
/**
 *合计金额规范
 *
 * @param {*} value
 * @param {number} [fixedNum=2]
 * @returns
 */
export function formatMoney(value, fixedNum = 2) {
  if (Infinity === value) {
    return '0';
  }

  if (/^[+-]?\d+(\.\d+)?$/.test(value) || /^[+-]?[1-9](\.\d+)?[Ee][+-]?\d+$/.test(value)) {
    return parseFloat(value).toFixed(fixedNum).replace(/(\d{1,2})(?=(\d{3})+\.)/g, '$1,');
  }
  return value;
}
/**
 *日期格式化方法
 *
 * @export
 * @param {Date} content -时间对象
 * @param {String} fmt -格式化模板 yyyy-MM-dd
 * @returns {String}
 */
export function formatDate(content, fmt) {
  const o = {
    'M+': content.getMonth() + 1, // 月份
    'd+': content.getDate(), // 日
    'h+': content.getHours(), // 小时
    'm+': content.getMinutes(), // 分
    's+': content.getSeconds(), // 秒
    'q+': Math.floor((content.getMonth() + 3) / 3), // 季度
    S: content.getMilliseconds(), // 毫秒
  };
  if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (`${content.getFullYear()}`).substr(4 - RegExp.$1.length));
  Object.entries(o).map(([key, value]) => {
    if (new RegExp(`(${key})`).test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (value) : ((`00${value}`).substr((`${value}`).length)));
    }
    return value;
  });
  return fmt;
}
/**
 *报表  金额为0时 为空
 *
 * @param {*} val
 * @returns
 */
export function zeroMoney(row, column) {
  if(column) {
    return row[column?.property] === 0 ? '' : row[column?.property];
  }
  return row === 0 ? '' : row;
}
/**
 *
 *
 * @export
 * @param {*} value
 * @param {number} [decimalDigits=8]
 * @returns
 */
/**
 *格式化小数点位
 *
 * @export
 * @param {*} value
 * @param {number} [decimalDigits=8]
 * @returns
 */
export function formatDecimalNum(value, decimalDigits = 8) {
  // 下面注释掉的代码，直接在前面先加 zeroMoney 过滤器效果一样
  if (value == null) {
    return '';
  }
  if (Number.isNaN(value) || value === undefined || value === null) {
    return '';
  }
  value = String(value);
  // 判断一下有没有减号
  if (value.indexOf('-') > 0) {
    value = `0${String(Number(value) + 1).substr(1)}`;
    const values = value.split('.');
    const value2 = values[1].slice(0, decimalDigits);
    return values[0] + (Number(value2) === 0 ? '' : `.${value2}`);
  }
  if (value) {
    const result = /\.(\d+)/.exec(value);
    // 禅道bug 3701 生产环境，进项发票合计多了好多0
    const temp = (result && result[1].length > decimalDigits)
      ? Number(value).toFixed(decimalDigits)
      : Number(value);
    const numberFloat = Number(temp);
    const numberStr = numberFloat.toString();
    if(numberStr.match('e-')) { // 处理极小值 bug10026 (只能返回字符串显示)
      return numberFloat.toFixed(decimalDigits)
    }
    return numberFloat;
  }
  return '';
}
/**
 * 格式化小数点并且转为千分号显示
 *
 * @export
 * @param {*} value
 * @param {number} [fixedNum=2]
 * @returns
 */
export function warpFormatDecimalNum(value, fixedNum = 2) {
  let newValue = value === 0 ? 0 : Lib.Utils.formatDecimalNum(value, fixedNum);
  if (/^[+-]?\d+(\.\d+)?$/.test(newValue) || /^[+-]?[1-9](\.\d+)?[Ee][+-]?\d+$/.test(newValue)) {
    newValue = parseFloat(newValue).toString();
    return newValue.indexOf('.') > -1 ? newValue.replace(/(\d{1,2})(?=(\d{3})+\.)/g, '$1,') : newValue.replace(/(\d{1,2})(?=(\d{3})+$)/g, '$1,');
  }
  return newValue;
}

/**
 * 格式化 百分号
 *
 * @export
 * @param {*} value
 * @param {number} [decimalDigits=2]
 * @returns
 */
export function formatPercentageNum(value, fixedNum = 2) {
  if (value === null) {
    return '';
  }
  let percentageNum = Number(value);

  percentageNum = percentageNum.mul(100).toFixed(fixedNum);

  return `${percentageNum}%`;
}

export function isCharAllowedInDigitalBox(e, pattern = /[\d]/) {
  const event = window.event || e;
  const code = event.keyCode || event.which;
  if (event.ctrlKey) {
    return [99, 118].indexOf(code) > -1;
  }
  return [8, 37, 39].indexOf(code) > -1 || pattern.test(String.fromCharCode(code));
}
/**
 *节流函数
 *
 * @export
 * @param {*} fun 需要传入的方法
 * @param {number} [delay=200]
 * @returns
 */
export function throttle(fun, delay = 200) {
  let last;
  let
    deferTimer;
  return async function func(...args) {
    const that = this;
    const now = +new Date();
    if (last && now < last + delay) {
      clearTimeout(deferTimer);
      deferTimer = setTimeout(async () => {
        last = now;
        await fun.apply(that, args);
      }, delay);
    } else {
      last = now;
      await fun.apply(that, args);
    }
  };
}
/**
 * 防抖函数
 * @param {*} fun 调用函数
 * @param {*} delay 间隔时间
 */
export function debounce(fun, delay) {
  return function func(...args) {
    const that = this;
    clearTimeout(fun.id);
    fun.id = setTimeout(() => {
      fun.apply(that, args);
    }, delay);
  };
}

/**
 *根据key值获取获取数组元素
 *
 * @export
 * @param {*} Arr
 * @param {string} [keyName='keyValue']
 * @param {*} value
 * @returns
 */
export function filterArray(Arr, keyName = 'keyValue', value) {
  if (!Arr || Arr.length === 0) {
    return null;
  }
  return Arr.find((item) => item[keyName] === value);
}
/**
 *测试错误对象
 *
 * @export
 * @param {*} e
 */
export function testErrRsp(e) {
  console.error(e);
}

/**
 *删除提示的标识
 *
 * @export
 * @param {*} data
 * @returns
 */
export function setVisible(data) {
  if (data) {
    for (let i = 0; i < data.length; i += 1) {
      data[i].visible = false;
    }
  }
  return data;
}
/**
 *获取url中的参数
 *
 * @export
 * @param {*} name
 * @returns
 */
export function getUrlParam(name) {
  const search = `?${window.location.hash.split('?')[1]}`;
  const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`); // 构造一个含有目标参数的正则表达式对象
  const r = search.substr(1).match(reg); // 匹配目标参数
  if (r != null) return unescape(r[2]);
  return null; // 返回参数值
}
/**
 *深拷贝
 *
 * @export
 * @param {*} initalObj
 * @param {*} finalObj
 * @returns
 */
export const deepClone = cloneDeep;
/**
 * 判断两个对象是否相等
 * @param  {[type]} obj1
 * @param  {[type]} obj2
 * @return {[type]}
 */
export function equalObject(obj1, obj2) {
  let key;

  if (obj1 === obj2) {
    return true;
  }
  if (Object.keys(obj1).length !== Object.keys(obj2).length) {
    return false;
  }
  // eslint-disable-next-line no-restricted-syntax
  for (key in obj2) {
    if (obj1[key] instanceof Date && obj2[key] instanceof Date) {
      if (obj1[key].getTime() !== obj2[key].getTime()) {
        return false;
      }
    } else if (obj1[key] instanceof Array && obj2[key] instanceof Array) {
      if (obj1[key].length !== obj2[key].length) {
        return false;
      }
      // eslint-disable-next-line no-loop-func
      if (obj1[key].some((item, index) => item !== obj2[key][index])) {
        return false;
      }
    } else if (obj1[key] !== obj2[key]) {
      return false;
    }
  }
  return true;
}
/**
 *根据字符串得到时间对象yyyy-MM-dd
 *
 * @export
 * @param {*} dateString
 * @returns
 */
export function getDateObj(dateString) {
  let myDate = null;
  let dateArray;
  let y;
  let m;
  let d;
  if (dateString) {
    dateArray = dateString.substring(0, 10).split('-');
    y = +(dateArray[0] || 2017);
    m = +(dateArray[1] || 2) - 1;
    d = +(dateArray[2] || 14);
    myDate = new Date();
    myDate.setFullYear(y, m, d);
    // 使时分秒保持0，保证时间比较的准确
    myDate.setHours(0);
    myDate.setMinutes(0);
    myDate.setSeconds(0);
  }
  return myDate;
}
/**
 *二分法快速排序
 *
 * @export
 * @param {*} arr
 * @param {*} key
 * @returns
 */
export function quickSort(arr, key) {
  if (arr.length <= 1) {
    return arr;
  }
  const pivotIndex = Math.floor(arr.length / 2);
  const pivot = arr.splice(pivotIndex, 1)[0];
  const pivotValue = key ? pivot[key] : pivot;
  const left = [];
  const right = [];
  for (let i = 0; i < arr.length; i += 1) {
    if (key ? arr[i][key] < pivotValue : arr[i] < pivotValue) {
      left.push(arr[i]);
    } else {
      right.push(arr[i]);
    }
  }
  return this.quickSort(left, key).concat([pivot], this.quickSort(right, key));
}
/**
 *根据字符串得到时间对象yyyy-MM-dd hh:mm:ss
 *
 * @export
 * @param {*} dateTimeString
 * @returns
 */
export function getDateTimeObj(dateTimeString) {
  let myDate = null;
  if (dateTimeString) {
    dateTimeString.replace(/(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})/g, (match, y, m, d, h, M, s) => {
      myDate = new Date();
      myDate.setFullYear(y, m - 1, d);
      myDate.setHours(h || 0);
      myDate.setMinutes(M || 0);
      myDate.setSeconds(s || 0);
    });
  }
  return myDate;
}
/**
 *url 转码
 *
 * @export
 * @param {*} param
 * @param {*} key
 * @param {*} encode
 * @returns
 */
export function urlEncode(param, key, encode) {
  console.log(param);
  if (param == null) return '';
  let paramStr = '';
  const t = typeof (param);
  if (t === 'string' || t === 'number' || t === 'boolean') {
    paramStr += `&${key}=${(encode == null || encode) ? encodeURIComponent(param) : param}`;
  } else {
    Object.keys(param).map((i) => {
      const k = key == null ? i : key + (param instanceof Array ? `[${i}]` : `.${i}`);
      paramStr += this.urlEncode(param[i], k, encode);
      return i;
    });
  }
  return paramStr;
}
/**
 *计算累计折旧
 *
 * @export
 * @param {*} usedDate
 * @param {number} [monthDepreciation=0]
 * @returns
 */
export function getAccumulatedDepreciation(usedDate, monthDepreciation = 0) {
  const n = new Date();
  const startY = usedDate.getYear();
  const startM = usedDate.getMonth();
  const endY = n.getYear();
  const endM = n.getMonth();
  const timeGap = (endY * 12 + endM) - (startY * 12 + startM); // 计算两个日期相差的月
  return timeGap < 0 ? 0 : (timeGap * Number(monthDepreciation.toFixed(2)));
}
/**
 *
 * hash表类
 * @export
 * @class Hashtable
 */
export class Hashtable {
  constructor() {
    this.hash = {}; // 创建Object对象
  }

  /**
     *哈希表的添加方法
     *
     * @param {*} key
     * @param {*} value
     * @returns
     * @memberof Hashtable
     */
  add(key, value) {
    if (typeof (key) !== 'undefined') {
      if (this.contains(key) === false) {
        this.hash[key] = typeof (value) === 'undefined' ? null : value;
        return true;
      }
      return false;
    }
    return false;
  }

  /**
     *哈希表的移除方法
     *
     * @param {*} key
     * @memberof Hashtable
     */
  remove(key) { delete this.hash[key]; }

  /**
     *
     *哈希表内部键的数量
     * @returns Number
     * @memberof Hashtable
     */
  count() {
    return Object.keys(this.hash).length;
  }

  /**
     *通过键值获取哈希表的值
     *
     * @param {string} key
     * @returns
     * @memberof Hashtable
     */
  items(key) { return this.hash[key]; }

  /**
     *在哈希表中判断某个值是否存在
     *
     * @param {string} key
     * @returns Boolean
     * @memberof Hashtable
     */
  contains(key) { return typeof (this.hash[key]) !== 'undefined'; }

  /**
     *清空哈希表内容的方法
     *
     * @memberof Hashtable
     */
  clear() {
    const keys = Object.keys(this.hash);
    keys.map((key) => {
      delete this.hash[key];
      return key;
    });
  }
}

/**
 *将对象转为url参数字符串
 *
 * @export
 * @param {*} obj
 * @returns string
 */
export function objToUrl(obj) {
  if (!obj) {
    return '';
  }
  const strArr = [];
  Object.entries(obj).map(([key, value]) => {
    strArr.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
    return value;
  });
  return strArr.join('&');
}
/**
 *获取当前浏览器信息
 *
 * @export
 * @returns string
 */
export function getBrowserInfo() {
  function mime(option, value) {
    const { mimeTypes } = navigator;
    const mimeTypeValues = Object.values(mimeTypes);
    for (let i = 0; i < mimeTypeValues.length; i += 1) {
      if (mimeTypeValues[i][option] === value) {
        return true;
      }
    }
    return false;
  }
  const ua = navigator.userAgent.toLocaleLowerCase();
  let browserType = null;
  if (ua.match(/msie/) != null || ua.match(/trident/) != null) {
    browserType = 'IE';
  } else if (ua.match(/firefox/) != null) {
    browserType = '火狐';
  } else if (ua.match(/ubrowser/) != null) {
    browserType = 'UC';
  } else if (ua.match(/opera/) != null) {
    browserType = '欧朋';
  } else if (ua.match(/bidubrowser/) != null) {
    browserType = '百度';
  } else if (ua.match(/metasr/) != null) {
    browserType = '搜狗';
  } else if (ua.match(/tencenttraveler/) != null || ua.match(/qqbrowse/) != null) {
    browserType = 'QQ';
  } else if (ua.match(/maxthon/) != null) {
    browserType = '遨游';
  } else if (ua.match(/chrome/) != null) {
    const is360 = mime('type', 'application/vnd.chromium.remoting-viewer');
    if (is360) {
      browserType = '360';
    } else {
      browserType = 'chrome';
    }
  } else if (ua.match(/safari/) != null) {
    browserType = 'Safari';
  }
  return browserType;
}

/**
 *判断链接是否是外链
 *
 * @export
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:|http:)/.test(path);
}

/**
 *按照传入的格式格式化时间对象
 *
 * @export
 * @param {Date} date
 * @param {String} fmt
 * @returns {String}
 */
export function dateFormat(date, fmt) { // author: meizz
  const o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
  };
  if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (`${date.getFullYear()}`).substr(4 - RegExp.$1.length));
  Object.entries(o).map(([key, value]) => {
    if (new RegExp(`(${key})`).test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (value) : ((`00${value}`).substr((`${value}`).length)));
    }
    return value;
  });
  return fmt;
}
/**
 *判断销方是否是税务局
 *
 * @export
 * @param {*} sellerName 销方名称
 * @returns {Boolean}
 */
export function checkFromTaxOffice(sellerName) {
  if (sellerName === null || sellerName === undefined) return false;
  const arr = ['国家税务总局', '税务局', '税务分局', '税务所'];
  return arr.some((item) => sellerName.indexOf(item) !== -1);
}
/**
 *用正则表达式实现html解码
 *
 * @export
 * @param {string} str
 * @returns {string}
 */
export function htmlDecodeByRegExp(str) {
  let s = '';
  if (str.length === 0) return '';
  s = str.replace(/&amp;/g, '&');
  s = s.replace(/&lt;/g, '<');
  s = s.replace(/&gt;/g, '>');
  s = s.replace(/&nbsp;/g, ' ');
  s = s.replace(/&#39;/g, "\'");
  s = s.replace(/&quot;/g, '"');
  return s;
}
/**
 *异步环境中，实现程序挂起
 *
 * @export
 * @param {string} timestr,单位毫秒数
 */
export function sleep(timestr) {
  return new Promise((resolve) => setTimeout(resolve, timestr));
}

export function coerceTruthyValueToArray(val) {
  if (Array.isArray(val)) {
    return val;
  }
  if (val) {
    return [val];
  }
  return [];
}

export function valueEquals(a, b) {
  // see: https://stackoverflow.com/questions/3115982/how-to-check-if-two-arrays-are-equal-with-javascript
  if (a === b) return true;
  if (!(a instanceof Array)) return false;
  if (!(b instanceof Array)) return false;
  if (a.length !== b.length) return false;
  for (let i = 0; i !== a.length; i += 1) {
    if (a[i] !== b[i]) return false;
  }
  return true;
}
export function noop() {

}

export function isDef(val) {
  return val !== undefined && val !== null;
}

/**
 * 平铺树结构
 *
 * @export
 * @param {Array} tree
 * @param {string} [childrenKey='children']
 * @returns {Array}
 */
export function flatTree(tree, childrenKey = 'children') {
  return tree.reduce((res, node) => {
    res.push(node);
    if (Array.isArray(node[childrenKey]) && node[childrenKey].length > 0) {
      res = res.concat(flatTree(node[childrenKey], childrenKey));
    }
    return res;
  }, []);
}

/**
 *遍历树
 *
 * @export
 * @param {Array} tree
 * @param {Function} callback
 * @param {string} [childrenKey='children']
 */
export function traverseTree(tree, callback, childrenKey = 'children') {
  const flatTreed = flatTree(tree, childrenKey);
  flatTreed.forEach(callback);
}

/**
 * 从路由的params或query中获取参数
 *
 * @export
 * @param {*} route 路由对象
 * @param {*} paramName 参数名称
 * @returns {*} 返回的参数内容
 */
export function getRouterParamFromParmsOrQuery(route, paramName) {
  const params = route?.params;
  const query = route?.query;
  return get(params, paramName) || get(query, paramName);
}

/**
 *合并表格行
 *
 * @export
 * @param {Object} obj
 * @param {Array} tableData
 * @param {Array} columnObjectArr
 */
export function calTableRowSpan(obj, tableData, columnObjectArr) {
  columnObjectArr = columnObjectArr || [{ columnIndex: 0, dataField: '' }];
  // tableData = tableData || [];
  let calArr = []; // 数组存放结果
  let flagVal = null;　 // 标识字段内容
  let flagPlace = -1; // 标识字段的位置
  let flagRepeatNum = 0; // 标识字段出现的重复次数
  // ELE的TABLE合并单元格方法每遍历一个单元格会调用一次，消耗巨大
  // 设置一个localStorage减少计算，目标是算出合并行的标记位和数量
  if (localStorage.getItem('RowSpanCalculateFlag') === null
        || Date.parse(new Date()) - localStorage.getItem('RowSpanCalculateFlag') > 1000
        || localStorage.getItem('RowSpanCalculateArr') === null) {
    columnObjectArr.forEach((item) => {
      for (let i = 0; i < tableData.length; i += 1) {
        if (tableData[i][item.dataField] === flagVal) {
          if (flagPlace === -1) flagPlace = i - 1;
          flagRepeatNum += 1;
        } else if (flagPlace !== -1) {
          calArr.push({ columnIndex: item.columnIndex, place: flagPlace, num: flagRepeatNum });
          flagVal = tableData[i][item.dataField];
          flagPlace = -1;
          flagRepeatNum = 0;
        } else {
          flagVal = tableData[i][item.dataField];
          flagPlace = -1;
          flagRepeatNum = 0;
        }
      }
    });
    localStorage.setItem('RowSpanCalculateFlag', Date.parse(new Date()));
    localStorage.setItem('RowSpanCalculateArr', JSON.stringify(calArr));
  } else { calArr = JSON.parse(localStorage.getItem('RowSpanCalculateArr')); }
  const {
    row,
    column,
    rowIndex,
    columnIndex,
  } = obj; // 从ele table传过来的数据进行解构

  // eslint-disable-next-line no-restricted-syntax
  for (const arrItem of calArr) {
    if (columnIndex === arrItem.columnIndex) {
      if (rowIndex === arrItem.place) {
        return {
          rowspan: arrItem.num + 1,
          colspan: 1,
        };
      }
      if (rowIndex > arrItem.place && rowIndex < arrItem.place + arrItem.num + 1) {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    }
  }
}

export function setCookie(name, value) {
  var Days = 30;
  var exp = new Date();
  exp.setTime(exp.getTime() + Days*24*60*60*1000);
    document.cookie = name + '=' + escape(value) + ';path=/;expires=' + exp.toGMTString();
  }

export function getCookie(name) {
  const matches = document.cookie.match(
    new RegExp(
      `(?:^|; )${name.replace(/([.$?*|{}()[\]\\/+^])/g, '\\$1')}=([^;]*)`,
    ),
  );
  return matches ? decodeURIComponent(matches[1]) : undefined;
}
export function delCookie(name) {
  const exp = new Date();
  exp.setTime(exp.getTime() - 100);
  const cval = getCookie(name);
  if (cval != null) { document.cookie = `${name}=${cval};expires=${exp.toGMTString()}`; }
}


/**
 * 根据发票代码 判断  发票类型
 *
 * @export
 * @param {*} InvoiceCode 路发票代码由对象
 * @param {*}
 * @returns {*}
 */
export function getInvoiceTypeByInvoiceCode(InvoiceCode) {
  let InvoiceTypeText = '其他';
  if(!InvoiceCode) return InvoiceTypeText
  const code = String(InvoiceCode)

  const hascodeHead = code.substring(0,1); // 第一位
  const codeEnd = code.substr(-2); // 最后两位
  const codeEight = code.substring(7,8) // 第8位

  if(code.length === 12) {
    // '144031539110','131001570151','133011501118','111001571071'或者以0开头，以11结尾。
    const hasCode = ['144031539110','131001570151','133011501118','111001571071'].indexOf(code) !== -1;
    if (hasCode || (hascodeHead && codeEnd === '11')) { // 1 发票号码为12位
      InvoiceTypeText = '增值税电子普通发票'
    }
    if (hascodeHead && (codeEnd === '04' || codeEnd=== '05')) {
      InvoiceTypeText = '增值税普通发票'
    }
    if (hascodeHead && (codeEnd === '06' || codeEnd=== '07')) {
      InvoiceTypeText = '增值税普通发票（卷票）'
    }
    if (hascodeHead && codeEnd === '12') {
      InvoiceTypeText = '增值税电子普通发票'
    }
    if (hascodeHead && codeEnd === '17') {
      InvoiceTypeText = '二手车销售统一发票'
    }
    if (hascodeHead && codeEnd === '13') {
      InvoiceTypeText = '增值税电子专用发票'
    }
    if (!hascodeHead && codeEight === '2') {
      InvoiceTypeText = '机动车销售统一发票'
    }
  } else if (code.length === 10) { // 2 发票号码为10位
    if (codeEight === '1' || codeEight === '5') {
      InvoiceTypeText = '增值税专用发票'
    }
    if (codeEight === '7' || codeEight === '2') {
      InvoiceTypeText = '增值税专用发票'
    }
    if (codeEight === '6' || codeEight === '3') {
      InvoiceTypeText = '增值税普通发票'
    }
  }
  return InvoiceTypeText
}

class Cancel {
  timer

  countTime = 0

  isCancel = false

  intervalTimer

  constructor() {
    this.intervalTimer = setInterval(() => {
      this.countTime += 1;
    }, 1000);
  }

  setTimer(timer) {
    this.timer = timer;
  }

  cancel() {
    this.isCancel = true;
    clearTimeout(this.timer);
    clearInterval(this.intervalTimer);
  }
}
/**
 * 递增的定时器，已默认步长*2 的速度递进
 * @param {Function} 需要执行的函数，如果是返回值为Promise的函数则在then之后才开始下一个计时器
 * @param {Number} 初始步长默认值为1000
 * @param {maxDelay} 最大的步长
 *
 * @return {Cancel} 计时器对象，带有已耗时countTime属性以及cancel停止计时器方法
 */
 export function progressTimer(fn, delay = 1000, maxDelay = 10000, cancel) {
  if (!cancel) {
    cancel = new Cancel();
  }
  const doProgress = () => {
    if (cancel.isCancel) return;
    cancel.setTimer(setTimeout(() => {
      progressTimer(fn, delay < maxDelay ? delay * 2 : delay, maxDelay, cancel);
    }, delay));
  };
  const rsp = fn();
  if (rsp instanceof Promise) {
    rsp.then(doProgress);
  } else {
    doProgress();
  }

  return cancel;
}

export const blobToBase64 = (file) => {
  return new Promise(function(resolve, reject) {
      const reader = new FileReader()
      let imgResult= ""
      reader.readAsDataURL(file)
      reader.onload = function() {
        imgResult = reader.result;
      }
      reader.onerror = function(error) {
      reject(error)
      }
      reader.onloadend = function() {
        resolve(imgResult)
      }
  })
}

/**
 * 控制 在v-for 如果出现太多渲染 白屏的情况
 */
export  const useDefer = (maxLength = 100)  => {
  let frameCount = 0

  const refreshFrameCount = () =>  {
    requestAnimationFrame(() => {
      frameCount+= 1
      if(frameCount < maxLength) {
        return refreshFrameCount()
      }
    })
  }

  refreshFrameCount()
  return (showInFrameCount) => {
    return frameCount >= showInFrameCount
  }
}



/**
 * FormData传数组对象
 * @param {formData} 传入原始new FormData() 处理好 返回出去
 * @param {data} 传入添加入 FormData的 对象或数组
 * @param {keyArray} 传入添加入 keyArray 数组
 */
 export const formDateProc = (formData, data, keyArray = []) => {
   /* 对数组及对象的索引方式做处理 */
   const keyProc = key => (typeof key === 'number' ? `[${key}]` : `.${key}`)

   /* 当data是数组的时候，data中的数据需要使用'[]'去索引，故先对数组数据做处理 */
   const keys = data instanceof Array
   ? Object.keys(data).map(v => parseInt(v, 10))
   : Object.keys(data)
   if (!formData) return

  keys.forEach(key => {
    /* 递归处理data中的Array及Object的情况 */
    if (data[key] instanceof Object && key !== 'multipartFile') {
        formDateProc(formData, data[key], [...keyArray, key])
    } else if (data[key] !== undefined && data[key] !== '') {
      /* 当keyArray中没值的时候直接使用key作为formData的key反之需要使用keySerialized作为key */
      const keySerialized = keyArray.reduce((pre, cur, index) => `${pre}${index ? `${keyProc(cur)}` : cur}`, '')
      if (!keySerialized) {
        formData.append(key, data[key])
      } else {
        formData.append(`${keySerialized}${keyProc(key)}`, data[key])
      }
    }
  })
  return formData
}


/**
 * 处理传参  为空的字符串 改为 null
 * @param {params} 传入原始params 处理好 返回出去
 */
export const paramsFormat = (params) => {
  const newParams = {}
  if(!isObject(params)) return params;
  Object.entries(params).forEach(([keyName, value]) => {
    if(isString(value)) {
      const val = value.trim() === '' ? null : value;
      Reflect.set(newParams, keyName, val);
    } else if(isArray(value)) {
      const newVal = value.map(item => paramsFormat(item))
      Reflect.set(newParams, keyName, newVal);
    } else if(isObject(value)) {
      const val = paramsFormat(value)
      Reflect.set(newParams, keyName, val);
    } else {
      Reflect.set(newParams, keyName, value);
    }
  })

  return newParams
}
