/* 修改表格的行高 */
.el-table .cell {
    min-height: 24px;
    line-height: 24px;
    font-size: 12px;
}

/* 加表格竖线 */
/* .el-table--border td, .el-table--border th{ border-right:1px solid #ecdfdf;} */
/* .el-table th.is-leaf,
  .el-table td {
    border-bottom: 1px solid #eaeef0; }
.el-table__footer-wrapper {
    border-top: 1px solid #eaeef0; } */
/* 选中行颜色修改 */
.el-table__body tr.current-row>td,
.el-table--striped .el-table__body tr.el-table__row--striped.current-row td {
    background-color: rgb(178, 217, 255);
}

/* 减少单元格的左padding */
.el-table .cell {
    padding-left: 6px;
    padding-right: 6px;
}

.el-table--border th:first-child .cell,
.el-table--border td:first-child .cell {
    padding-left: 6px;
    padding-right: 6px;
}

.el-table-column--selection .cell {
    padding-left: 6px;
    padding-right: 6px;
}

.el-table th {
    padding: 4px 0;
    font-weight: 500;
}

/* 去除格子td的padding */
.el-table td {
    padding: 0px 0;
    height: 30px;
    line-height: 30px;
}

.el-table .el-table__cell {
    padding: 0px 0;
    height: 30px;
    line-height: 30px;
}

.el-table td .el-button--text {
    padding: 0px 0;
}

/*解决固定列加合计，固定列滚动条不能拖动问题*/
.el-table__body-wrapper {
    z-index: 2;
}

/* 表格复选框拖拽多选蒙层样式 */
.el-table .moveSelected {
    position: absolute;
    background-color: blue;
    opacity: 0.3;
    pointer-events: none;
    border: 1px dashed #d9d9d9;
    top: 0;
    left: 0;
}

.el-cascader-menu__hover-zone {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.el-cascader-menu__item__keyword {
    padding-right: 0 !important;
}

/* 恢复原来的虚拟滚动条位置 */
.el-scrollbar .el-scrollbar__bar.is-vertical {
    left: unset !important;
    width: 5px !important;
}

/* 使用element-ui 兼容 pl-table的样式文件 */
.el-table__virtual-wrapper .el-table__body {
    width: 100%;
}

.el-table__fixed-body-wrapper {
    overflow: hidden;
}

.el-table--fixed__virtual-wrapper {
    width: auto !important;
}

/* 修复表格下边框显示不出来的问题 */
.el-table--group::after,
.el-table--border::after,
.el-table::before {
    content: '';
    position: absolute;
    background-color: #ecdfdf;
    z-index: 8;
}

.el-date-picker.has-sidebar {
    width: 342px;
}

.el-date-picker td.today div {
    color: #333;
}

.el-date-picker td.today {
    position: relative;

    &::after {
        content: "";
        position: absolute;
        width: 4px;
        height: 4px;
        border-radius: 4px;
        background-color: var(--color-theme-base);
        left: 50%;
        bottom: 20px;
        transform: translateX(-50%);
    }

}
.el-date-picker .el-date-table td.today {
    position: relative;
    div span{
        color: #333;
    }
    &::after {
        content: "";
        position: absolute;
        width: 4px;
        height: 4px;
        border-radius: 4px;
        background-color: var(--color-theme-base);
        left: 50%;
        bottom: 4px;
        transform: translateX(-50%);
    }

}

.tree--btn-wrapper.tree--btn-wrapper-show {
    transform: rotate(90deg);
}

.pl-tree-cell {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    padding-left: 1.5em;
}

.tree--btn-wrapper {
    position: absolute;
    top: 50%;
    width: 1em;
    height: 1em;
    line-height: 1em;
    margin-top: -.5em;
    transition: transform .2s ease-in-out;
    z-index: 1;
    user-select: none;
    color: #333 !important;
    font-size: 16px;
    cursor: pointer;
}

.el-button--text {
    color: #4992FF;
}

.el-upload-dragger .el-upload__text em {
    color: #4992FF;
}

/*解决固定列加合计，固定列滚动条不能拖动问题*/
.plTableBox .fixed-columns-roll-style .el-table__body-wrapper {
    z-index: 2 !important;
}

.plTableBox .fixed-columns-roll-style .pltableFixedWrapper {
    z-index: 2 !important;
}

.plTableBox .el-table__fixed {
    box-shadow: 5px 0 5px rgba(0, 0, 0, 0.1);
}

.plTableBox .el-table td {
    padding: 3px 0;
}


.el-autocomplete-suggestion li:hover {
    color: #188ae2;
}

.el-select-dropdown__item:hover {
    color: #188ae2;
}

/* 使form 错误提示放置在左上方 */
.el-form-item__error {
    color: #fff;
    font-size: 12px;
    line-height: 18px;
    position: absolute;
    background: #F56C6C;
    top: -18px;
    right: 0;
    left: auto;
    top: -14px;
    padding: 2px 4px;
    word-break: keep-all;
}

/* el-form-item__content 字号12 */
.el-form-item__content {
    font-size: var(--base-font-size);
}

.el-form-item--mini.el-form-item {
    margin-bottom: 8px;
}

/* .el-dropdown-menu__item{

} */

.el-dropdown-menu--medium .el-dropdown-menu__item {
    line-height: 30px;
    padding: 0 17px;
    font-size: 12px;
}


/* CheckBox 缩小右边距 */
.el-checkbox {
    margin-right: 12px;
}


/* el-drawer header 下边距由32调整为20 */
.el-drawer__header {
    margin-bottom: 20px;
}

/* el-message-box header  加居中属性后， padding-top 30 调整为11（默认） */
.el-message-box--center .el-message-box__header {
    padding: 11px 20px;
}

/* el-message-box  加居中属性后， padding-bottom 30 调整为（默认） */
.el-message-box--center {
    padding-bottom: 0px;
}

/* 修复时间区间选择器中间文字行高不对的问题 */
.el-range-editor--mini .el-range-separator {
    line-height: 24px;
}

.el-range-editor--mini .el-range__icon,
.el-range-editor--mini .el-range__close-icon {
    line-height: 23px;
}

/* radio 右边距过大，改小些 */
.el-radio {
    margin-right: 12px;
}

/* 单选和复选框的选中效果，改为只高亮选中框，文字保持黑色 */
.el-checkbox__input.is-checked+.el-checkbox__label,
.el-radio__input.is-checked+.el-radio__label {
    color: #262626;
}