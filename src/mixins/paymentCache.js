export default {
  mounted() {
    const module = this.$store.state.path[this.$store.state.path.length - 1];
    const cache = this.$store.state.cache[module];
    const arr = ['receiveForm', 'paymentForm', 'addLeftForm', 'editLeftForm'];

    if (cache) {
      for (let i = 0; i < arr.length; i += 1) {
        if (arr[i] === cache.type) {
          Object.entries(cache.data).forEach(([key, value]) => {
            this.$refs[arr[i]][key] = value;
          });
          // eslint-disable-next-line no-underscore-dangle
          this.$refs[arr[i]]._dataOrigin = 'cache';
          this.$store.state.cache[module] = null;
        }
      }
    }
  },
  beforeDestroy() {
    const module = this.$store.state.fromPath[this.$store.state.fromPath.length - 1];
    const arr = ['receiveForm', 'paymentForm', 'addLeftForm', 'editLeftForm', ''];
    for (let i = 0; i < arr.length; i += 1) {
      console.log(this.$refs[arr[i]] && this.$refs[arr[i]].show);
      if (this.$refs[arr[i]] && this.$refs[arr[i]].show) {
        console.log(this.$refs[arr[i]], module, '====');
        this.$store.state.cache[module] = {
          // eslint-disable-next-line no-underscore-dangle
          data: this.$refs[arr[i]]._data,
          type: arr[i],
        };
        console.log(this.$store.state.cache[module]);
      }
    }
  },
};
