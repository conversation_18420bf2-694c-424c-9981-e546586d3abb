/**
 * 币别汇率计算方法的mixin
 */

import dayjs from 'dayjs';

export default {
  data() {
    return {
      currencyListWithYear: [],
      currentCurrencyYear: '',
    };
  },
  computed: {
    currencyList() {
      return this.$store.state.selectData?.currencyList || [];
    },
  },
  mounted() {
    // this.$store.dispatch('selectData/getCurrencyList');
  },
  methods: {
    /**
     * 通过币别名称和时间获取汇率
     * @param currencyCode {String} 币别代码
     * @param accountPeriod {Number|String} 汇率属期
     * @param isMonthStart {boolean} 汇率取月初还是月底，默认月初
    */
    async getCurrencyRate(currencyCode, accountPeriod, isMonthStart = true) {
      const year = dayjs(String(accountPeriod)).year();
      if (year !== this.currentCurrencyYear || this.currencyListWithYear.length === 0) {
        this.currentCurrencyYear = year;
        const rsp = await this.$http.get('/rest/companyConfig/companyBasis/currency/v1.0/list',
          { params: { usedStatus: 1, year } });
        this.currencyListWithYear = rsp.data.data;
      }
      const currency = this.currencyListWithYear.find((item) => item.currencyCode === currencyCode);
      if (currency) {
        const { rates } = currency;
        console.log(accountPeriod);
        const rate = rates.find((item) => item.accountPeriod === Number(accountPeriod));
        if (!rate) return 1;
        return isMonthStart ? rate.monthStartRate : rate.monthEndRate;
      }
      return 1;
    },
  },
};
