import isEqual from 'lodash-es/isEqual';
import cloneDeep from 'lodash-es/cloneDeep';
import assign from 'lodash-es/assign';

export default {
  props: {},

  data() {
    return {
      // 正在编辑的表格行数据对象
      editingRow: null,
      // 正在编辑的表格行数据最后一次已同步的数据
      // （可用作比较的基准，判断数据是否被改变）
      oldRow: null,
      // 插入行的数据模板
      addRow: {},
      //
      addRowCopy: {},
      // 新行插入位置
      addRowIndex: 0,
      // 新插入的行的数据引用
      addCite: {},
      rowKey: '', // 判断行数据唯一的字段，用于比对，如果不传则直接全比对
    };
  },

  created() {
    // 初始化节流函数
    this.throttleBodyClick = Lib.Utils.throttle(this.rowClickControl).bind(this);
  },

  mounted() {
    // 记住新增行的引用，在提交后聚焦新增行时使用
    // this.addCite = this.addRow;
    setTimeout(() => {
      window.addEventListener('keyup', this.keup);
      // 监听用户点击， 在编辑行以外区域时，触发保存操作
      window.addEventListener('click', this.throttleBodyClick);
    }, 100);
  },

  beforeDestroy() {
    // 组件销毁时必须取消事件监听
    window.removeEventListener('keyup', this.keup);
    window.removeEventListener('click', this.throttleBodyClick);
  },

  watch: {

  },

  methods: {
    // 回车保存 esc 恢复修改
    keup(e) {
      if (e.keyCode === 13) {
        // 防止与各种下拉控件回车事件冲突， 判断下拉控件的展示状态
        if (
          $('.el-autocomplete-suggestion:not(.el-autocomplete-suggestion-auto):visible').length
          || $('.el-select-dropdown:visible').length
          || $('.el-picker-panel:visible').length
          || $('.el-dialog__wrapper:visible').length
        ) {
          return;
        }
        this.saveRow();
      } else if (e.keyCode === 27) {
        this.repealRow();
      }
    },

    nextInput() {
      const el = this.getNextElement($('input:focus')[0]);
      if (!el) {
        return false;
      }
      $('input:focus').trigger('blur');
      el.focus();
      return true;
    },

    getNextElement(field, key) {
      const form = document.querySelectorAll('input');
      let e;
      for (e = 0; e < form.length; e += 1) {
        if (field === form[e]) {
          break;
        }
      }
      if (key === 33) {
        return form[e - 1];
      }
      return form[e + 1];
    },

    rowClickControl($e) {
      if (!this.$refs.table) return;
      setTimeout(() => {
        const excludeKeyword = [
          '确定',
          '取消',
          '删除',
          '退出',
          '上一步',
          '下一步',
        ];
        const index = this.tableData.indexOf(this.editingRow);
        const $currentTable = this.$refs.table.$el;
        const $app = document.querySelector('#app');

        // 获取 $currentRow 的方式，优先改成直接搜索 class，因为通过 index 的方式无法支持树形 Table 的子行
        const $rows = $currentTable.querySelectorAll('.el-table__row');
        let $currentRow = $currentTable.querySelector('.el-table__row.current-row');
        if (!$currentRow) {
          $currentRow = $rows.item(index);
        }

        const $currentFixedRow = document
          .querySelectorAll('.el-table__fixed .el-table__row')
          .item(index);
        const $target = $e.target;
        let $parent = $target;
        let isContain = false;
        let isAppInner = false;
        let isTableInner = false;

        if (excludeKeyword.indexOf($target.innerText) !== -1) {
          return;
        }
        if ($currentRow) {
        // 遍历点击点的所有父节点，看是否有当前编辑行
          while ($parent) {
            if ($parent === $currentTable) {
              isTableInner = true;
            }
            if ($parent === $app) {
              isAppInner = true;
            }
            if ($parent === $currentRow || $parent === $currentFixedRow) {
              isContain = true;
              isTableInner = true;
              break;
            }
            $parent = $parent.parentNode;
          }
          if (!isContain && isAppInner) {
            this.saveRow();
          }
          if (!isTableInner && this.$refs.table) {
            this.$refs.table.setCurrentRow(null);
          }
        }
      });
    },

    isEditing(row) {
      return this.editingRow === row;
    },

    isEditable() {
      return true;
    },

    addInsertRow(noSetEditRow) {
      this.addRow.isAddRow = true;
      this.addRowCopy = cloneDeep(this.addRow);
      this.addCite = cloneDeep(this.addRow);
      this.tableData.splice(
        this.addRowIndex,
        0,
        this.addCite,
      );
      if (noSetEditRow) return;
      this.editingRow = this.tableData[this.addRowIndex];
      this.oldRow = cloneDeep(this.addRow);
      this.$refs.table.setCurrentRow(this.editingRow);
    },
    checkRowRemovable(props) {
      const { row, index } = props;
      return (
        this.canEdit
        && index > 0
        && (this.editingRow !== row || this.isEqualObject(row, this.oldRow))
        && this.tableData.indexOf(row) !== -1
      );
    },
    async editRow(row, col, e) {
      // 阻止冒泡，防止触发window的mousedown事件
      e.stopPropagation();

      // 不能编辑或当前行已经处于编辑
      if (this.canEdit === false || this.editingRow === row) {
        return;
      }

      // 执行保存，保证正在编辑的行合法保存再编辑下条
      const saveFlag = await this.saveRow();

      if (saveFlag && this.isEditable(row)) {
        this.oldRow = cloneDeep(row);
        this.editingRow = row;
        this.$refs.table.setCurrentRow(row);
      }
    },

    deleteRow(row) {
      this.postDelete(row);
    },

    async saveRow(noSetEditRow) {
      // 没有在编辑的行，直接返回保存成功

      if (!this.editingRow) {
        return true;
      }

      if (!this.editingRow.isAddRow) {
        if (!this.validRowFn()) return false;
        if (!this.isEqualObject(this.editingRow, this.oldRow)) {
          const falg = await this.postSave(this.editingRow);
          if (!falg) return false;
        }
        if (noSetEditRow) return true;
        if (this.addRow.isAddRow) {
          // 有新增行
          this.editingRow = this.tableData[this.addRowIndex];
          this.oldRow = cloneDeep(this.tableData[this.addRowIndex]);
        } else {
          // 没有新增行
          this.editingRow = null;
          this.oldRow = null;
        }
        if (this.$refs.table) {
          this.$refs.table.setCurrentRow(null);
        }
        return true;
      }
      // 是新增行，并与新增行的备份不同（新增行有输入、更改）
      if (!this.isEqualObject(this.editingRow, this.addRowCopy)) {
        if (!this.validRowFn()) {
          return false;
        }
        const falg = await this.postSave(this.editingRow);

        this.$nextTick(() => {
          const $table = this.$refs.table;

          $table.setCurrentRow(this.addCite);
          const firstTd = $table.$el.querySelector('.el-table__body-wrapper table tbody tr:first-child td');

          if (firstTd) {
            const input = firstTd.querySelector('input');
            if (firstTd.querySelector('.el-select')) {
              // 非输入框按回车聚焦
              $(window).one('keydown', (event) => {
                if (event.which === 13 && input) {
                  input.focus();
                }
              });
            } else if (input) {
              // 输入框直接聚焦
              input.focus();
            }
          }
          // console.log('firstTd', firstTd);
        });

        if (!falg) return false;
      }
      return true;
    },

    validRowFn() {
      return true;
    },

    getSpecialField() {
      return {};
    },

    initAddRow() {
      // TODO 该行无意义，实现 bug？
      Object.entries(([keyName, value]) => {
        this.$set(this.addRow, keyName, value);
      });
    },

    repealRow() {
      assign(this.editingRow, this.oldRow);
      this.editingRow = null;

      if (this.addRow.isAddRow) {
        // 有新增行
        this.editingRow = this.tableData[this.addRowIndex];
        this.oldRow = cloneDeep(this.addRow);
      } else {
        // 没有新增行
        this.editingRow = null;
        this.oldRow = null;
      }
      if (this.$refs.table) {
        this.$refs.table.setCurrentRow(null);
      }
    },

    isEqualObject(obj1, obj2) {
      return isEqual(obj1, obj2);
    },
  },

};
