import isFunction from 'lodash-es/isFunction';
import isString from 'lodash-es/isString';
import isPlainObject from 'lodash-es/isPlainObject';
import endsWith from 'lodash-es/endsWith';
import forOwn from 'lodash-es/forOwn';
import result from 'lodash-es/result';
import get from 'lodash-es/get';
import { invokeClick } from '@/assets/Utils';

export default {
  methods: {
    /**
     * 执行导出动作
     * @param {string} downloadUrl 导出请求地址
     * @param {Object} options 导出选项
     * @param {Object} options.params 导出请求参数
     * @param {()=>boolean | string} 校验方法或校验当前实例上的校验方法名
     */
    doExport(downloadUrl, options) {
      if (isPlainObject(downloadUrl)) {
        options = downloadUrl;
        downloadUrl = result(options, 'downloadUrl');
      }

      if (!downloadUrl) return;

      // 校验是否要继续导出
      let validate = get(options, 'validate');
      if (isString(validate) && isFunction(this[validate])) {
        validate = this[validate];
      }
      if (isFunction(validate)) {
        if (!validate.call(this)) return;
      }

      // 处理参数
      let params = get(options, 'params');
      if (isString(params) && isFunction(this[params])) {
        params = this[params]();
      }
      if (isPlainObject(params)) {
        if (!endsWith(downloadUrl, '?')) downloadUrl += '?';
        forOwn(params, (value, key) => {
          downloadUrl += `${key}=${value}&`;
        });
      }

      // 伪造 a 元素，并派发点击事件
      const anchorElement = document.createElement('a');
      anchorElement.setAttribute('href', downloadUrl);
      invokeClick(anchorElement);

      if (this.$message) {
        this.$message('正在导出文件，请等待。');
      }

      // 通知外部组件
      this.$emit('on-export');
    },
  },
};
