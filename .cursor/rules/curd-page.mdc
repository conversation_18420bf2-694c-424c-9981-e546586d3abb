---
description: 
globs: 
alwaysApply: true
---
# QuickfWeb 项目专属 AI 规则

## 1. 技术栈相关规则
- 项目使用 Vue.js 2.7版本框架，需要遵循 Vue 的最佳实践
- 使用 Element UI 组件库, 不是 element-plus，保持界面风格统一
- 代码修改需符合项目既有的文件组织结构（src/pages, src/components 等）

## 2. 业务领域规则
- 重点关注税务相关功能的实现和优化
- 涉及税务申报、发票管理等核心业务逻辑时需特别谨慎
- 数据处理需考虑各种税种的特殊性

## 3. 代码风格规则
- 使用 Vue Setup 语法
- 组件命名采用 PascalCase
- 方法命名采用 camelCase
- 保持既有的文件命名规范（如 index.vue）

## 4. 性能优化规则
- 大数据列表需要考虑性能优化
- 合理使用 computed 和 watch
- 避免不必要的数据请求和计算

## 5. UI/UX 规则
- 保持与 Element UI 的设计风格一致
- 表单操作需要有适当的验证和提示
- 批量操作需要有确认机制

## 6. 安全规则
- 敏感数据（如税务信息）的处理需要特别注意
- API 调用需要做好错误处理
- 用户权限控制需要严格把关

## 7. 文档规则
- 关键业务逻辑需要添加清晰的注释
- 复杂的税务处理逻辑需要详细说明
- 组件的 props 和事件需要有明确的文档说明

## 8. 测试建议
- 税务计算相关的功能需要完整的测试用例
- 批量操作功能需要考虑边界情况
- 数据验证逻辑需要充分测试

## 9. 代码复用规则
- 提取共用的税务处理逻辑到 hooks 中
- 通用的工具函数放在 utils 目录
- 共用的接口调用封装在 api 目录

## 10. 错误处理规则
- 网络请求错误需要统一处理
- 用户操作错误需要有友好的提示